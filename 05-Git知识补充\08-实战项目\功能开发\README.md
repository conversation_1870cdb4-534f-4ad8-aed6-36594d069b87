# 功能开发实战

## 📋 概述

本实战项目演示了完整的功能开发流程，从需求分析到代码合并，涵盖了 Git 在实际开发中的各种应用场景。

## 🎯 项目背景

**项目名称**: 在线任务管理系统  
**功能需求**: 添加用户通知功能  
**开发周期**: 1-2 周  
**团队规模**: 3-5 人  

## 🚀 开发流程实战

### 阶段 1: 需求分析和分支创建

**1.1 需求分析**
```markdown
## 用户通知功能需求

### 功能描述
- 用户可以接收任务相关通知
- 支持邮件和站内消息两种方式
- 用户可以自定义通知偏好

### 技术要求
- 后端 API 开发
- 前端界面实现
- 数据库表设计
- 单元测试覆盖

### 验收标准
- [ ] 通知发送成功率 > 99%
- [ ] 界面响应时间 < 2s
- [ ] 测试覆盖率 > 80%
```

**1.2 创建功能分支**
```bash
# 同步主分支
git checkout main
git pull origin main

# 创建功能分支
git checkout -b feature/TASK-123-user-notifications

# 推送分支到远程
git push -u origin feature/TASK-123-user-notifications
```

### 阶段 2: 数据库设计

**2.1 设计数据表**
```sql
-- notifications.sql
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

CREATE TABLE notification_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL UNIQUE,
    email_enabled BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    task_assigned BOOLEAN DEFAULT TRUE,
    task_completed BOOLEAN DEFAULT TRUE,
    task_overdue BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**2.2 提交数据库变更**
```bash
# 添加数据库迁移文件
git add database/migrations/2023_12_01_create_notifications_table.sql
git commit -m "feat(db): 添加通知相关数据表

- 创建 notifications 表存储通知信息
- 创建 notification_preferences 表存储用户偏好
- 添加必要的索引优化查询性能

Related to TASK-123"
```

### 阶段 3: 后端 API 开发

**3.1 实现通知服务**
```javascript
// src/services/NotificationService.js
class NotificationService {
    async createNotification(userId, type, title, content) {
        const notification = await Notification.create({
            user_id: userId,
            type,
            title,
            content
        });
        
        // 发送实时通知
        await this.sendRealTimeNotification(notification);
        
        // 根据用户偏好发送邮件
        await this.sendEmailIfEnabled(notification);
        
        return notification;
    }
    
    async getUserNotifications(userId, page = 1, limit = 20) {
        return await Notification.findAndCountAll({
            where: { user_id: userId },
            order: [['created_at', 'DESC']],
            limit,
            offset: (page - 1) * limit
        });
    }
    
    async markAsRead(notificationId, userId) {
        return await Notification.update(
            { is_read: true },
            { 
                where: { 
                    id: notificationId, 
                    user_id: userId 
                } 
            }
        );
    }
}
```

**3.2 实现 API 控制器**
```javascript
// src/controllers/NotificationController.js
class NotificationController {
    async getNotifications(req, res) {
        try {
            const { page = 1, limit = 20 } = req.query;
            const userId = req.user.id;
            
            const result = await notificationService.getUserNotifications(
                userId, 
                parseInt(page), 
                parseInt(limit)
            );
            
            res.json({
                success: true,
                data: result.rows,
                pagination: {
                    total: result.count,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    totalPages: Math.ceil(result.count / limit)
                }
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }
    
    async markAsRead(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;
            
            await notificationService.markAsRead(id, userId);
            
            res.json({
                success: true,
                message: '通知已标记为已读'
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }
}
```

**3.3 提交后端代码**
```bash
# 添加后端文件
git add src/services/NotificationService.js
git add src/controllers/NotificationController.js
git add src/routes/notifications.js

git commit -m "feat(api): 实现通知 API 接口

- 添加 NotificationService 处理通知业务逻辑
- 实现 NotificationController 处理 HTTP 请求
- 支持获取通知列表和标记已读功能
- 添加分页和错误处理

Related to TASK-123"
```

### 阶段 4: 前端界面开发

**4.1 通知组件实现**
```jsx
// src/components/NotificationList.jsx
import React, { useState, useEffect } from 'react';
import { notificationAPI } from '../api/notifications';

const NotificationList = () => {
    const [notifications, setNotifications] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({});

    useEffect(() => {
        loadNotifications();
    }, []);

    const loadNotifications = async (page = 1) => {
        try {
            setLoading(true);
            const response = await notificationAPI.getNotifications(page);
            setNotifications(response.data);
            setPagination(response.pagination);
        } catch (error) {
            console.error('加载通知失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const markAsRead = async (id) => {
        try {
            await notificationAPI.markAsRead(id);
            setNotifications(prev => 
                prev.map(notification => 
                    notification.id === id 
                        ? { ...notification, is_read: true }
                        : notification
                )
            );
        } catch (error) {
            console.error('标记已读失败:', error);
        }
    };

    if (loading) {
        return <div className="loading">加载中...</div>;
    }

    return (
        <div className="notification-list">
            <h2>通知中心</h2>
            {notifications.length === 0 ? (
                <div className="empty-state">暂无通知</div>
            ) : (
                <div className="notifications">
                    {notifications.map(notification => (
                        <div 
                            key={notification.id}
                            className={`notification-item ${notification.is_read ? 'read' : 'unread'}`}
                        >
                            <div className="notification-content">
                                <h3>{notification.title}</h3>
                                <p>{notification.content}</p>
                                <span className="timestamp">
                                    {new Date(notification.created_at).toLocaleString()}
                                </span>
                            </div>
                            {!notification.is_read && (
                                <button 
                                    onClick={() => markAsRead(notification.id)}
                                    className="mark-read-btn"
                                >
                                    标记已读
                                </button>
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default NotificationList;
```

**4.2 提交前端代码**
```bash
# 添加前端文件
git add src/components/NotificationList.jsx
git add src/api/notifications.js
git add src/styles/notifications.css

git commit -m "feat(ui): 实现通知列表界面

- 添加 NotificationList 组件显示通知
- 实现通知加载和分页功能
- 支持标记通知为已读
- 添加响应式样式设计

Related to TASK-123"
```

### 阶段 5: 测试开发

**5.1 单元测试**
```javascript
// tests/services/NotificationService.test.js
const { NotificationService } = require('../../src/services/NotificationService');
const { Notification } = require('../../src/models');

describe('NotificationService', () => {
    let notificationService;

    beforeEach(() => {
        notificationService = new NotificationService();
    });

    describe('createNotification', () => {
        it('应该成功创建通知', async () => {
            const mockNotification = {
                id: 1,
                user_id: 1,
                type: 'task_assigned',
                title: '新任务分配',
                content: '您有一个新的任务需要处理'
            };

            Notification.create = jest.fn().mockResolvedValue(mockNotification);
            
            const result = await notificationService.createNotification(
                1, 'task_assigned', '新任务分配', '您有一个新的任务需要处理'
            );

            expect(result).toEqual(mockNotification);
            expect(Notification.create).toHaveBeenCalledWith({
                user_id: 1,
                type: 'task_assigned',
                title: '新任务分配',
                content: '您有一个新的任务需要处理'
            });
        });
    });

    describe('getUserNotifications', () => {
        it('应该返回用户通知列表', async () => {
            const mockResult = {
                rows: [
                    { id: 1, title: '通知1', is_read: false },
                    { id: 2, title: '通知2', is_read: true }
                ],
                count: 2
            };

            Notification.findAndCountAll = jest.fn().mockResolvedValue(mockResult);

            const result = await notificationService.getUserNotifications(1, 1, 20);

            expect(result).toEqual(mockResult);
            expect(Notification.findAndCountAll).toHaveBeenCalledWith({
                where: { user_id: 1 },
                order: [['created_at', 'DESC']],
                limit: 20,
                offset: 0
            });
        });
    });
});
```

**5.2 集成测试**
```javascript
// tests/integration/notifications.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('Notifications API', () => {
    let authToken;

    beforeEach(async () => {
        // 获取认证令牌
        const loginResponse = await request(app)
            .post('/api/auth/login')
            .send({
                email: '<EMAIL>',
                password: 'password123'
            });
        
        authToken = loginResponse.body.token;
    });

    describe('GET /api/notifications', () => {
        it('应该返回用户通知列表', async () => {
            const response = await request(app)
                .get('/api/notifications')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.data).toBeInstanceOf(Array);
            expect(response.body.pagination).toBeDefined();
        });

        it('应该支持分页参数', async () => {
            const response = await request(app)
                .get('/api/notifications?page=2&limit=10')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.pagination.page).toBe(2);
            expect(response.body.pagination.limit).toBe(10);
        });
    });

    describe('PUT /api/notifications/:id/read', () => {
        it('应该成功标记通知为已读', async () => {
            const response = await request(app)
                .put('/api/notifications/1/read')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('通知已标记为已读');
        });
    });
});
```

**5.3 提交测试代码**
```bash
# 添加测试文件
git add tests/services/NotificationService.test.js
git add tests/integration/notifications.test.js
git add tests/components/NotificationList.test.jsx

git commit -m "test: 添加通知功能测试用例

- 添加 NotificationService 单元测试
- 实现通知 API 集成测试
- 添加前端组件测试
- 测试覆盖率达到 85%

Related to TASK-123"
```

### 阶段 6: 文档和代码审查

**6.1 更新文档**
```markdown
# API 文档更新

## 通知相关接口

### 获取通知列表
- **URL**: `GET /api/notifications`
- **参数**: 
  - `page`: 页码（可选，默认 1）
  - `limit`: 每页数量（可选，默认 20）
- **响应**: 
  ```json
  {
    "success": true,
    "data": [...],
    "pagination": {...}
  }
  ```

### 标记通知已读
- **URL**: `PUT /api/notifications/:id/read`
- **响应**: 
  ```json
  {
    "success": true,
    "message": "通知已标记为已读"
  }
  ```
```

**6.2 提交文档**
```bash
git add docs/api/notifications.md
git add README.md

git commit -m "docs: 更新通知功能文档

- 添加通知 API 接口文档
- 更新项目 README 说明
- 添加功能使用示例

Related to TASK-123"
```

**6.3 创建 Pull Request**
```bash
# 推送所有更改
git push origin feature/TASK-123-user-notifications

# 在 GitHub/GitLab 创建 PR
# PR 标题: feat: 实现用户通知功能 (TASK-123)
# PR 描述: 详细说明功能实现和测试情况
```

### 阶段 7: 代码审查和合并

**7.1 响应审查意见**
```bash
# 根据审查意见修改代码
git add modified-files
git commit -m "fix: 根据代码审查意见优化实现

- 优化数据库查询性能
- 改进错误处理逻辑
- 修复前端样式问题

Related to TASK-123"

git push origin feature/TASK-123-user-notifications
```

**7.2 合并到主分支**
```bash
# 审查通过后，合并分支
git checkout main
git pull origin main
git merge --no-ff feature/TASK-123-user-notifications
git push origin main

# 删除功能分支
git branch -d feature/TASK-123-user-notifications
git push origin --delete feature/TASK-123-user-notifications
```

## 📊 项目总结

### 开发统计
- **开发时间**: 10 天
- **提交次数**: 8 次
- **代码行数**: +1,200 行
- **测试覆盖率**: 87%

### 学到的经验
1. **分支管理**: 使用功能分支隔离开发
2. **提交规范**: 清晰的提交信息便于追踪
3. **测试驱动**: 完善的测试保证代码质量
4. **文档同步**: 及时更新文档避免信息滞后

### 改进建议
1. 更早进行代码审查
2. 增加自动化测试覆盖
3. 优化 CI/CD 流程
4. 加强团队沟通协调

---

通过这个完整的功能开发实战，展示了 Git 在实际项目中的应用，从分支创建到代码合并的全流程管理。
