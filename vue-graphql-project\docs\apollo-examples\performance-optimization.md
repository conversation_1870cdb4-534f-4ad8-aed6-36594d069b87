# Apollo Server Express 性能优化指南

## 1. DataLoader 实现

### 基础 DataLoader 设置

```typescript
// src/loaders/DataLoaders.ts
import DataLoader from 'dataloader';
import { db } from '../data/mockData';
import { User, Task, Project, Comment } from '../models';

export class DataLoaders {
  // 用户加载器
  public readonly userLoader = new DataLoader<string, User | null>(
    async (userIds: readonly string[]) => {
      console.log(`Loading users: ${userIds.join(', ')}`);
      const users = await db.getUsersByIds([...userIds]);
      return userIds.map(id => users.find(user => user.id === id) || null);
    },
    {
      // 配置选项
      maxBatchSize: 100, // 最大批处理大小
      cache: true, // 启用缓存
      cacheKeyFn: (key) => key, // 缓存键函数
    }
  );

  // 任务加载器（按用户ID）
  public readonly tasksByUserLoader = new DataLoader<string, Task[]>(
    async (userIds: readonly string[]) => {
      console.log(`Loading tasks for users: ${userIds.join(', ')}`);
      const allTasks = await db.getTasks();
      return userIds.map(userId => 
        allTasks.filter(task => 
          task.assigneeId === userId || task.creatorId === userId
        )
      );
    }
  );

  // 项目加载器（按用户ID）
  public readonly projectsByUserLoader = new DataLoader<string, Project[]>(
    async (userIds: readonly string[]) => {
      console.log(`Loading projects for users: ${userIds.join(', ')}`);
      const allProjects = await db.getProjects();
      return userIds.map(userId => 
        allProjects.filter(project => 
          project.ownerId === userId || project.memberIds.includes(userId)
        )
      );
    }
  );

  // 评论加载器（按任务ID）
  public readonly commentsByTaskLoader = new DataLoader<string, Comment[]>(
    async (taskIds: readonly string[]) => {
      console.log(`Loading comments for tasks: ${taskIds.join(', ')}`);
      const allComments = await db.getComments();
      return taskIds.map(taskId => 
        allComments.filter(comment => comment.taskId === taskId)
      );
    }
  );

  // 任务加载器（按项目ID）
  public readonly tasksByProjectLoader = new DataLoader<string, Task[]>(
    async (projectIds: readonly string[]) => {
      console.log(`Loading tasks for projects: ${projectIds.join(', ')}`);
      const allTasks = await db.getTasks();
      return projectIds.map(projectId => 
        allTasks.filter(task => task.projectId === projectId)
      );
    }
  );

  // 项目成员加载器
  public readonly projectMembersLoader = new DataLoader<string, User[]>(
    async (projectIds: readonly string[]) => {
      console.log(`Loading members for projects: ${projectIds.join(', ')}`);
      const allUsers = await db.getUsers();
      const allProjects = await db.getProjects();
      
      return projectIds.map(projectId => {
        const project = allProjects.find(p => p.id === projectId);
        if (!project) return [];
        
        const memberIds = [project.ownerId, ...project.memberIds];
        return allUsers.filter(user => memberIds.includes(user.id));
      });
    }
  );

  // 清除所有缓存
  public clearAll(): void {
    this.userLoader.clearAll();
    this.tasksByUserLoader.clearAll();
    this.projectsByUserLoader.clearAll();
    this.commentsByTaskLoader.clearAll();
    this.tasksByProjectLoader.clearAll();
    this.projectMembersLoader.clearAll();
  }

  // 清除特定用户相关的缓存
  public clearUserCache(userId: string): void {
    this.userLoader.clear(userId);
    this.tasksByUserLoader.clear(userId);
    this.projectsByUserLoader.clear(userId);
  }
}

// 创建 DataLoaders 实例的工厂函数
export const createDataLoaders = (): DataLoaders => {
  return new DataLoaders();
};
```

### 在 Resolvers 中使用 DataLoader

```typescript
// src/resolvers/optimizedResolvers.ts
const optimizedResolvers = {
  // 字段解析器使用 DataLoader
  User: {
    tasks: async (parent: User, args: any, context: Context) => {
      return await context.loaders.tasksByUserLoader.load(parent.id);
    },
    
    projects: async (parent: User, args: any, context: Context) => {
      return await context.loaders.projectsByUserLoader.load(parent.id);
    },
  },

  Project: {
    owner: async (parent: Project, args: any, context: Context) => {
      return await context.loaders.userLoader.load(parent.ownerId);
    },
    
    members: async (parent: Project, args: any, context: Context) => {
      return await context.loaders.projectMembersLoader.load(parent.id);
    },
    
    tasks: async (parent: Project, args: any, context: Context) => {
      return await context.loaders.tasksByProjectLoader.load(parent.id);
    },
  },

  Task: {
    creator: async (parent: Task, args: any, context: Context) => {
      return await context.loaders.userLoader.load(parent.creatorId);
    },
    
    assignee: async (parent: Task, args: any, context: Context) => {
      return parent.assigneeId 
        ? await context.loaders.userLoader.load(parent.assigneeId)
        : null;
    },
    
    project: async (parent: Task, args: any, context: Context) => {
      // 这里可能需要一个项目加载器
      return db.getProjectById(parent.projectId);
    },
    
    comments: async (parent: Task, args: any, context: Context) => {
      return await context.loaders.commentsByTaskLoader.load(parent.id);
    },
  },

  Comment: {
    author: async (parent: Comment, args: any, context: Context) => {
      return await context.loaders.userLoader.load(parent.authorId);
    },
    
    task: async (parent: Comment, args: any, context: Context) => {
      return db.getTaskById(parent.taskId);
    },
  },
};
```

## 2. 查询复杂度限制

```typescript
// src/middleware/queryComplexity.ts
import { createComplexityLimitRule } from 'graphql-query-complexity';
import { GraphQLSchema } from 'graphql';

// 复杂度分析配置
export const complexityLimitRule = createComplexityLimitRule(1000, {
  // 自定义字段复杂度
  scalarCost: 1,
  objectCost: 2,
  listFactor: 10,
  introspectionCost: 1000,
  
  // 字段特定复杂度
  fieldExtensions: {
    complexity: (args: any) => {
      // 分页查询的复杂度计算
      if (args.limit) {
        return args.limit * 2;
      }
      return 1;
    },
  },
  
  // 错误处理
  onComplete: (complexity: number) => {
    console.log(`Query complexity: ${complexity}`);
  },
});

// 查询深度限制
export const depthLimitRule = (maxDepth: number) => {
  return (context: any) => {
    const { document } = context;
    const depth = getQueryDepth(document);
    
    if (depth > maxDepth) {
      throw new Error(`Query depth ${depth} exceeds maximum depth ${maxDepth}`);
    }
  };
};

// 计算查询深度的辅助函数
function getQueryDepth(document: any): number {
  let maxDepth = 0;
  
  function calculateDepth(node: any, currentDepth: number = 0): void {
    if (node.selectionSet) {
      const newDepth = currentDepth + 1;
      maxDepth = Math.max(maxDepth, newDepth);
      
      node.selectionSet.selections.forEach((selection: any) => {
        calculateDepth(selection, newDepth);
      });
    }
  }
  
  document.definitions.forEach((definition: any) => {
    if (definition.selectionSet) {
      calculateDepth(definition);
    }
  });
  
  return maxDepth;
}
```

## 3. 缓存策略

### Redis 缓存实现

```typescript
// src/cache/RedisCache.ts
import Redis from 'ioredis';

export class RedisCache {
  private redis: Redis;
  private defaultTTL: number = 3600; // 1小时

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    this.redis.on('error', (error) => {
      console.error('Redis connection error:', error);
    });

    this.redis.on('connect', () => {
      console.log('Connected to Redis');
    });
  }

  // 获取缓存
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  // 设置缓存
  async set(key: string, value: any, ttl: number = this.defaultTTL): Promise<void> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  // 删除缓存
  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  // 批量删除（通过模式）
  async delPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      console.error('Cache pattern delete error:', error);
    }
  }

  // 检查键是否存在
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  // 设置过期时间
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.redis.expire(key, ttl);
    } catch (error) {
      console.error('Cache expire error:', error);
    }
  }

  // 获取剩余过期时间
  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key);
    } catch (error) {
      console.error('Cache TTL error:', error);
      return -1;
    }
  }

  // 关闭连接
  async disconnect(): Promise<void> {
    await this.redis.disconnect();
  }
}

export const cache = new RedisCache();
```

### 缓存装饰器

```typescript
// src/decorators/cache.ts
import { cache } from '../cache/RedisCache';

// 缓存装饰器
export function Cached(ttl: number = 3600, keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // 生成缓存键
      const cacheKey = keyGenerator 
        ? keyGenerator(...args)
        : `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cachedResult = await cache.get(cacheKey);
      if (cachedResult !== null) {
        console.log(`Cache hit: ${cacheKey}`);
        return cachedResult;
      }

      // 执行原方法
      console.log(`Cache miss: ${cacheKey}`);
      const result = await method.apply(this, args);

      // 存储到缓存
      await cache.set(cacheKey, result, ttl);

      return result;
    };
  };
}

// 使用示例
class UserService {
  @Cached(300, (userId: string) => `user:${userId}`)
  async getUserById(userId: string): Promise<User | null> {
    return db.getUserById(userId);
  }

  @Cached(600, (userId: string) => `user:${userId}:stats`)
  async getUserStats(userId: string): Promise<any> {
    const tasks = await db.getTasksByUserId(userId);
    const projects = await db.getProjectsByUserId(userId);
    
    return {
      taskCount: tasks.length,
      projectCount: projects.length,
      completedTasks: tasks.filter(t => t.status === 'DONE').length,
    };
  }
}
```
