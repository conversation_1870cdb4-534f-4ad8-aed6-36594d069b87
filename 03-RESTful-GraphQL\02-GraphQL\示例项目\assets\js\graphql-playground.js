/**
 * GraphQL Playground 主逻辑
 */

import { users, comments, graphqlSchema, exampleQueries } from '../data/graphql-data.js';

class GraphQLPlayground {
    constructor() {
        this.users = [...users];
        this.comments = [...comments];
        this.queryCount = 0;
        this.totalResponseTime = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.displaySchema();
        console.log('🚀 GraphQL Playground 已加载');
        console.log('💡 提示: 尝试修改查询并点击执行按钮');
    }

    bindEvents() {
        // 绑定执行查询按钮
        const executeBtn = document.querySelector('.execute-btn');
        if (executeBtn) {
            executeBtn.addEventListener('click', () => this.executeQuery());
        }

        // 绑定标签页切换
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.addEventListener('click', (e) => this.showTab(e));
        });

        // 绑定示例查询按钮
        document.querySelectorAll('.try-button').forEach(btn => {
            btn.addEventListener('click', (e) => this.tryExample(e));
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.executeQuery();
            }
        });
    }

    async executeQuery() {
        const query = document.getElementById('queryEditor').value;
        const variables = document.getElementById('variablesEditor').value;
        const resultDisplay = document.getElementById('resultDisplay');

        if (!query.trim()) {
            resultDisplay.innerHTML = '<div class="error">请输入GraphQL查询</div>';
            return;
        }

        // 显示加载状态
        resultDisplay.innerHTML = '<div class="loading">执行查询中...</div>';

        const startTime = Date.now();

        try {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

            const parsedVariables = variables ? JSON.parse(variables) : {};
            const result = this.mockExecuteQuery(query, parsedVariables);
            
            const responseTime = Date.now() - startTime;
            this.queryCount++;
            this.totalResponseTime += responseTime;

            // 格式化并显示结果
            const formattedResult = JSON.stringify(result, null, 2);
            resultDisplay.innerHTML = formattedResult;

            // 添加执行信息
            const executionInfo = `\n\n// 执行时间: ${responseTime}ms\n// 查询次数: ${this.queryCount}\n// 平均响应时间: ${Math.round(this.totalResponseTime / this.queryCount)}ms`;
            resultDisplay.innerHTML += executionInfo;

        } catch (error) {
            const errorResult = {
                errors: [{ 
                    message: error.message,
                    locations: [{ line: 1, column: 1 }],
                    path: null
                }]
            };
            resultDisplay.innerHTML = JSON.stringify(errorResult, null, 2);
        }
    }

    mockExecuteQuery(query, variables) {
        // 简单的GraphQL查询解析和响应生成
        
        // 获取所有用户
        if (query.includes('users') && !query.includes('user(')) {
            return {
                data: {
                    users: this.users.map(user => ({
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        role: user.role,
                        status: user.status,
                        ...(query.includes('posts') && { posts: user.posts }),
                        ...(query.includes('createdAt') && { createdAt: user.createdAt })
                    }))
                }
            };
        }

        // 获取单个用户
        if (query.includes('user(id:') || query.includes('user(id: ')) {
            const userId = variables.userId || variables.id || "1";
            const user = this.users.find(u => u.id === userId);
            
            if (!user) {
                return {
                    data: { user: null },
                    errors: [{ message: "User not found", path: ["user"] }]
                };
            }

            return {
                data: {
                    user: {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        role: user.role,
                        status: user.status,
                        ...(query.includes('posts') && { posts: user.posts }),
                        ...(query.includes('createdAt') && { createdAt: user.createdAt })
                    }
                }
            };
        }

        // 获取文章
        if (query.includes('posts') && !query.includes('user')) {
            const allPosts = this.users.flatMap(user => 
                user.posts.map(post => ({
                    ...post,
                    author: { name: user.name, avatar: user.avatar }
                }))
            );

            return {
                data: {
                    posts: allPosts
                }
            };
        }

        // 获取单个文章
        if (query.includes('post(id:')) {
            const postId = variables.postId || "1";
            let foundPost = null;
            let foundAuthor = null;

            for (const user of this.users) {
                const post = user.posts.find(p => p.id === postId);
                if (post) {
                    foundPost = post;
                    foundAuthor = user;
                    break;
                }
            }

            if (!foundPost) {
                return {
                    data: { post: null },
                    errors: [{ message: "Post not found", path: ["post"] }]
                };
            }

            const postComments = this.comments.filter(c => c.postId === postId);

            return {
                data: {
                    post: {
                        ...foundPost,
                        author: {
                            name: foundAuthor.name,
                            avatar: foundAuthor.avatar
                        },
                        ...(query.includes('comments') && { 
                            comments: postComments.map(comment => ({
                                ...comment,
                                author: this.users.find(u => u.id === comment.authorId)
                            }))
                        })
                    }
                }
            };
        }

        // 创建用户
        if (query.includes('createUser')) {
            const input = variables.input || {};
            const newUser = {
                id: String(this.users.length + 1),
                name: input.name || "新用户",
                email: input.email || "<EMAIL>",
                role: input.role || "USER",
                status: "ACTIVE",
                avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`,
                posts: [],
                createdAt: new Date().toISOString()
            };

            this.users.push(newUser);

            return {
                data: {
                    createUser: newUser
                }
            };
        }

        // 更新用户
        if (query.includes('updateUser')) {
            const userId = variables.id;
            const input = variables.input || {};
            const userIndex = this.users.findIndex(u => u.id === userId);
            
            if (userIndex === -1) {
                return {
                    data: { updateUser: null },
                    errors: [{ message: "User not found", path: ["updateUser"] }]
                };
            }

            const updatedUser = {
                ...this.users[userIndex],
                ...input,
                updatedAt: new Date().toISOString()
            };

            this.users[userIndex] = updatedUser;

            return {
                data: {
                    updateUser: updatedUser
                }
            };
        }

        // 删除用户
        if (query.includes('deleteUser')) {
            const userId = variables.id;
            const userIndex = this.users.findIndex(u => u.id === userId);
            
            if (userIndex === -1) {
                return {
                    data: { deleteUser: false },
                    errors: [{ message: "User not found", path: ["deleteUser"] }]
                };
            }

            this.users.splice(userIndex, 1);

            return {
                data: {
                    deleteUser: true
                }
            };
        }

        // 搜索功能
        if (query.includes('search')) {
            const searchQuery = variables.query || "";
            const results = [];
            
            // 搜索用户
            this.users.forEach(user => {
                if (user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                    user.email.toLowerCase().includes(searchQuery.toLowerCase())) {
                    results.push({
                        __typename: 'User',
                        id: user.id,
                        name: user.name,
                        email: user.email
                    });
                }
            });

            // 搜索文章
            this.users.forEach(user => {
                user.posts.forEach(post => {
                    if (post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        post.content.toLowerCase().includes(searchQuery.toLowerCase())) {
                        results.push({
                            __typename: 'Post',
                            id: post.id,
                            title: post.title,
                            author: { name: user.name }
                        });
                    }
                });
            });

            return {
                data: {
                    search: results
                }
            };
        }

        // 订阅示例
        if (query.includes('userCreated')) {
            return {
                data: {
                    userCreated: {
                        id: "999",
                        name: "实时用户",
                        email: "<EMAIL>",
                        createdAt: new Date().toISOString()
                    }
                }
            };
        }

        // 默认响应
        throw new Error("查询暂不支持，这是一个演示环境");
    }

    showTab(event) {
        const tabName = event.target.textContent.includes('查询') ? 'queries' :
                       event.target.textContent.includes('修改') ? 'mutations' :
                       event.target.textContent.includes('订阅') ? 'subscriptions' : 'advanced';

        // 隐藏所有标签内容
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // 移除所有按钮的激活状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // 显示选中的标签内容
        const targetTab = document.getElementById(tabName);
        if (targetTab) {
            targetTab.classList.add('active');
        }
        
        // 激活对应的按钮
        event.target.classList.add('active');
    }

    tryExample(event) {
        const exampleTitle = event.target.parentElement.querySelector('.example-title').textContent;
        let exampleQuery = null;

        // 根据标题匹配示例
        if (exampleTitle.includes('获取所有用户')) {
            exampleQuery = exampleQueries.find(e => e.title === '获取所有用户');
        } else if (exampleTitle.includes('获取用户及其文章')) {
            exampleQuery = exampleQueries.find(e => e.title === '获取用户及其文章');
        } else if (exampleTitle.includes('创建新用户')) {
            exampleQuery = exampleQueries.find(e => e.title === '创建新用户');
        } else if (exampleTitle.includes('搜索内容')) {
            exampleQuery = exampleQueries.find(e => e.title === '搜索内容');
        }

        if (exampleQuery) {
            document.getElementById('queryEditor').value = exampleQuery.query;
            document.getElementById('variablesEditor').value = exampleQuery.variables;
        } else {
            // 从页面中提取示例代码
            const codeElement = event.target.parentElement.querySelector('.example-code');
            if (codeElement) {
                document.getElementById('queryEditor').value = codeElement.textContent.trim();
                document.getElementById('variablesEditor').value = '{}';
            }
        }
    }

    displaySchema() {
        const schemaDisplay = document.getElementById('schemaDisplay');
        if (schemaDisplay) {
            schemaDisplay.textContent = graphqlSchema;
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.graphqlPlayground = new GraphQLPlayground();
});
