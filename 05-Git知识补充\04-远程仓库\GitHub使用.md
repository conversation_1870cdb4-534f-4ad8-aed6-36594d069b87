# GitHub 使用详解

## 🐙 GitHub 平台概览

### 📋 什么是 GitHub？
GitHub 是全球最大的代码托管平台，基于 Git 版本控制系统，提供了丰富的协作功能和开发工具。

```bash
# GitHub 的核心功能：
├── 代码托管和版本控制
├── Pull Request 和代码审查
├── Issues 问题跟踪
├── Actions CI/CD 自动化
├── Projects 项目管理
├── Wiki 文档管理
├── Packages 包管理
└── Security 安全扫描
```

### 🎯 GitHub 账户类型
```bash
Personal Account    # 个人账户，免费/付费
Organization       # 组织账户，团队协作
Enterprise         # 企业账户，高级功能
```

## 📦 仓库管理

### 🆕 创建仓库

#### 在 GitHub 网站创建
```bash
1. 登录 GitHub
2. 点击右上角 "+" -> "New repository"
3. 填写仓库信息：
   - Repository name: 仓库名称
   - Description: 描述（可选）
   - Public/Private: 公开/私有
   - Initialize with README: 是否创建 README
   - Add .gitignore: 选择 .gitignore 模板
   - Choose a license: 选择开源许可证
4. 点击 "Create repository"
```

#### 使用 GitHub CLI 创建
```bash
# 安装 GitHub CLI
# macOS: brew install gh
# Windows: winget install GitHub.cli

# 登录
gh auth login

# 创建仓库
gh repo create my-project --public --clone
gh repo create my-project --private --description "My awesome project"

# 从现有目录创建
cd my-project
gh repo create --source=. --public --push
```

### 📋 仓库设置

#### 基本设置
```bash
# 仓库设置页面包含：
├── General - 基本信息和危险操作
├── Access - 访问权限管理
├── Security & analysis - 安全分析
├── Branches - 分支保护规则
├── Tags - 标签保护规则
├── Actions - CI/CD 设置
├── Webhooks - 网络钩子
├── Environments - 环境管理
├── Pages - GitHub Pages 设置
└── Security - 安全策略
```

#### 分支保护规则
```bash
# 在 Settings -> Branches 中设置：
✅ Require pull request reviews before merging
✅ Require status checks to pass before merging
✅ Require branches to be up to date before merging
✅ Require conversation resolution before merging
✅ Restrict pushes that create files larger than 100MB
✅ Require signed commits
```

## 🔄 Pull Request 工作流程

### 📝 创建 Pull Request

#### 标准流程
```bash
# 1. Fork 仓库（如果是外部贡献）
# 在 GitHub 上点击 Fork 按钮

# 2. 克隆仓库
git clone https://github.com/yourusername/repo.git
cd repo

# 3. 创建功能分支
git checkout -b feature/new-feature

# 4. 开发功能
# 编写代码...
git add .
git commit -m "feat: add new feature"

# 5. 推送分支
git push -u origin feature/new-feature

# 6. 创建 Pull Request
# 在 GitHub 上点击 "Compare & pull request"
```

#### PR 模板
```markdown
## 描述
简要描述这个 PR 的目的和内容。

## 变更类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 重构
- [ ] 文档更新
- [ ] 性能优化

## 测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 没有引入破坏性变更

## 相关 Issue
Closes #123
```

### 🔍 代码审查

#### 审查流程
```bash
# 1. 审查者收到通知
# 2. 查看代码变更
# 3. 添加评论和建议
# 4. 批准或请求修改
# 5. 开发者响应反馈
# 6. 重复直到批准
# 7. 合并 PR
```

#### 审查最佳实践
```bash
# 审查要点：
✅ 代码逻辑正确性
✅ 代码风格一致性
✅ 性能影响评估
✅ 安全性检查
✅ 测试覆盖率
✅ 文档完整性
✅ 向后兼容性
```

### 🔀 合并策略
```bash
# GitHub 提供三种合并方式：

1. Create a merge commit
   # 创建合并提交，保留分支历史
   git merge --no-ff feature-branch

2. Squash and merge
   # 压缩所有提交为一个
   git merge --squash feature-branch

3. Rebase and merge
   # 变基后快进合并
   git rebase main
   git merge --ff-only feature-branch
```

## 📋 Issues 管理

### 🐛 创建 Issue

#### Issue 模板
```markdown
---
name: Bug Report
about: 报告一个 bug
title: '[BUG] '
labels: bug
assignees: ''
---

## Bug 描述
清晰简洁地描述 bug。

## 重现步骤
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 期望行为
描述你期望发生的行为。

## 实际行为
描述实际发生的行为。

## 环境信息
- OS: [e.g. iOS]
- Browser: [e.g. chrome, safari]
- Version: [e.g. 22]

## 附加信息
添加任何其他相关信息。
```

### 🏷️ Labels 管理
```bash
# 常用标签分类：
Type:
├── bug - 错误报告
├── enhancement - 功能增强
├── feature - 新功能
├── documentation - 文档相关
└── question - 问题咨询

Priority:
├── critical - 严重问题
├── high - 高优先级
├── medium - 中优先级
└── low - 低优先级

Status:
├── in-progress - 进行中
├── needs-review - 需要审查
├── blocked - 被阻塞
└── wontfix - 不会修复
```

### 📊 Project 管理
```bash
# GitHub Projects 功能：
├── Kanban 看板视图
├── Table 表格视图
├── Timeline 时间线视图
├── Roadmap 路线图视图
└── Custom 自定义视图

# 工作流程：
Todo -> In Progress -> In Review -> Done
```

## 🚀 GitHub Actions

### 📝 基本 Workflow
```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build
      run: npm run build
```

### 🔧 常用 Actions
```yaml
# 代码检查
- name: ESLint
  run: npm run lint

# 类型检查
- name: TypeScript Check
  run: npm run type-check

# 安全扫描
- name: Security Audit
  run: npm audit

# 部署到 GitHub Pages
- name: Deploy to GitHub Pages
  uses: peaceiris/actions-gh-pages@v3
  with:
    github_token: ${{ secrets.GITHUB_TOKEN }}
    publish_dir: ./dist
```

## 📚 GitHub Pages

### 🌐 静态网站托管
```bash
# 启用 GitHub Pages：
1. 进入仓库 Settings
2. 滚动到 Pages 部分
3. 选择源分支（main/gh-pages）
4. 选择目录（/ 或 /docs）
5. 保存设置

# 自定义域名：
1. 在仓库根目录创建 CNAME 文件
2. 文件内容为你的域名：example.com
3. 在域名提供商处设置 DNS 记录
```

### 📝 Jekyll 集成
```yaml
# _config.yml
title: My GitHub Pages Site
description: A site built with Jekyll and GitHub Pages
baseurl: "/repository-name"
url: "https://username.github.io"

# 自动构建和部署
name: Build and Deploy
on:
  push:
    branches: [ main ]
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    
    - name: Build and Deploy
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./_site
```

## 🔐 安全功能

### 🛡️ Security Advisories
```bash
# 安全建议功能：
├── 私密报告安全漏洞
├── 协调漏洞披露
├── 发布安全公告
└── 自动化安全更新
```

### 🔍 Dependabot
```yaml
# .github/dependabot.yml
version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    reviewers:
      - "username"
    assignees:
      - "username"
```

### 🔒 Code Scanning
```yaml
# .github/workflows/codeql.yml
name: "CodeQL"

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        language: [ 'javascript' ]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
```

## 🤝 开源协作

### 🍴 Fork 工作流程
```bash
# 1. Fork 原仓库
# 在 GitHub 上点击 Fork

# 2. 克隆你的 fork
git clone https://github.com/yourusername/repo.git

# 3. 添加上游仓库
git remote add upstream https://github.com/original/repo.git

# 4. 同步上游更新
git fetch upstream
git checkout main
git merge upstream/main
git push origin main

# 5. 创建功能分支
git checkout -b feature/awesome-feature

# 6. 开发并推送
git push -u origin feature/awesome-feature

# 7. 创建 Pull Request
```

### 📄 开源许可证
```bash
# 常用开源许可证：
MIT License         # 最宽松的许可证
Apache License 2.0  # 商业友好的许可证
GPL v3             # 强制开源的许可证
BSD 3-Clause       # 简单的许可证
Creative Commons   # 创作共用许可证
```

## 💡 GitHub 最佳实践

### ✅ 仓库管理建议
1. **清晰的 README** - 包含项目描述、安装和使用说明
2. **合适的 .gitignore** - 忽略不必要的文件
3. **有意义的提交信息** - 遵循约定式提交规范
4. **分支保护规则** - 保护主分支不被直接推送
5. **自动化测试** - 使用 GitHub Actions 进行 CI/CD

### 🔒 安全实践
```bash
# 1. 启用双因素认证
# 2. 使用 SSH 密钥
# 3. 定期更新依赖
# 4. 启用安全扫描
# 5. 谨慎管理 Secrets
```

### 🚀 效率提升
```bash
# GitHub CLI 常用命令
gh repo clone owner/repo
gh pr create --title "Feature" --body "Description"
gh pr list
gh pr checkout 123
gh issue create --title "Bug" --body "Description"
gh release create v1.0.0
```

---

**记住**: GitHub 不仅是代码托管平台，更是一个完整的开发协作生态系统。熟练掌握这些功能将大大提升你的开发效率和团队协作能力！ 🐙
