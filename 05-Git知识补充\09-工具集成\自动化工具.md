# Git 自动化工具详解

## 🤖 自动化概览

### 📋 为什么需要 Git 自动化？
Git 自动化工具可以减少重复性工作、提高代码质量、确保流程一致性，让开发团队专注于核心业务逻辑而不是繁琐的版本控制操作。

```bash
# 自动化的价值：
├── 提高效率 - 自动化重复性任务
├── 保证质量 - 自动化代码检查和测试
├── 流程标准化 - 统一的工作流程
├── 减少错误 - 避免人为操作失误
└── 持续集成 - 无缝的 CI/CD 流程
```

## 🎣 Git Hooks 自动化

### 📝 客户端钩子自动化

#### pre-commit 工具
```bash
# 安装 pre-commit
pip install pre-commit

# 或使用包管理器
brew install pre-commit  # macOS
sudo apt install pre-commit  # Ubuntu

# 创建配置文件 .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-json
      - id: pretty-format-json
        args: ['--autofix']

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8

# 安装钩子
pre-commit install

# 手动运行所有文件
pre-commit run --all-files
```

#### Husky + lint-staged
```bash
# 安装 Husky
npm install --save-dev husky

# 初始化
npx husky install
npm pkg set scripts.prepare="husky install"

# 安装 lint-staged
npm install --save-dev lint-staged

# 添加 pre-commit 钩子
npx husky add .husky/pre-commit "npx lint-staged"

# package.json 配置
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,css,md}": [
      "prettier --write"
    ],
    "*.py": [
      "black",
      "flake8"
    ]
  }
}
```

### 🖥️ 服务器端钩子自动化

#### 自动部署钩子
```bash
#!/bin/bash
# hooks/post-receive

# 自动部署脚本
while read oldrev newrev refname; do
    branch=$(git rev-parse --symbolic --abbrev-ref $refname)
    
    if [ "$branch" = "main" ]; then
        echo "Deploying to production..."
        
        # 部署目录
        DEPLOY_DIR="/var/www/myapp"
        
        # 切换到部署目录
        cd $DEPLOY_DIR
        
        # 拉取最新代码
        git --git-dir=$DEPLOY_DIR/.git --work-tree=$DEPLOY_DIR pull origin main
        
        # 安装依赖
        npm ci --production
        
        # 构建项目
        npm run build
        
        # 重启服务
        sudo systemctl restart myapp
        
        # 发送通知
        curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"🚀 Production deployment completed"}' \
            $SLACK_WEBHOOK_URL
            
        echo "Deployment completed!"
    fi
done
```

#### 代码质量检查钩子
```bash
#!/bin/bash
# hooks/pre-receive

# 代码质量检查
while read oldrev newrev refname; do
    # 检查提交信息格式
    if [ "$oldrev" != "0000000000000000000000000000000000000000" ]; then
        git rev-list $oldrev..$newrev | while read commit; do
            msg=$(git log --format=%s -n 1 $commit)
            if ! echo "$msg" | grep -qE '^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .+'; then
                echo "❌ Invalid commit message format: $msg"
                echo "Please use: <type>(<scope>): <description>"
                exit 1
            fi
        done
    fi
    
    # 检查文件大小
    git diff --name-only $oldrev..$newrev | while read file; do
        if [ -f "$file" ]; then
            size=$(stat -c%s "$file")
            if [ $size -gt 10485760 ]; then  # 10MB
                echo "❌ File too large: $file ($size bytes)"
                exit 1
            fi
        fi
    done
done
```

## 🔄 CI/CD 集成

### 🐙 GitHub Actions

#### 基本工作流
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整历史
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run tests
      run: npm test -- --coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
    
    - name: Build project
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploying to production..."
        # 部署脚本
```

#### 自动发布工作流
```yaml
# .github/workflows/release.yml
name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Generate changelog
      id: changelog
      run: |
        # 生成变更日志
        PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD~1)
        CHANGELOG=$(git log $PREVIOUS_TAG..HEAD --pretty=format:"- %s (%h)")
        echo "changelog<<EOF" >> $GITHUB_OUTPUT
        echo "$CHANGELOG" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
    
    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        body: |
          ## Changes
          ${{ steps.changelog.outputs.changelog }}
        draft: false
        prerelease: false
```

### 🦊 GitLab CI/CD

#### .gitlab-ci.yml 配置
```yaml
stages:
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "18"

before_script:
  - apt-get update -qq && apt-get install -y -qq git curl
  - curl -sL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
  - apt-get install -y nodejs

test:
  stage: test
  script:
    - npm ci
    - npm run lint
    - npm test -- --coverage
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'

build:
  stage: build
  script:
    - npm ci
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour

deploy_staging:
  stage: deploy
  script:
    - echo "Deploying to staging..."
    - rsync -av dist/ user@staging-server:/var/www/app/
  environment:
    name: staging
    url: https://staging.example.com
  only:
    - develop

deploy_production:
  stage: deploy
  script:
    - echo "Deploying to production..."
    - rsync -av dist/ user@prod-server:/var/www/app/
  environment:
    name: production
    url: https://example.com
  only:
    - main
  when: manual
```

## 🔧 自动化脚本

### 📝 Git 工作流自动化

#### 功能分支自动化
```bash
#!/bin/bash
# scripts/new-feature.sh

FEATURE_NAME=$1

if [ -z "$FEATURE_NAME" ]; then
    echo "Usage: $0 <feature-name>"
    exit 1
fi

echo "🚀 Creating new feature: $FEATURE_NAME"

# 确保在主分支
git checkout main
git pull origin main

# 创建功能分支
git checkout -b "feature/$FEATURE_NAME"

# 推送分支
git push -u origin "feature/$FEATURE_NAME"

# 创建初始提交
echo "# Feature: $FEATURE_NAME" > "docs/$FEATURE_NAME.md"
git add "docs/$FEATURE_NAME.md"
git commit -m "feat: initialize $FEATURE_NAME feature"
git push

echo "✅ Feature branch created: feature/$FEATURE_NAME"
echo "📝 Documentation file created: docs/$FEATURE_NAME.md"
```

#### 发布自动化
```bash
#!/bin/bash
# scripts/release.sh

VERSION=$1
CURRENT_BRANCH=$(git branch --show-current)

if [ -z "$VERSION" ]; then
    echo "Usage: $0 <version>"
    exit 1
fi

if [ "$CURRENT_BRANCH" != "main" ]; then
    echo "❌ Must be on main branch to create release"
    exit 1
fi

echo "🚀 Creating release: v$VERSION"

# 确保工作目录干净
if [ -n "$(git status --porcelain)" ]; then
    echo "❌ Working directory is not clean"
    exit 1
fi

# 拉取最新代码
git pull origin main

# 更新版本号
npm version $VERSION --no-git-tag-version

# 生成变更日志
LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
if [ -n "$LAST_TAG" ]; then
    echo "## v$VERSION ($(date +%Y-%m-%d))" > CHANGELOG_NEW.md
    echo "" >> CHANGELOG_NEW.md
    git log $LAST_TAG..HEAD --pretty=format:"- %s (%h)" >> CHANGELOG_NEW.md
    echo "" >> CHANGELOG_NEW.md
    echo "" >> CHANGELOG_NEW.md
    cat CHANGELOG.md >> CHANGELOG_NEW.md
    mv CHANGELOG_NEW.md CHANGELOG.md
fi

# 提交版本更新
git add package.json package-lock.json CHANGELOG.md
git commit -m "chore: bump version to $VERSION"

# 创建标签
git tag -a "v$VERSION" -m "Release version $VERSION"

# 推送
git push origin main
git push origin "v$VERSION"

echo "✅ Release v$VERSION created successfully"
```

### 🔄 自动同步脚本

#### 多仓库同步
```bash
#!/bin/bash
# scripts/sync-repos.sh

REPOS=(
    "/path/to/repo1"
    "/path/to/repo2"
    "/path/to/repo3"
)

for repo in "${REPOS[@]}"; do
    echo "🔄 Syncing $repo"
    cd "$repo"
    
    # 检查是否是 Git 仓库
    if [ ! -d ".git" ]; then
        echo "❌ Not a Git repository: $repo"
        continue
    fi
    
    # 获取当前分支
    current_branch=$(git branch --show-current)
    
    # 检查工作目录状态
    if [ -n "$(git status --porcelain)" ]; then
        echo "⚠️  Uncommitted changes in $repo, skipping..."
        continue
    fi
    
    # 拉取更新
    git fetch origin
    git pull origin "$current_branch"
    
    echo "✅ Synced $repo"
done

echo "🎉 All repositories synced!"
```

#### 分支清理自动化
```bash
#!/bin/bash
# scripts/cleanup-branches.sh

echo "🧹 Cleaning up merged branches..."

# 获取已合并的分支
merged_branches=$(git branch --merged main | grep -v "main\|master\|\*")

if [ -z "$merged_branches" ]; then
    echo "✅ No merged branches to clean up"
    exit 0
fi

echo "📋 Found merged branches:"
echo "$merged_branches"

read -p "🗑️  Delete these branches? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "$merged_branches" | xargs -n 1 git branch -d
    echo "✅ Merged branches deleted"
    
    # 清理远程跟踪分支
    git remote prune origin
    echo "✅ Remote tracking branches cleaned"
else
    echo "❌ Branch cleanup cancelled"
fi
```

## 🤖 智能化工具

### 🧠 AI 辅助工具

#### GitHub Copilot CLI
```bash
# 安装 GitHub Copilot CLI
npm install -g @githubnext/github-copilot-cli

# 配置别名
echo 'eval "$(github-copilot-cli alias -- "$0")"' >> ~/.bashrc

# 使用示例
?? git commit message for adding user authentication
# 输出：git commit -m "feat: add user authentication system"

?? git revert last 3 commits
# 输出：git reset --hard HEAD~3
```

#### AI 提交信息生成
```python
#!/usr/bin/env python3
# scripts/ai-commit.py

import subprocess
import openai
import sys

def get_git_diff():
    """获取 Git 差异"""
    result = subprocess.run(['git', 'diff', '--staged'], 
                          capture_output=True, text=True)
    return result.stdout

def generate_commit_message(diff):
    """使用 AI 生成提交信息"""
    client = openai.OpenAI()
    
    prompt = f"""
    Based on the following git diff, generate a concise commit message 
    following conventional commits format:
    
    {diff}
    
    Format: <type>(<scope>): <description>
    Types: feat, fix, docs, style, refactor, test, chore
    """
    
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=100
    )
    
    return response.choices[0].message.content.strip()

def main():
    diff = get_git_diff()
    if not diff:
        print("No staged changes found")
        sys.exit(1)
    
    commit_msg = generate_commit_message(diff)
    print(f"Suggested commit message: {commit_msg}")
    
    confirm = input("Use this message? (y/N): ")
    if confirm.lower() == 'y':
        subprocess.run(['git', 'commit', '-m', commit_msg])
        print("Committed successfully!")

if __name__ == "__main__":
    main()
```

### 📊 自动化报告

#### Git 统计报告
```bash
#!/bin/bash
# scripts/git-report.sh

SINCE=${1:-"1 month ago"}
OUTPUT_FILE="git-report-$(date +%Y%m%d).md"

echo "# Git Activity Report" > $OUTPUT_FILE
echo "Generated on: $(date)" >> $OUTPUT_FILE
echo "Period: Since $SINCE" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

# 提交统计
echo "## Commit Statistics" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE
echo "### Total Commits" >> $OUTPUT_FILE
total_commits=$(git rev-list --count --since="$SINCE" HEAD)
echo "- Total commits: $total_commits" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

# 贡献者统计
echo "### Contributors" >> $OUTPUT_FILE
git shortlog -sn --since="$SINCE" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

# 文件变更统计
echo "### File Changes" >> $OUTPUT_FILE
echo "Most modified files:" >> $OUTPUT_FILE
git log --since="$SINCE" --name-only --pretty=format: | \
sort | uniq -c | sort -rg | head -10 >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

# 分支活动
echo "### Branch Activity" >> $OUTPUT_FILE
git for-each-ref --format='%(refname:short) %(committerdate:relative)' \
refs/heads/ | sort -k2 >> $OUTPUT_FILE

echo "📊 Report generated: $OUTPUT_FILE"
```

## 💡 最佳实践

### ✅ 自动化实施建议
1. **渐进式实施** - 从简单的自动化开始
2. **团队共识** - 确保团队理解和接受自动化流程
3. **监控和调整** - 定期检查自动化效果并优化
4. **文档化** - 详细记录自动化流程和配置
5. **备份和恢复** - 为自动化系统建立备份机制

### 🔒 安全考虑
```bash
# 1. 敏感信息保护
# 使用环境变量存储密钥
export GITHUB_TOKEN="your-token"
export SLACK_WEBHOOK="your-webhook"

# 2. 权限控制
# 限制自动化脚本的执行权限
chmod 750 scripts/deploy.sh

# 3. 审计日志
# 记录自动化操作日志
echo "$(date): Automated deployment completed" >> /var/log/git-automation.log
```

### 🚨 常见陷阱
1. **过度自动化** - 不要自动化所有操作
2. **缺乏监控** - 确保自动化流程有适当的监控
3. **单点故障** - 避免关键流程依赖单一自动化工具
4. **版本兼容** - 确保自动化工具与 Git 版本兼容
5. **错误处理** - 为自动化脚本添加适当的错误处理

---

**记住**: 自动化是提高效率的强大工具，但需要谨慎实施。从简单的任务开始，逐步建立完善的自动化体系，确保团队能够理解和维护这些自动化流程！ 🤖
