# 多阶段构建 Dockerfile
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 复制根目录的 package.json
COPY package*.json ./

# 安装根目录依赖
RUN npm install

# 构建阶段 - 客户端
FROM base AS client-builder

# 复制客户端代码
COPY client/ ./client/

# 安装客户端依赖
WORKDIR /app/client
RUN npm install

# 构建客户端
RUN npm run build

# 构建阶段 - 服务端
FROM base AS server-builder

# 复制服务端代码
COPY server/ ./server/

# 安装服务端依赖
WORKDIR /app/server
RUN npm install

# 构建服务端
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 复制服务端构建结果
COPY --from=server-builder /app/server/dist ./server/dist
COPY --from=server-builder /app/server/package*.json ./server/
COPY --from=server-builder /app/server/node_modules ./server/node_modules

# 复制客户端构建结果到服务端静态文件目录
COPY --from=client-builder /app/client/dist ./server/public

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=4000

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 4000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:4000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["node", "server/dist/server.js"]
