# GraphQL 实战练习

## 练习1：博客系统GraphQL API设计

### 需求分析
设计一个博客系统的GraphQL API，包含以下功能：
- 用户管理（注册、登录、个人资料）
- 文章管理（创建、编辑、删除、查看）
- 评论系统（发表评论、回复评论）
- 标签系统（文章标签、标签分类）
- 点赞功能（文章点赞、评论点赞）

### 任务1：Schema设计
```graphql
# 请完成以下Schema设计

# 用户类型
type User {
  # TODO: 设计用户字段
}

# 文章类型
type Post {
  # TODO: 设计文章字段
}

# 评论类型
type Comment {
  # TODO: 设计评论字段
}

# 标签类型
type Tag {
  # TODO: 设计标签字段
}

# 查询根类型
type Query {
  # TODO: 设计查询操作
}

# 变更根类型
type Mutation {
  # TODO: 设计变更操作
}

# 订阅根类型
type Subscription {
  # TODO: 设计订阅操作
}
```

### 任务2：Resolver实现
```javascript
// 请实现以下Resolver

const resolvers = {
  Query: {
    // TODO: 实现查询解析器
    posts: async (parent, args, context) => {
      // 获取文章列表
    },
    
    post: async (parent, { id }, context) => {
      // 获取单篇文章
    },
    
    user: async (parent, { id }, context) => {
      // 获取用户信息
    }
  },
  
  Mutation: {
    // TODO: 实现变更解析器
    createPost: async (parent, { input }, context) => {
      // 创建文章
    },
    
    updatePost: async (parent, { id, input }, context) => {
      // 更新文章
    },
    
    deletePost: async (parent, { id }, context) => {
      // 删除文章
    }
  },
  
  // TODO: 实现字段解析器
  Post: {
    author: async (post) => {
      // 解析文章作者
    },
    
    comments: async (post) => {
      // 解析文章评论
    },
    
    tags: async (post) => {
      // 解析文章标签
    }
  }
};
```

### 任务3：客户端查询
```javascript
// 请编写以下GraphQL查询

// 1. 获取文章列表（包含作者信息和评论数量）
const GET_POSTS = gql`
  # TODO: 编写查询
`;

// 2. 获取文章详情（包含完整的评论和标签）
const GET_POST_DETAIL = gql`
  # TODO: 编写查询
`;

// 3. 创建新文章
const CREATE_POST = gql`
  # TODO: 编写变更
`;

// 4. 订阅新评论
const COMMENT_SUBSCRIPTION = gql`
  # TODO: 编写订阅
`;
```

## 练习2：电商系统购物车功能

### 需求分析
实现一个电商系统的购物车功能，包括：
- 添加商品到购物车
- 更新购物车商品数量
- 删除购物车商品
- 计算购物车总价
- 购物车持久化

### 任务1：Schema设计
```graphql
# 请设计购物车相关的Schema

type Product {
  id: ID!
  name: String!
  price: Money!
  # TODO: 添加其他字段
}

type CartItem {
  # TODO: 设计购物车项字段
}

type Cart {
  # TODO: 设计购物车字段
}

type Money {
  amount: Float!
  currency: String!
}

# TODO: 设计输入类型
input AddToCartInput {
  # TODO: 设计输入字段
}

# TODO: 设计查询和变更
```

### 任务2：业务逻辑实现
```javascript
// 请实现购物车业务逻辑

class CartService {
  // TODO: 添加商品到购物车
  async addToCart(userId, productId, quantity) {
    // 实现逻辑
  }
  
  // TODO: 更新购物车商品数量
  async updateCartItem(userId, itemId, quantity) {
    // 实现逻辑
  }
  
  // TODO: 删除购物车商品
  async removeFromCart(userId, itemId) {
    // 实现逻辑
  }
  
  // TODO: 计算购物车总价
  async calculateTotal(cartItems) {
    // 实现逻辑
  }
  
  // TODO: 清空购物车
  async clearCart(userId) {
    // 实现逻辑
  }
}
```

### 任务3：前端组件实现
```javascript
// 请实现React购物车组件

// 购物车列表组件
function CartList() {
  // TODO: 使用useQuery获取购物车数据
  // TODO: 实现商品数量更新
  // TODO: 实现商品删除
  
  return (
    <div>
      {/* TODO: 渲染购物车商品列表 */}
    </div>
  );
}

// 添加到购物车按钮组件
function AddToCartButton({ productId }) {
  // TODO: 使用useMutation实现添加到购物车
  // TODO: 实现乐观更新
  // TODO: 处理错误状态
  
  return (
    <button>
      {/* TODO: 按钮内容和状态 */}
    </button>
  );
}
```

## 练习3：实时聊天系统

### 需求分析
实现一个实时聊天系统，包括：
- 用户加入/离开聊天室
- 发送/接收消息
- 在线用户列表
- 消息历史记录
- 私聊功能

### 任务1：Schema设计
```graphql
# 请设计聊天系统的Schema

type ChatRoom {
  # TODO: 设计聊天室字段
}

type Message {
  # TODO: 设计消息字段
}

type User {
  # TODO: 设计用户字段
}

# TODO: 设计订阅类型
type Subscription {
  # TODO: 消息订阅
  # TODO: 用户状态订阅
}
```

### 任务2：实时功能实现
```javascript
// 请实现实时聊天功能

const resolvers = {
  Subscription: {
    // TODO: 实现消息订阅
    messageAdded: {
      subscribe: () => {
        // 实现订阅逻辑
      }
    },
    
    // TODO: 实现用户状态订阅
    userStatusChanged: {
      subscribe: () => {
        // 实现订阅逻辑
      }
    }
  },
  
  Mutation: {
    // TODO: 实现发送消息
    sendMessage: async (parent, { input }, { user, pubsub }) => {
      // 实现发送消息逻辑
    },
    
    // TODO: 实现加入聊天室
    joinChatRoom: async (parent, { roomId }, { user, pubsub }) => {
      // 实现加入聊天室逻辑
    }
  }
};
```

### 任务3：前端聊天界面
```javascript
// 请实现React聊天界面

function ChatRoom({ roomId }) {
  // TODO: 使用useSubscription监听新消息
  // TODO: 使用useQuery获取历史消息
  // TODO: 使用useMutation发送消息
  
  return (
    <div className="chat-room">
      {/* TODO: 消息列表 */}
      {/* TODO: 消息输入框 */}
      {/* TODO: 在线用户列表 */}
    </div>
  );
}
```

## 练习4：性能优化挑战

### 问题场景
给定一个存在N+1查询问题的GraphQL API，请优化其性能。

```javascript
// 存在问题的Resolver
const resolvers = {
  Query: {
    posts: () => Post.findAll()
  },
  
  Post: {
    // ❌ N+1问题：每个post都会执行一次查询
    author: (post) => User.findById(post.authorId),
    comments: (post) => Comment.findByPostId(post.id),
    tags: (post) => Tag.findByPostId(post.id)
  }
};
```

### 任务：优化方案
```javascript
// TODO: 使用DataLoader优化N+1问题
// TODO: 实现批量查询
// TODO: 添加缓存机制
// TODO: 实现查询复杂度限制
```

## 练习答案提示

### 练习1答案提示
- User类型应包含：id, username, email, avatar, bio, createdAt等
- Post类型应包含：id, title, content, author, comments, tags, likes等
- 使用分页处理大量数据
- 实现适当的认证和授权

### 练习2答案提示
- 考虑购物车的持久化策略
- 处理库存不足的情况
- 实现价格计算的准确性
- 考虑并发操作的安全性

### 练习3答案提示
- 使用WebSocket实现实时通信
- 考虑消息的持久化存储
- 实现用户在线状态管理
- 处理连接断开和重连

### 练习4答案提示
- 使用DataLoader进行批量查询
- 实现Redis缓存
- 添加查询复杂度分析
- 使用查询白名单

---

**完成这些练习后，你将掌握：**
1. GraphQL Schema设计最佳实践
2. 复杂业务逻辑的Resolver实现
3. 前端GraphQL客户端开发
4. 实时功能的实现方法
5. GraphQL性能优化技巧
6. 错误处理和安全性考虑
