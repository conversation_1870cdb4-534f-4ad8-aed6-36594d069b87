<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Grid fr 单位详解</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .demo-section h2 {
            margin-bottom: 20px;
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .demo-section h3 {
            margin: 20px 0 15px 0;
            color: #2980b9;
        }

        /* 通用Grid容器样式 */
        .grid-container {
            display: grid;
            gap: 10px;
            background: #ecf0f1;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            min-height: 120px;
        }

        .grid-item {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .grid-item:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        /* 不同颜色的grid项目 */
        .grid-item:nth-child(1) { background: #e74c3c; }
        .grid-item:nth-child(2) { background: #3498db; }
        .grid-item:nth-child(3) { background: #2ecc71; }
        .grid-item:nth-child(4) { background: #f39c12; }
        .grid-item:nth-child(5) { background: #9b59b6; }
        .grid-item:nth-child(6) { background: #1abc9c; }

        .grid-item:nth-child(1):hover { background: #c0392b; }
        .grid-item:nth-child(2):hover { background: #2980b9; }
        .grid-item:nth-child(3):hover { background: #27ae60; }
        .grid-item:nth-child(4):hover { background: #e67e22; }
        .grid-item:nth-child(5):hover { background: #8e44ad; }
        .grid-item:nth-child(6):hover { background: #16a085; }

        /* 代码展示 */
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }

        .code-block .comment {
            color: #95a5a6;
        }

        .code-block .property {
            color: #3498db;
        }

        .code-block .value {
            color: #2ecc71;
        }

        /* 具体演示样式 */
        
        /* 1. 基础fr演示 */
        .demo-basic-fr {
            grid-template-columns: 1fr 2fr 1fr;
        }

        /* 2. 等分演示 */
        .demo-equal-fr {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }

        /* 3. fr与固定单位混合 */
        .demo-mixed-units {
            grid-template-columns: 200px 1fr 100px;
        }

        /* 4. 复杂比例 */
        .demo-complex-ratio {
            grid-template-columns: 1fr 3fr 2fr;
        }

        /* 5. 行的fr单位 */
        .demo-rows-fr {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 2fr;
            height: 200px;
        }

        /* 6. minmax与fr结合 */
        .demo-minmax-fr {
            grid-template-columns: minmax(100px, 1fr) 2fr minmax(80px, 1fr);
        }

        /* 7. repeat与fr */
        .demo-repeat-fr {
            grid-template-columns: repeat(5, 1fr);
        }

        /* 说明文字 */
        .description {
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }

        .highlight {
            background: #f1c40f;
            color: #2c3e50;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* 表格样式 */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #3498db;
            color: white;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        /* 交互控制 */
        .controls {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        .control-group .value-display {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin-top: 5px;
        }

        /* 计算演示 */
        .calculation-demo {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .calculation-step {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #f39c12;
        }

        /* 8. Grid Areas 演示 */
        .demo-grid-areas {
            grid-template-columns: 200px 1fr 150px;
            grid-template-rows: 80px 1fr 60px;
            grid-template-areas:
                "header header header"
                "sidebar main aside"
                "footer footer footer";
            height: 300px;
        }

        .demo-grid-areas .header { grid-area: header; background: #e74c3c; }
        .demo-grid-areas .sidebar { grid-area: sidebar; background: #3498db; }
        .demo-grid-areas .main { grid-area: main; background: #2ecc71; }
        .demo-grid-areas .aside { grid-area: aside; background: #f39c12; }
        .demo-grid-areas .footer { grid-area: footer; background: #9b59b6; }

        /* 9. Grid Lines 演示 */
        .demo-grid-lines {
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 80px);
        }

        .demo-grid-lines .item-span {
            grid-column: 2 / 4;  /* 从第2条线到第4条线 */
            grid-row: 1 / 3;     /* 从第1条线到第3条线 */
            background: #e74c3c;
        }

        /* 10. Auto-fit vs Auto-fill */
        .demo-auto-fit {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .demo-auto-fill {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }

        /* 11. Grid Gap 演示 */
        .demo-gap-small { gap: 5px; }
        .demo-gap-medium { gap: 15px; }
        .demo-gap-large { gap: 30px; }
        .demo-gap-asymmetric {
            row-gap: 10px;
            column-gap: 20px;
        }

        /* 12. Implicit Grid */
        .demo-implicit {
            grid-template-columns: repeat(3, 1fr);
            grid-auto-rows: 80px;
            grid-auto-flow: row;
        }

        /* 13. Dense Packing */
        .demo-dense {
            grid-template-columns: repeat(4, 1fr);
            grid-auto-flow: row dense;
        }

        .demo-dense .wide { grid-column: span 2; }
        .demo-dense .tall { grid-row: span 2; }

        /* 14. Alignment 演示 */
        .demo-alignment {
            grid-template-columns: repeat(3, 100px);
            grid-template-rows: repeat(2, 80px);
            justify-content: center;
            align-content: center;
            height: 200px;
        }

        .demo-item-alignment {
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: 100px;
        }

        .demo-item-alignment .item-start { justify-self: start; align-self: start; }
        .demo-item-alignment .item-center { justify-self: center; align-self: center; }
        .demo-item-alignment .item-end { justify-self: end; align-self: end; }

        /* 响应式 */
        @media (max-width: 768px) {
            .demo-mixed-units {
                grid-template-columns: 100px 1fr 80px;
            }

            .demo-rows-fr {
                height: 150px;
            }

            .demo-grid-areas {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "header"
                    "main"
                    "sidebar"
                    "aside"
                    "footer";
                height: auto;
            }

            .demo-auto-fit,
            .demo-auto-fill {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📐 CSS Grid fr 单位详解</h1>

        <!-- 基础概念 -->
        <div class="demo-section">
            <h2>📖 基础概念</h2>
            <p><code>fr</code> 是 <strong>fraction（分数）</strong> 的缩写，用于在Grid布局中按比例分配剩余空间。</p>
            
            <div class="code-block">
<span class="comment">/* fr 单位基本语法 */</span>
.<span class="property">grid-container</span> {
    <span class="property">display</span>: <span class="value">grid</span>;
    <span class="property">grid-template-columns</span>: <span class="value">1fr 2fr 1fr</span>;
    <span class="comment">/* 总共4份：第1列占1份，第2列占2份，第3列占1份 */</span>
}
            </div>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>单位</th>
                        <th>含义</th>
                        <th>特点</th>
                        <th>使用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>1fr</code></td>
                        <td>1个分数单位</td>
                        <td>按比例分配剩余空间</td>
                        <td>响应式列宽</td>
                    </tr>
                    <tr>
                        <td><code>px</code></td>
                        <td>固定像素</td>
                        <td>绝对尺寸，不变</td>
                        <td>固定宽度列</td>
                    </tr>
                    <tr>
                        <td><code>%</code></td>
                        <td>百分比</td>
                        <td>相对于容器尺寸</td>
                        <td>比例布局</td>
                    </tr>
                    <tr>
                        <td><code>auto</code></td>
                        <td>自动尺寸</td>
                        <td>基于内容大小</td>
                        <td>内容自适应</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 基础fr演示 -->
        <div class="demo-section">
            <h2>🎯 基础 fr 演示</h2>

            <h3>1. 不同比例分配 (1fr 2fr 1fr)</h3>
            <div class="description">
                总共4份空间：第1列占1份(25%)，第2列占2份(50%)，第3列占1份(25%)
            </div>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">1fr 2fr 1fr</span>;
            </div>
            <div class="grid-container demo-basic-fr">
                <div class="grid-item">1fr<br>(25%)</div>
                <div class="grid-item">2fr<br>(50%)</div>
                <div class="grid-item">1fr<br>(25%)</div>
            </div>

            <div class="calculation-demo">
                <strong>🧮 计算过程：</strong>
                <div class="calculation-step">1. 总份数 = 1 + 2 + 1 = 4份</div>
                <div class="calculation-step">2. 第1列 = 1/4 = 25%</div>
                <div class="calculation-step">3. 第2列 = 2/4 = 50%</div>
                <div class="calculation-step">4. 第3列 = 1/4 = 25%</div>
            </div>

            <h3>2. 等分布局 (1fr 1fr 1fr 1fr)</h3>
            <div class="description">
                所有列等分容器宽度，每列占25%
            </div>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">1fr 1fr 1fr 1fr</span>;
<span class="comment">/* 或者简写为 */</span>
<span class="property">grid-template-columns</span>: <span class="value">repeat(4, 1fr)</span>;
            </div>
            <div class="grid-container demo-equal-fr">
                <div class="grid-item">1fr</div>
                <div class="grid-item">1fr</div>
                <div class="grid-item">1fr</div>
                <div class="grid-item">1fr</div>
            </div>
        </div>

        <!-- fr与其他单位混合 -->
        <div class="demo-section">
            <h2>🔄 fr 与其他单位混合</h2>
            
            <h3>fr + 固定单位 (200px 1fr 100px)</h3>
            <div class="description">
                固定单位先占用空间，<code>fr</code>分配剩余空间
            </div>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">200px 1fr 100px</span>;
<span class="comment">/* 第1列固定200px，第3列固定100px，第2列占剩余空间 */</span>
            </div>
            <div class="grid-container demo-mixed-units">
                <div class="grid-item">200px<br>(固定)</div>
                <div class="grid-item">1fr<br>(剩余空间)</div>
                <div class="grid-item">100px<br>(固定)</div>
            </div>

            <div class="calculation-demo">
                <strong>🧮 计算过程（假设容器宽度800px）：</strong>
                <div class="calculation-step">1. 固定空间 = 200px + 100px = 300px</div>
                <div class="calculation-step">2. 剩余空间 = 800px - 300px = 500px</div>
                <div class="calculation-step">3. 第2列(1fr) = 500px</div>
            </div>
        </div>

        <!-- 复杂比例 -->
        <div class="demo-section">
            <h2>⚖️ 复杂比例演示</h2>
            
            <h3>1fr 3fr 2fr (1:3:2 比例)</h3>
            <div class="description">
                总共6份：第1列占1份，第2列占3份，第3列占2份
            </div>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">1fr 3fr 2fr</span>;
            </div>
            <div class="grid-container demo-complex-ratio">
                <div class="grid-item">1fr<br>(16.7%)</div>
                <div class="grid-item">3fr<br>(50%)</div>
                <div class="grid-item">2fr<br>(33.3%)</div>
            </div>
        </div>

        <!-- 行的fr单位 -->
        <div class="demo-section">
            <h2>📏 行中的 fr 单位</h2>
            <div class="description">
                <code>fr</code>不仅可以用于列，也可以用于行的高度分配
            </div>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">1fr 1fr</span>;
<span class="property">grid-template-rows</span>: <span class="value">1fr 2fr</span>;
<span class="property">height</span>: <span class="value">200px</span>;
            </div>
            <div class="grid-container demo-rows-fr">
                <div class="grid-item">第1行<br>1fr</div>
                <div class="grid-item">第1行<br>1fr</div>
                <div class="grid-item">第2行<br>2fr</div>
                <div class="grid-item">第2行<br>2fr</div>
            </div>
        </div>

        <!-- 高级用法 -->
        <div class="demo-section">
            <h2>🚀 高级用法</h2>
            
            <h3>1. minmax() 与 fr 结合</h3>
            <div class="description">
                设置最小/最大尺寸的同时使用fr分配
            </div>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">minmax(100px, 1fr) 2fr minmax(80px, 1fr)</span>;
<span class="comment">/* 第1列：最小100px，最大1fr */</span>
<span class="comment">/* 第2列：2fr */</span>
<span class="comment">/* 第3列：最小80px，最大1fr */</span>
            </div>
            <div class="grid-container demo-minmax-fr">
                <div class="grid-item">minmax<br>(100px, 1fr)</div>
                <div class="grid-item">2fr</div>
                <div class="grid-item">minmax<br>(80px, 1fr)</div>
            </div>

            <h3>2. repeat() 与 fr</h3>
            <div class="description">
                使用repeat()函数创建重复的fr列
            </div>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">repeat(5, 1fr)</span>;
<span class="comment">/* 等价于：1fr 1fr 1fr 1fr 1fr */</span>
            </div>
            <div class="grid-container demo-repeat-fr">
                <div class="grid-item">1</div>
                <div class="grid-item">2</div>
                <div class="grid-item">3</div>
                <div class="grid-item">4</div>
                <div class="grid-item">5</div>
            </div>
        </div>

        <!-- 交互演示 -->
        <div class="demo-section">
            <h2>🎮 交互演示</h2>
            <div class="description">
                调整下面的滑块来实时看到不同fr值的效果
            </div>

            <div class="controls">
                <div class="control-group">
                    <label>第1列 fr值:</label>
                    <input type="range" id="col1Fr" min="0.5" max="5" step="0.5" value="1">
                    <div class="value-display" id="col1Value">1fr</div>
                </div>
                <div class="control-group">
                    <label>第2列 fr值:</label>
                    <input type="range" id="col2Fr" min="0.5" max="5" step="0.5" value="2">
                    <div class="value-display" id="col2Value">2fr</div>
                </div>
                <div class="control-group">
                    <label>第3列 fr值:</label>
                    <input type="range" id="col3Fr" min="0.5" max="5" step="0.5" value="1">
                    <div class="value-display" id="col3Value">1fr</div>
                </div>
            </div>

            <div class="grid-container" id="interactiveGrid">
                <div class="grid-item" id="item1">第1列</div>
                <div class="grid-item" id="item2">第2列</div>
                <div class="grid-item" id="item3">第3列</div>
            </div>

            <div class="code-block" id="currentGridCode">
<span class="property">grid-template-columns</span>: <span class="value">1fr 2fr 1fr</span>;
            </div>

            <div class="calculation-demo" id="calculationResult">
                <strong>🧮 当前比例计算：</strong>
                <div id="calculationSteps"></div>
            </div>
        </div>

        <!-- Grid Areas 命名区域 -->
        <div class="demo-section">
            <h2>�️ Grid Areas - 命名区域布局</h2>
            <div class="description">
                使用 <code>grid-template-areas</code> 可以为网格区域命名，创建语义化的布局
            </div>
            <div class="code-block">
.<span class="property">container</span> {
    <span class="property">grid-template-columns</span>: <span class="value">200px 1fr 150px</span>;
    <span class="property">grid-template-rows</span>: <span class="value">80px 1fr 60px</span>;
    <span class="property">grid-template-areas</span>:
        <span class="value">"header header header"
        "sidebar main aside"
        "footer footer footer"</span>;
}

.<span class="property">header</span> { <span class="property">grid-area</span>: <span class="value">header</span>; }
.<span class="property">sidebar</span> { <span class="property">grid-area</span>: <span class="value">sidebar</span>; }
.<span class="property">main</span> { <span class="property">grid-area</span>: <span class="value">main</span>; }
            </div>
            <div class="grid-container demo-grid-areas">
                <div class="grid-item header">Header</div>
                <div class="grid-item sidebar">Sidebar</div>
                <div class="grid-item main">Main Content</div>
                <div class="grid-item aside">Aside</div>
                <div class="grid-item footer">Footer</div>
            </div>
        </div>

        <!-- Grid Lines 网格线 -->
        <div class="demo-section">
            <h2>📏 Grid Lines - 网格线定位</h2>
            <div class="description">
                使用网格线编号精确控制项目位置和跨度
            </div>
            <div class="code-block">
.<span class="property">container</span> {
    <span class="property">grid-template-columns</span>: <span class="value">repeat(4, 1fr)</span>;
    <span class="property">grid-template-rows</span>: <span class="value">repeat(3, 80px)</span>;
}

.<span class="property">item-span</span> {
    <span class="property">grid-column</span>: <span class="value">2 / 4</span>;  <span class="comment">/* 从第2条线到第4条线 */</span>
    <span class="property">grid-row</span>: <span class="value">1 / 3</span>;     <span class="comment">/* 从第1条线到第3条线 */</span>
}
            </div>
            <div class="grid-container demo-grid-lines">
                <div class="grid-item">1</div>
                <div class="grid-item item-span">跨越项目<br>(2/4, 1/3)</div>
                <div class="grid-item">3</div>
                <div class="grid-item">4</div>
                <div class="grid-item">5</div>
                <div class="grid-item">6</div>
                <div class="grid-item">7</div>
            </div>
        </div>

        <!-- Auto-fit vs Auto-fill -->
        <div class="demo-section">
            <h2>🔄 Auto-fit vs Auto-fill</h2>
            <div class="description">
                两者都能创建响应式网格，但处理空白空间的方式不同
            </div>

            <h3>auto-fit (自适应填充)</h3>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">repeat(auto-fit, minmax(150px, 1fr))</span>;
<span class="comment">/* 项目会拉伸填满容器 */</span>
            </div>
            <div class="grid-container demo-auto-fit">
                <div class="grid-item">Item 1</div>
                <div class="grid-item">Item 2</div>
                <div class="grid-item">Item 3</div>
            </div>

            <h3>auto-fill (自动填充)</h3>
            <div class="code-block">
<span class="property">grid-template-columns</span>: <span class="value">repeat(auto-fill, minmax(150px, 1fr))</span>;
<span class="comment">/* 保持项目尺寸，可能留有空白 */</span>
            </div>
            <div class="grid-container demo-auto-fill">
                <div class="grid-item">Item 1</div>
                <div class="grid-item">Item 2</div>
                <div class="grid-item">Item 3</div>
            </div>
        </div>

        <!-- Grid Gap 间距 -->
        <div class="demo-section">
            <h2>📐 Grid Gap - 网格间距</h2>
            <div class="description">
                控制网格项目之间的间距
            </div>

            <h3>不同间距大小</h3>
            <div class="code-block">
<span class="property">gap</span>: <span class="value">5px</span>;   <span class="comment">/* 小间距 */</span>
<span class="property">gap</span>: <span class="value">15px</span>;  <span class="comment">/* 中等间距 */</span>
<span class="property">gap</span>: <span class="value">30px</span>;  <span class="comment">/* 大间距 */</span>

<span class="comment">/* 分别设置行列间距 */</span>
<span class="property">row-gap</span>: <span class="value">10px</span>;
<span class="property">column-gap</span>: <span class="value">20px</span>;
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>小间距 (5px)</h4>
                    <div class="grid-container demo-gap-small" style="grid-template-columns: repeat(3, 1fr);">
                        <div class="grid-item">1</div>
                        <div class="grid-item">2</div>
                        <div class="grid-item">3</div>
                    </div>
                </div>
                <div>
                    <h4>大间距 (30px)</h4>
                    <div class="grid-container demo-gap-large" style="grid-template-columns: repeat(3, 1fr);">
                        <div class="grid-item">1</div>
                        <div class="grid-item">2</div>
                        <div class="grid-item">3</div>
                    </div>
                </div>
            </div>

            <h4>非对称间距</h4>
            <div class="grid-container demo-gap-asymmetric" style="grid-template-columns: repeat(4, 1fr);">
                <div class="grid-item">行间距10px</div>
                <div class="grid-item">列间距20px</div>
                <div class="grid-item">不同</div>
                <div class="grid-item">间距</div>
                <div class="grid-item">效果</div>
                <div class="grid-item">演示</div>
                <div class="grid-item">Grid</div>
                <div class="grid-item">Gap</div>
            </div>
        </div>
    </div>

    <script>
        // 交互演示功能
        const col1Slider = document.getElementById('col1Fr');
        const col2Slider = document.getElementById('col2Fr');
        const col3Slider = document.getElementById('col3Fr');
        
        const col1Value = document.getElementById('col1Value');
        const col2Value = document.getElementById('col2Value');
        const col3Value = document.getElementById('col3Value');
        
        const interactiveGrid = document.getElementById('interactiveGrid');
        const currentGridCode = document.getElementById('currentGridCode');
        const calculationSteps = document.getElementById('calculationSteps');

        function updateGridLayout() {
            const fr1 = parseFloat(col1Slider.value);
            const fr2 = parseFloat(col2Slider.value);
            const fr3 = parseFloat(col3Slider.value);
            
            // 更新显示值
            col1Value.textContent = `${fr1}fr`;
            col2Value.textContent = `${fr2}fr`;
            col3Value.textContent = `${fr3}fr`;
            
            // 应用样式
            interactiveGrid.style.gridTemplateColumns = `${fr1}fr ${fr2}fr ${fr3}fr`;
            
            // 更新代码显示
            currentGridCode.innerHTML = `<span class="property">grid-template-columns</span>: <span class="value">${fr1}fr ${fr2}fr ${fr3}fr</span>;`;
            
            // 计算比例
            const total = fr1 + fr2 + fr3;
            const percent1 = ((fr1 / total) * 100).toFixed(1);
            const percent2 = ((fr2 / total) * 100).toFixed(1);
            const percent3 = ((fr3 / total) * 100).toFixed(1);
            
            calculationSteps.innerHTML = `
                <div class="calculation-step">总份数 = ${fr1} + ${fr2} + ${fr3} = ${total}份</div>
                <div class="calculation-step">第1列 = ${fr1}/${total} = ${percent1}%</div>
                <div class="calculation-step">第2列 = ${fr2}/${total} = ${percent2}%</div>
                <div class="calculation-step">第3列 = ${fr3}/${total} = ${percent3}%</div>
            `;
        }

        // 绑定事件监听器
        col1Slider.addEventListener('input', updateGridLayout);
        col2Slider.addEventListener('input', updateGridLayout);
        col3Slider.addEventListener('input', updateGridLayout);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateGridLayout();
            
            console.log('=== CSS Grid fr 单位演示 ===');
            console.log('💡 提示：');
            console.log('1. 调整交互演示中的滑块观察效果');
            console.log('2. fr单位会按比例分配剩余空间');
            console.log('3. 与固定单位混合使用时，先分配固定空间');
        });

        // 添加点击效果
        document.querySelectorAll('.grid-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
