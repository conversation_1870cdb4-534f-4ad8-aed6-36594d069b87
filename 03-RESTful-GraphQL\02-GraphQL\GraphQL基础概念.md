# GraphQL 基础概念详解

## 1. GraphQL 简介

### 1.1 什么是 GraphQL？
**GraphQL** 是由Facebook在2012年开发，2015年开源的一种API查询语言和运行时。它提供了一种更高效、强大和灵活的替代REST的方案。

### 1.2 GraphQL 的核心特性
- **声明式数据获取**: 客户端精确指定需要的数据
- **单一端点**: 所有操作通过一个URL进行
- **强类型系统**: 严格的类型定义和验证
- **实时订阅**: 支持实时数据推送
- **内省能力**: API可以描述自己的结构

## 2. GraphQL vs REST 对比

### 2.1 数据获取方式对比

#### REST 方式
```javascript
// 获取用户信息需要多次请求
GET /users/123           // 获取用户基本信息
GET /users/123/posts     // 获取用户文章
GET /users/123/friends   // 获取用户好友

// 响应数据可能包含不需要的字段
{
  "id": 123,
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "************",     // 可能不需要
  "address": "...",            // 可能不需要
  "created_at": "...",         // 可能不需要
  "updated_at": "..."          // 可能不需要
}
```

#### GraphQL 方式
```graphql
# 单次请求获取所有需要的数据
query {
  user(id: 123) {
    name
    email
    posts {
      title
      content
    }
    friends {
      name
    }
  }
}

# 响应只包含请求的字段
{
  "data": {
    "user": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "posts": [...],
      "friends": [...]
    }
  }
}
```

### 2.2 优缺点对比

| 特性 | REST | GraphQL |
|------|------|---------|
| **学习曲线** | 简单 | 较复杂 |
| **数据获取** | 多次请求 | 单次请求 |
| **过度获取** | 常见 | 避免 |
| **缓存** | HTTP缓存 | 复杂缓存 |
| **文件上传** | 简单 | 需要特殊处理 |
| **错误处理** | HTTP状态码 | 统一错误格式 |
| **工具生态** | 成熟 | 快速发展 |

## 3. GraphQL 核心概念

### 3.1 Schema (模式)
Schema定义了API的结构，包括可用的类型、字段和操作。

```graphql
# 定义用户类型
type User {
  id: ID!
  name: String!
  email: String!
  posts: [Post!]!
  friends: [User!]!
}

# 定义文章类型
type Post {
  id: ID!
  title: String!
  content: String!
  author: User!
  comments: [Comment!]!
  createdAt: DateTime!
}

# 定义评论类型
type Comment {
  id: ID!
  content: String!
  author: User!
  post: Post!
  createdAt: DateTime!
}

# 定义查询操作
type Query {
  user(id: ID!): User
  users: [User!]!
  post(id: ID!): Post
  posts: [Post!]!
}

# 定义修改操作
type Mutation {
  createUser(input: CreateUserInput!): User!
  updateUser(id: ID!, input: UpdateUserInput!): User!
  deleteUser(id: ID!): Boolean!
  createPost(input: CreatePostInput!): Post!
}

# 定义订阅操作
type Subscription {
  userCreated: User!
  postCreated: Post!
  commentAdded(postId: ID!): Comment!
}
```

### 3.2 Query (查询)
Query用于读取数据，类似于REST中的GET请求。

```graphql
# 基础查询
query {
  users {
    id
    name
    email
  }
}

# 带参数的查询
query GetUser($userId: ID!) {
  user(id: $userId) {
    id
    name
    email
    posts {
      id
      title
      createdAt
    }
  }
}

# 嵌套查询
query {
  user(id: "123") {
    name
    posts {
      title
      comments {
        content
        author {
          name
        }
      }
    }
  }
}

# 使用别名
query {
  user1: user(id: "123") {
    name
    email
  }
  user2: user(id: "456") {
    name
    email
  }
}

# 使用片段
fragment UserInfo on User {
  id
  name
  email
}

query {
  user1: user(id: "123") {
    ...UserInfo
  }
  user2: user(id: "456") {
    ...UserInfo
  }
}
```

### 3.3 Mutation (修改)
Mutation用于修改数据，类似于REST中的POST、PUT、DELETE请求。

```graphql
# 创建用户
mutation CreateUser($input: CreateUserInput!) {
  createUser(input: $input) {
    id
    name
    email
    createdAt
  }
}

# 变量
{
  "input": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}

# 更新用户
mutation UpdateUser($id: ID!, $input: UpdateUserInput!) {
  updateUser(id: $id, input: $input) {
    id
    name
    email
    updatedAt
  }
}

# 删除用户
mutation DeleteUser($id: ID!) {
  deleteUser(id: $id)
}

# 多个操作
mutation {
  createUser(input: { name: "Alice", email: "<EMAIL>" }) {
    id
    name
  }
  createPost(input: { title: "Hello World", content: "...", authorId: "123" }) {
    id
    title
  }
}
```

### 3.4 Subscription (订阅)
Subscription用于实时数据推送，通常基于WebSocket实现。

```graphql
# 订阅新用户创建
subscription {
  userCreated {
    id
    name
    email
    createdAt
  }
}

# 订阅特定文章的新评论
subscription CommentAdded($postId: ID!) {
  commentAdded(postId: $postId) {
    id
    content
    author {
      name
    }
    createdAt
  }
}
```

## 4. GraphQL 类型系统

### 4.1 标量类型 (Scalar Types)
```graphql
# 内置标量类型
Int       # 32位整数
Float     # 双精度浮点数
String    # UTF-8字符串
Boolean   # true或false
ID        # 唯一标识符

# 自定义标量类型
scalar DateTime
scalar Email
scalar URL
```

### 4.2 对象类型 (Object Types)
```graphql
type User {
  id: ID!
  name: String!
  email: Email!
  age: Int
  isActive: Boolean!
  createdAt: DateTime!
}
```

### 4.3 枚举类型 (Enum Types)
```graphql
enum UserRole {
  ADMIN
  MODERATOR
  USER
  GUEST
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

type User {
  id: ID!
  name: String!
  role: UserRole!
}
```

### 4.4 接口类型 (Interface Types)
```graphql
interface Node {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type User implements Node {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String!
  email: String!
}

type Post implements Node {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  title: String!
  content: String!
}
```

### 4.5 联合类型 (Union Types)
```graphql
union SearchResult = User | Post | Comment

type Query {
  search(query: String!): [SearchResult!]!
}

# 查询联合类型
query Search($query: String!) {
  search(query: $query) {
    ... on User {
      id
      name
      email
    }
    ... on Post {
      id
      title
      content
    }
    ... on Comment {
      id
      content
    }
  }
}
```

### 4.6 输入类型 (Input Types)
```graphql
input CreateUserInput {
  name: String!
  email: String!
  age: Int
  role: UserRole = USER
}

input UpdateUserInput {
  name: String
  email: String
  age: Int
  role: UserRole
}

input PostFilter {
  authorId: ID
  status: PostStatus
  createdAfter: DateTime
  createdBefore: DateTime
}
```

## 5. Resolver (解析器)

Resolver是GraphQL的核心，负责为每个字段获取数据。

```javascript
// JavaScript/Node.js 示例
const resolvers = {
  Query: {
    // 获取所有用户
    users: async () => {
      return await User.findAll();
    },
    
    // 获取单个用户
    user: async (parent, { id }) => {
      return await User.findById(id);
    },
    
    // 搜索
    search: async (parent, { query }) => {
      const users = await User.search(query);
      const posts = await Post.search(query);
      return [...users, ...posts];
    }
  },
  
  Mutation: {
    // 创建用户
    createUser: async (parent, { input }) => {
      return await User.create(input);
    },
    
    // 更新用户
    updateUser: async (parent, { id, input }) => {
      return await User.update(id, input);
    }
  },
  
  Subscription: {
    // 用户创建订阅
    userCreated: {
      subscribe: () => pubsub.asyncIterator(['USER_CREATED'])
    }
  },
  
  // 字段解析器
  User: {
    // 解析用户的文章
    posts: async (user) => {
      return await Post.findByAuthorId(user.id);
    },
    
    // 解析用户的好友
    friends: async (user) => {
      return await User.findFriends(user.id);
    }
  },
  
  Post: {
    // 解析文章的作者
    author: async (post) => {
      return await User.findById(post.authorId);
    },
    
    // 解析文章的评论
    comments: async (post) => {
      return await Comment.findByPostId(post.id);
    }
  }
};
```

## 6. GraphQL 工具和生态

### 6.1 服务端工具
- **Apollo Server**: 功能完整的GraphQL服务器
- **GraphQL Yoga**: 简单易用的GraphQL服务器
- **Hasura**: 自动生成GraphQL API
- **Prisma**: 数据库工具包

### 6.2 客户端工具
- **Apollo Client**: 功能强大的GraphQL客户端
- **Relay**: Facebook的GraphQL客户端
- **urql**: 轻量级GraphQL客户端
- **graphql-request**: 简单的GraphQL客户端

### 6.3 开发工具
- **GraphQL Playground**: 交互式查询IDE
- **GraphiQL**: 浏览器内GraphQL IDE
- **Apollo Studio**: GraphQL开发平台

## 7. 最佳实践

### 7.1 Schema设计
- 使用描述性的类型和字段名
- 合理使用非空类型(!)
- 避免过深的嵌套
- 使用枚举类型替代字符串常量

### 7.2 查询优化
- 使用DataLoader解决N+1问题
- 实现查询复杂度分析
- 设置查询深度限制
- 使用查询白名单

### 7.3 错误处理
```graphql
# 统一的错误格式
{
  "data": null,
  "errors": [
    {
      "message": "User not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["user"],
      "extensions": {
        "code": "USER_NOT_FOUND",
        "userId": "123"
      }
    }
  ]
}
```

## 8. GraphQL 高级特性

### 8.1 指令 (Directives)
指令提供了在执行时动态修改查询结构的能力。

```graphql
# 内置指令
query GetUser($includeEmail: Boolean!, $skipPosts: Boolean!) {
  user(id: "123") {
    id
    name
    email @include(if: $includeEmail)  # 条件包含
    posts @skip(if: $skipPosts) {      # 条件跳过
      title
    }
  }
}

# 自定义指令
directive @auth(requires: UserRole = USER) on FIELD_DEFINITION

type Query {
  users: [User!]! @auth(requires: ADMIN)
  profile: User @auth(requires: USER)
}

# 格式化指令
directive @date(format: String = "YYYY-MM-DD") on FIELD_DEFINITION

type User {
  id: ID!
  name: String!
  createdAt: DateTime! @date(format: "YYYY-MM-DD HH:mm:ss")
}
```

### 8.2 分页 (Pagination)

#### 基于偏移量的分页
```graphql
type Query {
  users(offset: Int = 0, limit: Int = 10): UserConnection!
}

type UserConnection {
  users: [User!]!
  totalCount: Int!
  hasMore: Boolean!
}

# 查询示例
query {
  users(offset: 20, limit: 10) {
    users {
      id
      name
    }
    totalCount
    hasMore
  }
}
```

#### 基于游标的分页 (Relay规范)
```graphql
type Query {
  users(first: Int, after: String, last: Int, before: String): UserConnection!
}

type UserConnection {
  edges: [UserEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type UserEdge {
  node: User!
  cursor: String!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

# 查询示例
query {
  users(first: 10, after: "cursor123") {
    edges {
      node {
        id
        name
      }
      cursor
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
```

### 8.3 文件上传
```graphql
# Schema定义
scalar Upload

type Mutation {
  uploadFile(file: Upload!): File!
  uploadMultipleFiles(files: [Upload!]!): [File!]!
}

type File {
  id: ID!
  filename: String!
  mimetype: String!
  encoding: String!
  url: String!
}
```

```javascript
// 客户端上传 (使用Apollo Client)
import { gql, useMutation } from '@apollo/client';

const UPLOAD_FILE = gql`
  mutation UploadFile($file: Upload!) {
    uploadFile(file: $file) {
      id
      filename
      url
    }
  }
`;

function FileUpload() {
  const [uploadFile] = useMutation(UPLOAD_FILE);

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    try {
      const { data } = await uploadFile({
        variables: { file }
      });
      console.log('上传成功:', data.uploadFile);
    } catch (error) {
      console.error('上传失败:', error);
    }
  };

  return <input type="file" onChange={handleFileUpload} />;
}
```

### 8.4 实时订阅详解

#### WebSocket实现
```javascript
// 服务端 - Apollo Server
import { PubSub } from 'graphql-subscriptions';
import { createServer } from 'http';
import { ApolloServer } from 'apollo-server-express';
import { SubscriptionServer } from 'subscriptions-transport-ws';

const pubsub = new PubSub();

const resolvers = {
  Subscription: {
    messageAdded: {
      subscribe: () => pubsub.asyncIterator(['MESSAGE_ADDED'])
    },
    userOnline: {
      subscribe: withFilter(
        () => pubsub.asyncIterator(['USER_STATUS_CHANGED']),
        (payload, variables) => {
          return payload.userOnline.id === variables.userId;
        }
      )
    }
  },

  Mutation: {
    sendMessage: async (parent, { input }) => {
      const message = await Message.create(input);
      pubsub.publish('MESSAGE_ADDED', { messageAdded: message });
      return message;
    }
  }
};

// 创建HTTP服务器
const httpServer = createServer(app);

// 创建订阅服务器
SubscriptionServer.create(
  { schema, execute, subscribe },
  { server: httpServer, path: '/graphql' }
);
```

```javascript
// 客户端 - React + Apollo Client
import { useSubscription, gql } from '@apollo/client';

const MESSAGE_SUBSCRIPTION = gql`
  subscription MessageAdded($chatId: ID!) {
    messageAdded(chatId: $chatId) {
      id
      content
      author {
        name
      }
      createdAt
    }
  }
`;

function ChatRoom({ chatId }) {
  const { data, loading } = useSubscription(MESSAGE_SUBSCRIPTION, {
    variables: { chatId }
  });

  if (loading) return <p>连接中...</p>;

  return (
    <div>
      {data && (
        <div>新消息: {data.messageAdded.content}</div>
      )}
    </div>
  );
}
```

## 9. 性能优化

### 9.1 N+1 查询问题
```javascript
// 问题：每个用户都会触发一次数据库查询
const resolvers = {
  Query: {
    users: () => User.findAll()
  },
  User: {
    // ❌ N+1问题：如果有100个用户，会执行101次查询
    posts: (user) => Post.findByAuthorId(user.id)
  }
};

// 解决方案：使用DataLoader
import DataLoader from 'dataloader';

const postLoader = new DataLoader(async (userIds) => {
  const posts = await Post.findByAuthorIds(userIds);
  return userIds.map(id => posts.filter(post => post.authorId === id));
});

const resolvers = {
  User: {
    // ✅ 批量加载，只执行2次查询
    posts: (user) => postLoader.load(user.id)
  }
};
```

### 9.2 查询复杂度分析
```javascript
import { costAnalysis, maximumCost } from 'graphql-cost-analysis';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  plugins: [
    costAnalysis({
      maximumCost: 1000,
      defaultCost: 1,
      scalarCost: 1,
      objectCost: 1,
      listFactor: 10,
      introspectionCost: 1000,
      createError: (max, actual) => {
        return new Error(`查询复杂度 ${actual} 超过最大限制 ${max}`);
      }
    })
  ]
});
```

### 9.3 查询深度限制
```javascript
import depthLimit from 'graphql-depth-limit';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  validationRules: [depthLimit(7)]
});

// 这个查询会被拒绝（深度超过7层）
query {
  user {
    friends {
      friends {
        friends {
          friends {
            friends {
              friends {
                friends {  // 第8层，超过限制
                  name
                }
              }
            }
          }
        }
      }
    }
  }
}
```

### 9.4 缓存策略

#### 查询级缓存
```javascript
import { InMemoryLRUCache } from 'apollo-server-caching';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  cache: new InMemoryLRUCache(),
  cacheControl: {
    defaultMaxAge: 300, // 5分钟
  }
});

// 在resolver中设置缓存
const resolvers = {
  Query: {
    users: (parent, args, { dataSources }) => {
      return dataSources.userAPI.getUsers();
    }
  }
};

// 在schema中设置缓存指令
type Query {
  users: [User!]! @cacheControl(maxAge: 600)
  user(id: ID!): User @cacheControl(maxAge: 300)
}
```

#### 字段级缓存
```javascript
// 使用Redis进行字段级缓存
import Redis from 'ioredis';
const redis = new Redis();

const resolvers = {
  User: {
    expensiveCalculation: async (user) => {
      const cacheKey = `user:${user.id}:calculation`;

      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // 执行昂贵的计算
      const result = await performExpensiveCalculation(user);

      // 缓存结果（1小时）
      await redis.setex(cacheKey, 3600, JSON.stringify(result));

      return result;
    }
  }
};

## 10. 安全性考虑

### 10.1 认证和授权
```javascript
// JWT认证
import jwt from 'jsonwebtoken';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: ({ req }) => {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (token) {
      try {
        const user = jwt.verify(token, process.env.JWT_SECRET);
        return { user };
      } catch (error) {
        throw new AuthenticationError('无效的token');
      }
    }

    return {};
  }
});

// 基于角色的授权
const resolvers = {
  Query: {
    users: (parent, args, { user }) => {
      if (!user || user.role !== 'ADMIN') {
        throw new ForbiddenError('需要管理员权限');
      }
      return User.findAll();
    }
  },

  User: {
    email: (userObj, args, { user }) => {
      // 只有用户本人或管理员可以查看邮箱
      if (user && (user.id === userObj.id || user.role === 'ADMIN')) {
        return userObj.email;
      }
      return null;
    }
  }
};
```

### 10.2 输入验证
```javascript
import Joi from 'joi';

const createUserSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  age: Joi.number().integer().min(0).max(150)
});

const resolvers = {
  Mutation: {
    createUser: async (parent, { input }) => {
      // 验证输入
      const { error, value } = createUserSchema.validate(input);
      if (error) {
        throw new UserInputError('输入验证失败', {
          validationErrors: error.details
        });
      }

      return User.create(value);
    }
  }
};
```

### 10.3 查询白名单
```javascript
// 生产环境中只允许预定义的查询
const allowedQueries = new Map([
  ['GetUser', 'query GetUser($id: ID!) { user(id: $id) { id name email } }'],
  ['GetPosts', 'query GetPosts { posts { id title author { name } } }']
]);

const server = new ApolloServer({
  typeDefs,
  resolvers,
  validationRules: [
    require('graphql-query-complexity').createComplexityLimitRule(1000),
    (context) => {
      const query = context.getDocument();
      const queryName = query.definitions[0]?.name?.value;

      if (!allowedQueries.has(queryName)) {
        throw new Error(`查询 ${queryName} 不在白名单中`);
      }

      return {};
    }
  ]
});
```

## 11. 测试策略

### 11.1 Schema测试
```javascript
import { buildSchema } from 'graphql';
import { validateSchema } from 'graphql/validation';

describe('GraphQL Schema', () => {
  test('schema应该是有效的', () => {
    const schema = buildSchema(typeDefs);
    const errors = validateSchema(schema);
    expect(errors).toHaveLength(0);
  });

  test('所有类型都应该有必需的字段', () => {
    const schema = buildSchema(typeDefs);
    const userType = schema.getType('User');

    expect(userType.getFields()).toHaveProperty('id');
    expect(userType.getFields()).toHaveProperty('name');
    expect(userType.getFields()).toHaveProperty('email');
  });
});
```

### 11.2 Resolver测试
```javascript
import { graphql } from 'graphql';
import { makeExecutableSchema } from '@graphql-tools/schema';

describe('User Resolvers', () => {
  let schema;

  beforeEach(() => {
    schema = makeExecutableSchema({
      typeDefs,
      resolvers: {
        Query: {
          user: jest.fn().mockResolvedValue({
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>'
          })
        }
      }
    });
  });

  test('应该返回用户信息', async () => {
    const query = `
      query {
        user(id: "1") {
          id
          name
          email
        }
      }
    `;

    const result = await graphql(schema, query);

    expect(result.errors).toBeUndefined();
    expect(result.data.user).toEqual({
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>'
    });
  });
});
```

### 11.3 集成测试
```javascript
import { createTestClient } from 'apollo-server-testing';
import { ApolloServer } from 'apollo-server-express';

describe('GraphQL Integration Tests', () => {
  let server;
  let query, mutate;

  beforeEach(() => {
    server = new ApolloServer({
      typeDefs,
      resolvers,
      context: () => ({
        user: { id: '1', role: 'USER' }
      })
    });

    const testClient = createTestClient(server);
    query = testClient.query;
    mutate = testClient.mutate;
  });

  test('应该创建新用户', async () => {
    const CREATE_USER = gql`
      mutation CreateUser($input: CreateUserInput!) {
        createUser(input: $input) {
          id
          name
          email
        }
      }
    `;

    const result = await mutate({
      mutation: CREATE_USER,
      variables: {
        input: {
          name: 'Jane Doe',
          email: '<EMAIL>'
        }
      }
    });

    expect(result.errors).toBeUndefined();
    expect(result.data.createUser).toMatchObject({
      name: 'Jane Doe',
      email: '<EMAIL>'
    });
  });
});
```

## 12. 部署和监控

### 12.1 生产环境配置
```javascript
// 生产环境Apollo Server配置
const server = new ApolloServer({
  typeDefs,
  resolvers,

  // 安全配置
  introspection: process.env.NODE_ENV !== 'production',
  playground: process.env.NODE_ENV !== 'production',

  // 性能配置
  cache: new RedisCache({
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
  }),

  // 错误处理
  formatError: (error) => {
    // 记录错误日志
    console.error(error);

    // 生产环境中隐藏内部错误详情
    if (process.env.NODE_ENV === 'production') {
      delete error.extensions.exception.stacktrace;
    }

    return error;
  },

  // 插件
  plugins: [
    // 查询复杂度分析
    require('graphql-cost-analysis')({
      maximumCost: 1000,
    }),

    // 性能监控
    {
      requestDidStart() {
        return {
          willSendResponse(requestContext) {
            console.log(`查询执行时间: ${Date.now() - requestContext.request.http.startTime}ms`);
          }
        };
      }
    }
  ]
});
```

### 12.2 监控和日志
```javascript
// 使用Apollo Studio进行监控
const server = new ApolloServer({
  typeDefs,
  resolvers,

  // Apollo Studio配置
  engine: {
    apiKey: process.env.APOLLO_KEY,
    schemaTag: process.env.APOLLO_GRAPH_VARIANT || 'current'
  },

  // 自定义指标收集
  plugins: [
    {
      requestDidStart() {
        return {
          didResolveOperation(requestContext) {
            // 记录操作类型
            const operationType = requestContext.request.operationName;
            metrics.increment(`graphql.operation.${operationType}`);
          },

          didEncounterErrors(requestContext) {
            // 记录错误
            requestContext.errors.forEach(error => {
              logger.error('GraphQL Error', {
                error: error.message,
                query: requestContext.request.query,
                variables: requestContext.request.variables
              });
            });
          }
        };
      }
    }
  ]
});

## 13. 实际项目示例

### 13.1 电商系统GraphQL API

#### Schema设计
```graphql
# 产品相关类型
type Product {
  id: ID!
  name: String!
  description: String
  price: Money!
  category: Category!
  images: [Image!]!
  inventory: Inventory!
  reviews: ReviewConnection!
  averageRating: Float
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Category {
  id: ID!
  name: String!
  slug: String!
  parent: Category
  children: [Category!]!
  products(first: Int, after: String): ProductConnection!
}

type Money {
  amount: Float!
  currency: String!
}

type Image {
  id: ID!
  url: String!
  alt: String
  width: Int
  height: Int
}

type Inventory {
  quantity: Int!
  inStock: Boolean!
  lowStockThreshold: Int!
}

# 订单相关类型
type Order {
  id: ID!
  orderNumber: String!
  customer: User!
  items: [OrderItem!]!
  subtotal: Money!
  tax: Money!
  shipping: Money!
  total: Money!
  status: OrderStatus!
  shippingAddress: Address!
  billingAddress: Address!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type OrderItem {
  id: ID!
  product: Product!
  quantity: Int!
  unitPrice: Money!
  totalPrice: Money!
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

type Address {
  id: ID!
  street: String!
  city: String!
  state: String!
  zipCode: String!
  country: String!
}

# 用户相关类型
type User {
  id: ID!
  email: String!
  firstName: String!
  lastName: String!
  phone: String
  addresses: [Address!]!
  orders: OrderConnection!
  cart: Cart!
  wishlist: [Product!]!
  createdAt: DateTime!
}

type Cart {
  id: ID!
  items: [CartItem!]!
  subtotal: Money!
  itemCount: Int!
}

type CartItem {
  id: ID!
  product: Product!
  quantity: Int!
  unitPrice: Money!
  totalPrice: Money!
}

# 查询根类型
type Query {
  # 产品查询
  product(id: ID!): Product
  products(
    first: Int = 20
    after: String
    category: String
    search: String
    priceRange: PriceRangeInput
    sortBy: ProductSortInput
  ): ProductConnection!

  # 分类查询
  categories: [Category!]!
  category(id: ID, slug: String): Category

  # 用户查询
  me: User @auth
  user(id: ID!): User @auth(requires: ADMIN)

  # 订单查询
  order(id: ID!): Order @auth
  orders(first: Int, after: String): OrderConnection! @auth

  # 搜索
  search(query: String!, type: SearchType): SearchResult!
}

# 变更根类型
type Mutation {
  # 用户认证
  register(input: RegisterInput!): AuthPayload!
  login(input: LoginInput!): AuthPayload!
  logout: Boolean! @auth

  # 购物车操作
  addToCart(productId: ID!, quantity: Int = 1): Cart! @auth
  updateCartItem(itemId: ID!, quantity: Int!): Cart! @auth
  removeFromCart(itemId: ID!): Cart! @auth
  clearCart: Cart! @auth

  # 订单操作
  createOrder(input: CreateOrderInput!): Order! @auth
  updateOrderStatus(orderId: ID!, status: OrderStatus!): Order! @auth(requires: ADMIN)
  cancelOrder(orderId: ID!): Order! @auth

  # 产品管理
  createProduct(input: CreateProductInput!): Product! @auth(requires: ADMIN)
  updateProduct(id: ID!, input: UpdateProductInput!): Product! @auth(requires: ADMIN)
  deleteProduct(id: ID!): Boolean! @auth(requires: ADMIN)
}

# 订阅根类型
type Subscription {
  # 订单状态更新
  orderStatusChanged(userId: ID!): Order! @auth

  # 库存变化
  inventoryChanged(productId: ID!): Inventory!

  # 新产品上架
  productAdded(categoryId: ID): Product!
}

# 输入类型
input RegisterInput {
  email: String!
  password: String!
  firstName: String!
  lastName: String!
  phone: String
}

input LoginInput {
  email: String!
  password: String!
}

input CreateOrderInput {
  items: [OrderItemInput!]!
  shippingAddressId: ID!
  billingAddressId: ID!
  paymentMethodId: ID!
}

input OrderItemInput {
  productId: ID!
  quantity: Int!
}

input PriceRangeInput {
  min: Float
  max: Float
}

input ProductSortInput {
  field: ProductSortField!
  direction: SortDirection!
}

enum ProductSortField {
  NAME
  PRICE
  CREATED_AT
  RATING
}

enum SortDirection {
  ASC
  DESC
}

# 连接类型（分页）
type ProductConnection {
  edges: [ProductEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type ProductEdge {
  node: Product!
  cursor: String!
}

type OrderConnection {
  edges: [OrderEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type OrderEdge {
  node: Order!
  cursor: String!
}

# 认证载荷
type AuthPayload {
  token: String!
  user: User!
  expiresIn: Int!
}

# 搜索结果
union SearchResult = Product | Category | User

enum SearchType {
  ALL
  PRODUCTS
  CATEGORIES
  USERS
}
```

#### Resolver实现
```javascript
import { AuthenticationError, ForbiddenError } from 'apollo-server-express';
import { combineResolvers } from 'graphql-resolvers';

// 认证中间件
const isAuthenticated = (parent, args, { user }) => {
  if (!user) {
    throw new AuthenticationError('必须登录');
  }
};

const isAdmin = combineResolvers(
  isAuthenticated,
  (parent, args, { user }) => {
    if (user.role !== 'ADMIN') {
      throw new ForbiddenError('需要管理员权限');
    }
  }
);

const resolvers = {
  Query: {
    // 产品查询
    product: async (parent, { id }) => {
      return await Product.findById(id);
    },

    products: async (parent, { first, after, category, search, priceRange, sortBy }) => {
      const filters = {};

      if (category) filters.categoryId = category;
      if (search) filters.name = { $regex: search, $options: 'i' };
      if (priceRange) {
        filters.price = {};
        if (priceRange.min) filters.price.$gte = priceRange.min;
        if (priceRange.max) filters.price.$lte = priceRange.max;
      }

      const sort = {};
      if (sortBy) {
        sort[sortBy.field.toLowerCase()] = sortBy.direction === 'ASC' ? 1 : -1;
      }

      return await Product.findWithPagination({
        filters,
        sort,
        first,
        after
      });
    },

    // 用户查询
    me: combineResolvers(isAuthenticated, async (parent, args, { user }) => {
      return await User.findById(user.id);
    }),

    // 订单查询
    orders: combineResolvers(isAuthenticated, async (parent, { first, after }, { user }) => {
      return await Order.findByUserWithPagination(user.id, { first, after });
    })
  },

  Mutation: {
    // 用户注册
    register: async (parent, { input }) => {
      const existingUser = await User.findByEmail(input.email);
      if (existingUser) {
        throw new Error('邮箱已被注册');
      }

      const hashedPassword = await bcrypt.hash(input.password, 10);
      const user = await User.create({
        ...input,
        password: hashedPassword
      });

      const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET);

      return {
        token,
        user,
        expiresIn: 3600
      };
    },

    // 添加到购物车
    addToCart: combineResolvers(isAuthenticated, async (parent, { productId, quantity }, { user }) => {
      const product = await Product.findById(productId);
      if (!product) {
        throw new Error('产品不存在');
      }

      if (product.inventory.quantity < quantity) {
        throw new Error('库存不足');
      }

      return await Cart.addItem(user.id, productId, quantity);
    }),

    // 创建订单
    createOrder: combineResolvers(isAuthenticated, async (parent, { input }, { user, pubsub }) => {
      const order = await Order.create({
        ...input,
        customerId: user.id
      });

      // 发布订单创建事件
      pubsub.publish('ORDER_STATUS_CHANGED', {
        orderStatusChanged: order,
        userId: user.id
      });

      return order;
    })
  },

  Subscription: {
    orderStatusChanged: {
      subscribe: combineResolvers(
        isAuthenticated,
        withFilter(
          (parent, args, { pubsub }) => pubsub.asyncIterator(['ORDER_STATUS_CHANGED']),
          (payload, variables) => payload.userId === variables.userId
        )
      )
    }
  },

  // 字段解析器
  Product: {
    category: async (product) => {
      return await Category.findById(product.categoryId);
    },

    reviews: async (product, { first, after }) => {
      return await Review.findByProductWithPagination(product.id, { first, after });
    },

    averageRating: async (product) => {
      return await Review.getAverageRating(product.id);
    }
  },

  User: {
    orders: async (user, { first, after }) => {
      return await Order.findByUserWithPagination(user.id, { first, after });
    },

    cart: async (user) => {
      return await Cart.findByUserId(user.id);
    }
  },

  Order: {
    customer: async (order) => {
      return await User.findById(order.customerId);
    },

    items: async (order) => {
      return await OrderItem.findByOrderId(order.id);
    }
  },

  // 联合类型解析
  SearchResult: {
    __resolveType(obj) {
      if (obj.price) return 'Product';
      if (obj.slug) return 'Category';
      if (obj.email) return 'User';
      return null;
    }
  }
};

export default resolvers;
```

### 13.2 前端集成示例 (React + Apollo Client)

```javascript
// Apollo Client配置
import { ApolloClient, InMemoryCache, createHttpLink, split } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { getMainDefinition } from '@apollo/client/utilities';
import { WebSocketLink } from '@apollo/client/link/ws';

// HTTP链接
const httpLink = createHttpLink({
  uri: 'http://localhost:4000/graphql',
});

// WebSocket链接（用于订阅）
const wsLink = new WebSocketLink({
  uri: 'ws://localhost:4000/graphql',
  options: {
    reconnect: true,
    connectionParams: {
      authToken: localStorage.getItem('token'),
    },
  },
});

// 认证链接
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('token');
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  };
});

// 分割链接（HTTP用于查询和变更，WebSocket用于订阅）
const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return (
      definition.kind === 'OperationDefinition' &&
      definition.operation === 'subscription'
    );
  },
  wsLink,
  authLink.concat(httpLink),
);

const client = new ApolloClient({
  link: splitLink,
  cache: new InMemoryCache({
    typePolicies: {
      Product: {
        fields: {
          reviews: {
            merge(existing = { edges: [] }, incoming) {
              return {
                ...incoming,
                edges: [...existing.edges, ...incoming.edges],
              };
            },
          },
        },
      },
    },
  }),
});

// 产品列表组件
import { useQuery, gql } from '@apollo/client';

const GET_PRODUCTS = gql`
  query GetProducts($first: Int, $after: String, $category: String, $search: String) {
    products(first: $first, after: $after, category: $category, search: $search) {
      edges {
        node {
          id
          name
          price {
            amount
            currency
          }
          images {
            url
            alt
          }
          averageRating
        }
        cursor
      }
      pageInfo {
        hasNextPage
        endCursor
      }
      totalCount
    }
  }
`;

function ProductList({ category, search }) {
  const { data, loading, error, fetchMore } = useQuery(GET_PRODUCTS, {
    variables: { first: 20, category, search },
    notifyOnNetworkStatusChange: true,
  });

  const loadMore = () => {
    fetchMore({
      variables: {
        after: data.products.pageInfo.endCursor,
      },
    });
  };

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      <div className="product-grid">
        {data.products.edges.map(({ node: product }) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {data.products.pageInfo.hasNextPage && (
        <button onClick={loadMore}>加载更多</button>
      )}
    </div>
  );
}

// 购物车组件
const ADD_TO_CART = gql`
  mutation AddToCart($productId: ID!, $quantity: Int!) {
    addToCart(productId: $productId, quantity: $quantity) {
      id
      items {
        id
        product {
          id
          name
          price {
            amount
            currency
          }
        }
        quantity
        totalPrice {
          amount
          currency
        }
      }
      subtotal {
        amount
        currency
      }
      itemCount
    }
  }
`;

function AddToCartButton({ productId }) {
  const [addToCart, { loading }] = useMutation(ADD_TO_CART, {
    // 更新缓存
    update(cache, { data: { addToCart } }) {
      cache.writeQuery({
        query: GET_CART,
        data: { cart: addToCart },
      });
    },
    // 乐观更新
    optimisticResponse: {
      addToCart: {
        __typename: 'Cart',
        id: 'temp-id',
        itemCount: 1,
        // ... 其他字段
      },
    },
  });

  const handleAddToCart = () => {
    addToCart({
      variables: { productId, quantity: 1 },
    });
  };

  return (
    <button onClick={handleAddToCart} disabled={loading}>
      {loading ? '添加中...' : '加入购物车'}
    </button>
  );
}

// 订阅组件
const ORDER_STATUS_SUBSCRIPTION = gql`
  subscription OrderStatusChanged($userId: ID!) {
    orderStatusChanged(userId: $userId) {
      id
      orderNumber
      status
      updatedAt
    }
  }
`;

function OrderTracking({ userId }) {
  const { data, loading } = useSubscription(ORDER_STATUS_SUBSCRIPTION, {
    variables: { userId },
  });

  if (loading) return <div>连接中...</div>;

  return (
    <div>
      {data && (
        <div className="order-update">
          订单 {data.orderStatusChanged.orderNumber} 状态更新为: {data.orderStatusChanged.status}
        </div>
      )}
    </div>
  );
}
```

---

**学习检验**:
1. 解释GraphQL的核心概念和优势
2. 编写一个完整的GraphQL Schema
3. 实现基本的Resolver函数
4. 对比GraphQL和REST的适用场景
5. 实现GraphQL的认证和授权
6. 优化GraphQL查询性能
7. 设计GraphQL的缓存策略
8. 编写GraphQL的测试用例
```
```
