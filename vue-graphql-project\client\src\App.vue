<template>
	<div id="app" :class="{ dark: appStore.theme.isDark }">
		<router-view />

		<!-- 全局通知 -->
		<div class="notification-container">
			<transition-group name="notification" tag="div">
				<div
					v-for="notification in appStore.notifications"
					:key="notification.id"
					:class="[
						'notification',
						`notification--${notification.type}`,
					]"
					@click="appStore.removeNotification(notification.id)"
				>
					<div class="notification__icon">
						<el-icon>
							<SuccessFilled
								v-if="notification.type === 'success'"
							/>
							<WarningFilled
								v-else-if="notification.type === 'warning'"
							/>
							<InfoFilled
								v-else-if="notification.type === 'info'"
							/>
							<CircleCloseFilled v-else />
						</el-icon>
					</div>
					<div class="notification__content">
						<div class="notification__title">
							{{ notification.title }}
						</div>
						<div
							v-if="notification.message"
							class="notification__message"
						>
							{{ notification.message }}
						</div>
					</div>
					<div class="notification__close">
						<el-icon><Close /></el-icon>
					</div>
				</div>
			</transition-group>
		</div>

		<!-- 全局加载遮罩 -->
		<div v-if="appStore.loading" class="global-loading">
			<el-icon class="loading-icon">
				<Loading />
			</el-icon>
			<p>加载中...</p>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import {
	SuccessFilled,
	WarningFilled,
	InfoFilled,
	CircleCloseFilled,
	Close,
	Loading,
} from '@element-plus/icons-vue'

const appStore = useAppStore()
</script>

<style lang="scss">
// 全局样式
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

html,
body {
	height: 100%;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
		'Helvetica Neue', Arial, sans-serif;
}

#app {
	height: 100%;
	color: var(--el-text-color-primary);
	background-color: var(--el-bg-color);
}

// 通知样式
.notification-container {
	position: fixed;
	top: 20px;
	right: 20px;
	z-index: 9999;
	pointer-events: none;
}

.notification {
	display: flex;
	align-items: flex-start;
	gap: 12px;
	min-width: 300px;
	max-width: 400px;
	padding: 16px;
	margin-bottom: 12px;
	background: white;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	border-left: 4px solid;
	cursor: pointer;
	pointer-events: auto;
	transition: all 0.3s ease;

	&:hover {
		transform: translateX(-4px);
		box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
	}

	&--success {
		border-left-color: var(--el-color-success);
		.notification__icon {
			color: var(--el-color-success);
		}
	}

	&--warning {
		border-left-color: var(--el-color-warning);
		.notification__icon {
			color: var(--el-color-warning);
		}
	}

	&--info {
		border-left-color: var(--el-color-info);
		.notification__icon {
			color: var(--el-color-info);
		}
	}

	&--error {
		border-left-color: var(--el-color-danger);
		.notification__icon {
			color: var(--el-color-danger);
		}
	}

	&__icon {
		font-size: 20px;
		flex-shrink: 0;
	}

	&__content {
		flex: 1;
	}

	&__title {
		font-weight: 600;
		font-size: 14px;
		line-height: 1.4;
		margin-bottom: 4px;
	}

	&__message {
		font-size: 13px;
		color: var(--el-text-color-regular);
		line-height: 1.4;
	}

	&__close {
		font-size: 14px;
		color: var(--el-text-color-placeholder);
		cursor: pointer;
		flex-shrink: 0;
		opacity: 0;
		transition: opacity 0.2s;

		&:hover {
			color: var(--el-text-color-regular);
		}
	}

	&:hover &__close {
		opacity: 1;
	}
}

// 通知动画
.notification-enter-active,
.notification-leave-active {
	transition: all 0.3s ease;
}

.notification-enter-from {
	opacity: 0;
	transform: translateX(100%);
}

.notification-leave-to {
	opacity: 0;
	transform: translateX(100%);
}

// 全局加载遮罩
.global-loading {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 9998;

	.loading-icon {
		font-size: 32px;
		color: var(--el-color-primary);
		animation: rotate 2s linear infinite;
	}

	p {
		margin-top: 16px;
		color: var(--el-text-color-regular);
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

// 暗色主题
.dark {
	.notification {
		background: var(--el-bg-color-overlay);
		color: var(--el-text-color-primary);
	}

	.global-loading {
		background: rgba(0, 0, 0, 0.8);
	}
}

// 滚动条样式
::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

::-webkit-scrollbar-track {
	background: var(--el-fill-color-lighter);
}

::-webkit-scrollbar-thumb {
	background: var(--el-fill-color-dark);
	border-radius: 3px;

	&:hover {
		background: var(--el-fill-color-darker);
	}
}

// 响应式设计
@media (max-width: 768px) {
	.notification-container {
		left: 20px;
		right: 20px;
	}

	.notification {
		min-width: auto;
		max-width: none;
	}
}
</style>
