<template>
	<div class="user-list-container">
		<div class="page-header">
			<div class="header-left">
				<h1>用户管理</h1>
				<p>管理系统用户和权限</p>
			</div>
			<div class="header-right">
				<el-button type="primary" @click="showCreateDialog = true">
					<el-icon><Plus /></el-icon>
					新建用户
				</el-button>
			</div>
		</div>

		<!-- 搜索和筛选 -->
		<el-card class="filter-card">
			<el-row :gutter="20">
				<el-col :span="6">
					<el-input
						v-model="searchQuery"
						placeholder="搜索用户名或邮箱"
						:prefix-icon="Search"
						clearable
						@input="handleSearch"
					/>
				</el-col>
				<el-col :span="4">
					<el-select
						v-model="roleFilter"
						placeholder="用户角色"
						clearable
					>
						<el-option label="全部" value="" />
						<el-option label="管理员" value="ADMIN" />
						<el-option label="项目经理" value="MANAGER" />
						<el-option label="开发者" value="DEVELOPER" />
						<el-option label="设计师" value="DESIGNER" />
						<el-option label="测试员" value="TESTER" />
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-select
						v-model="statusFilter"
						placeholder="账户状态"
						clearable
					>
						<el-option label="全部" value="" />
						<el-option label="活跃" value="ACTIVE" />
						<el-option label="非活跃" value="INACTIVE" />
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-date-picker
						v-model="dateRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						size="default"
					/>
				</el-col>
				<el-col :span="3">
					<el-button @click="resetFilters">重置</el-button>
				</el-col>
				<el-col :span="3">
					<el-button @click="exportUsers">
						<el-icon><Download /></el-icon>
						导出
					</el-button>
				</el-col>
			</el-row>
		</el-card>

		<!-- 用户表格 -->
		<el-card class="user-table">
			<el-table
				:data="filteredUsers"
				style="width: 100%"
				v-loading="loading"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55" />

				<el-table-column label="用户信息" min-width="200">
					<template #default="{ row }">
						<div class="user-info">
							<el-avatar :size="40" :src="row.avatar">
								{{ row.username.charAt(0) }}
							</el-avatar>
							<div class="user-details">
								<div class="username">{{ row.username }}</div>
								<div class="fullname">
									{{ row.firstName }} {{ row.lastName }}
								</div>
								<div class="email">{{ row.email }}</div>
							</div>
						</div>
					</template>
				</el-table-column>

				<el-table-column prop="role" label="角色" width="120">
					<template #default="{ row }">
						<el-tag :type="getRoleType(row.role)" size="small">
							{{ getRoleText(row.role) }}
						</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="status" label="状态" width="100">
					<template #default="{ row }">
						<el-tag :type="getStatusType(row.status)" size="small">
							{{ getStatusText(row.status) }}
						</el-tag>
					</template>
				</el-table-column>

				<el-table-column
					prop="lastLoginAt"
					label="最后登录"
					width="150"
				>
					<template #default="{ row }">
						{{
							row.lastLoginAt
								? formatDate(row.lastLoginAt)
								: '从未登录'
						}}
					</template>
				</el-table-column>

				<el-table-column prop="createdAt" label="注册时间" width="150">
					<template #default="{ row }">
						{{ formatDate(row.createdAt) }}
					</template>
				</el-table-column>

				<el-table-column label="操作" width="200" fixed="right">
					<template #default="{ row }">
						<el-button size="small" @click="viewUser(row.id)"
							>查看</el-button
						>
						<el-button size="small" @click="editUser(row)"
							>编辑</el-button
						>
						<el-dropdown @command="handleUserAction">
							<el-button size="small">
								更多<el-icon class="el-icon--right"
									><arrow-down
								/></el-icon>
							</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item
										:command="`reset-password:${row.id}`"
									>
										重置密码
									</el-dropdown-item>
									<el-dropdown-item
										:command="`toggle-status:${row.id}`"
										:divided="true"
									>
										{{
											row.status === 'ACTIVE'
												? '禁用账户'
												: '启用账户'
										}}
									</el-dropdown-item>
									<el-dropdown-item
										:command="`delete:${row.id}`"
										class="danger-item"
									>
										删除用户
									</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</template>
				</el-table-column>
			</el-table>

			<div v-if="filteredUsers.length === 0" class="empty-state">
				<el-empty description="暂无用户数据" />
			</div>
		</el-card>

		<!-- 批量操作 -->
		<div v-if="selectedUsers.length > 0" class="batch-actions">
			<el-card>
				<div class="batch-info">
					<span>已选择 {{ selectedUsers.length }} 个用户</span>
					<div class="batch-buttons">
						<el-button @click="batchUpdateRole"
							>批量修改角色</el-button
						>
						<el-button @click="batchToggleStatus"
							>批量启用/禁用</el-button
						>
						<el-button type="danger" @click="batchDelete"
							>批量删除</el-button
						>
					</div>
				</div>
			</el-card>
		</div>

		<!-- 分页 -->
		<div class="pagination-container">
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:page-sizes="[10, 20, 50, 100]"
				:total="totalUsers"
				layout="total, sizes, prev, pager, next, jumper"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 创建/编辑用户对话框 -->
		<el-dialog
			v-model="showCreateDialog"
			:title="editingUser ? '编辑用户' : '新建用户'"
			width="600px"
		>
			<el-form
				ref="userFormRef"
				:model="userForm"
				:rules="userRules"
				label-width="100px"
			>
				<el-form-item label="用户名" prop="username">
					<el-input
						v-model="userForm.username"
						placeholder="请输入用户名"
					/>
				</el-form-item>

				<el-form-item label="邮箱" prop="email">
					<el-input
						v-model="userForm.email"
						placeholder="请输入邮箱地址"
					/>
				</el-form-item>

				<el-form-item v-if="!editingUser" label="密码" prop="password">
					<el-input
						v-model="userForm.password"
						type="password"
						placeholder="请输入密码"
						show-password
					/>
				</el-form-item>

				<el-form-item label="姓" prop="firstName">
					<el-input
						v-model="userForm.firstName"
						placeholder="请输入姓"
					/>
				</el-form-item>

				<el-form-item label="名" prop="lastName">
					<el-input
						v-model="userForm.lastName"
						placeholder="请输入名"
					/>
				</el-form-item>

				<el-form-item label="用户角色" prop="role">
					<el-select v-model="userForm.role" placeholder="请选择角色">
						<el-option label="管理员" value="ADMIN" />
						<el-option label="项目经理" value="MANAGER" />
						<el-option label="开发者" value="DEVELOPER" />
						<el-option label="设计师" value="DESIGNER" />
						<el-option label="测试员" value="TESTER" />
					</el-select>
				</el-form-item>

				<el-form-item label="账户状态" prop="status">
					<el-select
						v-model="userForm.status"
						placeholder="请选择状态"
					>
						<el-option label="活跃" value="ACTIVE" />
						<el-option label="非活跃" value="INACTIVE" />
					</el-select>
				</el-form-item>
			</el-form>

			<template #footer>
				<el-button @click="showCreateDialog = false">取消</el-button>
				<el-button type="primary" @click="saveUser" :loading="saving">
					{{ editingUser ? '更新' : '创建' }}
				</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQuery, useMutation } from '@vue/apollo-composable'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Download, ArrowDown } from '@element-plus/icons-vue'
import {
	GET_USERS_QUERY,
	CREATE_USER_MUTATION,
	UPDATE_USER_MUTATION,
	DELETE_USER_MUTATION,
} from '@/graphql/users'
import type { User } from '@/types'

const router = useRouter()

// 响应式数据
const saving = ref(false)
const showCreateDialog = ref(false)
const editingUser = ref<User | null>(null)
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const dateRange = ref<[Date, Date] | []>([])
const selectedUsers = ref<User[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalUsers = ref(0)

const userFormRef = ref()
const userForm = reactive({
	username: '',
	email: '',
	password: '',
	firstName: '',
	lastName: '',
	role: 'DEVELOPER',
	status: 'ACTIVE',
})

// GraphQL 查询和变更
const { result, loading, refetch } = useQuery(GET_USERS_QUERY, () => ({
	pagination: {
		page: currentPage.value,
		limit: pageSize.value,
	},
	search: searchQuery.value || undefined,
}))

const { mutate: createUser } = useMutation(CREATE_USER_MUTATION)
const { mutate: updateUser } = useMutation(UPDATE_USER_MUTATION)
const { mutate: deleteUserMutation } = useMutation(DELETE_USER_MUTATION)

// 计算属性 - 从 GraphQL 结果获取用户数据
const users = computed(() => result.value?.users?.users || [])

// 表单验证规则
const userRules = {
	username: [
		{ required: true, message: '请输入用户名', trigger: 'blur' },
		{
			min: 2,
			max: 20,
			message: '用户名长度在 2 到 20 个字符',
			trigger: 'blur',
		},
	],
	email: [
		{ required: true, message: '请输入邮箱地址', trigger: 'blur' },
		{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
	],
	password: [
		{ required: true, message: '请输入密码', trigger: 'blur' },
		{ min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
	],
	role: [{ required: true, message: '请选择用户角色', trigger: 'change' }],
	status: [{ required: true, message: '请选择账户状态', trigger: 'change' }],
}

// 计算属性
const filteredUsers = computed(() => {
	let filtered = users.value

	if (searchQuery.value) {
		filtered = filtered.filter(
			(user) =>
				user.username
					.toLowerCase()
					.includes(searchQuery.value.toLowerCase()) ||
				user.email
					.toLowerCase()
					.includes(searchQuery.value.toLowerCase())
		)
	}

	if (roleFilter.value) {
		filtered = filtered.filter((user) => user.role === roleFilter.value)
	}

	if (statusFilter.value) {
		filtered = filtered.filter((user) => user.status === statusFilter.value)
	}

	if (dateRange.value && dateRange.value.length === 2) {
		const [startDate, endDate] = dateRange.value
		filtered = filtered.filter((user) => {
			const userDate = new Date(user.createdAt)
			return userDate >= startDate && userDate <= endDate
		})
	}

	return filtered
})

// 方法
const getRoleType = (role: string) => {
	const types: Record<string, string> = {
		ADMIN: 'danger',
		MANAGER: 'warning',
		DEVELOPER: 'primary',
		DESIGNER: 'success',
		TESTER: 'info',
	}
	return types[role] || 'info'
}

const getRoleText = (role: string) => {
	const texts: Record<string, string> = {
		ADMIN: '管理员',
		MANAGER: '项目经理',
		DEVELOPER: '开发者',
		DESIGNER: '设计师',
		TESTER: '测试员',
	}
	return texts[role] || '未知'
}

const getStatusType = (status: string) => {
	const types: Record<string, string> = {
		ACTIVE: 'success',
		INACTIVE: 'info',
	}
	return types[status] || 'info'
}

const getStatusText = (status: string) => {
	const texts: Record<string, string> = {
		ACTIVE: '活跃',
		INACTIVE: '非活跃',
	}
	return texts[status] || '未知'
}

const formatDate = (dateString: string) => {
	return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleSearch = () => {
	// 搜索逻辑已在计算属性中处理
}

const resetFilters = () => {
	searchQuery.value = ''
	roleFilter.value = ''
	statusFilter.value = ''
	dateRange.value = []
}

const handleSelectionChange = (selection: any[]) => {
	selectedUsers.value = selection
}

const viewUser = (userId: string) => {
	router.push(`/users/${userId}`)
}

const editUser = (user: any) => {
	editingUser.value = user
	Object.assign(userForm, user)
	showCreateDialog.value = true
}

const handleUserAction = async (command: string) => {
	const [action, userId] = command.split(':')

	switch (action) {
		case 'reset-password':
			await resetUserPassword(userId)
			break
		case 'toggle-status':
			await toggleUserStatus(userId)
			break
		case 'delete':
			await deleteUser(userId)
			break
	}
}

const resetUserPassword = async (userId: string) => {
	try {
		await ElMessageBox.confirm('确定要重置该用户的密码吗？', '确认重置', {
			type: 'warning',
		})

		// 这里应该调用 GraphQL mutation 重置密码
		ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
	} catch {
		// 用户取消
	}
}

const toggleUserStatus = async (userId: string) => {
	const user = users.value.find((u) => u.id === userId)
	if (!user) return

	const newStatus = user.status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE'
	const action = newStatus === 'ACTIVE' ? '启用' : '禁用'

	try {
		await ElMessageBox.confirm(
			`确定要${action}该用户吗？`,
			`确认${action}`,
			{
				type: 'warning',
			}
		)

		user.status = newStatus
		ElMessage.success(`用户${action}成功`)
	} catch {
		// 用户取消
	}
}

const deleteUser = async (userId: string) => {
	try {
		await ElMessageBox.confirm(
			'确定要删除该用户吗？此操作不可恢复！',
			'确认删除',
			{
				type: 'warning',
			}
		)

		await deleteUserMutation({ id: userId })
		ElMessage.success('用户删除成功')
		// 重新获取数据
		await refetch()
	} catch (error) {
		if (error !== 'cancel') {
			console.error('删除用户失败:', error)
			ElMessage.error('删除失败')
		}
	}
}

const saveUser = async () => {
	if (!userFormRef.value) return

	try {
		await userFormRef.value.validate()
		saving.value = true

		if (editingUser.value) {
			// 更新用户
			await updateUser({
				id: editingUser.value.id,
				input: {
					username: userForm.username,
					email: userForm.email,
					firstName: userForm.firstName,
					lastName: userForm.lastName,
					role: userForm.role,
					status: userForm.status,
				},
			})
			ElMessage.success('用户更新成功')
		} else {
			// 创建新用户
			await createUser({
				input: {
					username: userForm.username,
					email: userForm.email,
					password: userForm.password,
					firstName: userForm.firstName,
					lastName: userForm.lastName,
					role: userForm.role,
					status: userForm.status,
				},
			})
			ElMessage.success('用户创建成功')
		}

		showCreateDialog.value = false
		resetForm()
		// 重新获取数据
		await refetch()
	} catch (error) {
		console.error('保存用户失败:', error)
		ElMessage.error('保存失败')
	} finally {
		saving.value = false
	}
}

const resetForm = () => {
	editingUser.value = null
	Object.assign(userForm, {
		username: '',
		email: '',
		password: '',
		firstName: '',
		lastName: '',
		role: 'DEVELOPER',
		status: 'ACTIVE',
	})
}

const exportUsers = () => {
	ElMessage.info('导出功能开发中')
}

const batchUpdateRole = () => {
	ElMessage.info('批量修改角色功能开发中')
}

const batchToggleStatus = () => {
	ElMessage.info('批量启用/禁用功能开发中')
}

const batchDelete = async () => {
	try {
		await ElMessageBox.confirm(
			`确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
			'确认批量删除',
			{ type: 'warning' }
		)

		const selectedIds = selectedUsers.value.map((user) => user.id)
		users.value = users.value.filter(
			(user) => !selectedIds.includes(user.id)
		)
		selectedUsers.value = []

		ElMessage.success('批量删除成功')
	} catch {
		// 用户取消
	}
}

const handleSizeChange = (size: number) => {
	pageSize.value = size
}

const handleCurrentChange = (page: number) => {
	currentPage.value = page
}

// 生命周期
onMounted(() => {
	totalUsers.value = users.value.length
})
</script>

<style scoped lang="scss">
.user-list-container {
	padding: 20px;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;

	.header-left {
		h1 {
			margin: 0 0 5px 0;
			color: #303133;
		}

		p {
			margin: 0;
			color: #909399;
		}
	}
}

.filter-card {
	margin-bottom: 20px;
}

.user-table {
	margin-bottom: 20px;
}

.user-info {
	display: flex;
	align-items: center;
	gap: 12px;

	.user-details {
		.username {
			font-weight: 500;
			color: #303133;
			margin-bottom: 4px;
		}

		.email {
			font-size: 12px;
			color: #909399;
		}
	}
}

.batch-actions {
	margin-bottom: 20px;

	.batch-info {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.batch-buttons {
			display: flex;
			gap: 10px;
		}
	}
}

.empty-state {
	text-align: center;
	padding: 40px;
}

.pagination-container {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}

.danger-item {
	color: #f56c6c;
}

@media (max-width: 768px) {
	.page-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 15px;
	}

	.filter-card .el-row {
		flex-direction: column;
		gap: 10px;
	}

	.batch-info {
		flex-direction: column;
		gap: 15px;
		align-items: flex-start;
	}
}

.user-info {
	display: flex;
	align-items: center;
	gap: 12px;

	.user-details {
		.username {
			font-weight: 500;
			color: var(--el-text-color-primary);
		}

		.fullname {
			font-size: 13px;
			color: var(--el-text-color-regular);
			margin-top: 2px;
		}

		.email {
			font-size: 12px;
			color: var(--el-text-color-secondary);
			margin-top: 2px;
		}
	}
}
</style>
