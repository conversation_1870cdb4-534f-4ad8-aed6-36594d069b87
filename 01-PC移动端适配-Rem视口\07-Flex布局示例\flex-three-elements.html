<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flex布局：一行三元素左中右排列</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 2rem;
        }

        .demo-section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .demo-section h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }

        .demo-container {
            margin: 1rem 0;
            border: 2px dashed #ddd;
            min-height: 60px;
        }

        /* 方法一：space-between */
        .method1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #e8f4fd;
        }

        .method1 .item {
            padding: 0.5rem 1rem;
            background: #3498db;
            color: white;
            border-radius: 4px;
            font-weight: bold;
        }

        /* 方法二：flex: 1 + text-align */
        .method2 {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: #e8f5e8;
        }

        .method2 .left {
            flex: 1;
            text-align: left;
            padding: 0.5rem;
            background: #27ae60;
            color: white;
            border-radius: 4px;
            margin-right: 0.5rem;
        }

        .method2 .center {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            background: #27ae60;
            color: white;
            border-radius: 4px;
            margin: 0 0.25rem;
        }

        .method2 .right {
            flex: 1;
            text-align: right;
            padding: 0.5rem;
            background: #27ae60;
            color: white;
            border-radius: 4px;
            margin-left: 0.5rem;
        }

        /* 方法三：margin auto */
        .method3 {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: #fdf2e9;
        }

        .method3 .item {
            padding: 0.5rem 1rem;
            background: #e67e22;
            color: white;
            border-radius: 4px;
            font-weight: bold;
        }

        .method3 .center {
            margin: 0 auto;
        }

        .method3 .right {
            margin-left: auto;
        }

        /* 方法四：固定宽度 + justify-content */
        .method4 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f4e6ff;
        }

        .method4 .left,
        .method4 .right {
            width: 120px;
            padding: 0.5rem;
            background: #9b59b6;
            color: white;
            border-radius: 4px;
            text-align: center;
        }

        .method4 .center {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            background: #9b59b6;
            color: white;
            border-radius: 4px;
            margin: 0 1rem;
        }

        /* 方法五：网格布局对比 */
        .method5 {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            align-items: center;
            padding: 1rem;
            background: #ffe6e6;
        }

        .method5 .left {
            justify-self: start;
            padding: 0.5rem 1rem;
            background: #e74c3c;
            color: white;
            border-radius: 4px;
        }

        .method5 .center {
            justify-self: center;
            padding: 0.5rem 1rem;
            background: #e74c3c;
            color: white;
            border-radius: 4px;
        }

        .method5 .right {
            justify-self: end;
            padding: 0.5rem 1rem;
            background: #e74c3c;
            color: white;
            border-radius: 4px;
        }

        /* 响应式示例 */
        .responsive-demo {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f0f8ff;
            flex-wrap: wrap;
        }

        .responsive-demo .item {
            padding: 0.5rem 1rem;
            background: #2980b9;
            color: white;
            border-radius: 4px;
            margin: 0.25rem;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .responsive-demo {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .responsive-demo .item {
                width: 100%;
                text-align: center;
            }
        }

        /* 实际应用示例：导航栏 */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background: #2c3e50;
            color: white;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .navbar .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
        }

        .navbar .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .navbar .nav-menu a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }

        .navbar .nav-menu a:hover {
            color: #3498db;
        }

        .navbar .user-actions {
            display: flex;
            gap: 1rem;
        }

        .navbar .btn {
            padding: 0.5rem 1rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }

        .navbar .btn:hover {
            background: #2980b9;
        }

        /* 代码展示 */
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }

        .highlight {
            background: #ffeaa7;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            color: #2d3436;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Flex布局：一行三元素左中右排列</h1>
    <p>以下展示了多种实现一行三个元素左中右排列的方法</p>

    <div class="demo-section">
        <h3>方法一：justify-content: space-between</h3>
        <div class="code-block">
.container {
    display: flex;
    justify-content: <span class="highlight">space-between</span>;
    align-items: center;
}
        </div>
        <div class="demo-container method1">
            <div class="item">左侧</div>
            <div class="item">中间</div>
            <div class="item">右侧</div>
        </div>
        <p><strong>特点：</strong>元素两端对齐，中间自动分配空间。适合元素宽度不固定的情况。</p>
    </div>

    <div class="demo-section">
        <h3>方法二：flex: 1 + text-align</h3>
        <div class="code-block">
.left, .center, .right {
    <span class="highlight">flex: 1</span>;
}
.left { text-align: left; }
.center { text-align: center; }
.right { text-align: right; }
        </div>
        <div class="demo-container method2">
            <div class="left">左侧内容</div>
            <div class="center">中间内容</div>
            <div class="right">右侧内容</div>
        </div>
        <p><strong>特点：</strong>每个元素占据相等空间，内容在各自区域内对齐。</p>
    </div>

    <div class="demo-section">
        <h3>方法三：margin: auto</h3>
        <div class="code-block">
.center { <span class="highlight">margin: 0 auto</span>; }
.right { <span class="highlight">margin-left: auto</span>; }
        </div>
        <div class="demo-container method3">
            <div class="item left">左侧</div>
            <div class="item center">中间</div>
            <div class="item right">右侧</div>
        </div>
        <p><strong>特点：</strong>使用margin自动分配空间，中间元素绝对居中。</p>
    </div>

    <div class="demo-section">
        <h3>方法四：固定宽度 + 弹性中间</h3>
        <div class="code-block">
.left, .right { <span class="highlight">width: 120px</span>; }
.center { <span class="highlight">flex: 1</span>; }
        </div>
        <div class="demo-container method4">
            <div class="left">固定左</div>
            <div class="center">弹性中间区域</div>
            <div class="right">固定右</div>
        </div>
        <p><strong>特点：</strong>左右固定宽度，中间自适应。适合导航栏等场景。</p>
    </div>

    <div class="demo-section">
        <h3>方法五：Grid布局对比</h3>
        <div class="code-block">
.container {
    display: grid;
    <span class="highlight">grid-template-columns: 1fr auto 1fr</span>;
}
        </div>
        <div class="demo-container method5">
            <div class="left">左侧</div>
            <div class="center">中间</div>
            <div class="right">右侧</div>
        </div>
        <p><strong>特点：</strong>使用Grid布局实现，中间自适应，两边平分剩余空间。</p>
    </div>

    <div class="demo-section">
        <h3>响应式示例</h3>
        <div class="code-block">
/* 桌面端 */
.container { justify-content: space-between; }

/* 移动端 */
@media (max-width: 768px) {
    .container { <span class="highlight">flex-direction: column</span>; }
}
        </div>
        <div class="demo-container responsive-demo">
            <div class="item">左侧元素</div>
            <div class="item">中间元素</div>
            <div class="item">右侧元素</div>
        </div>
        <p><strong>特点：</strong>桌面端水平排列，移动端垂直堆叠。</p>
    </div>

    <div class="demo-section">
        <h3>实际应用：导航栏</h3>
        <div class="navbar">
            <div class="logo">Logo</div>
            <nav class="nav-menu">
                <a href="#">首页</a>
                <a href="#">产品</a>
                <a href="#">服务</a>
                <a href="#">关于</a>
            </nav>
            <div class="user-actions">
                <a href="#" class="btn">登录</a>
                <a href="#" class="btn">注册</a>
            </div>
        </div>
        <p><strong>说明：</strong>左侧Logo，中间导航菜单，右侧用户操作按钮。</p>
    </div>

    <div class="demo-section">
        <h3>选择建议</h3>
        <ul>
            <li><strong>space-between：</strong>元素数量固定，宽度不定时使用</li>
            <li><strong>flex: 1：</strong>需要元素等宽分布时使用</li>
            <li><strong>margin: auto：</strong>需要中间元素绝对居中时使用</li>
            <li><strong>固定宽度：</strong>左右宽度固定，中间自适应时使用</li>
            <li><strong>Grid布局：</strong>需要更复杂的二维布局时使用</li>
        </ul>
    </div>

    <script>
        // 动态演示
        console.log('Flex布局演示页面已加载');
        
        // 可以添加交互功能
        document.querySelectorAll('.demo-container').forEach(container => {
            container.addEventListener('click', () => {
                console.log('点击了演示容器:', container.className);
            });
        });
    </script>
</body>
</html>
