# 🚀 Vue GraphQL 项目 - 快速测试卡片

## 启动项目
```bash
# Windows
start.bat

# Linux/macOS
chmod +x start.sh && ./start.sh

# 手动启动
npm run install:all && npm run dev
```

## 🌐 访问地址
| 服务 | 地址 |
|------|------|
| **前端应用** | http://localhost:3000 |
| **GraphQL API** | http://localhost:4000/graphql |
| **API信息** | http://localhost:4000/api/info |
| **健康检查** | http://localhost:4000/health |

## 👥 测试账户
| 角色 | 邮箱 | 密码 |
|------|------|------|
| **管理员** | <EMAIL> | admin123 |
| **项目经理** | <EMAIL> | password123 |
| **开发者** | <EMAIL> | password123 |
| **设计师** | <EMAIL> | password123 |
| **测试员** | <EMAIL> | password123 |

## 🎯 快速测试流程

### 1️⃣ 基础功能测试
- [ ] 访问 http://localhost:3000
- [ ] 使用任意账户登录
- [ ] 查看仪表板数据
- [ ] 切换明暗主题

### 2️⃣ 项目管理测试
- [ ] 点击"项目管理"菜单
- [ ] 查看项目列表
- [ ] 创建新项目
- [ ] 查看项目详情

### 3️⃣ 任务管理测试
- [ ] 点击"任务管理"菜单
- [ ] 查看任务列表
- [ ] 创建新任务
- [ ] 编辑任务状态
- [ ] 添加任务评论

### 4️⃣ 权限测试
- [ ] 使用管理员账户访问"用户管理"
- [ ] 使用普通用户验证权限限制
- [ ] 测试不同角色的功能权限

### 5️⃣ 实时功能测试
- [ ] 打开两个浏览器窗口
- [ ] 分别登录不同账户
- [ ] 在一个窗口创建/更新任务
- [ ] 观察另一个窗口实时更新

### 6️⃣ GraphQL API测试
- [ ] 访问 http://localhost:4000/graphql
- [ ] 执行用户查询
- [ ] 执行登录变更
- [ ] 测试任务创建

## 📱 移动端测试
- [ ] 手机浏览器访问 http://YOUR_IP:3000
- [ ] 测试响应式布局
- [ ] 测试触摸交互

## 🔧 GraphQL 示例查询

**登录获取Token:**
```graphql
mutation {
  login(input: {
    email: "<EMAIL>"
    password: "admin123"
  }) {
    token
    user { username role }
  }
}
```

**获取仪表板数据:**
```graphql
query {
  dashboardStats {
    taskStats { total done }
    projectStats { total active }
    userCount
  }
}
```

**创建任务:**
```graphql
mutation {
  createTask(input: {
    title: "测试任务"
    projectId: "1"
    priority: MEDIUM
  }) {
    id title status
  }
}
```

## ⚠️ 注意事项
- 确保Node.js版本 >= 16
- 端口3000和4000需要空闲
- 首次启动需要安装依赖，请耐心等待
- 数据存储在内存中，重启后会重置

## 🐛 常见问题
- **端口被占用**: 修改配置文件中的端口号
- **无法访问**: 检查防火墙和网络设置
- **登录失败**: 确认邮箱密码正确
- **页面空白**: 检查浏览器控制台错误

---
📚 **详细文档**: 查看 TESTING.md 获取完整测试指南
🔗 **项目地址**: https://github.com/yourusername/vue-graphql-project
