# Git 性能优化

## 📋 概述

随着项目规模增长，Git 仓库可能会遇到性能问题。本文档介绍了 Git 性能优化的各种策略和技巧，帮助提升大型仓库的操作效率。

## 🐌 常见性能问题

### 1. 仓库体积过大

**问题表现**:
- 克隆时间过长
- 推送/拉取缓慢
- 本地操作响应慢
- 存储空间占用大

**原因分析**:
```bash
# 检查仓库大小
du -sh .git
git count-objects -vH

# 查找大文件
git rev-list --objects --all | \
  git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
  awk '/^blob/ {print substr($0,6)}' | \
  sort --numeric-sort --key=2 | \
  tail -20
```

### 2. 历史记录过长

**问题表现**:
- `git log` 操作缓慢
- 分支切换耗时
- 合并操作复杂

**分析工具**:
```bash
# 统计提交数量
git rev-list --all --count

# 查看分支数量
git branch -a | wc -l

# 分析提交频率
git log --oneline --since="1 year ago" | wc -l
```

## 🚀 仓库优化策略

### 1. 仓库清理

**垃圾回收**:
```bash
# 基础垃圾回收
git gc

# 激进垃圾回收
git gc --aggressive --prune=now

# 检查回收效果
git count-objects -vH
```

**清理未跟踪文件**:
```bash
# 查看未跟踪文件
git clean -n

# 清理未跟踪文件
git clean -fd

# 清理忽略的文件
git clean -fX
```

### 2. 大文件处理

**Git LFS (Large File Storage)**:
```bash
# 安装 Git LFS
git lfs install

# 跟踪大文件类型
git lfs track "*.psd"
git lfs track "*.zip"
git lfs track "*.mp4"

# 查看 LFS 文件
git lfs ls-files

# 迁移现有大文件到 LFS
git lfs migrate import --include="*.zip"
```

**BFG Repo-Cleaner 清理历史**:
```bash
# 安装 BFG
# 下载 bfg.jar

# 清理大于 50MB 的文件
java -jar bfg.jar --strip-blobs-bigger-than 50M my-repo.git

# 清理特定文件
java -jar bfg.jar --delete-files "*.zip" my-repo.git

# 清理敏感数据
java -jar bfg.jar --replace-text passwords.txt my-repo.git

# 清理后的垃圾回收
cd my-repo
git reflog expire --expire=now --all
git gc --prune=now --aggressive
```

### 3. 分支管理优化

**清理远程分支**:
```bash
# 查看远程分支状态
git remote prune origin --dry-run

# 清理远程分支引用
git remote prune origin

# 删除已合并的本地分支
git branch --merged main | grep -v main | xargs -n 1 git branch -d

# 批量删除远程分支
git branch -r --merged main | grep -v main | sed 's/origin\///' | xargs -n 1 git push --delete origin
```

**分支策略优化**:
```bash
# 使用 squash 合并减少提交历史
git merge --squash feature-branch

# 使用 rebase 保持线性历史
git rebase main feature-branch
```

## ⚡ 操作性能优化

### 1. 配置优化

**核心配置**:
```bash
# 启用并行处理
git config --global core.preloadindex true
git config --global core.fscache true

# 优化网络传输
git config --global http.postBuffer 524288000
git config --global http.maxRequestBuffer 100M

# 启用增量传输
git config --global transfer.fsckObjects false
git config --global fetch.fsckObjects false
git config --global receive.fsckObjects false

# 优化压缩
git config --global core.compression 9
git config --global pack.compression 9
```

**Windows 特定优化**:
```bash
# 禁用文件系统监控
git config --global core.fsmonitor false

# 优化符号链接处理
git config --global core.symlinks false

# 启用长路径支持
git config --global core.longpaths true
```

### 2. 克隆优化

**浅克隆**:
```bash
# 浅克隆（只获取最新提交）
git clone --depth 1 https://github.com/user/repo.git

# 指定深度的浅克隆
git clone --depth 50 https://github.com/user/repo.git

# 单分支克隆
git clone --single-branch --branch main https://github.com/user/repo.git

# 部分克隆（Git 2.19+）
git clone --filter=blob:none https://github.com/user/repo.git
```

**增量克隆**:
```bash
# 先浅克隆
git clone --depth 1 https://github.com/user/repo.git
cd repo

# 后续获取更多历史
git fetch --unshallow

# 获取特定分支
git fetch origin other-branch:other-branch
```

### 3. 网络优化

**并行传输**:
```bash
# 启用并行推送
git config --global push.default simple
git config --global push.followTags true

# 优化 fetch 性能
git config --global fetch.parallel 4
git config --global submodule.fetchJobs 4
```

**缓存优化**:
```bash
# 启用凭据缓存
git config --global credential.helper cache
git config --global credential.helper 'cache --timeout=3600'

# 启用 HTTP 缓存
git config --global http.saveCookies true
```

## 🔧 高级优化技巧

### 1. 子模块优化

**子模块性能配置**:
```bash
# 并行克隆子模块
git clone --recurse-submodules --jobs 4 https://github.com/user/repo.git

# 浅克隆子模块
git config --global submodule.fetchJobs 4
git config --global submodule.recurse true

# 子模块浅更新
git submodule update --init --recursive --depth 1
```

### 2. 工作树优化

**多工作树管理**:
```bash
# 创建工作树而不是克隆
git worktree add ../feature-branch feature-branch

# 列出工作树
git worktree list

# 清理工作树
git worktree prune
```

### 3. 索引优化

**索引性能配置**:
```bash
# 启用索引预加载
git config --global core.preloadindex true

# 优化索引版本
git config --global index.version 4

# 启用文件系统监控（如果支持）
git config --global core.fsmonitor true
```

## 📊 性能监控

### 1. 性能测量

**基准测试脚本**:
```bash
#!/bin/bash
# git-performance-test.sh

echo "=== Git 性能测试 ==="
echo "仓库: $(pwd)"
echo "时间: $(date)"
echo

# 测试状态检查
echo "测试 git status..."
time git status > /dev/null

# 测试日志查看
echo "测试 git log..."
time git log --oneline -n 100 > /dev/null

# 测试分支切换
echo "测试分支切换..."
current_branch=$(git branch --show-current)
time git checkout HEAD~1 > /dev/null 2>&1
time git checkout $current_branch > /dev/null 2>&1

# 测试文件搜索
echo "测试文件搜索..."
time git grep -n "function" > /dev/null 2>&1

echo "测试完成"
```

### 2. 仓库分析

**仓库统计脚本**:
```bash
#!/bin/bash
# repo-stats.sh

echo "=== 仓库统计信息 ==="

# 基本信息
echo "仓库大小: $(du -sh .git | cut -f1)"
echo "对象数量: $(git count-objects | grep 'count' | cut -d' ' -f2)"
echo "包文件数: $(git count-objects | grep 'packs' | cut -d' ' -f2)"

# 提交统计
echo "总提交数: $(git rev-list --all --count)"
echo "分支数量: $(git branch -a | wc -l)"
echo "标签数量: $(git tag | wc -l)"

# 文件统计
echo "跟踪文件数: $(git ls-files | wc -l)"
echo "最大文件: $(git ls-files | xargs ls -la | sort -k5 -nr | head -1 | awk '{print $9 " (" $5 " bytes)"}')"

# 贡献者统计
echo "贡献者数量: $(git shortlog -sn | wc -l)"
echo "最活跃贡献者: $(git shortlog -sn | head -1)"
```

## 🎯 最佳实践

### 1. 预防性措施

**仓库设计原则**:
- 避免提交大文件到 Git
- 使用 .gitignore 排除不必要文件
- 定期清理合并的分支
- 使用 Git LFS 管理大文件

**提交策略**:
```bash
# 小而频繁的提交
git add -p  # 部分提交
git commit -m "feat: 实现基础功能"

# 避免大量文件的单次提交
find . -name "*.log" -delete  # 清理前提交
git add .
git commit -m "chore: 清理日志文件"
```

### 2. 维护计划

**定期维护任务**:
```bash
#!/bin/bash
# weekly-maintenance.sh

echo "开始每周维护..."

# 垃圾回收
git gc --auto

# 清理远程分支
git remote prune origin

# 更新索引
git update-index --refresh

# 检查仓库完整性
git fsck --full

echo "维护完成"
```

### 3. 监控指标

**关键性能指标**:
- 仓库大小增长率
- 克隆时间
- 常用操作响应时间
- 网络传输效率

**告警阈值**:
```bash
# 仓库大小告警
REPO_SIZE=$(du -s .git | cut -f1)
if [ $REPO_SIZE -gt 1000000 ]; then  # 1GB
    echo "警告: 仓库大小超过 1GB"
fi

# 对象数量告警
OBJECT_COUNT=$(git count-objects | grep 'count' | cut -d' ' -f2)
if [ $OBJECT_COUNT -gt 100000 ]; then
    echo "警告: 对象数量过多"
fi
```

## 🔍 故障排查

### 1. 性能问题诊断

**诊断步骤**:
```bash
# 1. 检查仓库状态
git status
git fsck

# 2. 分析仓库大小
git count-objects -vH
du -sh .git/objects/

# 3. 查找性能瓶颈
git log --oneline --since="1 month ago" | wc -l
git branch -a | wc -l

# 4. 网络诊断
git config --get-regexp http
ping github.com
```

### 2. 常见问题解决

**索引损坏**:
```bash
# 重建索引
rm .git/index
git reset
```

**包文件损坏**:
```bash
# 重新打包
git repack -ad
git gc --aggressive
```

---

通过系统性的性能优化，可以显著提升 Git 仓库的操作效率，改善开发体验。定期维护和监控是保持良好性能的关键。
