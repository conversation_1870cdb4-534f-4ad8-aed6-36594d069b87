// 枚举类型定义
export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  DEVELOPER = 'DEVELOPER',
  DESIGNER = 'DESIGNER',
  TESTER = 'TESTER'
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

export enum ProjectStatus {
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  ON_HOLD = 'ON_HOLD',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  IN_REVIEW = 'IN_REVIEW',
  TESTING = 'TESTING',
  DONE = 'DONE',
  CANCELLED = 'CANCELLED'
}

export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 基础接口
export interface BaseModel {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// 用户模型
export interface User extends BaseModel {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
}

// 项目模型
export interface Project extends BaseModel {
  name: string;
  description?: string;
  status: ProjectStatus;
  priority: Priority;
  startDate?: Date;
  endDate?: Date;
  ownerId: string;
  memberIds: string[];
}

// 任务模型
export interface Task extends BaseModel {
  title: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  dueDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  projectId: string;
  creatorId: string;
  assigneeId?: string;
  tags: string[];
}

// 评论模型
export interface Comment extends BaseModel {
  content: string;
  authorId: string;
  taskId: string;
}

// 输入类型
export interface CreateUserInput {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
}

export interface UpdateUserInput {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role?: UserRole;
  status?: UserStatus;
}

export interface CreateProjectInput {
  name: string;
  description?: string;
  priority?: Priority;
  startDate?: Date;
  endDate?: Date;
  memberIds?: string[];
}

export interface UpdateProjectInput {
  name?: string;
  description?: string;
  status?: ProjectStatus;
  priority?: Priority;
  startDate?: Date;
  endDate?: Date;
  memberIds?: string[];
}

export interface CreateTaskInput {
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  priority?: Priority;
  dueDate?: Date;
  estimatedHours?: number;
  tags?: string[];
}

export interface UpdateTaskInput {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: Priority;
  assigneeId?: string;
  dueDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
}

export interface CreateCommentInput {
  content: string;
  taskId: string;
}

// 过滤器类型
export interface TaskFilter {
  status?: TaskStatus;
  priority?: Priority;
  assigneeId?: string;
  projectId?: string;
  search?: string;
}

export interface ProjectFilter {
  status?: ProjectStatus;
  priority?: Priority;
  ownerId?: string;
  search?: string;
}

// 分页类型
export interface PaginationInput {
  page?: number;
  limit?: number;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 连接类型
export interface TaskConnection {
  tasks: Task[];
  pagination: PaginationInfo;
}

export interface ProjectConnection {
  projects: Project[];
  pagination: PaginationInfo;
}

export interface UserConnection {
  users: User[];
  pagination: PaginationInfo;
}

// 统计类型
export interface TaskStats {
  total: number;
  todo: number;
  inProgress: number;
  inReview: number;
  testing: number;
  done: number;
  cancelled: number;
}

export interface ProjectStats {
  total: number;
  planning: number;
  active: number;
  onHold: number;
  completed: number;
  cancelled: number;
}

export interface DashboardStats {
  taskStats: TaskStats;
  projectStats: ProjectStats;
  userCount: number;
  recentTasks: Task[];
  recentProjects: Project[];
}

// 认证类型
export interface AuthPayload {
  token: string;
  user: User;
}

export interface LoginInput {
  email: string;
  password: string;
}

// GraphQL上下文类型
export interface Context {
  user?: User;
  token?: string;
}
