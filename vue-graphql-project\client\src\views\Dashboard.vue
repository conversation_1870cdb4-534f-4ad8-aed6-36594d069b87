<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p>欢迎回来，{{ authStore.fullName }}！</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--primary">
            <el-icon><List /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats?.taskStats.total || 0 }}</div>
            <div class="stat-label">总任务数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--success">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats?.taskStats.done || 0 }}</div>
            <div class="stat-label">已完成任务</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--warning">
            <el-icon><FolderOpened /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats?.projectStats.total || 0 }}</div>
            <div class="stat-label">总项目数</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--info">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats?.userCount || 0 }}</div>
            <div class="stat-label">团队成员</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表和列表 -->
    <div class="dashboard-content">
      <div class="dashboard-left">
        <!-- 任务状态分布 -->
        <el-card class="chart-card">
          <template #header>
            <h3>任务状态分布</h3>
          </template>
          <div class="task-stats">
            <div class="task-stat-item">
              <div class="task-stat-bar">
                <div 
                  class="task-stat-fill task-stat-fill--todo"
                  :style="{ width: getTaskPercentage('todo') + '%' }"
                ></div>
              </div>
              <div class="task-stat-info">
                <span class="task-stat-label">待办</span>
                <span class="task-stat-count">{{ stats?.taskStats.todo || 0 }}</span>
              </div>
            </div>

            <div class="task-stat-item">
              <div class="task-stat-bar">
                <div 
                  class="task-stat-fill task-stat-fill--progress"
                  :style="{ width: getTaskPercentage('inProgress') + '%' }"
                ></div>
              </div>
              <div class="task-stat-info">
                <span class="task-stat-label">进行中</span>
                <span class="task-stat-count">{{ stats?.taskStats.inProgress || 0 }}</span>
              </div>
            </div>

            <div class="task-stat-item">
              <div class="task-stat-bar">
                <div 
                  class="task-stat-fill task-stat-fill--review"
                  :style="{ width: getTaskPercentage('inReview') + '%' }"
                ></div>
              </div>
              <div class="task-stat-info">
                <span class="task-stat-label">待审核</span>
                <span class="task-stat-count">{{ stats?.taskStats.inReview || 0 }}</span>
              </div>
            </div>

            <div class="task-stat-item">
              <div class="task-stat-bar">
                <div 
                  class="task-stat-fill task-stat-fill--done"
                  :style="{ width: getTaskPercentage('done') + '%' }"
                ></div>
              </div>
              <div class="task-stat-info">
                <span class="task-stat-label">已完成</span>
                <span class="task-stat-count">{{ stats?.taskStats.done || 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 项目状态 -->
        <el-card class="chart-card">
          <template #header>
            <h3>项目状态</h3>
          </template>
          <div class="project-stats">
            <div class="project-stat-item">
              <el-tag type="info">规划中</el-tag>
              <span>{{ stats?.projectStats.planning || 0 }}</span>
            </div>
            <div class="project-stat-item">
              <el-tag type="success">进行中</el-tag>
              <span>{{ stats?.projectStats.active || 0 }}</span>
            </div>
            <div class="project-stat-item">
              <el-tag type="warning">暂停</el-tag>
              <span>{{ stats?.projectStats.onHold || 0 }}</span>
            </div>
            <div class="project-stat-item">
              <el-tag>已完成</el-tag>
              <span>{{ stats?.projectStats.completed || 0 }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <div class="dashboard-right">
        <!-- 最近任务 -->
        <el-card class="list-card">
          <template #header>
            <div class="card-header">
              <h3>最近任务</h3>
              <router-link to="/tasks">
                <el-button type="text">查看全部</el-button>
              </router-link>
            </div>
          </template>
          <div class="task-list">
            <div
              v-for="task in stats?.recentTasks"
              :key="task.id"
              class="task-item"
              @click="$router.push(`/tasks/${task.id}`)"
            >
              <div class="task-info">
                <div class="task-title">{{ task.title }}</div>
                <div class="task-meta">
                  <el-tag :type="getTaskStatusType(task.status)" size="small">
                    {{ getTaskStatusText(task.status) }}
                  </el-tag>
                  <span class="task-project">{{ task.project.name }}</span>
                </div>
              </div>
              <div class="task-assignee">
                <el-avatar :size="24" :src="task.assignee?.avatar">
                  {{ task.assignee?.firstName?.[0] }}{{ task.assignee?.lastName?.[0] }}
                </el-avatar>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 最近项目 -->
        <el-card class="list-card">
          <template #header>
            <div class="card-header">
              <h3>最近项目</h3>
              <router-link to="/projects">
                <el-button type="text">查看全部</el-button>
              </router-link>
            </div>
          </template>
          <div class="project-list">
            <div
              v-for="project in stats?.recentProjects"
              :key="project.id"
              class="project-item"
              @click="$router.push(`/projects/${project.id}`)"
            >
              <div class="project-info">
                <div class="project-title">{{ project.name }}</div>
                <div class="project-meta">
                  <el-tag :type="getProjectStatusType(project.status)" size="small">
                    {{ getProjectStatusText(project.status) }}
                  </el-tag>
                  <span class="project-owner">{{ project.owner.firstName }} {{ project.owner.lastName }}</span>
                </div>
              </div>
              <div class="project-priority">
                <el-tag :type="getPriorityType(project.priority)" size="small">
                  {{ getPriorityText(project.priority) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useQuery } from '@vue/apollo-composable'
import { GET_DASHBOARD_STATS_QUERY } from '@/graphql/dashboard'
import { useAuthStore } from '@/stores/auth'
import type { DashboardStats } from '@/types'

const authStore = useAuthStore()

// 获取仪表板统计数据
const { result, loading, error } = useQuery(GET_DASHBOARD_STATS_QUERY, {}, {
  fetchPolicy: 'cache-and-network',
  pollInterval: 30000 // 30秒轮询一次
})

const stats = computed<DashboardStats | null>(() => result.value?.dashboardStats || null)

// 计算任务百分比
const getTaskPercentage = (type: string) => {
  if (!stats.value) return 0
  const total = stats.value.taskStats.total
  if (total === 0) return 0
  
  const count = stats.value.taskStats[type as keyof typeof stats.value.taskStats] as number
  return Math.round((count / total) * 100)
}

// 获取任务状态类型
const getTaskStatusType = (status: string) => {
  const types: Record<string, string> = {
    TODO: 'info',
    IN_PROGRESS: 'warning',
    IN_REVIEW: 'primary',
    TESTING: 'warning',
    DONE: 'success',
    CANCELLED: 'danger'
  }
  return types[status] || 'info'
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  const texts: Record<string, string> = {
    TODO: '待办',
    IN_PROGRESS: '进行中',
    IN_REVIEW: '待审核',
    TESTING: '测试中',
    DONE: '已完成',
    CANCELLED: '已取消'
  }
  return texts[status] || status
}

// 获取项目状态类型
const getProjectStatusType = (status: string) => {
  const types: Record<string, string> = {
    PLANNING: 'info',
    ACTIVE: 'success',
    ON_HOLD: 'warning',
    COMPLETED: 'primary',
    CANCELLED: 'danger'
  }
  return types[status] || 'info'
}

// 获取项目状态文本
const getProjectStatusText = (status: string) => {
  const texts: Record<string, string> = {
    PLANNING: '规划中',
    ACTIVE: '进行中',
    ON_HOLD: '暂停',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return texts[status] || status
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    LOW: 'success',
    MEDIUM: 'warning',
    HIGH: 'danger',
    URGENT: 'danger'
  }
  return types[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    LOW: '低',
    MEDIUM: '中',
    HIGH: '高',
    URGENT: '紧急'
  }
  return texts[priority] || priority
}
</script>

<style lang="scss" scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 24px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px;
    color: var(--el-text-color-primary);
  }

  p {
    color: var(--el-text-color-regular);
    margin: 0;
    font-size: 16px;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;

    &--primary {
      background: var(--el-color-primary);
    }

    &--success {
      background: var(--el-color-success);
    }

    &--warning {
      background: var(--el-color-warning);
    }

    &--info {
      background: var(--el-color-info);
    }
  }

  .stat-info {
    flex: 1;
  }

  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    line-height: 1;
    margin-bottom: 4px;
  }

  .stat-label {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-card,
.list-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  :deep(.el-card__header) {
    padding: 20px 20px 0;
    border: none;

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-stat-bar {
  flex: 1;
  height: 8px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  overflow: hidden;
}

.task-stat-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;

  &--todo {
    background: var(--el-color-info);
  }

  &--progress {
    background: var(--el-color-warning);
  }

  &--review {
    background: var(--el-color-primary);
  }

  &--done {
    background: var(--el-color-success);
  }
}

.task-stat-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.task-stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.task-stat-count {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.project-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;

  span {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.task-list,
.project-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item,
.project-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  background: var(--el-fill-color-lighter);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: var(--el-fill-color-light);
    transform: translateY(-1px);
  }
}

.task-info,
.project-info {
  flex: 1;
}

.task-title,
.project-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  font-size: 14px;
}

.task-meta,
.project-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

// 响应式设计
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .dashboard-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stat-card {
    .stat-content {
      gap: 12px;
    }

    .stat-icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
    }

    .stat-number {
      font-size: 20px;
    }
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-header {
    h1 {
      font-size: 24px;
    }

    p {
      font-size: 14px;
    }
  }
}
</style>
