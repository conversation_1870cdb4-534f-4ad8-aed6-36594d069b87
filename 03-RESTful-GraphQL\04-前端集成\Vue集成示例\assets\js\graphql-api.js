/**
 * GraphQL API 服务类
 * 提供GraphQL查询解析和执行功能
 */
class GraphQLAPI {
    constructor() {
        this.mockData = window.mockData || {};
        this.schema = this.buildSchema();
        this.resolvers = this.buildResolvers();
    }

    /**
     * 构建GraphQL Schema
     */
    buildSchema() {
        return `
            type User {
                id: ID!
                name: String!
                email: String!
                role: String!
                bio: String
                avatar: String
                status: String!
                created_at: String!
                updated_at: String!
            }

            type Task {
                id: ID!
                title: String!
                description: String
                priority: String!
                status: String!
                dueDate: String
                assignee: String
                tags: [String!]!
                createdAt: String!
                updatedAt: String!
            }

            type Query {
                users: [User!]!
                user(id: ID!): User
                tasks: [Task!]!
                task(id: ID!): Task
                tasksByStatus(status: String!): [Task!]!
                tasksByPriority(priority: String!): [Task!]!
            }

            type Mutation {
                createUser(name: String!, email: String!, role: String!): User!
                updateUser(id: ID!, name: String, email: String, role: String): User!
                deleteUser(id: ID!): Boolean!
                
                createTask(title: String!, description: String, priority: String!, assignee: String): Task!
                updateTask(id: ID!, title: String, description: String, priority: String, status: String): Task!
                deleteTask(id: ID!): Boolean!
            }

            type Subscription {
                taskAdded: Task!
                taskUpdated: Task!
                taskDeleted: ID!
            }
        `;
    }

    /**
     * 构建解析器
     */
    buildResolvers() {
        return {
            Query: {
                users: () => this.mockData.users || [],
                user: (_, { id }) => this.mockData.users?.find(u => u.id == id),
                tasks: () => this.mockData.tasks || [],
                task: (_, { id }) => this.mockData.tasks?.find(t => t.id == id),
                tasksByStatus: (_, { status }) => 
                    this.mockData.tasks?.filter(t => t.status === status) || [],
                tasksByPriority: (_, { priority }) => 
                    this.mockData.tasks?.filter(t => t.priority === priority) || []
            },
            Mutation: {
                createUser: (_, { name, email, role }) => {
                    const newUser = {
                        id: Date.now(),
                        name,
                        email,
                        role,
                        bio: '',
                        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`,
                        status: 'active',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };
                    this.mockData.users = this.mockData.users || [];
                    this.mockData.users.push(newUser);
                    return newUser;
                },
                updateUser: (_, { id, ...updates }) => {
                    const userIndex = this.mockData.users?.findIndex(u => u.id == id);
                    if (userIndex >= 0) {
                        this.mockData.users[userIndex] = {
                            ...this.mockData.users[userIndex],
                            ...updates,
                            updated_at: new Date().toISOString()
                        };
                        return this.mockData.users[userIndex];
                    }
                    throw new Error('User not found');
                },
                deleteUser: (_, { id }) => {
                    const userIndex = this.mockData.users?.findIndex(u => u.id == id);
                    if (userIndex >= 0) {
                        this.mockData.users.splice(userIndex, 1);
                        return true;
                    }
                    return false;
                },
                createTask: (_, { title, description, priority, assignee }) => {
                    const newTask = {
                        id: Date.now(),
                        title,
                        description: description || '',
                        priority,
                        status: 'todo',
                        dueDate: null,
                        assignee: assignee || '',
                        tags: [],
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };
                    this.mockData.tasks = this.mockData.tasks || [];
                    this.mockData.tasks.push(newTask);
                    this.notifySubscribers('taskAdded', newTask);
                    return newTask;
                },
                updateTask: (_, { id, ...updates }) => {
                    const taskIndex = this.mockData.tasks?.findIndex(t => t.id == id);
                    if (taskIndex >= 0) {
                        this.mockData.tasks[taskIndex] = {
                            ...this.mockData.tasks[taskIndex],
                            ...updates,
                            updatedAt: new Date().toISOString()
                        };
                        this.notifySubscribers('taskUpdated', this.mockData.tasks[taskIndex]);
                        return this.mockData.tasks[taskIndex];
                    }
                    throw new Error('Task not found');
                },
                deleteTask: (_, { id }) => {
                    const taskIndex = this.mockData.tasks?.findIndex(t => t.id == id);
                    if (taskIndex >= 0) {
                        this.mockData.tasks.splice(taskIndex, 1);
                        this.notifySubscribers('taskDeleted', id);
                        return true;
                    }
                    return false;
                }
            }
        };
    }

    /**
     * 执行GraphQL查询
     */
    async execute(query, variables = {}) {
        try {
            const startTime = performance.now();
            
            // 简单的查询解析和执行
            const result = await this.parseAndExecute(query, variables);
            
            const endTime = performance.now();
            const executionTime = Math.round(endTime - startTime);

            return {
                data: result,
                extensions: {
                    executionTime: `${executionTime}ms`
                }
            };
        } catch (error) {
            return {
                errors: [{
                    message: error.message,
                    locations: [{ line: 1, column: 1 }]
                }]
            };
        }
    }

    /**
     * 解析和执行查询
     */
    async parseAndExecute(query, variables) {
        // 简化的GraphQL解析器
        const trimmedQuery = query.trim();
        
        if (trimmedQuery.startsWith('query') || trimmedQuery.startsWith('{')) {
            return this.executeQuery(trimmedQuery, variables);
        } else if (trimmedQuery.startsWith('mutation')) {
            return this.executeMutation(trimmedQuery, variables);
        } else {
            throw new Error('Unsupported operation type');
        }
    }

    /**
     * 执行查询操作
     */
    executeQuery(query, variables) {
        // 简单的查询匹配
        if (query.includes('users')) {
            if (query.includes('user(id:')) {
                const idMatch = query.match(/user\(id:\s*"?(\d+)"?\)/);
                if (idMatch) {
                    const id = idMatch[1];
                    return { user: this.resolvers.Query.user(null, { id }) };
                }
            }
            return { users: this.resolvers.Query.users() };
        }
        
        if (query.includes('tasks')) {
            if (query.includes('tasksByStatus')) {
                const statusMatch = query.match(/tasksByStatus\(status:\s*"([^"]+)"\)/);
                if (statusMatch) {
                    const status = statusMatch[1];
                    return { tasksByStatus: this.resolvers.Query.tasksByStatus(null, { status }) };
                }
            }
            if (query.includes('tasksByPriority')) {
                const priorityMatch = query.match(/tasksByPriority\(priority:\s*"([^"]+)"\)/);
                if (priorityMatch) {
                    const priority = priorityMatch[1];
                    return { tasksByPriority: this.resolvers.Query.tasksByPriority(null, { priority }) };
                }
            }
            return { tasks: this.resolvers.Query.tasks() };
        }
        
        throw new Error('Query not supported');
    }

    /**
     * 执行变更操作
     */
    executeMutation(mutation, variables) {
        if (mutation.includes('createTask')) {
            const args = this.extractMutationArgs(mutation, variables);
            return { createTask: this.resolvers.Mutation.createTask(null, args) };
        }
        
        if (mutation.includes('updateTask')) {
            const args = this.extractMutationArgs(mutation, variables);
            return { updateTask: this.resolvers.Mutation.updateTask(null, args) };
        }
        
        if (mutation.includes('deleteTask')) {
            const args = this.extractMutationArgs(mutation, variables);
            return { deleteTask: this.resolvers.Mutation.deleteTask(null, args) };
        }
        
        throw new Error('Mutation not supported');
    }

    /**
     * 提取变更参数
     */
    extractMutationArgs(mutation, variables) {
        // 简化的参数提取
        const args = {};
        
        // 从variables中提取参数
        Object.keys(variables).forEach(key => {
            args[key] = variables[key];
        });
        
        return args;
    }

    /**
     * 订阅通知
     */
    notifySubscribers(event, data) {
        if (this.subscribers && this.subscribers[event]) {
            this.subscribers[event].forEach(callback => callback(data));
        }
    }

    /**
     * 订阅事件
     */
    subscribe(event, callback) {
        this.subscribers = this.subscribers || {};
        this.subscribers[event] = this.subscribers[event] || [];
        this.subscribers[event].push(callback);
        
        return () => {
            const index = this.subscribers[event].indexOf(callback);
            if (index > -1) {
                this.subscribers[event].splice(index, 1);
            }
        };
    }
}

// 导出GraphQL API实例
window.GraphQLAPI = GraphQLAPI;
