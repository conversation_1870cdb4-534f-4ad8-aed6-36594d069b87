# Git 工作树详解

## 🌳 工作树概念

### 📋 什么是 Git 工作树？
Git 工作树（Worktree）允许你从同一个仓库创建多个工作目录，每个工作目录可以检出不同的分支，实现并行开发。

```bash
# 工作树的优势：
├── 并行开发 - 同时在多个分支上工作
├── 快速切换 - 无需 stash 或 commit 就能切换分支
├── 独立环境 - 每个工作树有独立的工作目录
├── 共享历史 - 所有工作树共享同一个 .git 目录
└── 节省空间 - 比多个克隆更节省磁盘空间
```

### 🎯 工作树 vs 传统方法
```bash
# 传统方法的问题：
├── 分支切换需要 stash 或 commit
├── 无法同时在多个分支工作
├── 频繁切换影响 IDE 索引
└── 构建产物需要重新生成

# 工作树的解决方案：
├── 每个分支独立的工作目录
├── 可以同时运行多个开发环境
├── IDE 可以同时打开多个项目
└── 每个工作树独立的构建环境
```

## 🔧 创建工作树

### 📦 基本创建操作

#### 创建新工作树
```bash
# 为现有分支创建工作树
git worktree add ../project-feature feature-branch

# 创建新分支并建立工作树
git worktree add -b new-feature ../project-new-feature

# 基于远程分支创建工作树
git worktree add ../project-hotfix origin/hotfix-branch

# 创建临时工作树
git worktree add --detach ../project-temp commit-hash
```

#### 指定工作树位置
```bash
# 在当前目录的兄弟目录创建
git worktree add ../project-develop develop

# 在指定路径创建
git worktree add /tmp/project-test test-branch

# 在子目录创建
git worktree add worktrees/feature-a feature-a
```

### 🔍 查看工作树状态
```bash
# 列出所有工作树
git worktree list

# 详细信息
git worktree list --porcelain

# 示例输出：
# worktree /path/to/main-project
# HEAD 1a2b3c4d5e6f7890abcdef1234567890abcdef12
# branch refs/heads/main
# 
# worktree /path/to/project-feature
# HEAD 2b3c4d5e6f7890abcdef1234567890abcdef123
# branch refs/heads/feature-branch
```

## 🔄 管理工作树

### 🚀 工作树操作

#### 在工作树间切换
```bash
# 进入不同的工作树目录
cd ../project-feature
git status  # 显示 feature-branch 的状态

cd ../project-develop  
git status  # 显示 develop 分支的状态

# 在工作树中正常使用 Git 命令
git add .
git commit -m "Feature development"
git push origin feature-branch
```

#### 同步工作树
```bash
# 在主工作树中获取更新
cd /path/to/main-project
git fetch origin

# 其他工作树自动看到更新
cd ../project-feature
git log origin/main  # 可以看到最新的提交
```

### 🔄 移动和重命名工作树
```bash
# 移动工作树到新位置
git worktree move ../project-feature ../new-location/project-feature

# 重命名工作树目录
mv ../project-feature ../project-feature-v2
git worktree move ../project-feature-v2 ../project-feature-renamed
```

## 🗑️ 删除工作树

### ❌ 删除操作

#### 安全删除
```bash
# 删除工作树（保留分支）
git worktree remove ../project-feature

# 强制删除（即使有未提交的更改）
git worktree remove --force ../project-feature

# 删除工作树目录后清理引用
rm -rf ../project-feature
git worktree prune
```

#### 批量清理
```bash
# 清理所有无效的工作树引用
git worktree prune

# 清理时显示详细信息
git worktree prune --verbose

# 试运行（不实际删除）
git worktree prune --dry-run
```

## 🎯 工作树应用场景

### 🚀 并行开发

#### 场景1：功能开发 + 紧急修复
```bash
# 主工作树：日常开发
cd /path/to/main-project  # main 分支
# 正在开发新功能...

# 紧急修复：创建 hotfix 工作树
git worktree add ../project-hotfix -b hotfix/critical-bug main

# 在 hotfix 工作树中修复问题
cd ../project-hotfix
# 修复代码...
git add .
git commit -m "fix: resolve critical security issue"
git push origin hotfix/critical-bug

# 回到主工作树继续开发
cd /path/to/main-project
# 继续功能开发...
```

#### 场景2：多版本维护
```bash
# 主工作树：最新版本开发
git worktree add ../project-v2 main

# 维护旧版本
git worktree add ../project-v1 release/v1.x

# 维护更老的版本
git worktree add ../project-legacy release/legacy

# 在不同版本间独立工作
cd ../project-v1
# 修复 v1.x 的 bug...

cd ../project-v2  
# 开发 v2.0 新功能...
```

### 🔬 测试和验证

#### 场景3：测试不同分支
```bash
# 创建测试工作树
git worktree add ../test-feature-a feature-a
git worktree add ../test-feature-b feature-b

# 在不同工作树中运行测试
cd ../test-feature-a
npm test

cd ../test-feature-b
npm test

# 比较测试结果
cd ../test-feature-a
npm run benchmark > ../results-a.txt

cd ../test-feature-b  
npm run benchmark > ../results-b.txt

diff ../results-a.txt ../results-b.txt
```

#### 场景4：代码审查
```bash
# 为 PR 创建专门的工作树
git fetch origin pull/123/head:pr-123
git worktree add ../review-pr-123 pr-123

# 在审查工作树中检查代码
cd ../review-pr-123
# 运行测试、检查代码质量...
npm test
npm run lint
npm run build

# 审查完成后删除
cd /path/to/main-project
git worktree remove ../review-pr-123
git branch -d pr-123
```

## 🔧 高级工作树技巧

### 🎨 工作树配置

#### 配置工作树行为
```bash
# 配置工作树默认位置
git config worktree.guessRemote true

# 配置清理行为
git config gc.worktreePruneExpire "3.months.ago"

# 查看工作树相关配置
git config --get-regexp worktree
```

#### 工作树模板
```bash
# 创建工作树模板脚本
#!/bin/bash
# create_worktree.sh

BRANCH_NAME=$1
WORKTREE_PATH="../project-$BRANCH_NAME"

if [ -z "$BRANCH_NAME" ]; then
    echo "Usage: $0 <branch-name>"
    exit 1
fi

# 创建工作树
git worktree add -b "$BRANCH_NAME" "$WORKTREE_PATH"

# 进入工作树目录
cd "$WORKTREE_PATH"

# 初始化开发环境
npm install
cp ../main-project/.env.example .env

echo "Worktree created at $WORKTREE_PATH"
echo "Branch: $BRANCH_NAME"
```

### 🔄 工作树同步

#### 同步脚本
```bash
#!/bin/bash
# sync_worktrees.sh

echo "Syncing all worktrees..."

# 获取所有工作树路径
git worktree list --porcelain | grep "worktree " | cut -d' ' -f2 | while read worktree_path; do
    echo "Syncing $worktree_path"
    
    cd "$worktree_path"
    
    # 获取当前分支
    current_branch=$(git branch --show-current)
    
    if [ -n "$current_branch" ]; then
        # 如果有对应的远程分支，则拉取更新
        if git show-ref --verify --quiet "refs/remotes/origin/$current_branch"; then
            echo "  Pulling updates for $current_branch"
            git pull origin "$current_branch"
        else
            echo "  No remote branch for $current_branch"
        fi
    else
        echo "  Detached HEAD, skipping pull"
    fi
    
    echo "  Status:"
    git status --short
    echo ""
done

echo "Sync completed!"
```

### 📊 工作树监控

#### 状态检查脚本
```bash
#!/bin/bash
# check_worktrees.sh

echo "=== Git Worktree Status ==="
echo ""

git worktree list --porcelain | while IFS= read -r line; do
    if [[ $line == worktree* ]]; then
        worktree_path=$(echo "$line" | cut -d' ' -f2)
        echo "📁 $worktree_path"
    elif [[ $line == "branch "* ]]; then
        branch=$(echo "$line" | cut -d' ' -f2 | sed 's|refs/heads/||')
        echo "🌿 Branch: $branch"
        
        # 检查工作树状态
        cd "$worktree_path"
        
        # 检查是否有未提交的更改
        if [ -n "$(git status --porcelain)" ]; then
            echo "⚠️  Uncommitted changes:"
            git status --short | sed 's/^/   /'
        else
            echo "✅ Clean working directory"
        fi
        
        # 检查是否领先/落后远程分支
        if git show-ref --verify --quiet "refs/remotes/origin/$branch"; then
            ahead=$(git rev-list --count origin/$branch..HEAD)
            behind=$(git rev-list --count HEAD..origin/$branch)
            
            if [ "$ahead" -gt 0 ]; then
                echo "⬆️  $ahead commits ahead of origin/$branch"
            fi
            
            if [ "$behind" -gt 0 ]; then
                echo "⬇️  $behind commits behind origin/$branch"
            fi
            
            if [ "$ahead" -eq 0 ] && [ "$behind" -eq 0 ]; then
                echo "🔄 Up to date with origin/$branch"
            fi
        fi
        
        echo ""
    fi
done
```

## 🚨 常见问题和解决方案

### 问题1：工作树创建失败
```bash
# 错误：fatal: 'branch-name' is already checked out at '/path'
# 原因：分支已经在其他工作树中检出
# 解决方案：
git worktree list  # 查看哪个工作树在使用该分支
git worktree remove /path/to/existing/worktree  # 删除冲突的工作树
```

### 问题2：工作树目录被意外删除
```bash
# 目录被删除但 Git 仍认为工作树存在
# 解决方案：
git worktree prune  # 清理无效的工作树引用
```

### 问题3：工作树分支同步问题
```bash
# 工作树中的分支与远程不同步
# 解决方案：
cd /path/to/worktree
git fetch origin
git reset --hard origin/branch-name  # 强制同步（丢失本地更改）
# 或者
git pull origin branch-name  # 合并远程更改
```

### 问题4：工作树权限问题
```bash
# 工作树目录权限不正确
# 解决方案：
chmod -R 755 /path/to/worktree
chown -R user:group /path/to/worktree
```

## 💡 最佳实践

### ✅ 工作树使用建议
1. **命名规范** - 使用清晰的工作树目录命名
2. **定期清理** - 删除不再需要的工作树
3. **同步更新** - 定期同步所有工作树
4. **文档记录** - 记录工作树的用途和状态
5. **自动化管理** - 使用脚本自动化常见操作

### 🔒 安全考虑
```bash
# 1. 备份重要工作树
tar -czf worktree-backup.tar.gz /path/to/important/worktree

# 2. 检查工作树状态
git worktree list
git status  # 在每个工作树中检查

# 3. 避免在工作树间移动 .git 文件
# 4. 定期运行 git worktree prune
```

### 🚨 避免的陷阱
1. **忘记删除工作树** - 定期清理不用的工作树
2. **分支冲突** - 一个分支只能在一个工作树中检出
3. **磁盘空间** - 多个工作树会占用更多磁盘空间
4. **IDE 配置** - 每个工作树可能需要独立的 IDE 配置
5. **环境变量** - 注意不同工作树的环境配置

### 🔧 工作树管理脚本
```bash
#!/bin/bash
# worktree_manager.sh

ACTION=$1
BRANCH_NAME=$2

case $ACTION in
    "create")
        if [ -z "$BRANCH_NAME" ]; then
            echo "Usage: $0 create <branch-name>"
            exit 1
        fi
        git worktree add -b "$BRANCH_NAME" "../project-$BRANCH_NAME"
        echo "Created worktree for branch: $BRANCH_NAME"
        ;;
    "list")
        git worktree list
        ;;
    "clean")
        git worktree prune --verbose
        echo "Cleaned up invalid worktrees"
        ;;
    "status")
        ./check_worktrees.sh
        ;;
    *)
        echo "Usage: $0 [create|list|clean|status] [branch-name]"
        exit 1
        ;;
esac
```

---

**记住**: Git 工作树是提高开发效率的强大工具，特别适合需要并行开发多个功能或维护多个版本的场景。合理使用工作树可以大大简化你的开发工作流程！ 🌳
