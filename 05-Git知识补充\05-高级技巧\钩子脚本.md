# Git 钩子脚本详解

## 🎣 钩子概念

### 📋 什么是 Git 钩子？
Git 钩子（Hooks）是在特定 Git 事件发生时自动执行的脚本，用于自动化工作流程、强制执行规范、触发部署等。

```bash
# 钩子的作用：
├── 代码质量检查 - 提交前检查代码规范
├── 自动化测试 - 推送前运行测试
├── 部署触发 - 推送后自动部署
├── 消息验证 - 检查提交信息格式
└── 权限控制 - 限制特定操作
```

### 🎯 钩子类型
```bash
# 客户端钩子（本地执行）
├── pre-commit - 提交前执行
├── prepare-commit-msg - 准备提交信息
├── commit-msg - 验证提交信息
├── post-commit - 提交后执行
├── pre-rebase - 变基前执行
├── post-checkout - 检出后执行
├── post-merge - 合并后执行
└── pre-push - 推送前执行

# 服务器端钩子（远程执行）
├── pre-receive - 接收前执行
├── update - 更新引用时执行
└── post-receive - 接收后执行
```

## 📁 钩子文件位置

### 🗂️ 钩子目录
```bash
# 钩子脚本位置
.git/hooks/
├── pre-commit.sample          # 示例文件
├── prepare-commit-msg.sample
├── commit-msg.sample
├── post-commit.sample
├── pre-rebase.sample
├── post-checkout.sample
├── post-merge.sample
├── pre-push.sample
├── pre-receive.sample
├── update.sample
└── post-receive.sample

# 激活钩子：移除 .sample 后缀
mv .git/hooks/pre-commit.sample .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

### 🔧 钩子脚本要求
```bash
# 钩子脚本要求：
├── 可执行权限 - chmod +x hook-name
├── 返回状态码 - 0 表示成功，非 0 表示失败
├── 支持多种语言 - Shell、Python、Node.js 等
└── 接收参数 - 某些钩子接收特定参数
```

## 🔍 客户端钩子详解

### 📝 pre-commit 钩子

#### 基本示例
```bash
#!/bin/sh
# .git/hooks/pre-commit

# 检查是否有文件被暂存
if git diff --cached --quiet; then
    echo "No changes staged for commit."
    exit 1
fi

# 运行代码检查
echo "Running code quality checks..."

# ESLint 检查
if command -v eslint >/dev/null 2>&1; then
    echo "Running ESLint..."
    eslint $(git diff --cached --name-only --diff-filter=ACM | grep '\.js$')
    if [ $? -ne 0 ]; then
        echo "ESLint failed. Please fix the issues before committing."
        exit 1
    fi
fi

# Prettier 格式检查
if command -v prettier >/dev/null 2>&1; then
    echo "Running Prettier..."
    prettier --check $(git diff --cached --name-only --diff-filter=ACM)
    if [ $? -ne 0 ]; then
        echo "Code formatting issues found. Run 'prettier --write .' to fix."
        exit 1
    fi
fi

echo "All checks passed!"
exit 0
```

#### Python 版本
```python
#!/usr/bin/env python3
# .git/hooks/pre-commit

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_staged_files():
    """检查是否有暂存的文件"""
    success, stdout, _ = run_command("git diff --cached --quiet")
    return not success  # diff --quiet 返回 0 表示没有差异

def run_tests():
    """运行测试"""
    print("Running tests...")
    success, stdout, stderr = run_command("npm test")
    if not success:
        print(f"Tests failed:\n{stderr}")
        return False
    print("Tests passed!")
    return True

def check_code_style():
    """检查代码风格"""
    print("Checking code style...")
    
    # 获取暂存的 JavaScript 文件
    success, stdout, _ = run_command("git diff --cached --name-only --diff-filter=ACM | grep '\\.js$'")
    if not success or not stdout.strip():
        print("No JavaScript files to check.")
        return True
    
    js_files = stdout.strip().split('\n')
    
    # 运行 ESLint
    for file in js_files:
        if os.path.exists(file):
            success, _, stderr = run_command(f"eslint {file}")
            if not success:
                print(f"ESLint failed for {file}:\n{stderr}")
                return False
    
    print("Code style checks passed!")
    return True

def main():
    """主函数"""
    if not check_staged_files():
        print("No changes staged for commit.")
        sys.exit(1)
    
    if not check_code_style():
        sys.exit(1)
    
    if not run_tests():
        sys.exit(1)
    
    print("All pre-commit checks passed!")
    sys.exit(0)

if __name__ == "__main__":
    main()
```

### 📨 commit-msg 钩子

#### 提交信息验证
```bash
#!/bin/sh
# .git/hooks/commit-msg

commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

error_msg="Aborting commit. Your commit message is invalid. Please use the format:
<type>(<scope>): <subject>

Where:
- type: feat, fix, docs, style, refactor, test, chore
- scope: optional, e.g., (auth), (ui), (api)
- subject: brief description (1-50 characters)

Examples:
- feat(auth): add user login functionality
- fix: resolve memory leak in data processing
- docs: update API documentation"

if ! grep -qE "$commit_regex" "$1"; then
    echo "$error_msg" >&2
    exit 1
fi

# 检查提交信息长度
if [ $(head -n1 "$1" | wc -c) -gt 72 ]; then
    echo "Aborting commit. First line of commit message is too long (>72 characters)." >&2
    exit 1
fi

exit 0
```

#### Node.js 版本
```javascript
#!/usr/bin/env node
// .git/hooks/commit-msg

const fs = require('fs');
const path = require('path');

const commitMsgFile = process.argv[2];
const commitMsg = fs.readFileSync(commitMsgFile, 'utf8').trim();

// 约定式提交规范
const conventionalCommitRegex = /^(feat|fix|docs|style|refactor|perf|test|chore|ci|build|revert)(\(.+\))?: .{1,50}/;

// 检查提交信息格式
if (!conventionalCommitRegex.test(commitMsg)) {
    console.error(`
❌ Invalid commit message format!

Your commit message: "${commitMsg}"

Please follow the Conventional Commits specification:
<type>[optional scope]: <description>

Types:
- feat: A new feature
- fix: A bug fix
- docs: Documentation only changes
- style: Changes that do not affect the meaning of the code
- refactor: A code change that neither fixes a bug nor adds a feature
- perf: A code change that improves performance
- test: Adding missing tests or correcting existing tests
- chore: Changes to the build process or auxiliary tools
- ci: Changes to CI configuration files and scripts
- build: Changes that affect the build system or external dependencies
- revert: Reverts a previous commit

Examples:
✅ feat(auth): add user authentication
✅ fix: resolve memory leak in parser
✅ docs: update installation guide
    `);
    process.exit(1);
}

// 检查首行长度
const firstLine = commitMsg.split('\n')[0];
if (firstLine.length > 72) {
    console.error(`❌ First line too long (${firstLine.length} > 72 characters)`);
    process.exit(1);
}

// 检查是否包含禁用词
const forbiddenWords = ['WIP', 'TODO', 'FIXME', 'XXX'];
const hasForbiddenWords = forbiddenWords.some(word => 
    commitMsg.toUpperCase().includes(word)
);

if (hasForbiddenWords) {
    console.error('❌ Commit message contains forbidden words (WIP, TODO, FIXME, XXX)');
    process.exit(1);
}

console.log('✅ Commit message format is valid!');
process.exit(0);
```

### 🚀 pre-push 钩子

#### 推送前检查
```bash
#!/bin/sh
# .git/hooks/pre-push

protected_branch='main'
current_branch=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')

# 检查是否推送到受保护的分支
if [ $protected_branch = $current_branch ]; then
    echo "Direct push to main branch is not allowed!"
    echo "Please create a pull request instead."
    exit 1
fi

# 运行测试
echo "Running tests before push..."
npm test
if [ $? -ne 0 ]; then
    echo "Tests failed. Push aborted."
    exit 1
fi

# 检查是否有未提交的更改
if [ -n "$(git status --porcelain)" ]; then
    echo "You have uncommitted changes. Please commit or stash them before pushing."
    exit 1
fi

echo "Pre-push checks passed!"
exit 0
```

## 🖥️ 服务器端钩子

### 📥 pre-receive 钩子

#### 服务器端验证
```bash
#!/bin/sh
# hooks/pre-receive

# 读取推送的引用信息
while read oldrev newrev refname; do
    # 检查分支名
    branch=$(git rev-parse --symbolic --abbrev-ref $refname)
    
    # 禁止删除主分支
    if [ "$newrev" = "0000000000000000000000000000000000000000" ]; then
        if [ "$branch" = "main" ] || [ "$branch" = "master" ]; then
            echo "Deleting main/master branch is not allowed!"
            exit 1
        fi
    fi
    
    # 检查提交信息
    if [ "$oldrev" != "0000000000000000000000000000000000000000" ]; then
        # 检查新提交的提交信息
        git rev-list $oldrev..$newrev | while read commit; do
            msg=$(git log --format=%s -n 1 $commit)
            if ! echo "$msg" | grep -qE '^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .+'; then
                echo "Invalid commit message format in $commit: $msg"
                exit 1
            fi
        done
    fi
done

exit 0
```

### 📤 post-receive 钩子

#### 自动部署
```bash
#!/bin/sh
# hooks/post-receive

# 部署到生产环境
deploy_to_production() {
    echo "Deploying to production..."
    
    # 切换到部署目录
    cd /var/www/myapp || exit 1
    
    # 拉取最新代码
    git --git-dir=/var/www/myapp/.git --work-tree=/var/www/myapp pull origin main
    
    # 安装依赖
    npm ci --production
    
    # 构建项目
    npm run build
    
    # 重启服务
    sudo systemctl restart myapp
    
    echo "Deployment completed!"
}

# 发送通知
send_notification() {
    local branch=$1
    local commit=$2
    local author=$3
    
    # 发送到 Slack
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚀 New deployment to $branch by $author: $commit\"}" \
        $SLACK_WEBHOOK_URL
}

# 处理推送的引用
while read oldrev newrev refname; do
    branch=$(git rev-parse --symbolic --abbrev-ref $refname)
    
    # 只处理主分支的推送
    if [ "$branch" = "main" ]; then
        # 获取最新提交信息
        commit=$(git log --format="%h %s" -n 1 $newrev)
        author=$(git log --format="%an" -n 1 $newrev)
        
        # 部署到生产环境
        deploy_to_production
        
        # 发送通知
        send_notification "$branch" "$commit" "$author"
    fi
done
```

## 🛠️ 钩子管理工具

### 📦 Husky 集成

#### 安装和配置
```bash
# 安装 Husky
npm install --save-dev husky

# 初始化 Husky
npx husky install

# 添加到 package.json
npm pkg set scripts.prepare="husky install"

# 添加 pre-commit 钩子
npx husky add .husky/pre-commit "npm test"

# 添加 commit-msg 钩子
npx husky add .husky/commit-msg 'npx --no -- commitlint --edit "$1"'
```

#### package.json 配置
```json
{
  "scripts": {
    "prepare": "husky install",
    "test": "jest",
    "lint": "eslint src/",
    "format": "prettier --write src/"
  },
  "devDependencies": {
    "husky": "^8.0.0",
    "@commitlint/cli": "^17.0.0",
    "@commitlint/config-conventional": "^17.0.0",
    "lint-staged": "^13.0.0"
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,css,md}": [
      "prettier --write"
    ]
  }
}
```

### 🎯 lint-staged 集成
```javascript
// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
```

```javascript
// commitlint.config.js
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',
        'fix',
        'docs',
        'style',
        'refactor',
        'perf',
        'test',
        'chore',
        'ci',
        'build',
        'revert'
      ]
    ],
    'subject-max-length': [2, 'always', 72],
    'subject-case': [2, 'always', 'lower-case']
  }
};
```

## 🔧 高级钩子技巧

### 🎨 动态钩子
```bash
#!/bin/sh
# .git/hooks/pre-commit

# 根据项目类型选择不同的检查
if [ -f "package.json" ]; then
    # Node.js 项目
    echo "Detected Node.js project"
    npm run lint
    npm test
elif [ -f "requirements.txt" ]; then
    # Python 项目
    echo "Detected Python project"
    flake8 .
    pytest
elif [ -f "Cargo.toml" ]; then
    # Rust 项目
    echo "Detected Rust project"
    cargo fmt --check
    cargo clippy
    cargo test
else
    echo "Unknown project type, skipping checks"
fi
```

### 🔄 钩子链
```bash
#!/bin/sh
# .git/hooks/pre-commit

HOOK_DIR=".git/hooks/pre-commit.d"

if [ -d "$HOOK_DIR" ]; then
    for hook in "$HOOK_DIR"/*; do
        if [ -x "$hook" ]; then
            echo "Running $(basename "$hook")..."
            "$hook"
            if [ $? -ne 0 ]; then
                echo "Hook $(basename "$hook") failed!"
                exit 1
            fi
        fi
    done
fi

echo "All pre-commit hooks passed!"
```

### 📊 钩子日志
```bash
#!/bin/sh
# .git/hooks/post-commit

LOG_FILE=".git/hooks.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
COMMIT_HASH=$(git rev-parse HEAD)
COMMIT_MSG=$(git log --format=%s -n 1)
AUTHOR=$(git log --format=%an -n 1)

echo "[$TIMESTAMP] POST-COMMIT: $COMMIT_HASH - $COMMIT_MSG by $AUTHOR" >> "$LOG_FILE"
```

## 💡 最佳实践

### ✅ 钩子开发建议
1. **快速执行** - 钩子应该快速完成，避免阻塞工作流
2. **明确反馈** - 提供清晰的错误信息和修复建议
3. **可配置性** - 允许开发者配置或跳过某些检查
4. **跨平台兼容** - 确保钩子在不同操作系统上正常工作
5. **版本控制** - 将钩子脚本纳入版本控制

### 🔒 安全考虑
```bash
# 1. 验证输入参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <commit-msg-file>"
    exit 1
fi

# 2. 检查文件存在性
if [ ! -f "$1" ]; then
    echo "Commit message file not found: $1"
    exit 1
fi

# 3. 限制执行权限
# 确保钩子脚本只能被仓库所有者修改

# 4. 避免执行外部不可信代码
# 谨慎使用 eval 和动态执行
```

### 🚨 常见陷阱
1. **钩子过慢** - 避免在钩子中执行耗时操作
2. **依赖缺失** - 确保钩子依赖的工具已安装
3. **权限问题** - 检查钩子脚本的执行权限
4. **路径问题** - 使用绝对路径或正确设置 PATH
5. **跨平台问题** - 注意不同操作系统的差异

---

**记住**: Git 钩子是强大的自动化工具，可以帮助团队保持代码质量和工作流程的一致性。合理使用钩子可以大大提高开发效率和代码质量！ 🎣
