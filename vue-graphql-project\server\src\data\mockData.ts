import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import {
  User,
  Project,
  Task,
  Comment,
  UserRole,
  UserStatus,
  ProjectStatus,
  TaskStatus,
  Priority
} from '../models';

// 生成哈希密码
const hashPassword = (password: string): string => {
  return bcrypt.hashSync(password, 10);
};

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    password: hashPassword('admin123'),
    firstName: '管理员',
    lastName: '用户',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '2',
    username: 'john_doe',
    email: '<EMAIL>',
    password: hashPassword('password123'),
    firstName: '<PERSON>',
    lastName: 'Doe',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
    role: UserRole.MANAGER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  },
  {
    id: '3',
    username: 'jane_smith',
    email: '<EMAIL>',
    password: hashPassword('password123'),
    firstName: 'Jane',
    lastName: 'Smith',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=jane',
    role: UserRole.DEVELOPER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03')
  },
  {
    id: '4',
    username: 'bob_wilson',
    email: '<EMAIL>',
    password: hashPassword('password123'),
    firstName: 'Bob',
    lastName: 'Wilson',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=bob',
    role: UserRole.DESIGNER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2024-01-04'),
    updatedAt: new Date('2024-01-04')
  },
  {
    id: '5',
    username: 'alice_brown',
    email: '<EMAIL>',
    password: hashPassword('password123'),
    firstName: 'Alice',
    lastName: 'Brown',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=alice',
    role: UserRole.TESTER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  }
];

// 模拟项目数据
export const mockProjects: Project[] = [
  {
    id: '1',
    name: '电商平台重构',
    description: '重构现有电商平台，提升性能和用户体验',
    status: ProjectStatus.ACTIVE,
    priority: Priority.HIGH,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-06-30'),
    ownerId: '2',
    memberIds: ['2', '3', '4', '5'],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '2',
    name: '移动端应用开发',
    description: '开发配套的移动端应用',
    status: ProjectStatus.PLANNING,
    priority: Priority.MEDIUM,
    startDate: new Date('2024-03-01'),
    endDate: new Date('2024-08-31'),
    ownerId: '2',
    memberIds: ['3', '4'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '3',
    name: '数据分析系统',
    description: '构建用户行为数据分析系统',
    status: ProjectStatus.ACTIVE,
    priority: Priority.MEDIUM,
    startDate: new Date('2024-02-01'),
    endDate: new Date('2024-05-31'),
    ownerId: '1',
    memberIds: ['3', '5'],
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  }
];

// 模拟任务数据
export const mockTasks: Task[] = [
  {
    id: '1',
    title: '设计用户界面原型',
    description: '设计电商平台的用户界面原型，包括首页、商品页、购物车等',
    status: TaskStatus.DONE,
    priority: Priority.HIGH,
    dueDate: new Date('2024-02-15'),
    estimatedHours: 40,
    actualHours: 38,
    projectId: '1',
    creatorId: '2',
    assigneeId: '4',
    tags: ['设计', 'UI', '原型'],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-02-10')
  },
  {
    id: '2',
    title: '实现用户认证模块',
    description: '开发用户注册、登录、密码重置等功能',
    status: TaskStatus.IN_PROGRESS,
    priority: Priority.HIGH,
    dueDate: new Date('2024-03-01'),
    estimatedHours: 32,
    actualHours: 20,
    projectId: '1',
    creatorId: '2',
    assigneeId: '3',
    tags: ['开发', '认证', '安全'],
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-02-20')
  },
  {
    id: '3',
    title: '商品管理系统',
    description: '开发商品的增删改查功能',
    status: TaskStatus.TODO,
    priority: Priority.MEDIUM,
    dueDate: new Date('2024-03-15'),
    estimatedHours: 48,
    projectId: '1',
    creatorId: '2',
    assigneeId: '3',
    tags: ['开发', '商品', 'CRUD'],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: '4',
    title: '购物车功能测试',
    description: '测试购物车的添加、删除、修改数量等功能',
    status: TaskStatus.TODO,
    priority: Priority.MEDIUM,
    dueDate: new Date('2024-04-01'),
    estimatedHours: 16,
    projectId: '1',
    creatorId: '2',
    assigneeId: '5',
    tags: ['测试', '购物车', '功能测试'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '5',
    title: '数据库设计',
    description: '设计数据分析系统的数据库结构',
    status: TaskStatus.IN_REVIEW,
    priority: Priority.HIGH,
    dueDate: new Date('2024-02-28'),
    estimatedHours: 24,
    actualHours: 26,
    projectId: '3',
    creatorId: '1',
    assigneeId: '3',
    tags: ['数据库', '设计', '架构'],
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-25')
  },
  {
    id: '6',
    title: '移动端UI设计',
    description: '设计移动端应用的用户界面',
    status: TaskStatus.TODO,
    priority: Priority.LOW,
    dueDate: new Date('2024-04-15'),
    estimatedHours: 60,
    projectId: '2',
    creatorId: '2',
    assigneeId: '4',
    tags: ['移动端', 'UI', '设计'],
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-01')
  }
];

// 模拟评论数据
export const mockComments: Comment[] = [
  {
    id: '1',
    content: '原型设计看起来很不错，用户体验很好！',
    authorId: '2',
    taskId: '1',
    createdAt: new Date('2024-02-12'),
    updatedAt: new Date('2024-02-12')
  },
  {
    id: '2',
    content: '建议在登录页面添加记住密码功能',
    authorId: '4',
    taskId: '2',
    createdAt: new Date('2024-02-18'),
    updatedAt: new Date('2024-02-18')
  },
  {
    id: '3',
    content: '认证模块的安全性需要进一步加强',
    authorId: '1',
    taskId: '2',
    createdAt: new Date('2024-02-22'),
    updatedAt: new Date('2024-02-22')
  },
  {
    id: '4',
    content: '数据库设计文档已经完成，请查看',
    authorId: '3',
    taskId: '5',
    createdAt: new Date('2024-02-26'),
    updatedAt: new Date('2024-02-26')
  }
];

// 内存数据库类
export class MemoryDatabase {
  private users: User[] = [...mockUsers];
  private projects: Project[] = [...mockProjects];
  private tasks: Task[] = [...mockTasks];
  private comments: Comment[] = [...mockComments];

  // 用户操作
  getUsers(): User[] {
    return this.users;
  }

  getUserById(id: string): User | undefined {
    return this.users.find(user => user.id === id);
  }

  getUserByEmail(email: string): User | undefined {
    return this.users.find(user => user.email === email);
  }

  createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User {
    const user: User = {
      ...userData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.users.push(user);
    return user;
  }

  updateUser(id: string, updates: Partial<User>): User | null {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) return null;
    
    this.users[index] = {
      ...this.users[index],
      ...updates,
      updatedAt: new Date()
    };
    return this.users[index];
  }

  deleteUser(id: string): boolean {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) return false;
    
    this.users.splice(index, 1);
    return true;
  }

  // 项目操作
  getProjects(): Project[] {
    return this.projects;
  }

  getProjectById(id: string): Project | undefined {
    return this.projects.find(project => project.id === id);
  }

  createProject(projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Project {
    const project: Project = {
      ...projectData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.projects.push(project);
    return project;
  }

  updateProject(id: string, updates: Partial<Project>): Project | null {
    const index = this.projects.findIndex(project => project.id === id);
    if (index === -1) return null;
    
    this.projects[index] = {
      ...this.projects[index],
      ...updates,
      updatedAt: new Date()
    };
    return this.projects[index];
  }

  deleteProject(id: string): boolean {
    const index = this.projects.findIndex(project => project.id === id);
    if (index === -1) return false;
    
    this.projects.splice(index, 1);
    return true;
  }

  // 任务操作
  getTasks(): Task[] {
    return this.tasks;
  }

  getTaskById(id: string): Task | undefined {
    return this.tasks.find(task => task.id === id);
  }

  createTask(taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Task {
    const task: Task = {
      ...taskData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.tasks.push(task);
    return task;
  }

  updateTask(id: string, updates: Partial<Task>): Task | null {
    const index = this.tasks.findIndex(task => task.id === id);
    if (index === -1) return null;
    
    this.tasks[index] = {
      ...this.tasks[index],
      ...updates,
      updatedAt: new Date()
    };
    return this.tasks[index];
  }

  deleteTask(id: string): boolean {
    const index = this.tasks.findIndex(task => task.id === id);
    if (index === -1) return false;
    
    this.tasks.splice(index, 1);
    return true;
  }

  // 评论操作
  getComments(): Comment[] {
    return this.comments;
  }

  getCommentById(id: string): Comment | undefined {
    return this.comments.find(comment => comment.id === id);
  }

  createComment(commentData: Omit<Comment, 'id' | 'createdAt' | 'updatedAt'>): Comment {
    const comment: Comment = {
      ...commentData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.comments.push(comment);
    return comment;
  }

  deleteComment(id: string): boolean {
    const index = this.comments.findIndex(comment => comment.id === id);
    if (index === -1) return false;
    
    this.comments.splice(index, 1);
    return true;
  }
}

// 导出数据库实例
export const db = new MemoryDatabase();
