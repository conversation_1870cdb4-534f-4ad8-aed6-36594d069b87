# LSP中"不改变行为契约"的深度解析

## 🎯 核心误解澄清

### ❌ 错误理解
"子类不应该改变父类的行为契约" ≠ "子类不能重写父类方法"

### ✅ 正确理解
"子类不应该改变父类的行为契约" = "子类重写方法时，必须遵守父类方法的预期行为规范"

---

## 🤝 什么是"行为契约"？

### 契约的组成部分

**行为契约**包含以下几个方面：

1. **前置条件 (Preconditions)**: 方法执行前必须满足的条件
2. **后置条件 (Postconditions)**: 方法执行后必须保证的结果
3. **不变量 (Invariants)**: 对象状态必须始终满足的条件
4. **异常行为**: 什么情况下抛出什么异常
5. **副作用**: 方法执行对系统状态的影响

### 生活中的契约例子

想象一个"银行取款"的契约：
- **前置条件**: 账户余额 >= 取款金额
- **后置条件**: 账户余额减少相应金额，返回现金
- **不变量**: 账户余额不能为负数
- **异常**: 余额不足时抛出异常
- **副作用**: 记录交易日志

---

## ✅ 正确的重写 - 遵守契约

### 例子1：图形面积计算

```javascript
// 父类定义契约
class Shape {
    /**
     * 计算图形面积
     * 契约：
     * - 前置条件：图形必须是有效的
     * - 后置条件：返回非负数
     * - 异常：无效图形时抛出错误
     */
    getArea() {
        throw new Error('子类必须实现getArea方法');
    }
    
    isValid() {
        return true; // 基础验证
    }
}

// ✅ 正确的重写 - 遵守契约
class Rectangle extends Shape {
    constructor(width, height) {
        super();
        this.width = width;
        this.height = height;
    }
    
    isValid() {
        return this.width > 0 && this.height > 0;
    }
    
    getArea() {
        // 遵守前置条件：检查图形有效性
        if (!this.isValid()) {
            throw new Error('无效的长方形：宽度和高度必须大于0');
        }
        
        // 遵守后置条件：返回非负数
        const area = this.width * this.height;
        return area; // 总是非负数
    }
}

// ✅ 正确的重写 - 遵守契约
class Circle extends Shape {
    constructor(radius) {
        super();
        this.radius = radius;
    }
    
    isValid() {
        return this.radius > 0;
    }
    
    getArea() {
        // 遵守前置条件
        if (!this.isValid()) {
            throw new Error('无效的圆形：半径必须大于0');
        }
        
        // 遵守后置条件：返回非负数
        const area = Math.PI * this.radius * this.radius;
        return area; // 总是非负数
    }
}

// 客户端代码可以安全地使用任何Shape
function calculateTotalArea(shapes) {
    let total = 0;
    for (const shape of shapes) {
        // 可以安全地调用，因为所有子类都遵守契约
        const area = shape.getArea(); // 保证返回非负数
        total += area;
    }
    return total;
}

// 使用
const shapes = [
    new Rectangle(5, 3),  // 面积: 15
    new Circle(2)         // 面积: 12.56...
];

console.log(calculateTotalArea(shapes)); // 正常工作
```

---

## ❌ 错误的重写 - 违反契约

### 例子1：违反后置条件

```javascript
// ❌ 错误的重写 - 违反后置条件
class WeirdShape extends Shape {
    getArea() {
        // 违反契约：返回负数！
        return -10; // 面积不可能是负数
    }
}

// 这会导致客户端代码出错
const shapes = [
    new Rectangle(5, 3),  // 15
    new WeirdShape()      // -10 (违反契约)
];

console.log(calculateTotalArea(shapes)); // 结果: 5，这是错误的！
```

### 例子2：违反异常行为

```javascript
// ❌ 错误的重写 - 改变异常行为
class SilentShape extends Shape {
    constructor() {
        super();
        this.isValidShape = false;
    }
    
    getArea() {
        if (!this.isValidShape) {
            // 违反契约：应该抛出异常，但返回了0
            return 0; // 静默失败，违反了父类的异常契约
        }
        return 10;
    }
}

// 客户端期望无效图形会抛出异常，但没有
const shape = new SilentShape();
try {
    const area = shape.getArea(); // 期望抛出异常，但返回了0
    console.log(`面积: ${area}`); // 输出: 面积: 0 (错误的行为)
} catch (error) {
    console.log('捕获到异常'); // 这行不会执行
}
```

---

## 🔍 契约的具体要求

### 1. 前置条件不能加强

```javascript
class BankAccount {
    /**
     * 取款方法
     * 契约：只要余额 >= 金额就可以取款
     */
    withdraw(amount) {
        if (this.balance < amount) {
            throw new Error('余额不足');
        }
        this.balance -= amount;
        return amount;
    }
}

// ❌ 错误：加强了前置条件
class PremiumAccount extends BankAccount {
    withdraw(amount) {
        // 违反LSP：加强了前置条件
        if (amount > 1000) {
            throw new Error('单次取款不能超过1000元'); // 父类没有这个限制
        }
        return super.withdraw(amount);
    }
}

// 客户端代码会出错
function withdrawMoney(account, amount) {
    return account.withdraw(amount); // 期望只检查余额，但子类有额外限制
}

const account = new PremiumAccount();
account.balance = 2000;
withdrawMoney(account, 1500); // 抛出异常，违反了LSP
```

### 2. 后置条件不能削弱

```javascript
class DataProcessor {
    /**
     * 处理数据
     * 契约：总是返回处理后的数据，不会返回null
     */
    process(data) {
        if (!data) {
            throw new Error('数据不能为空');
        }
        return data.toUpperCase();
    }
}

// ❌ 错误：削弱了后置条件
class LazyProcessor extends DataProcessor {
    process(data) {
        if (data.length > 100) {
            // 违反LSP：可能返回null，削弱了后置条件
            return null; // 父类保证不返回null
        }
        return super.process(data);
    }
}

// 客户端代码会出错
function handleData(processor, data) {
    const result = processor.process(data);
    return result.length; // 期望result不为null，但子类可能返回null
}
```

### 3. 不变量必须维护

```javascript
class Counter {
    constructor() {
        this.count = 0;
    }
    
    /**
     * 增加计数
     * 不变量：count总是非负数
     */
    increment() {
        this.count++;
    }
    
    getCount() {
        return this.count;
    }
}

// ❌ 错误：违反不变量
class WeirdCounter extends Counter {
    increment() {
        // 违反LSP：破坏了不变量
        this.count -= 5; // count可能变成负数
    }
}

// 客户端代码依赖不变量
function displayProgress(counter) {
    counter.increment();
    const count = counter.getCount();
    // 期望count总是非负数
    const percentage = (count / 100) * 100; // 如果count为负数，结果错误
    console.log(`进度: ${percentage}%`);
}
```

---

## ✅ 正确重写的最佳实践

### 1. 扩展而非改变

```javascript
class Logger {
    log(message) {
        console.log(`[LOG] ${message}`);
    }
}

// ✅ 正确：扩展功能，不改变基本契约
class TimestampLogger extends Logger {
    log(message) {
        const timestamp = new Date().toISOString();
        // 调用父类方法，保持基本契约
        super.log(`${timestamp} - ${message}`);
        // 可以添加额外功能
        this.saveToFile(message);
    }
    
    saveToFile(message) {
        // 额外功能
        console.log(`保存到文件: ${message}`);
    }
}
```

### 2. 放宽前置条件

```javascript
class FileReader {
    read(filename) {
        if (!filename || filename.trim() === '') {
            throw new Error('文件名不能为空');
        }
        return `读取文件: ${filename}`;
    }
}

// ✅ 正确：放宽前置条件（接受更多输入）
class FlexibleFileReader extends FileReader {
    read(filename) {
        // 放宽前置条件：接受null/undefined，提供默认值
        if (!filename) {
            filename = 'default.txt';
        }
        return super.read(filename);
    }
}
```

### 3. 加强后置条件

```javascript
class Validator {
    validate(data) {
        // 基本验证：返回boolean
        return data !== null && data !== undefined;
    }
}

// ✅ 正确：加强后置条件（提供更多保证）
class DetailedValidator extends Validator {
    validate(data) {
        const isValid = super.validate(data);
        if (isValid) {
            // 加强后置条件：额外验证
            return typeof data === 'string' && data.length > 0;
        }
        return false;
    }
}
```

---

## 🎯 实践指南

### 契约检查清单

在重写方法时，问自己：

1. **前置条件**: 我是否要求了比父类更严格的输入条件？
2. **后置条件**: 我是否提供了比父类更弱的输出保证？
3. **异常行为**: 我是否改变了异常的抛出条件或类型？
4. **不变量**: 我是否破坏了对象应该始终满足的条件？
5. **副作用**: 我是否引入了父类没有的副作用？

### 设计建议

1. **文档化契约**: 在父类中明确说明方法的契约
2. **单元测试**: 为契约编写测试，确保子类遵守
3. **渐进增强**: 子类应该增强功能，而不是改变基本行为
4. **契约式设计**: 使用断言或注释明确契约要求

---

## 📚 总结

### 核心要点

1. **重写是允许的**: LSP不禁止重写，而是规范重写的方式
2. **契约必须遵守**: 子类可以改变实现，但不能改变行为契约
3. **客户端透明**: 使用子类替换父类时，客户端代码不应该出错
4. **增强不改变**: 子类可以增强功能，但不能改变基本行为

### 记忆口诀

**"重写可以有，契约不能丢"**

- 可以重写方法 ✅
- 可以改变实现 ✅  
- 可以增加功能 ✅
- 不能改变契约 ❌
- 不能破坏预期 ❌

LSP的本质是确保继承的正确性，让多态能够安全可靠地工作！
