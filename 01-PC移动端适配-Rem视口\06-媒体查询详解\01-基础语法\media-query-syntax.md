# 媒体查询基础语法

## 语法结构

```css
@media [媒体类型] [逻辑操作符] ([媒体特性]) {
    /* CSS规则 */
}
```

## 媒体类型

| 类型 | 描述 | 使用场景 |
|------|------|----------|
| `all` | 所有设备（默认） | 通用样式 |
| `screen` | 屏幕设备 | 电脑、手机、平板 |
| `print` | 打印设备 | 打印预览和打印 |
| `speech` | 语音合成器 | 无障碍访问 |

## 逻辑操作符

### and 操作符
```css
/* 同时满足多个条件 */
@media screen and (min-width: 768px) and (orientation: landscape) {
    /* 屏幕设备 且 宽度≥768px 且 横向 */
}
```

### or 操作符（逗号分隔）
```css
/* 满足任一条件 */
@media (max-width: 767px), (orientation: portrait) {
    /* 宽度≤767px 或 纵向 */
}
```

### not 操作符
```css
/* 排除条件 */
@media not screen and (color) {
    /* 非彩色屏幕设备 */
}
```

### only 操作符
```css
/* 仅在支持媒体查询的浏览器中应用 */
@media only screen and (min-width: 768px) {
    /* 现代浏览器专用 */
}
```

## 常用媒体特性

### 尺寸相关
```css
/* 宽度 */
@media (min-width: 768px) { }
@media (max-width: 1023px) { }
@media (width: 768px) { }

/* 高度 */
@media (min-height: 600px) { }
@media (max-height: 800px) { }

/* 设备尺寸 */
@media (min-device-width: 320px) { }
@media (max-device-width: 768px) { }
```

### 方向相关
```css
/* 纵向（竖屏） */
@media (orientation: portrait) {
    /* 高度 > 宽度 */
}

/* 横向（横屏） */
@media (orientation: landscape) {
    /* 宽度 > 高度 */
}
```

### 分辨率相关
```css
/* 像素密度 */
@media (-webkit-min-device-pixel-ratio: 2) { }
@media (min-resolution: 192dpi) { }
@media (min-resolution: 2dppx) { }
```

## 实用示例

### 移动优先设计
```css
/* 基础样式（移动端） */
.container {
    width: 100%;
    padding: 1rem;
}

/* 平板样式 */
@media (min-width: 768px) {
    .container {
        max-width: 750px;
        margin: 0 auto;
    }
}

/* 桌面样式 */
@media (min-width: 1024px) {
    .container {
        max-width: 1200px;
        padding: 2rem;
    }
}
```

### 方向适配
```css
/* 通用样式 */
.header {
    height: 60px;
}

/* 横屏时调整 */
@media (orientation: landscape) {
    .header {
        height: 50px; /* 横屏时减少高度 */
    }
}

/* 竖屏时调整 */
@media (orientation: portrait) {
    .sidebar {
        display: none; /* 竖屏时隐藏侧边栏 */
    }
}
```

### 打印样式
```css
/* 屏幕样式 */
.no-print {
    display: block;
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        color: black;
        background: white;
    }
}
```

## 注意事项

1. **单位选择**：推荐使用 `em` 或 `rem` 而非 `px`
2. **性能考虑**：避免过度嵌套媒体查询
3. **测试重要**：在真实设备上验证效果
4. **渐进增强**：确保基础功能在所有设备上可用
