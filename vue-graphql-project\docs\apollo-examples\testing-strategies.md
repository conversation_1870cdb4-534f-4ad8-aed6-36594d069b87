# Apollo Server Express 测试策略

## 1. 单元测试设置

### 测试环境配置

```typescript
// tests/setup.ts
import { ApolloServer } from 'apollo-server-express';
import { createTestClient } from 'apollo-server-testing';
import { typeDefs } from '../src/schema/typeDefs';
import { resolvers } from '../src/resolvers';
import { db } from '../src/data/mockData';
import { createDataLoaders } from '../src/loaders/DataLoaders';

// 测试数据库重置
export const resetTestDatabase = () => {
  db.reset(); // 重置到初始状态
  db.seedTestData(); // 添加测试数据
};

// 创建测试服务器
export const createTestServer = (context: any = {}) => {
  const server = new ApolloServer({
    typeDefs,
    resolvers,
    context: () => ({
      loaders: createDataLoaders(),
      ...context,
    }),
  });

  return createTestClient(server);
};

// 创建认证用户上下文
export const createAuthContext = (user: any) => ({
  user,
  token: `Bearer test-token-${user.id}`,
  loaders: createDataLoaders(),
});

// 测试用户数据
export const testUsers = {
  admin: {
    id: 'test-admin-1',
    username: 'testadmin',
    email: '<EMAIL>',
    role: 'ADMIN',
    status: 'ACTIVE',
  },
  manager: {
    id: 'test-manager-1',
    username: 'testmanager',
    email: '<EMAIL>',
    role: 'MANAGER',
    status: 'ACTIVE',
  },
  developer: {
    id: 'test-dev-1',
    username: 'testdev',
    email: '<EMAIL>',
    role: 'DEVELOPER',
    status: 'ACTIVE',
  },
};

// 测试项目数据
export const testProjects = {
  project1: {
    id: 'test-project-1',
    name: 'Test Project 1',
    description: 'Test project description',
    status: 'ACTIVE',
    ownerId: testUsers.manager.id,
    memberIds: [testUsers.developer.id],
  },
};

// 测试任务数据
export const testTasks = {
  task1: {
    id: 'test-task-1',
    title: 'Test Task 1',
    description: 'Test task description',
    status: 'TODO',
    priority: 'MEDIUM',
    projectId: testProjects.project1.id,
    creatorId: testUsers.manager.id,
    assigneeId: testUsers.developer.id,
  },
};
```

## 2. Query 测试

### 基础查询测试

```typescript
// tests/queries/user.test.ts
import { gql } from 'apollo-server-express';
import { createTestServer, createAuthContext, resetTestDatabase, testUsers } from '../setup';

describe('User Queries', () => {
  let query: any;

  beforeEach(() => {
    resetTestDatabase();
    const { query: testQuery } = createTestServer();
    query = testQuery;
  });

  describe('users query', () => {
    const USERS_QUERY = gql`
      query GetUsers($pagination: PaginationInput, $filter: UserFilter) {
        users(pagination: $pagination, filter: $filter) {
          users {
            id
            username
            email
            role
            status
          }
          pageInfo {
            currentPage
            totalPages
            totalItems
            hasNextPage
            hasPreviousPage
          }
        }
      }
    `;

    it('should return users for admin', async () => {
      const { query: adminQuery } = createTestServer(createAuthContext(testUsers.admin));

      const result = await adminQuery({
        query: USERS_QUERY,
        variables: {
          pagination: { page: 1, limit: 10 },
        },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.users.users).toHaveLength(3);
      expect(result.data.users.pageInfo.totalItems).toBe(3);
    });

    it('should deny access for non-admin users', async () => {
      const { query: devQuery } = createTestServer(createAuthContext(testUsers.developer));

      const result = await devQuery({
        query: USERS_QUERY,
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].extensions.code).toBe('FORBIDDEN');
    });

    it('should filter users by role', async () => {
      const { query: adminQuery } = createTestServer(createAuthContext(testUsers.admin));

      const result = await adminQuery({
        query: USERS_QUERY,
        variables: {
          filter: { role: 'DEVELOPER' },
        },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.users.users).toHaveLength(1);
      expect(result.data.users.users[0].role).toBe('DEVELOPER');
    });

    it('should search users by username', async () => {
      const { query: adminQuery } = createTestServer(createAuthContext(testUsers.admin));

      const result = await adminQuery({
        query: USERS_QUERY,
        variables: {
          filter: { search: 'testdev' },
        },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.users.users).toHaveLength(1);
      expect(result.data.users.users[0].username).toBe('testdev');
    });
  });

  describe('user query', () => {
    const USER_QUERY = gql`
      query GetUser($id: ID!) {
        user(id: $id) {
          id
          username
          email
          role
          tasks {
            id
            title
            status
          }
          projects {
            id
            name
            status
          }
        }
      }
    `;

    it('should return user details for own profile', async () => {
      const { query: devQuery } = createTestServer(createAuthContext(testUsers.developer));

      const result = await devQuery({
        query: USER_QUERY,
        variables: { id: testUsers.developer.id },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.user.id).toBe(testUsers.developer.id);
      expect(result.data.user.tasks).toBeDefined();
      expect(result.data.user.projects).toBeDefined();
    });

    it('should deny access to other user profiles for non-admin', async () => {
      const { query: devQuery } = createTestServer(createAuthContext(testUsers.developer));

      const result = await devQuery({
        query: USER_QUERY,
        variables: { id: testUsers.manager.id },
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].extensions.code).toBe('FORBIDDEN');
    });

    it('should allow admin to view any user profile', async () => {
      const { query: adminQuery } = createTestServer(createAuthContext(testUsers.admin));

      const result = await adminQuery({
        query: USER_QUERY,
        variables: { id: testUsers.developer.id },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.user.id).toBe(testUsers.developer.id);
    });
  });
});
```

## 3. Mutation 测试

### 创建和更新测试

```typescript
// tests/mutations/user.test.ts
import { gql } from 'apollo-server-express';
import { createTestServer, createAuthContext, resetTestDatabase, testUsers } from '../setup';

describe('User Mutations', () => {
  beforeEach(() => {
    resetTestDatabase();
  });

  describe('createUser mutation', () => {
    const CREATE_USER_MUTATION = gql`
      mutation CreateUser($input: CreateUserInput!) {
        createUser(input: $input) {
          id
          username
          email
          role
          status
        }
      }
    `;

    it('should create user as admin', async () => {
      const { mutate } = createTestServer(createAuthContext(testUsers.admin));

      const input = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: 'DEVELOPER',
      };

      const result = await mutate({
        mutation: CREATE_USER_MUTATION,
        variables: { input },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.createUser.username).toBe('newuser');
      expect(result.data.createUser.email).toBe('<EMAIL>');
      expect(result.data.createUser.role).toBe('DEVELOPER');
    });

    it('should deny user creation for non-admin', async () => {
      const { mutate } = createTestServer(createAuthContext(testUsers.developer));

      const input = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: 'DEVELOPER',
      };

      const result = await mutate({
        mutation: CREATE_USER_MUTATION,
        variables: { input },
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].extensions.code).toBe('FORBIDDEN');
    });

    it('should validate required fields', async () => {
      const { mutate } = createTestServer(createAuthContext(testUsers.admin));

      const input = {
        username: '', // 无效的用户名
        email: 'invalid-email', // 无效的邮箱
        password: '123', // 密码太短
      };

      const result = await mutate({
        mutation: CREATE_USER_MUTATION,
        variables: { input },
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].extensions.code).toBe('VALIDATION_ERROR');
    });

    it('should prevent duplicate email', async () => {
      const { mutate } = createTestServer(createAuthContext(testUsers.admin));

      const input = {
        username: 'newuser',
        email: testUsers.developer.email, // 已存在的邮箱
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: 'DEVELOPER',
      };

      const result = await mutate({
        mutation: CREATE_USER_MUTATION,
        variables: { input },
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].extensions.code).toBe('DUPLICATE_ERROR');
    });
  });

  describe('updateUser mutation', () => {
    const UPDATE_USER_MUTATION = gql`
      mutation UpdateUser($id: ID!, $input: UpdateUserInput!) {
        updateUser(id: $id, input: $input) {
          id
          username
          email
          firstName
          lastName
          role
        }
      }
    `;

    it('should allow user to update own profile', async () => {
      const { mutate } = createTestServer(createAuthContext(testUsers.developer));

      const input = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const result = await mutate({
        mutation: UPDATE_USER_MUTATION,
        variables: {
          id: testUsers.developer.id,
          input,
        },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.updateUser.firstName).toBe('Updated');
      expect(result.data.updateUser.lastName).toBe('Name');
    });

    it('should deny role change for non-admin', async () => {
      const { mutate } = createTestServer(createAuthContext(testUsers.developer));

      const input = {
        role: 'ADMIN',
      };

      const result = await mutate({
        mutation: UPDATE_USER_MUTATION,
        variables: {
          id: testUsers.developer.id,
          input,
        },
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].extensions.code).toBe('FORBIDDEN');
    });

    it('should allow admin to update any user', async () => {
      const { mutate } = createTestServer(createAuthContext(testUsers.admin));

      const input = {
        role: 'MANAGER',
        status: 'INACTIVE',
      };

      const result = await mutate({
        mutation: UPDATE_USER_MUTATION,
        variables: {
          id: testUsers.developer.id,
          input,
        },
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.updateUser.role).toBe('MANAGER');
    });
  });
});
```

## 4. 订阅测试

### WebSocket 订阅测试

```typescript
// tests/subscriptions/task.test.ts
import { createTestClient } from 'apollo-server-testing';
import { gql } from 'apollo-server-express';
import { EventPublisher } from '../../src/pubsub/PubSubManager';
import { createTestServer, createAuthContext, resetTestDatabase, testUsers, testTasks } from '../setup';

describe('Task Subscriptions', () => {
  beforeEach(() => {
    resetTestDatabase();
  });

  describe('taskCreated subscription', () => {
    const TASK_CREATED_SUBSCRIPTION = gql`
      subscription TaskCreated {
        taskCreated {
          id
          title
          status
          project {
            id
            name
          }
        }
      }
    `;

    it('should receive task creation events for project members', async () => {
      const { subscribe } = createTestServer(createAuthContext(testUsers.developer));

      const subscription = await subscribe({
        query: TASK_CREATED_SUBSCRIPTION,
      });

      // 模拟任务创建
      const newTask = {
        ...testTasks.task1,
        id: 'new-task-1',
        title: 'New Task',
      };

      EventPublisher.publishTaskCreated(newTask, testTasks.task1.projectId);

      // 等待订阅事件
      const result = await subscription.next();

      expect(result.value.data.taskCreated.id).toBe('new-task-1');
      expect(result.value.data.taskCreated.title).toBe('New Task');
    });

    it('should not receive events for non-project members', async () => {
      // 创建一个不是项目成员的用户
      const outsideUser = {
        id: 'outside-user',
        username: 'outsider',
        role: 'DEVELOPER',
      };

      const { subscribe } = createTestServer(createAuthContext(outsideUser));

      const subscription = await subscribe({
        query: TASK_CREATED_SUBSCRIPTION,
      });

      // 模拟任务创建
      const newTask = {
        ...testTasks.task1,
        id: 'new-task-1',
        title: 'New Task',
      };

      EventPublisher.publishTaskCreated(newTask, testTasks.task1.projectId);

      // 应该不会收到事件
      const result = await Promise.race([
        subscription.next(),
        new Promise(resolve => setTimeout(() => resolve({ timeout: true }), 1000)),
      ]);

      expect(result).toEqual({ timeout: true });
    });
  });
});
```

## 5. 集成测试

### 端到端测试

```typescript
// tests/integration/workflow.test.ts
import { gql } from 'apollo-server-express';
import { createTestServer, createAuthContext, resetTestDatabase, testUsers } from '../setup';

describe('Project Workflow Integration', () => {
  beforeEach(() => {
    resetTestDatabase();
  });

  it('should complete full project workflow', async () => {
    // 1. 管理员创建项目
    const { mutate: adminMutate, query: adminQuery } = createTestServer(
      createAuthContext(testUsers.admin)
    );

    const CREATE_PROJECT = gql`
      mutation CreateProject($input: CreateProjectInput!) {
        createProject(input: $input) {
          id
          name
          status
          owner {
            id
            username
          }
        }
      }
    `;

    const projectResult = await adminMutate({
      mutation: CREATE_PROJECT,
      variables: {
        input: {
          name: 'Integration Test Project',
          description: 'Test project for integration testing',
          status: 'ACTIVE',
        },
      },
    });

    expect(projectResult.errors).toBeUndefined();
    const projectId = projectResult.data.createProject.id;

    // 2. 添加项目成员
    const ADD_MEMBER = gql`
      mutation AddProjectMember($projectId: ID!, $userId: ID!) {
        addProjectMember(projectId: $projectId, userId: $userId) {
          id
          members {
            id
            username
          }
        }
      }
    `;

    const memberResult = await adminMutate({
      mutation: ADD_MEMBER,
      variables: {
        projectId,
        userId: testUsers.developer.id,
      },
    });

    expect(memberResult.errors).toBeUndefined();
    expect(memberResult.data.addProjectMember.members).toHaveLength(2);

    // 3. 开发者创建任务
    const { mutate: devMutate } = createTestServer(createAuthContext(testUsers.developer));

    const CREATE_TASK = gql`
      mutation CreateTask($input: CreateTaskInput!) {
        createTask(input: $input) {
          id
          title
          status
          assignee {
            id
            username
          }
        }
      }
    `;

    const taskResult = await devMutate({
      mutation: CREATE_TASK,
      variables: {
        input: {
          title: 'Integration Test Task',
          description: 'Test task for integration testing',
          projectId,
          assigneeId: testUsers.developer.id,
          priority: 'MEDIUM',
        },
      },
    });

    expect(taskResult.errors).toBeUndefined();
    const taskId = taskResult.data.createTask.id;

    // 4. 更新任务状态
    const UPDATE_TASK = gql`
      mutation UpdateTask($id: ID!, $input: UpdateTaskInput!) {
        updateTask(id: $id, input: $input) {
          id
          status
          updatedAt
        }
      }
    `;

    const updateResult = await devMutate({
      mutation: UPDATE_TASK,
      variables: {
        id: taskId,
        input: {
          status: 'IN_PROGRESS',
        },
      },
    });

    expect(updateResult.errors).toBeUndefined();
    expect(updateResult.data.updateTask.status).toBe('IN_PROGRESS');

    // 5. 验证项目统计
    const PROJECT_STATS = gql`
      query ProjectAnalytics($projectId: ID!) {
        projectAnalytics(projectId: $projectId) {
          project {
            id
            name
          }
          taskDistribution {
            byStatus {
              todo
              inProgress
              done
            }
          }
          progress {
            totalTasks
            completedTasks
            completionPercentage
          }
        }
      }
    `;

    const statsResult = await adminQuery({
      query: PROJECT_STATS,
      variables: { projectId },
    });

    expect(statsResult.errors).toBeUndefined();
    expect(statsResult.data.projectAnalytics.taskDistribution.byStatus.inProgress).toBe(1);
    expect(statsResult.data.projectAnalytics.progress.totalTasks).toBe(1);
  });
});
```
