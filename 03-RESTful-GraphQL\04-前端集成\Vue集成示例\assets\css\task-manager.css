/* Vue.js 任务管理系统样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.api-switcher {
    display: flex;
    gap: 10px;
}

.api-btn {
    padding: 10px 20px;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.api-btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
}

.api-btn.active {
    background: white;
    color: #667eea;
    border-color: white;
}

.main {
    padding: 20px 0;
}

/* 错误提示 */
.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border-left: 4px solid #e74c3c;
}

.alert-close {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 16px;
}

/* 统计面板 */
.stats-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: #667eea;
}

.stat-icon.completed {
    background: #27ae60;
}

.stat-icon.progress {
    background: #f39c12;
}

.stat-icon.high-priority {
    background: #e74c3c;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

/* 操作面板 */
.action-panel {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

/* 过滤器 */
.filters {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    align-items: center;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.filter-select {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 新建任务表单 */
.new-task-form {
    border-top: 1px solid #eee;
    padding-top: 25px;
}

.new-task-form h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.form-input,
.form-select,
.form-textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-input {
    flex: 1;
    min-width: 200px;
}

.form-select {
    min-width: 150px;
    background: white;
    cursor: pointer;
}

.form-textarea {
    width: 100%;
    resize: vertical;
    font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 按钮样式 */
.btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-primary {
    background: #667eea;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-danger {
    background: #e74c3c;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-success {
    background: #27ae60;
}

.btn-success:hover {
    background: #229954;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 任务列表 */
.task-list {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

.task-grid {
    display: grid;
    gap: 20px;
}

.task-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s;
    position: relative;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.task-card.editing {
    border-color: #667eea;
    background: #f0f4ff;
}

.task-content {
    position: relative;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 15px;
}

.task-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    color: #2c3e50;
    flex: 1;
}

.task-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
}

.priority-badge {
    /* 颜色通过内联样式设置 */
}

.status-badge {
    /* 颜色通过内联样式设置 */
}

.task-description {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.6;
}

.task-meta {
    display: flex;
    gap: 20px;
    font-size: 0.9rem;
    color: #999;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.task-assignee,
.task-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.task-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 编辑表单 */
.edit-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 2px solid #667eea;
}

.edit-input,
.edit-select,
.edit-textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 15px;
    transition: border-color 0.3s;
}

.edit-input:focus,
.edit-select:focus,
.edit-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.edit-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.edit-select {
    flex: 1;
    background: white;
    cursor: pointer;
}

.edit-textarea {
    resize: vertical;
    font-family: inherit;
}

.edit-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 动画效果 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header .container {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .title {
        font-size: 1.5rem;
    }

    .api-switcher {
        justify-content: center;
    }

    .stats-panel {
        grid-template-columns: repeat(2, 1fr);
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .form-row {
        flex-direction: column;
    }

    .form-input {
        min-width: auto;
    }

    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .task-badges {
        align-self: flex-start;
    }

    .task-actions {
        justify-content: flex-start;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .header {
        padding: 15px 0;
    }

    .title {
        font-size: 1.3rem;
    }

    .api-btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .stats-panel {
        grid-template-columns: 1fr;
    }

    .action-panel,
    .task-list {
        padding: 20px;
    }

    .task-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .edit-controls {
        flex-direction: column;
    }

    .edit-actions {
        justify-content: stretch;
    }

    .edit-actions .btn {
        flex: 1;
        justify-content: center;
    }
}
