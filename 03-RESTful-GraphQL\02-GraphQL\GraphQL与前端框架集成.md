# GraphQL 与前端框架集成

## 1. React + Apollo Client

### 1.1 基础配置
```javascript
// 安装依赖
// npm install @apollo/client graphql

import { ApolloClient, InMemoryCache, ApolloProvider, gql } from '@apollo/client';

// 创建Apollo Client
const client = new ApolloClient({
  uri: 'http://localhost:4000/graphql',
  cache: new InMemoryCache()
});

// 根组件包装
function App() {
  return (
    <ApolloProvider client={client}>
      <div className="App">
        <Users />
      </div>
    </ApolloProvider>
  );
}
```

### 1.2 查询数据
```javascript
import { useQuery, gql } from '@apollo/client';

const GET_USERS = gql`
  query GetUsers {
    users {
      id
      name
      email
    }
  }
`;

function Users() {
  const { loading, error, data } = useQuery(GET_USERS);

  if (loading) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  return (
    <ul>
      {data.users.map(user => (
        <li key={user.id}>
          {user.name} - {user.email}
        </li>
      ))}
    </ul>
  );
}
```

### 1.3 变更数据
```javascript
import { useMutation, gql } from '@apollo/client';

const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      id
      name
      email
    }
  }
`;

function CreateUser() {
  const [createUser, { loading, error }] = useMutation(CREATE_USER, {
    // 更新缓存
    update(cache, { data: { createUser } }) {
      cache.modify({
        fields: {
          users(existingUsers = []) {
            const newUserRef = cache.writeFragment({
              data: createUser,
              fragment: gql`
                fragment NewUser on User {
                  id
                  name
                  email
                }
              `
            });
            return [...existingUsers, newUserRef];
          }
        }
      });
    }
  });

  const handleSubmit = (event) => {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    createUser({
      variables: {
        input: {
          name: formData.get('name'),
          email: formData.get('email')
        }
      }
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <input name="name" placeholder="Name" required />
      <input name="email" type="email" placeholder="Email" required />
      <button type="submit" disabled={loading}>
        {loading ? 'Creating...' : 'Create User'}
      </button>
      {error && <p>Error: {error.message}</p>}
    </form>
  );
}
```

### 1.4 实时订阅
```javascript
import { useSubscription, gql } from '@apollo/client';

const MESSAGE_SUBSCRIPTION = gql`
  subscription OnMessageAdded {
    messageAdded {
      id
      content
      user {
        name
      }
    }
  }
`;

function Messages() {
  const { data, loading } = useSubscription(MESSAGE_SUBSCRIPTION);

  if (loading) return <p>Connecting...</p>;

  return (
    <div>
      {data && (
        <div>
          New message from {data.messageAdded.user.name}: {data.messageAdded.content}
        </div>
      )}
    </div>
  );
}
```

## 2. Vue.js + Apollo Client

### 2.1 基础配置
```javascript
// 安装依赖
// npm install @apollo/client @vue/apollo-composable graphql

import { createApp, provide, h } from 'vue';
import { ApolloClient, InMemoryCache } from '@apollo/client/core';
import { DefaultApolloClient } from '@vue/apollo-composable';

const apolloClient = new ApolloClient({
  uri: 'http://localhost:4000/graphql',
  cache: new InMemoryCache()
});

const app = createApp({
  setup() {
    provide(DefaultApolloClient, apolloClient);
  },
  render: () => h(App)
});
```

### 2.2 Composition API使用
```vue
<template>
  <div>
    <div v-if="loading">Loading...</div>
    <div v-else-if="error">Error: {{ error.message }}</div>
    <ul v-else>
      <li v-for="user in users" :key="user.id">
        {{ user.name }} - {{ user.email }}
      </li>
    </ul>
  </div>
</template>

<script>
import { useQuery, gql } from '@vue/apollo-composable';

const GET_USERS = gql`
  query GetUsers {
    users {
      id
      name
      email
    }
  }
`;

export default {
  setup() {
    const { result, loading, error } = useQuery(GET_USERS);
    
    const users = computed(() => result.value?.users ?? []);
    
    return {
      users,
      loading,
      error
    };
  }
};
</script>
```

### 2.3 变更操作
```vue
<template>
  <form @submit.prevent="handleSubmit">
    <input v-model="form.name" placeholder="Name" required />
    <input v-model="form.email" type="email" placeholder="Email" required />
    <button type="submit" :disabled="loading">
      {{ loading ? 'Creating...' : 'Create User' }}
    </button>
    <div v-if="error">Error: {{ error.message }}</div>
  </form>
</template>

<script>
import { reactive } from 'vue';
import { useMutation, gql } from '@vue/apollo-composable';

const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      id
      name
      email
    }
  }
`;

export default {
  setup() {
    const form = reactive({
      name: '',
      email: ''
    });
    
    const { mutate: createUser, loading, error } = useMutation(CREATE_USER);
    
    const handleSubmit = async () => {
      try {
        await createUser({
          input: { ...form }
        });
        form.name = '';
        form.email = '';
      } catch (err) {
        console.error('Error creating user:', err);
      }
    };
    
    return {
      form,
      handleSubmit,
      loading,
      error
    };
  }
};
</script>
```

## 3. Angular + Apollo Angular

### 3.1 基础配置
```typescript
// 安装依赖
// npm install apollo-angular @apollo/client graphql

// app.module.ts
import { NgModule } from '@angular/core';
import { ApolloModule, APOLLO_OPTIONS } from 'apollo-angular';
import { ApolloClient, InMemoryCache } from '@apollo/client/core';
import { HttpLink } from 'apollo-angular/http';

@NgModule({
  imports: [ApolloModule],
  providers: [
    {
      provide: APOLLO_OPTIONS,
      useFactory: (httpLink: HttpLink) => {
        return new ApolloClient({
          link: httpLink.create({ uri: 'http://localhost:4000/graphql' }),
          cache: new InMemoryCache()
        });
      },
      deps: [HttpLink]
    }
  ]
})
export class AppModule {}
```

### 3.2 服务中使用
```typescript
// user.service.ts
import { Injectable } from '@angular/core';
import { Apollo, gql } from 'apollo-angular';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

const GET_USERS = gql`
  query GetUsers {
    users {
      id
      name
      email
    }
  }
`;

const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      id
      name
      email
    }
  }
`;

@Injectable({
  providedIn: 'root'
})
export class UserService {
  constructor(private apollo: Apollo) {}

  getUsers(): Observable<any[]> {
    return this.apollo
      .watchQuery({ query: GET_USERS })
      .valueChanges
      .pipe(
        map((result: any) => result.data.users)
      );
  }

  createUser(input: any): Observable<any> {
    return this.apollo
      .mutate({
        mutation: CREATE_USER,
        variables: { input }
      })
      .pipe(
        map((result: any) => result.data.createUser)
      );
  }
}
```

### 3.3 组件中使用
```typescript
// user-list.component.ts
import { Component, OnInit } from '@angular/core';
import { UserService } from './user.service';

@Component({
  selector: 'app-user-list',
  template: `
    <div *ngIf="loading">Loading...</div>
    <ul *ngIf="!loading">
      <li *ngFor="let user of users">
        {{ user.name }} - {{ user.email }}
      </li>
    </ul>
  `
})
export class UserListComponent implements OnInit {
  users: any[] = [];
  loading = true;

  constructor(private userService: UserService) {}

  ngOnInit() {
    this.userService.getUsers().subscribe(
      users => {
        this.users = users;
        this.loading = false;
      },
      error => {
        console.error('Error loading users:', error);
        this.loading = false;
      }
    );
  }
}
```

## 4. Svelte + GraphQL

### 4.1 基础配置
```javascript
// 安装依赖
// npm install @apollo/client graphql

// apollo.js
import { ApolloClient, InMemoryCache } from '@apollo/client/core';

export const client = new ApolloClient({
  uri: 'http://localhost:4000/graphql',
  cache: new InMemoryCache()
});
```

### 4.2 Svelte组件使用
```svelte
<!-- UserList.svelte -->
<script>
  import { onMount } from 'svelte';
  import { client } from './apollo.js';
  import { gql } from '@apollo/client/core';

  const GET_USERS = gql`
    query GetUsers {
      users {
        id
        name
        email
      }
    }
  `;

  let users = [];
  let loading = true;
  let error = null;

  onMount(async () => {
    try {
      const result = await client.query({ query: GET_USERS });
      users = result.data.users;
    } catch (err) {
      error = err.message;
    } finally {
      loading = false;
    }
  });
</script>

{#if loading}
  <p>Loading...</p>
{:else if error}
  <p>Error: {error}</p>
{:else}
  <ul>
    {#each users as user (user.id)}
      <li>{user.name} - {user.email}</li>
    {/each}
  </ul>
{/if}
```

## 5. 通用最佳实践

### 5.1 错误处理
```javascript
// 全局错误处理
import { onError } from '@apollo/client/link/error';

const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(`GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`);
    });
  }

  if (networkError) {
    console.error(`Network error: ${networkError}`);
  }
});

const client = new ApolloClient({
  link: from([errorLink, httpLink]),
  cache: new InMemoryCache()
});
```

### 5.2 加载状态管理
```javascript
// 自定义Hook for React
import { useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';

function useQueryWithLoading(query, options = {}) {
  const [globalLoading, setGlobalLoading] = useState(false);
  const queryResult = useQuery(query, options);

  useEffect(() => {
    setGlobalLoading(queryResult.loading);
  }, [queryResult.loading]);

  return {
    ...queryResult,
    globalLoading
  };
}
```

### 5.3 缓存优化
```javascript
// 类型策略配置
const cache = new InMemoryCache({
  typePolicies: {
    User: {
      fields: {
        posts: {
          merge(existing = [], incoming) {
            return [...existing, ...incoming];
          }
        }
      }
    },
    Query: {
      fields: {
        users: {
          merge(existing = [], incoming) {
            return incoming;
          }
        }
      }
    }
  }
});
```

### 5.4 代码生成
```bash
# 安装GraphQL代码生成工具
npm install -D @graphql-codegen/cli @graphql-codegen/typescript @graphql-codegen/typescript-operations

# codegen.yml
schema: 'http://localhost:4000/graphql'
documents: 'src/**/*.graphql'
generates:
  src/generated/graphql.ts:
    plugins:
      - typescript
      - typescript-operations
```

---

**总结：**
GraphQL与各种前端框架都有很好的集成支持，选择合适的客户端库和配置方式可以大大提高开发效率。关键是要理解GraphQL的核心概念，然后根据项目需求选择合适的工具和模式。
