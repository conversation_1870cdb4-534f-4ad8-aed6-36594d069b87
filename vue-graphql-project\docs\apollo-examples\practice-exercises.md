# Apollo Server Express 实践练习

## 练习目标
通过这些练习，您将掌握 Apollo Server Express 的核心概念和实际应用技能。

## 练习 1：基础服务器搭建

### 任务描述
创建一个简单的图书管理 GraphQL API

### 要求
1. 定义 Book 类型，包含：id, title, author, publishYear, genre
2. 实现以下查询：
   - `books`: 获取所有图书
   - `book(id: ID!)`: 根据ID获取图书
   - `booksByAuthor(author: String!)`: 根据作者获取图书
3. 实现以下变更：
   - `addBook(input: BookInput!)`: 添加新图书
   - `updateBook(id: ID!, input: BookUpdateInput!)`: 更新图书
   - `deleteBook(id: ID!)`: 删除图书

### 代码框架
```typescript
import { gql } from 'apollo-server-express';

// 1. 定义类型
const typeDefs = gql`
  type Book {
    # TODO: 定义 Book 类型字段
  }

  input BookInput {
    # TODO: 定义输入类型
  }

  input BookUpdateInput {
    # TODO: 定义更新输入类型
  }

  type Query {
    # TODO: 定义查询
  }

  type Mutation {
    # TODO: 定义变更
  }
`;

// 2. 模拟数据
let books = [
  // TODO: 添加示例数据
];

// 3. 实现 resolvers
const resolvers = {
  Query: {
    // TODO: 实现查询解析器
  },
  Mutation: {
    // TODO: 实现变更解析器
  }
};
```

### 测试查询
```graphql
# 获取所有图书
query {
  books {
    id
    title
    author
    publishYear
    genre
  }
}

# 添加新图书
mutation {
  addBook(input: {
    title: "GraphQL 实战"
    author: "张三"
    publishYear: 2023
    genre: "技术"
  }) {
    id
    title
    author
  }
}
```

## 练习 2：认证和授权

### 任务描述
为图书管理系统添加用户认证和权限控制

### 要求
1. 添加用户注册和登录功能
2. 实现 JWT 认证
3. 添加角色权限控制：
   - 普通用户：只能查看图书
   - 管理员：可以增删改图书
4. 保护需要认证的端点

### 代码框架
```typescript
// 1. 扩展类型定义
const typeDefs = gql`
  enum UserRole {
    USER
    ADMIN
  }

  type User {
    # TODO: 定义用户类型
  }

  type AuthPayload {
    # TODO: 定义认证返回类型
  }

  input RegisterInput {
    # TODO: 定义注册输入
  }

  input LoginInput {
    # TODO: 定义登录输入
  }

  extend type Query {
    me: User
  }

  extend type Mutation {
    register(input: RegisterInput!): AuthPayload!
    login(input: LoginInput!): AuthPayload!
  }
`;

// 2. 认证中间件
const getUser = async (token: string) => {
  // TODO: 实现用户认证逻辑
};

// 3. 权限检查函数
const requireAuth = (resolver: any) => {
  // TODO: 实现认证检查
};

const requireRole = (role: string) => (resolver: any) => {
  // TODO: 实现角色检查
};
```

## 练习 3：实时订阅

### 任务描述
为图书管理系统添加实时功能

### 要求
1. 当有新图书添加时，推送通知给所有订阅者
2. 当图书信息更新时，推送更新通知
3. 实现 WebSocket 连接管理

### 代码框架
```typescript
import { PubSub } from 'graphql-subscriptions';

const pubsub = new PubSub();

// 1. 扩展类型定义
const typeDefs = gql`
  extend type Subscription {
    bookAdded: Book!
    bookUpdated: Book!
    bookDeleted: ID!
  }
`;

// 2. 实现订阅解析器
const resolvers = {
  Subscription: {
    bookAdded: {
      // TODO: 实现图书添加订阅
    },
    bookUpdated: {
      // TODO: 实现图书更新订阅
    },
    bookDeleted: {
      // TODO: 实现图书删除订阅
    }
  },
  
  Mutation: {
    addBook: async (parent, { input }) => {
      // TODO: 添加图书并发布事件
    }
  }
};
```

### 测试订阅
```graphql
# 订阅新图书
subscription {
  bookAdded {
    id
    title
    author
  }
}

# 订阅图书更新
subscription {
  bookUpdated {
    id
    title
    author
  }
}
```

## 练习 4：错误处理和验证

### 任务描述
实现完善的错误处理和输入验证

### 要求
1. 自定义错误类型
2. 输入验证
3. 全局错误处理
4. 错误日志记录

### 代码框架
```typescript
import { 
  AuthenticationError, 
  ForbiddenError, 
  UserInputError,
  ApolloError 
} from 'apollo-server-express';

// 1. 自定义错误类
class BookNotFoundError extends ApolloError {
  constructor() {
    super('Book not found', 'BOOK_NOT_FOUND');
  }
}

// 2. 验证函数
const validateBookInput = (input: any) => {
  // TODO: 实现输入验证
};

// 3. 错误处理中间件
const withErrorHandling = (resolver: any) => {
  return async (...args: any[]) => {
    try {
      return await resolver(...args);
    } catch (error) {
      // TODO: 实现错误处理逻辑
      throw error;
    }
  };
};

// 4. 全局错误格式化
const server = new ApolloServer({
  typeDefs,
  resolvers,
  formatError: (error) => {
    // TODO: 实现错误格式化
    return error;
  }
});
```

## 练习 5：性能优化

### 任务描述
优化 GraphQL API 性能

### 要求
1. 使用 DataLoader 解决 N+1 查询问题
2. 实现查询复杂度限制
3. 添加缓存机制
4. 实现分页功能

### 代码框架
```typescript
import DataLoader from 'dataloader';

// 1. 创建 DataLoader
const createLoaders = () => ({
  bookLoader: new DataLoader(async (bookIds: string[]) => {
    // TODO: 批量加载图书
  }),
  
  authorBooksLoader: new DataLoader(async (authorNames: string[]) => {
    // TODO: 批量加载作者的图书
  })
});

// 2. 分页类型
const typeDefs = gql`
  type BookConnection {
    books: [Book!]!
    pageInfo: PageInfo!
  }

  type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
  }

  extend type Query {
    booksPaginated(
      first: Int
      after: String
      last: Int
      before: String
    ): BookConnection!
  }
`;

// 3. 实现分页查询
const resolvers = {
  Query: {
    booksPaginated: async (parent, args) => {
      // TODO: 实现分页逻辑
    }
  }
};
```

## 练习答案提示

### 练习 1 答案提示
- Book 类型应包含所有必要字段
- 使用数组模拟数据库操作
- 实现基本的 CRUD 操作
- 注意错误处理（如图书不存在）

### 练习 2 答案提示
- 使用 bcryptjs 加密密码
- 使用 jsonwebtoken 生成和验证 JWT
- 在 context 中传递用户信息
- 使用高阶函数实现权限检查

### 练习 3 答案提示
- 使用 PubSub 发布和订阅事件
- 在变更操作中发布相应事件
- 配置 WebSocket 服务器
- 处理连接和断开事件

### 练习 4 答案提示
- 使用 Apollo Server 内置错误类型
- 实现输入验证函数
- 使用 try-catch 包装解析器
- 在 formatError 中统一处理错误格式

### 练习 5 答案提示
- DataLoader 用于批量加载相关数据
- 使用 graphql-query-complexity 限制查询复杂度
- 实现基于游标的分页
- 考虑使用 Redis 等缓存方案

## 扩展练习

1. **集成数据库**：将模拟数据替换为真实数据库（如 MongoDB、PostgreSQL）
2. **文件上传**：实现图书封面上传功能
3. **搜索功能**：添加全文搜索功能
4. **API 版本控制**：实现 GraphQL Schema 版本管理
5. **监控和日志**：集成 APM 工具和结构化日志

## 学习资源

- [Apollo Server 官方文档](https://www.apollographql.com/docs/apollo-server/)
- [GraphQL 规范](https://spec.graphql.org/)
- [GraphQL 最佳实践](https://graphql.org/learn/best-practices/)
- [Apollo Federation](https://www.apollographql.com/docs/federation/)

通过完成这些练习，您将全面掌握 Apollo Server Express 的使用技巧和最佳实践。
