# 策略模式入门详解

## 🎯 什么是策略模式？

### 生活中的例子
想象一下，您要从家里到公司上班，有多种方式：
- 开车：快但费油，可能堵车
- 坐地铁：便宜但可能拥挤
- 骑自行车：环保但费体力
- 打车：舒适但贵

**每种方式都是一个"策略"，目标都是"到达公司"，但实现方式不同。**

根据不同情况（天气、时间、预算），您会选择不同的策略。

### 编程中的策略模式
策略模式就是把这种思想应用到代码中：
- **目标相同**：要完成同一件事
- **方法不同**：有多种不同的实现方式
- **可以切换**：根据情况选择不同的方式

---

## 🚫 没有策略模式的问题

### 问题代码示例：计算商品折扣

```javascript
class PriceCalculator {
    calculateDiscount(price, customerType) {
        if (customerType === 'regular') {
            // 普通客户：无折扣
            return price;
        } else if (customerType === 'vip') {
            // VIP客户：9折
            return price * 0.9;
        } else if (customerType === 'premium') {
            // 高级客户：8折
            return price * 0.8;
        } else if (customerType === 'employee') {
            // 员工：7折
            return price * 0.7;
        } else {
            return price;
        }
    }
}

// 使用
const calculator = new PriceCalculator();
const finalPrice = calculator.calculateDiscount(100, 'vip'); // 90
```

### 这种写法的问题

1. **难以扩展**：每次添加新的客户类型，都要修改这个方法
2. **代码冗长**：if-else链条越来越长
3. **难以测试**：要测试所有分支
4. **违反开闭原则**：对修改开放，对扩展封闭

---

## ✅ 使用策略模式解决

### 第一步：定义策略接口（可选）

```javascript
// 在JavaScript中，我们可以不用显式定义接口
// 但为了理解，我们先看看策略应该有什么样的结构

// 所有折扣策略都应该有一个calculate方法
// calculate(price) => 返回折扣后的价格
```

### 第二步：创建具体策略

```javascript
// 普通客户策略
class RegularCustomerStrategy {
    calculate(price) {
        return price; // 无折扣
    }
}

// VIP客户策略
class VipCustomerStrategy {
    calculate(price) {
        return price * 0.9; // 9折
    }
}

// 高级客户策略
class PremiumCustomerStrategy {
    calculate(price) {
        return price * 0.8; // 8折
    }
}

// 员工策略
class EmployeeStrategy {
    calculate(price) {
        return price * 0.7; // 7折
    }
}
```

### 第三步：创建策略管理器

```javascript
class PriceCalculator {
    constructor() {
        // 存储所有可用的策略
        this.strategies = {
            'regular': new RegularCustomerStrategy(),
            'vip': new VipCustomerStrategy(),
            'premium': new PremiumCustomerStrategy(),
            'employee': new EmployeeStrategy()
        };
    }
    
    calculateDiscount(price, customerType) {
        // 根据客户类型选择对应的策略
        const strategy = this.strategies[customerType];
        
        if (!strategy) {
            throw new Error(`未知的客户类型: ${customerType}`);
        }
        
        // 使用选中的策略计算价格
        return strategy.calculate(price);
    }
}
```

### 第四步：使用策略模式

```javascript
// 使用方式和之前完全一样
const calculator = new PriceCalculator();

console.log(calculator.calculateDiscount(100, 'regular')); // 100
console.log(calculator.calculateDiscount(100, 'vip'));     // 90
console.log(calculator.calculateDiscount(100, 'premium')); // 80
console.log(calculator.calculateDiscount(100, 'employee')); // 70
```

---

## 🎉 策略模式的优势

### 1. 易于扩展
添加新的客户类型非常简单：

```javascript
// 新增：学生客户策略
class StudentStrategy {
    calculate(price) {
        return price * 0.85; // 8.5折
    }
}

// 只需要在策略管理器中注册新策略
const calculator = new PriceCalculator();
calculator.strategies['student'] = new StudentStrategy();

// 立即可以使用
console.log(calculator.calculateDiscount(100, 'student')); // 85
```

### 2. 易于测试
每个策略都可以独立测试：

```javascript
// 测试VIP策略
describe('VipCustomerStrategy', () => {
    it('should apply 10% discount', () => {
        const strategy = new VipCustomerStrategy();
        expect(strategy.calculate(100)).toBe(90);
    });
});

// 测试员工策略
describe('EmployeeStrategy', () => {
    it('should apply 30% discount', () => {
        const strategy = new EmployeeStrategy();
        expect(strategy.calculate(100)).toBe(70);
    });
});
```

### 3. 代码更清晰
每个策略的逻辑都在独立的类中，职责单一，易于理解。

---

## 🌟 更多实际例子

### 例子1：支付方式

```javascript
// 不同的支付策略
class CreditCardPayment {
    pay(amount) {
        console.log(`使用信用卡支付 ${amount} 元`);
        // 信用卡支付逻辑
        return { success: true, method: 'credit_card' };
    }
}

class AlipayPayment {
    pay(amount) {
        console.log(`使用支付宝支付 ${amount} 元`);
        // 支付宝支付逻辑
        return { success: true, method: 'alipay' };
    }
}

class WechatPayment {
    pay(amount) {
        console.log(`使用微信支付 ${amount} 元`);
        // 微信支付逻辑
        return { success: true, method: 'wechat' };
    }
}

// 支付管理器
class PaymentProcessor {
    constructor() {
        this.paymentMethods = {
            'credit_card': new CreditCardPayment(),
            'alipay': new AlipayPayment(),
            'wechat': new WechatPayment()
        };
    }
    
    processPayment(amount, method) {
        const paymentStrategy = this.paymentMethods[method];
        
        if (!paymentStrategy) {
            throw new Error(`不支持的支付方式: ${method}`);
        }
        
        return paymentStrategy.pay(amount);
    }
}

// 使用
const processor = new PaymentProcessor();
processor.processPayment(100, 'alipay');  // 使用支付宝支付 100 元
processor.processPayment(200, 'wechat');  // 使用微信支付 200 元
```

### 例子2：数据验证

```javascript
// 不同的验证策略
class EmailValidator {
    validate(value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return {
            isValid: emailRegex.test(value),
            message: emailRegex.test(value) ? '' : '邮箱格式不正确'
        };
    }
}

class PhoneValidator {
    validate(value) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return {
            isValid: phoneRegex.test(value),
            message: phoneRegex.test(value) ? '' : '手机号格式不正确'
        };
    }
}

class PasswordValidator {
    validate(value) {
        const isValid = value.length >= 8;
        return {
            isValid: isValid,
            message: isValid ? '' : '密码长度至少8位'
        };
    }
}

// 验证管理器
class FormValidator {
    constructor() {
        this.validators = {
            'email': new EmailValidator(),
            'phone': new PhoneValidator(),
            'password': new PasswordValidator()
        };
    }
    
    validate(value, type) {
        const validator = this.validators[type];
        
        if (!validator) {
            throw new Error(`未知的验证类型: ${type}`);
        }
        
        return validator.validate(value);
    }
}

// 使用
const formValidator = new FormValidator();

console.log(formValidator.validate('<EMAIL>', 'email'));
// { isValid: true, message: '' }

console.log(formValidator.validate('123', 'password'));
// { isValid: false, message: '密码长度至少8位' }
```

---

## 🔧 简化版本：使用函数

如果策略比较简单，也可以直接使用函数：

```javascript
// 折扣策略函数
const discountStrategies = {
    regular: (price) => price,
    vip: (price) => price * 0.9,
    premium: (price) => price * 0.8,
    employee: (price) => price * 0.7
};

// 简化的价格计算器
function calculateDiscount(price, customerType) {
    const strategy = discountStrategies[customerType];
    
    if (!strategy) {
        throw new Error(`未知的客户类型: ${customerType}`);
    }
    
    return strategy(price);
}

// 使用
console.log(calculateDiscount(100, 'vip')); // 90

// 添加新策略也很简单
discountStrategies.student = (price) => price * 0.85;
console.log(calculateDiscount(100, 'student')); // 85
```

---

## 🎯 什么时候使用策略模式？

### 适用场景
1. **有多种方式完成同一任务**
   - 不同的排序算法
   - 不同的支付方式
   - 不同的折扣计算

2. **需要在运行时选择算法**
   - 根据用户选择
   - 根据配置文件
   - 根据环境条件

3. **想要避免大量的if-else或switch语句**

### 不适用场景
1. **只有一种实现方式**
2. **策略很少变化**
3. **策略之间差异很小**

---

## 📚 总结

### 策略模式的核心思想
1. **定义一系列算法**：每个策略都是一个算法
2. **封装每个算法**：每个策略都在独立的类中
3. **使它们可以互换**：可以在运行时选择不同的策略

### 主要优点
- ✅ **易于扩展**：添加新策略不需要修改现有代码
- ✅ **易于测试**：每个策略可以独立测试
- ✅ **代码清晰**：避免了复杂的if-else链
- ✅ **符合开闭原则**：对扩展开放，对修改封闭

### 记忆口诀
**"同一目标，多种方法，随时切换，各自独立"**

策略模式就像是您的工具箱，里面有各种工具（策略），根据不同的任务（情况）选择合适的工具来完成工作！
