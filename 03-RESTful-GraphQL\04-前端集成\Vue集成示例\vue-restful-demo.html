<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js + RESTful API 集成示例</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="assets/css/restful-demo.css">
</head>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 头部 -->
            <div class="header">
                <h1>🚀 Vue.js + RESTful API 集成示例</h1>
                <p>学习如何在Vue.js中集成RESTful API进行CRUD操作</p>
            </div>

            <!-- 统计信息 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{{ users.length }}</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ activeUsers }}</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ totalRequests }}</div>
                    <div class="stat-label">API请求次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ responseTime }}ms</div>
                    <div class="stat-label">平均响应时间</div>
                </div>
            </div>

            <!-- 错误和成功消息 -->
            <div v-if="error" class="error">
                {{ error }}
                <button @click="error = ''" style="float: right; background: none; border: none; color: inherit; cursor: pointer;">✕</button>
            </div>
            
            <div v-if="success" class="success">
                {{ success }}
                <button @click="success = ''" style="float: right; background: none; border: none; color: inherit; cursor: pointer;">✕</button>
            </div>

            <!-- 用户管理表单 -->
            <div class="section">
                <h2>{{ editingUser ? '编辑用户' : '添加新用户' }}</h2>
                <form @submit.prevent="editingUser ? updateUser() : createUser()">
                    <div class="form-group">
                        <label for="name">姓名:</label>
                        <input 
                            type="text" 
                            id="name" 
                            v-model="form.name" 
                            required 
                            placeholder="请输入用户姓名"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="email">邮箱:</label>
                        <input 
                            type="email" 
                            id="email" 
                            v-model="form.email" 
                            required 
                            placeholder="请输入邮箱地址"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="role">角色:</label>
                        <select id="role" v-model="form.role" required>
                            <option value="">请选择角色</option>
                            <option value="admin">管理员</option>
                            <option value="user">普通用户</option>
                            <option value="moderator">版主</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="bio">个人简介:</label>
                        <textarea 
                            id="bio" 
                            v-model="form.bio" 
                            rows="3" 
                            placeholder="请输入个人简介（可选）"
                        ></textarea>
                    </div>
                    
                    <button type="submit" class="btn" :disabled="loading">
                        {{ loading ? '处理中...' : (editingUser ? '更新用户' : '创建用户') }}
                    </button>
                    
                    <button 
                        v-if="editingUser" 
                        type="button" 
                        class="btn btn-secondary" 
                        @click="cancelEdit()"
                    >
                        取消编辑
                    </button>
                </form>
            </div>

            <!-- 搜索和过滤 -->
            <div class="section">
                <h2>用户列表</h2>
                
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input 
                        type="text" 
                        class="search-input" 
                        v-model="searchQuery" 
                        placeholder="搜索用户姓名或邮箱..."
                        @input="searchUsers"
                    >
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label>按角色过滤: </label>
                    <select v-model="filterRole" @change="filterUsers">
                        <option value="">所有角色</option>
                        <option value="admin">管理员</option>
                        <option value="user">普通用户</option>
                        <option value="moderator">版主</option>
                    </select>
                    
                    <button class="btn btn-secondary" @click="refreshUsers" :disabled="loading">
                        {{ loading ? '刷新中...' : '刷新列表' }}
                    </button>
                </div>

                <!-- 加载状态 -->
                <div v-if="loading" class="loading">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>

                <!-- 用户列表 -->
                <div v-else-if="filteredUsers.length > 0" class="users-grid">
                    <div v-for="user in paginatedUsers" :key="user.id" class="user-card">
                        <img :src="user.avatar" :alt="user.name" class="user-avatar">
                        <div class="user-name">{{ user.name }}</div>
                        <div class="user-email">{{ user.email }}</div>
                        <div class="user-meta">
                            角色: {{ getRoleLabel(user.role) }} | 
                            创建时间: {{ formatDate(user.created_at) }}
                        </div>
                        <div v-if="user.bio" style="margin-bottom: 15px; font-size: 14px; color: #666;">
                            {{ user.bio }}
                        </div>
                        <div>
                            <button class="btn" @click="editUser(user)">编辑</button>
                            <button class="btn btn-danger" @click="deleteUser(user.id)" :disabled="loading">
                                删除
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-else style="text-align: center; padding: 40px; color: #666;">
                    <p>{{ searchQuery || filterRole ? '没有找到匹配的用户' : '暂无用户数据' }}</p>
                </div>

                <!-- 分页 -->
                <div v-if="totalPages > 1" class="pagination">
                    <button 
                        class="page-btn" 
                        @click="currentPage = 1" 
                        :disabled="currentPage === 1"
                    >
                        首页
                    </button>
                    <button 
                        class="page-btn" 
                        @click="currentPage--" 
                        :disabled="currentPage === 1"
                    >
                        上一页
                    </button>
                    
                    <span v-for="page in visiblePages" :key="page">
                        <button 
                            class="page-btn" 
                            :class="{ active: page === currentPage }"
                            @click="currentPage = page"
                        >
                            {{ page }}
                        </button>
                    </span>
                    
                    <button 
                        class="page-btn" 
                        @click="currentPage++" 
                        :disabled="currentPage === totalPages"
                    >
                        下一页
                    </button>
                    <button 
                        class="page-btn" 
                        @click="currentPage = totalPages" 
                        :disabled="currentPage === totalPages"
                    >
                        末页
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入外部JavaScript文件 -->
    <script type="module" src="assets/js/restful-demo.js"></script>
</body>
</html>
