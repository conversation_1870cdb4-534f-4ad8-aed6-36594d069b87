# Git 远程仓库基础

## 🌐 远程仓库概念

### 📋 什么是远程仓库？
远程仓库是托管在网络上的 Git 仓库，用于团队协作和代码备份。它允许多个开发者共享代码，同步修改，协同开发。

```bash
# 远程仓库的作用：
├── 代码备份和存储
├── 团队协作和共享
├── 版本同步和管理
├── 代码审查和质量控制
└── 持续集成和部署
```

### 🔗 常见远程仓库平台
```bash
GitHub      # 最流行的代码托管平台
GitLab      # 企业级 DevOps 平台
Bitbucket   # Atlassian 的代码托管服务
Gitee       # 国内的代码托管平台
Azure DevOps # 微软的开发平台
```

## 🔧 远程仓库管理

### 📋 git remote - 远程仓库操作

#### 查看远程仓库
```bash
# 查看远程仓库列表
git remote

# 查看远程仓库详细信息
git remote -v
# origin  https://github.com/user/repo.git (fetch)
# origin  https://github.com/user/repo.git (push)

# 查看特定远程仓库信息
git remote show origin
```

#### 添加远程仓库
```bash
# 添加远程仓库
git remote add origin https://github.com/user/repo.git

# 添加多个远程仓库
git remote add upstream https://github.com/original/repo.git
git remote add backup https://gitlab.com/user/repo.git

# 添加 SSH 远程仓库
git remote <NAME_EMAIL>:user/repo.git
```

#### 修改远程仓库
```bash
# 修改远程仓库 URL
git remote set-url origin https://github.com/newuser/repo.git

# 重命名远程仓库
git remote rename origin main-repo

# 删除远程仓库
git remote remove backup
git remote rm backup
```

### 🔍 远程仓库信息查看
```bash
# 查看远程分支
git branch -r
git branch --remotes

# 查看所有分支（本地+远程）
git branch -a
git branch --all

# 查看远程仓库的详细信息
git ls-remote origin

# 查看远程仓库的引用
git show-ref
```

## 📥 获取远程更新

### 🔄 git fetch - 获取远程更新

#### 基本用法
```bash
# 获取默认远程仓库的更新
git fetch

# 获取指定远程仓库的更新
git fetch origin

# 获取所有远程仓库的更新
git fetch --all

# 获取指定分支的更新
git fetch origin main
```

#### 高级选项
```bash
# 获取并删除远程已删除的分支引用
git fetch --prune
git fetch -p

# 获取标签
git fetch --tags

# 获取所有引用
git fetch --all --tags --prune

# 浅获取（只获取最近的提交）
git fetch --depth=1
```

#### fetch 后的操作
```bash
# 查看获取的更新
git log HEAD..origin/main

# 查看远程分支的差异
git diff HEAD origin/main

# 合并远程更新
git merge origin/main

# 或者使用变基
git rebase origin/main
```

### 📥 git pull - 拉取并合并

#### 基本用法
```bash
# 拉取并合并（fetch + merge）
git pull

# 拉取指定远程分支
git pull origin main

# 拉取并变基（fetch + rebase）
git pull --rebase
git pull -r

# 拉取时使用快进合并
git pull --ff-only
```

#### 配置默认行为
```bash
# 设置 pull 的默认策略
git config --global pull.rebase true    # 默认使用 rebase
git config --global pull.ff only        # 只允许快进合并

# 查看当前配置
git config pull.rebase
git config pull.ff
```

#### pull 的不同策略
```bash
# 合并策略（默认）
git pull origin main
# 等同于：
git fetch origin main
git merge origin/main

# 变基策略
git pull --rebase origin main
# 等同于：
git fetch origin main
git rebase origin/main

# 快进策略
git pull --ff-only origin main
# 只有在可以快进时才合并
```

## 📤 推送到远程仓库

### 🚀 git push - 推送更新

#### 基本用法
```bash
# 推送到默认远程分支
git push

# 推送到指定远程仓库和分支
git push origin main

# 首次推送并设置上游分支
git push -u origin main
git push --set-upstream origin main
```

#### 推送选项
```bash
# 推送所有分支
git push --all origin

# 推送标签
git push --tags origin
git push origin --tags

# 推送所有内容（分支+标签）
git push --all --tags origin

# 强制推送（危险操作）
git push --force origin main
git push -f origin main

# 安全的强制推送
git push --force-with-lease origin main
```

#### 删除远程分支和标签
```bash
# 删除远程分支
git push origin --delete feature-branch
git push origin :feature-branch

# 删除远程标签
git push origin --delete v1.0.0
git push origin :refs/tags/v1.0.0
```

### 🔧 推送配置
```bash
# 设置默认推送行为
git config --global push.default simple    # 推送当前分支到同名远程分支
git config --global push.default current   # 推送当前分支到同名远程分支
git config --global push.default upstream  # 推送到上游分支

# 自动设置上游分支
git config --global push.autoSetupRemote true

# 查看推送配置
git config push.default
```

## 🔄 远程分支管理

### 🌿 远程分支操作

#### 创建和跟踪远程分支
```bash
# 基于远程分支创建本地分支
git checkout -b local-branch origin/remote-branch

# 创建跟踪分支
git checkout --track origin/feature-branch

# 设置现有分支跟踪远程分支
git branch --set-upstream-to=origin/main main
git branch -u origin/main main
```

#### 查看分支跟踪关系
```bash
# 查看分支跟踪信息
git branch -vv

# 查看远程分支状态
git remote show origin

# 查看分支的上游信息
git status -b
```

#### 同步远程分支
```bash
# 清理已删除的远程分支引用
git remote prune origin

# 或者在 fetch 时自动清理
git fetch --prune origin

# 删除本地的远程跟踪分支
git branch -dr origin/deleted-branch
```

### 🔄 分支同步策略
```bash
# 策略1：定期同步主分支
git checkout main
git pull origin main
git checkout feature-branch
git rebase main

# 策略2：保持功能分支最新
git fetch origin
git rebase origin/main

# 策略3：合并前同步
git checkout main
git pull origin main
git checkout feature-branch
git merge main
```

## 🔐 远程仓库认证

### 🔑 HTTPS 认证
```bash
# 使用用户名密码（已弃用）
git clone https://username:<EMAIL>/user/repo.git

# 使用个人访问令牌
git clone https://<EMAIL>/user/repo.git

# 配置凭据存储
git config --global credential.helper store
git config --global credential.helper cache
```

### 🔐 SSH 认证
```bash
# 生成 SSH 密钥
ssh-keygen -t ed25519 -C "<EMAIL>"

# 添加密钥到 SSH 代理
ssh-add ~/.ssh/id_ed25519

# 测试 SSH 连接
ssh -T **************

# 使用 SSH URL
git remote set-<NAME_EMAIL>:user/repo.git
```

## 🚨 常见问题和解决方案

### 问题1：推送被拒绝
```bash
# 错误信息
! [rejected] main -> main (fetch first)

# 解决方案1：先拉取再推送
git pull origin main
git push origin main

# 解决方案2：使用变基
git pull --rebase origin main
git push origin main

# 解决方案3：强制推送（谨慎使用）
git push --force-with-lease origin main
```

### 问题2：远程分支不存在
```bash
# 错误信息
fatal: couldn't find remote ref feature-branch

# 解决方案：更新远程引用
git fetch origin
git remote prune origin
```

### 问题3：认证失败
```bash
# 错误信息
Authentication failed

# 解决方案1：更新凭据
git config --global credential.helper manager
git push origin main  # 会提示输入新凭据

# 解决方案2：使用 SSH
git remote set-<NAME_EMAIL>:user/repo.git
```

### 问题4：网络连接问题
```bash
# 配置代理
git config --global http.proxy http://proxy.company.com:8080
git config --global https.proxy https://proxy.company.com:8080

# 取消代理
git config --global --unset http.proxy
git config --global --unset https.proxy

# 增加超时时间
git config --global http.postBuffer *********
git config --global http.lowSpeedLimit 0
git config --global http.lowSpeedTime 999999
```

## 💡 最佳实践

### ✅ 远程仓库使用建议
1. **定期同步** - 经常 fetch 和 pull 保持代码最新
2. **小而频繁的推送** - 避免大量修改一次性推送
3. **使用 SSH** - 比 HTTPS 更安全和方便
4. **设置上游分支** - 简化推送和拉取操作
5. **谨慎强制推送** - 使用 --force-with-lease 而不是 --force

### 🔒 安全实践
```bash
# 1. 使用 SSH 密钥认证
ssh-keygen -t ed25519 -C "<EMAIL>"

# 2. 设置 GPG 签名
git config --global user.signingkey YOUR_GPG_KEY
git config --global commit.gpgsign true

# 3. 验证远程仓库
git remote -v
git ls-remote origin

# 4. 使用安全的推送
git push --force-with-lease origin main
```

### 🚀 效率提升
```bash
# 设置有用的别名
git config --global alias.pom 'push origin main'
git config --global alias.poh 'push origin HEAD'
git config --global alias.puom 'pull origin main'
git config --global alias.fom 'fetch origin main'

# 自动设置上游分支
git config --global push.autoSetupRemote true

# 默认使用变基拉取
git config --global pull.rebase true
```

---

## 🚀 下一步学习

掌握远程仓库基础后，建议学习：
1. **GitHub使用** - 学习 GitHub 平台的高级功能
2. **SSH密钥配置** - 配置安全的 SSH 认证
3. **多远程仓库** - 管理多个远程仓库的技巧

**记住**: 远程仓库是团队协作的基础，熟练掌握这些操作将让你的团队协作更加顺畅！ 🌐
