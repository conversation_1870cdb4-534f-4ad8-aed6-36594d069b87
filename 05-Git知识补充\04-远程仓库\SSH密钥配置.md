# SSH 密钥配置详解

## 🔐 SSH 密钥概念

### 📋 什么是 SSH 密钥？
SSH（Secure Shell）密钥是一种安全的身份验证方法，使用公钥加密技术，比用户名密码更安全、更方便。

```bash
# SSH 密钥的优势：
├── 更高的安全性 - 无法被暴力破解
├── 便捷性 - 无需每次输入密码
├── 自动化友好 - 适合脚本和 CI/CD
└── 多平台支持 - GitHub、GitLab、Bitbucket 等
```

### 🔑 密钥类型对比
```bash
RSA (2048/4096 bit)  # 传统算法，兼容性好
├── 优点：广泛支持，成熟稳定
└── 缺点：密钥较大，性能较低

Ed25519              # 现代算法，推荐使用
├── 优点：安全性高，性能好，密钥小
└── 缺点：部分老系统不支持

ECDSA (256/384/521)  # 椭圆曲线算法
├── 优点：性能好，密钥小
└── 缺点：某些实现存在安全问题
```

## 🔧 生成 SSH 密钥

### 🆕 生成新密钥

#### Ed25519 密钥（推荐）
```bash
# 生成 Ed25519 密钥
ssh-keygen -t ed25519 -C "<EMAIL>"

# 指定文件名
ssh-keygen -t ed25519 -f ~/.ssh/id_ed25519_github -C "<EMAIL>"

# 生成过程中的提示：
# Enter file in which to save the key: [回车使用默认]
# Enter passphrase: [输入密码短语，可选]
# Enter same passphrase again: [再次输入密码短语]
```

#### RSA 密钥（兼容性）
```bash
# 生成 4096 位 RSA 密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 生成 2048 位 RSA 密钥（最小推荐）
ssh-keygen -t rsa -b 2048 -C "<EMAIL>"
```

#### 批量生成（脚本）
```bash
#!/bin/bash
# generate_ssh_keys.sh

EMAIL="<EMAIL>"
KEY_DIR="$HOME/.ssh"

# 为不同服务生成密钥
services=("github" "gitlab" "bitbucket")

for service in "${services[@]}"; do
    echo "Generating SSH key for $service..."
    ssh-keygen -t ed25519 -f "$KEY_DIR/id_ed25519_$service" -C "$EMAIL" -N ""
    echo "Generated: $KEY_DIR/id_ed25519_$service"
done
```

### 📁 密钥文件结构
```bash
~/.ssh/
├── id_ed25519          # 私钥（保密）
├── id_ed25519.pub      # 公钥（可分享）
├── id_rsa              # RSA 私钥
├── id_rsa.pub          # RSA 公钥
├── config              # SSH 配置文件
├── known_hosts         # 已知主机
└── authorized_keys     # 授权密钥（服务器端）
```

## 🔧 SSH 代理管理

### 🚀 启动 SSH 代理

#### 手动启动
```bash
# 启动 SSH 代理
eval "$(ssh-agent -s)"

# 添加密钥到代理
ssh-add ~/.ssh/id_ed25519

# 添加时指定密钥文件
ssh-add ~/.ssh/id_ed25519_github

# 查看已添加的密钥
ssh-add -l

# 删除所有密钥
ssh-add -D

# 删除特定密钥
ssh-add -d ~/.ssh/id_ed25519
```

#### 自动启动（macOS）
```bash
# 在 ~/.ssh/config 中添加
Host *
  AddKeysToAgent yes
  UseKeychain yes
  IdentityFile ~/.ssh/id_ed25519

# 添加密钥到 macOS 钥匙串
ssh-add --apple-use-keychain ~/.ssh/id_ed25519
```

#### 自动启动（Linux）
```bash
# 在 ~/.bashrc 或 ~/.zshrc 中添加
if [ -z "$SSH_AUTH_SOCK" ]; then
    eval "$(ssh-agent -s)"
    ssh-add ~/.ssh/id_ed25519
fi

# 或者使用 systemd 用户服务
# ~/.config/systemd/user/ssh-agent.service
[Unit]
Description=SSH key agent

[Service]
Type=simple
Environment=SSH_AUTH_SOCK=%t/ssh-agent.socket
ExecStart=/usr/bin/ssh-agent -D -a $SSH_AUTH_SOCK

[Install]
WantedBy=default.target
```

## ⚙️ SSH 配置文件

### 📝 基本配置

#### 单个主机配置
```bash
# ~/.ssh/config
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_github
    IdentitiesOnly yes
```

#### 多主机配置
```bash
# ~/.ssh/config
# GitHub 配置
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_github
    IdentitiesOnly yes

# GitLab 配置
Host gitlab.com
    HostName gitlab.com
    User git
    IdentityFile ~/.ssh/id_ed25519_gitlab
    IdentitiesOnly yes

# 企业 GitLab
Host gitlab.company.com
    HostName gitlab.company.com
    User git
    IdentityFile ~/.ssh/id_ed25519_company
    IdentitiesOnly yes
    Port 2222
```

#### 高级配置
```bash
# ~/.ssh/config
# 全局默认设置
Host *
    AddKeysToAgent yes
    UseKeychain yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    TCPKeepAlive yes
    Compression yes

# GitHub 个人账户
Host github-personal
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_personal
    IdentitiesOnly yes

# GitHub 工作账户
Host github-work
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_work
    IdentitiesOnly yes

# 使用别名克隆
# git clone git@github-personal:username/repo.git
# git clone git@github-work:company/repo.git
```

### 🔧 配置选项说明
```bash
Host                 # 主机别名
HostName            # 实际主机名
User                # 用户名
Port                # 端口号（默认 22）
IdentityFile        # 私钥文件路径
IdentitiesOnly      # 只使用指定的身份文件
AddKeysToAgent      # 自动添加密钥到代理
UseKeychain         # 使用系统钥匙串（macOS）
ServerAliveInterval # 保持连接间隔
ServerAliveCountMax # 最大保持连接次数
TCPKeepAlive        # TCP 保持连接
Compression         # 启用压缩
ForwardAgent        # 转发代理
ProxyCommand        # 代理命令
```

## 🌐 添加公钥到平台

### 🐙 GitHub
```bash
# 1. 复制公钥内容
cat ~/.ssh/id_ed25519.pub | pbcopy  # macOS
cat ~/.ssh/id_ed25519.pub | xclip -selection clipboard  # Linux

# 2. 在 GitHub 上添加
# Settings -> SSH and GPG keys -> New SSH key
# Title: 给密钥起个名字
# Key: 粘贴公钥内容

# 3. 测试连接
ssh -T **************
# Hi username! You've successfully authenticated...
```

### 🦊 GitLab
```bash
# 1. 复制公钥内容
cat ~/.ssh/id_ed25519.pub

# 2. 在 GitLab 上添加
# User Settings -> SSH Keys -> Add new key
# Title: 密钥名称
# Key: 公钥内容
# Expiration date: 过期时间（可选）

# 3. 测试连接
ssh -T **************
# Welcome to GitLab, @username!
```

### 🪣 Bitbucket
```bash
# 1. 复制公钥内容
cat ~/.ssh/id_ed25519.pub

# 2. 在 Bitbucket 上添加
# Personal settings -> SSH keys -> Add key
# Label: 密钥标签
# Key: 公钥内容

# 3. 测试连接
ssh -T *****************
# logged in as username.
```

## 🔄 多账户管理

### 👥 GitHub 多账户配置
```bash
# ~/.ssh/config
# 个人账户
Host github-personal
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_personal

# 工作账户
Host github-work
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_work

# 使用方法：
# 个人项目
git clone git@github-personal:personal-username/repo.git
git remote set-url origin git@github-personal:personal-username/repo.git

# 工作项目
git clone git@github-work:company-username/repo.git
git remote set-url origin git@github-work:company-username/repo.git
```

### 🔧 自动切换脚本
```bash
#!/bin/bash
# switch_git_account.sh

PERSONAL_EMAIL="<EMAIL>"
WORK_EMAIL="<EMAIL>"

if [[ "$1" == "personal" ]]; then
    git config --global user.email "$PERSONAL_EMAIL"
    git config --global user.name "Personal Name"
    echo "Switched to personal account"
elif [[ "$1" == "work" ]]; then
    git config --global user.email "$WORK_EMAIL"
    git config --global user.name "Work Name"
    echo "Switched to work account"
else
    echo "Usage: $0 [personal|work]"
fi
```

## 🛡️ 安全最佳实践

### 🔒 密钥安全
```bash
# 1. 设置正确的文件权限
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_ed25519
chmod 644 ~/.ssh/id_ed25519.pub
chmod 600 ~/.ssh/config

# 2. 使用密码短语保护私钥
ssh-keygen -t ed25519 -C "<EMAIL>"
# Enter passphrase: [输入强密码短语]

# 3. 定期轮换密钥
# 每年或在安全事件后更换密钥

# 4. 备份密钥
# 安全地备份私钥到加密存储
```

### 🔍 安全检查
```bash
# 检查密钥权限
ls -la ~/.ssh/

# 检查 SSH 代理中的密钥
ssh-add -l

# 检查连接
ssh -v **************

# 检查配置文件语法
ssh -F ~/.ssh/config -T **************
```

### 🚨 应急处理
```bash
# 如果密钥泄露：
# 1. 立即从所有平台删除公钥
# 2. 生成新的密钥对
# 3. 更新所有相关配置
# 4. 检查访问日志

# 删除泄露的密钥
rm ~/.ssh/id_ed25519*
ssh-add -D

# 生成新密钥
ssh-keygen -t ed25519 -C "<EMAIL>"
```

## 🔧 故障排除

### 🐛 常见问题

#### 权限被拒绝
```bash
# 错误：Permission denied (publickey)
# 解决方案：
ssh -v **************  # 查看详细信息
ssh-add -l             # 检查代理中的密钥
ssh-add ~/.ssh/id_ed25519  # 添加密钥到代理
```

#### 密钥未找到
```bash
# 错误：Could not open a connection to your authentication agent
# 解决方案：
eval "$(ssh-agent -s)"  # 启动 SSH 代理
ssh-add ~/.ssh/id_ed25519  # 添加密钥
```

#### 配置文件错误
```bash
# 检查配置文件语法
ssh -F ~/.ssh/config -T **************

# 测试特定主机配置
ssh -F ~/.ssh/config github.com
```

### 🔍 调试命令
```bash
# 详细连接信息
ssh -v **************
ssh -vv **************  # 更详细
ssh -vvv ************** # 最详细

# 测试配置
ssh -T **************

# 检查密钥指纹
ssh-keygen -lf ~/.ssh/id_ed25519.pub

# 检查已知主机
ssh-keygen -F github.com
```

---

**记住**: SSH 密钥是安全访问远程仓库的最佳方式。正确配置和管理 SSH 密钥将让你的 Git 操作更加安全和便捷！ 🔐
