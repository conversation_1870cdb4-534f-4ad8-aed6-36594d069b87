# 服务器配置
PORT=4000
NODE_ENV=development

# JWT 密钥
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 数据库配置 (可选，当前使用内存数据库)
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name
# MONGODB_URI=mongodb://localhost:27017/vue-graphql-project

# CORS 配置
CORS_ORIGIN=http://localhost:3000,http://localhost:5173

# 日志级别
LOG_LEVEL=debug

# GraphQL 配置
GRAPHQL_INTROSPECTION=true
GRAPHQL_PLAYGROUND=true

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
