import { gql } from '@apollo/client/core'

// 项目片段
export const PROJECT_FRAGMENT = gql`
  fragment ProjectInfo on Project {
    id
    name
    description
    status
    priority
    startDate
    endDate
    createdAt
    updatedAt
    owner {
      id
      username
      firstName
      lastName
      avatar
    }
    members {
      id
      username
      firstName
      lastName
      avatar
      role
    }
  }
`

export const PROJECT_DETAIL_FRAGMENT = gql`
  fragment ProjectDetail on Project {
    ...ProjectInfo
    tasks {
      id
      title
      status
      priority
      dueDate
      assignee {
        id
        username
        firstName
        lastName
        avatar
      }
    }
  }
  ${PROJECT_FRAGMENT}
`

// 项目查询
export const GET_PROJECTS_QUERY = gql`
  ${PROJECT_FRAGMENT}
  query GetProjects($filter: ProjectFilter, $pagination: PaginationInput) {
    projects(filter: $filter, pagination: $pagination) {
      projects {
        ...ProjectInfo
      }
      pagination {
        page
        limit
        total
        pages
        hasNext
        hasPrev
      }
    }
  }
`

export const GET_PROJECT_QUERY = gql`
  ${PROJECT_DETAIL_FRAGMENT}
  query GetProject($id: ID!) {
    project(id: $id) {
      ...ProjectDetail
    }
  }
`

export const GET_MY_PROJECTS_QUERY = gql`
  ${PROJECT_FRAGMENT}
  query GetMyProjects {
    myProjects {
      ...ProjectInfo
    }
  }
`

// 项目变更
export const CREATE_PROJECT_MUTATION = gql`
  ${PROJECT_FRAGMENT}
  mutation CreateProject($input: CreateProjectInput!) {
    createProject(input: $input) {
      ...ProjectInfo
    }
  }
`

export const UPDATE_PROJECT_MUTATION = gql`
  ${PROJECT_FRAGMENT}
  mutation UpdateProject($id: ID!, $input: UpdateProjectInput!) {
    updateProject(id: $id, input: $input) {
      ...ProjectInfo
    }
  }
`

export const DELETE_PROJECT_MUTATION = gql`
  mutation DeleteProject($id: ID!) {
    deleteProject(id: $id)
  }
`

export const ADD_PROJECT_MEMBER_MUTATION = gql`
  ${PROJECT_FRAGMENT}
  mutation AddProjectMember($projectId: ID!, $userId: ID!) {
    addProjectMember(projectId: $projectId, userId: $userId) {
      ...ProjectInfo
    }
  }
`

export const REMOVE_PROJECT_MEMBER_MUTATION = gql`
  ${PROJECT_FRAGMENT}
  mutation RemoveProjectMember($projectId: ID!, $userId: ID!) {
    removeProjectMember(projectId: $projectId, userId: $userId) {
      ...ProjectInfo
    }
  }
`

// 项目统计
export const GET_PROJECT_STATS_QUERY = gql`
  query GetProjectStats {
    projectStats {
      total
      planning
      active
      onHold
      completed
      cancelled
    }
  }
`

// 项目订阅
export const PROJECT_CREATED_SUBSCRIPTION = gql`
  ${PROJECT_FRAGMENT}
  subscription ProjectCreated {
    projectCreated {
      ...ProjectInfo
    }
  }
`

export const PROJECT_UPDATED_SUBSCRIPTION = gql`
  ${PROJECT_FRAGMENT}
  subscription ProjectUpdated {
    projectUpdated {
      ...ProjectInfo
    }
  }
`
