import { gql } from '@apollo/client/core'

// 任务片段
export const TASK_FRAGMENT = gql`
  fragment TaskInfo on Task {
    id
    title
    description
    status
    priority
    dueDate
    estimatedHours
    actualHours
    tags
    createdAt
    updatedAt
    project {
      id
      name
      status
    }
    creator {
      id
      username
      firstName
      lastName
      avatar
    }
    assignee {
      id
      username
      firstName
      lastName
      avatar
    }
  }
`

export const TASK_DETAIL_FRAGMENT = gql`
  fragment TaskDetail on Task {
    ...TaskInfo
    comments {
      id
      content
      createdAt
      author {
        id
        username
        firstName
        lastName
        avatar
      }
    }
  }
  ${TASK_FRAGMENT}
`

// 任务查询
export const GET_TASKS_QUERY = gql`
  ${TASK_FRAGMENT}
  query GetTasks($filter: TaskFilter, $pagination: PaginationInput) {
    tasks(filter: $filter, pagination: $pagination) {
      tasks {
        ...TaskInfo
      }
      pagination {
        page
        limit
        total
        pages
        hasNext
        hasPrev
      }
    }
  }
`

export const GET_TASK_QUERY = gql`
  ${TASK_DETAIL_FRAGMENT}
  query GetTask($id: ID!) {
    task(id: $id) {
      ...TaskDetail
    }
  }
`

export const GET_MY_TASKS_QUERY = gql`
  ${TASK_FRAGMENT}
  query GetMyTasks {
    myTasks {
      ...TaskInfo
    }
  }
`

// 任务变更
export const CREATE_TASK_MUTATION = gql`
  ${TASK_FRAGMENT}
  mutation CreateTask($input: CreateTaskInput!) {
    createTask(input: $input) {
      ...TaskInfo
    }
  }
`

export const UPDATE_TASK_MUTATION = gql`
  ${TASK_FRAGMENT}
  mutation UpdateTask($id: ID!, $input: UpdateTaskInput!) {
    updateTask(id: $id, input: $input) {
      ...TaskInfo
    }
  }
`

export const DELETE_TASK_MUTATION = gql`
  mutation DeleteTask($id: ID!) {
    deleteTask(id: $id)
  }
`

// 评论相关
export const CREATE_COMMENT_MUTATION = gql`
  mutation CreateComment($input: CreateCommentInput!) {
    createComment(input: $input) {
      id
      content
      createdAt
      author {
        id
        username
        firstName
        lastName
        avatar
      }
      task {
        id
      }
    }
  }
`

export const DELETE_COMMENT_MUTATION = gql`
  mutation DeleteComment($id: ID!) {
    deleteComment(id: $id)
  }
`

// 任务统计
export const GET_TASK_STATS_QUERY = gql`
  query GetTaskStats($projectId: ID) {
    taskStats(projectId: $projectId) {
      total
      todo
      inProgress
      inReview
      testing
      done
      cancelled
    }
  }
`

// 任务订阅
export const TASK_CREATED_SUBSCRIPTION = gql`
  ${TASK_FRAGMENT}
  subscription TaskCreated {
    taskCreated {
      ...TaskInfo
    }
  }
`

export const TASK_UPDATED_SUBSCRIPTION = gql`
  ${TASK_FRAGMENT}
  subscription TaskUpdated {
    taskUpdated {
      ...TaskInfo
    }
  }
`

export const TASK_DELETED_SUBSCRIPTION = gql`
  subscription TaskDeleted {
    taskDeleted
  }
`

export const COMMENT_ADDED_SUBSCRIPTION = gql`
  subscription CommentAdded($taskId: ID!) {
    commentAdded(taskId: $taskId) {
      id
      content
      createdAt
      author {
        id
        username
        firstName
        lastName
        avatar
      }
    }
  }
`
