# GraphQL Resolvers 实战练习

## 练习1：基础 Resolver 实现

### 任务描述
实现一个简单的图书管理系统的 Resolver

### Schema 定义
```graphql
type Query {
  books: [Book!]!
  book(id: ID!): Book
  authors: [Author!]!
  author(id: ID!): Author
}

type Mutation {
  createBook(input: CreateBookInput!): Book!
  updateBook(id: ID!, input: UpdateBookInput!): Book!
  deleteBook(id: ID!): Boolean!
}

type Book {
  id: ID!
  title: String!
  isbn: String!
  publishedYear: Int!
  author: Author!
  reviews: [Review!]!
  averageRating: Float
}

type Author {
  id: ID!
  name: String!
  bio: String
  books: [Book!]!
  bookCount: Int!
}

type Review {
  id: ID!
  rating: Int!
  comment: String
  book: Book!
  reviewer: String!
}

input CreateBookInput {
  title: String!
  isbn: String!
  publishedYear: Int!
  authorId: ID!
}

input UpdateBookInput {
  title: String
  isbn: String
  publishedYear: Int
}
```

### 练习任务
```javascript
// 模拟数据
const books = [
  { id: '1', title: 'GraphQL入门', isbn: '978-1234567890', publishedYear: 2023, authorId: '1' },
  { id: '2', title: 'React实战', isbn: '978-0987654321', publishedYear: 2022, authorId: '2' }
];

const authors = [
  { id: '1', name: '张三', bio: 'GraphQL专家' },
  { id: '2', name: '李四', bio: 'React开发者' }
];

const reviews = [
  { id: '1', rating: 5, comment: '很好的书', bookId: '1', reviewer: '读者A' },
  { id: '2', rating: 4, comment: '不错', bookId: '1', reviewer: '读者B' }
];

// TODO: 实现以下 Resolver
const resolvers = {
  Query: {
    books: () => {
      // TODO: 返回所有书籍
    },
    
    book: (parent, { id }) => {
      // TODO: 根据ID查找书籍
    },
    
    authors: () => {
      // TODO: 返回所有作者
    },
    
    author: (parent, { id }) => {
      // TODO: 根据ID查找作者
    }
  },
  
  Mutation: {
    createBook: (parent, { input }) => {
      // TODO: 创建新书籍
    },
    
    updateBook: (parent, { id, input }) => {
      // TODO: 更新书籍信息
    },
    
    deleteBook: (parent, { id }) => {
      // TODO: 删除书籍
    }
  },
  
  Book: {
    author: (book) => {
      // TODO: 获取书籍的作者
    },
    
    reviews: (book) => {
      // TODO: 获取书籍的评论
    },
    
    averageRating: (book) => {
      // TODO: 计算平均评分
    }
  },
  
  Author: {
    books: (author) => {
      // TODO: 获取作者的书籍
    },
    
    bookCount: (author) => {
      // TODO: 计算作者的书籍数量
    }
  },
  
  Review: {
    book: (review) => {
      // TODO: 获取评论对应的书籍
    }
  }
};
```

## 练习2：异步数据处理

### 任务描述
模拟真实的数据库操作，使用 Promise 和 async/await

### 练习任务
```javascript
// 模拟数据库操作
class BookDatabase {
  static async findAll() {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    return books;
  }
  
  static async findById(id) {
    await new Promise(resolve => setTimeout(resolve, 50));
    return books.find(book => book.id === id);
  }
  
  static async create(bookData) {
    await new Promise(resolve => setTimeout(resolve, 200));
    const newBook = {
      id: String(books.length + 1),
      ...bookData
    };
    books.push(newBook);
    return newBook;
  }
  
  static async update(id, updateData) {
    await new Promise(resolve => setTimeout(resolve, 150));
    const bookIndex = books.findIndex(book => book.id === id);
    if (bookIndex === -1) return null;
    
    books[bookIndex] = { ...books[bookIndex], ...updateData };
    return books[bookIndex];
  }
  
  static async delete(id) {
    await new Promise(resolve => setTimeout(resolve, 100));
    const bookIndex = books.findIndex(book => book.id === id);
    if (bookIndex === -1) return false;
    
    books.splice(bookIndex, 1);
    return true;
  }
}

// TODO: 重写 Resolver 使用异步操作
const asyncResolvers = {
  Query: {
    books: async () => {
      // TODO: 使用 BookDatabase.findAll()
    },
    
    book: async (parent, { id }) => {
      // TODO: 使用 BookDatabase.findById(id)
      // TODO: 如果不存在，抛出错误
    }
  },
  
  Mutation: {
    createBook: async (parent, { input }) => {
      try {
        // TODO: 验证作者是否存在
        // TODO: 使用 BookDatabase.create(input)
      } catch (error) {
        // TODO: 错误处理
      }
    },
    
    updateBook: async (parent, { id, input }) => {
      // TODO: 实现异步更新
    },
    
    deleteBook: async (parent, { id }) => {
      // TODO: 实现异步删除
    }
  }
};
```

## 练习3：性能优化 - 解决 N+1 问题

### 任务描述
使用 DataLoader 优化数据加载性能

### 练习任务
```javascript
import DataLoader from 'dataloader';

// TODO: 创建 DataLoader
const authorLoader = new DataLoader(async (authorIds) => {
  // TODO: 批量加载作者数据
  console.log('批量加载作者:', authorIds);
  
  // 模拟批量查询
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // TODO: 返回与 authorIds 顺序对应的作者数组
});

const reviewLoader = new DataLoader(async (bookIds) => {
  // TODO: 批量加载评论数据
  console.log('批量加载评论:', bookIds);
  
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // TODO: 返回每本书的评论数组
});

// TODO: 在 context 中提供 DataLoader
const createContext = () => ({
  loaders: {
    author: authorLoader,
    reviews: reviewLoader
  }
});

// TODO: 修改 Resolver 使用 DataLoader
const optimizedResolvers = {
  Book: {
    author: (book, args, { loaders }) => {
      // TODO: 使用 loaders.author.load(book.authorId)
    },
    
    reviews: (book, args, { loaders }) => {
      // TODO: 使用 loaders.reviews.load(book.id)
    }
  }
};
```

## 练习4：错误处理和验证

### 任务描述
实现完善的错误处理和输入验证

### 练习任务
```javascript
// TODO: 定义自定义错误类
class BookNotFoundError extends Error {
  constructor(bookId) {
    // TODO: 实现构造函数
  }
}

class AuthorNotFoundError extends Error {
  constructor(authorId) {
    // TODO: 实现构造函数
  }
}

class ValidationError extends Error {
  constructor(field, message) {
    // TODO: 实现构造函数
  }
}

// TODO: 输入验证函数
function validateCreateBookInput(input) {
  const errors = [];
  
  // TODO: 验证 title
  if (!input.title || input.title.trim().length === 0) {
    errors.push('标题不能为空');
  }
  
  // TODO: 验证 isbn
  if (!input.isbn || !/^978-\d{10}$/.test(input.isbn)) {
    errors.push('ISBN格式不正确');
  }
  
  // TODO: 验证 publishedYear
  const currentYear = new Date().getFullYear();
  if (!input.publishedYear || input.publishedYear > currentYear) {
    errors.push('出版年份不能大于当前年份');
  }
  
  if (errors.length > 0) {
    throw new ValidationError('input', errors.join(', '));
  }
}

// TODO: 带错误处理的 Resolver
const robustResolvers = {
  Query: {
    book: async (parent, { id }) => {
      try {
        // TODO: 验证 ID 格式
        if (!id || id.trim().length === 0) {
          throw new ValidationError('id', 'ID不能为空');
        }
        
        // TODO: 查找书籍
        const book = await BookDatabase.findById(id);
        
        // TODO: 检查是否存在
        if (!book) {
          throw new BookNotFoundError(id);
        }
        
        return book;
      } catch (error) {
        // TODO: 记录错误日志
        console.error('查询书籍失败:', error);
        
        // TODO: 重新抛出错误
        throw error;
      }
    }
  },
  
  Mutation: {
    createBook: async (parent, { input }) => {
      try {
        // TODO: 验证输入
        validateCreateBookInput(input);
        
        // TODO: 验证作者是否存在
        const author = authors.find(a => a.id === input.authorId);
        if (!author) {
          throw new AuthorNotFoundError(input.authorId);
        }
        
        // TODO: 创建书籍
        const book = await BookDatabase.create(input);
        
        return book;
      } catch (error) {
        // TODO: 错误处理
        console.error('创建书籍失败:', error);
        throw error;
      }
    }
  }
};
```

## 练习5：认证和授权

### 任务描述
实现基于角色的访问控制

### 练习任务
```javascript
// TODO: 认证中间件
const requireAuth = (resolver) => {
  return (parent, args, context, info) => {
    // TODO: 检查用户是否登录
    if (!context.user) {
      throw new Error('需要登录');
    }
    
    return resolver(parent, args, context, info);
  };
};

// TODO: 角色检查中间件
const requireRole = (roles) => (resolver) => {
  return (parent, args, context, info) => {
    // TODO: 检查用户角色
    if (!context.user) {
      throw new Error('需要登录');
    }
    
    if (!roles.includes(context.user.role)) {
      throw new Error('权限不足');
    }
    
    return resolver(parent, args, context, info);
  };
};

// TODO: 带权限控制的 Resolver
const secureResolvers = {
  Query: {
    // 公开查询
    books: () => BookDatabase.findAll(),
    
    // 需要登录的查询
    myBooks: requireAuth((parent, args, { user }) => {
      // TODO: 返回当前用户的书籍
    })
  },
  
  Mutation: {
    // 需要登录才能创建
    createBook: requireAuth(async (parent, { input }, { user }) => {
      // TODO: 实现创建逻辑
    }),
    
    // 只有管理员可以删除
    deleteBook: requireRole(['ADMIN'])(async (parent, { id }) => {
      // TODO: 实现删除逻辑
    })
  },
  
  Book: {
    // 敏感信息只有作者本人或管理员可以看到
    salesData: (book, args, { user }) => {
      // TODO: 检查权限
      if (!user) return null;
      
      const author = authors.find(a => a.id === book.authorId);
      if (user.id === author.id || user.role === 'ADMIN') {
        return { sales: 1000, revenue: 50000 };
      }
      
      return null;
    }
  }
};
```

## 练习答案提示

### 练习1提示
- 使用数组的 `find()` 和 `filter()` 方法
- 注意处理不存在的情况
- 计算字段需要遍历相关数据

### 练习2提示
- 所有数据库操作都应该使用 `await`
- 记得处理异步操作的错误
- 可以使用 `try-catch` 包装异步操作

### 练习3提示
- DataLoader 的批量函数必须返回与输入顺序对应的数组
- 使用 `Promise.all()` 进行并行查询
- 在 context 中创建新的 DataLoader 实例

### 练习4提示
- 自定义错误类应该设置 `extensions` 属性
- 验证函数应该收集所有错误而不是遇到第一个就停止
- 记录错误日志有助于调试

### 练习5提示
- 中间件函数返回新的 resolver 函数
- 权限检查应该在业务逻辑之前进行
- 考虑字段级别的权限控制

---

**完成这些练习后，你将掌握：**
1. 基础 Resolver 的编写
2. 异步数据处理
3. 性能优化技巧
4. 错误处理最佳实践
5. 认证和授权实现
