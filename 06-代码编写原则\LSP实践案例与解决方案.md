# LSP实践案例与解决方案

## 🎮 实战案例1：游戏角色系统

### 需求描述
设计一个游戏角色系统，包含不同类型的角色：战士、法师、弓箭手等。

### ❌ 违反LSP的设计

```javascript
class GameCharacter {
    constructor(name, health, mana) {
        this.name = name;
        this.health = health;
        this.mana = mana;
    }
    
    attack() {
        console.log(`${this.name} 进行普通攻击`);
        return 10;
    }
    
    castSpell() {
        if (this.mana >= 10) {
            this.mana -= 10;
            console.log(`${this.name} 释放魔法`);
            return 20;
        }
        return 0;
    }
    
    useSkill() {
        console.log(`${this.name} 使用技能`);
        return 15;
    }
}

class Warrior extends GameCharacter {
    constructor(name) {
        super(name, 150, 0); // 战士没有魔法值
    }
    
    castSpell() {
        throw new Error('战士不能使用魔法！'); // ❌ 违反LSP
    }
    
    useSkill() {
        console.log(`${this.name} 使用战士技能：重击`);
        return 25;
    }
}

class Mage extends GameCharacter {
    constructor(name) {
        super(name, 80, 100);
    }
    
    attack() {
        console.log(`${this.name} 用法杖攻击`);
        return 5; // 法师物理攻击很弱
    }
    
    castSpell() {
        if (this.mana >= 10) {
            this.mana -= 10;
            console.log(`${this.name} 释放火球术`);
            return 30;
        }
        return 0;
    }
}

// 客户端代码期望所有角色都能使用魔法
function battleSystem(characters) {
    characters.forEach(character => {
        character.attack();
        character.castSpell(); // 对战士会抛出异常！
        character.useSkill();
    });
}

const warrior = new Warrior('亚瑟');
const mage = new Mage('梅林');

battleSystem([warrior, mage]); // 会在战士使用魔法时出错
```

### ✅ 遵循LSP的正确设计

```javascript
// 基础角色类 - 只包含所有角色都有的能力
class GameCharacter {
    constructor(name, health) {
        this.name = name;
        this.health = health;
    }
    
    attack() {
        throw new Error('子类必须实现attack方法');
    }
    
    useSkill() {
        throw new Error('子类必须实现useSkill方法');
    }
    
    takeDamage(damage) {
        this.health -= damage;
        console.log(`${this.name} 受到 ${damage} 点伤害，剩余生命值: ${this.health}`);
    }
}

// 魔法使用者接口
class MagicUser {
    constructor(mana) {
        this.mana = mana;
    }
    
    castSpell() {
        throw new Error('子类必须实现castSpell方法');
    }
    
    hasMana(cost) {
        return this.mana >= cost;
    }
    
    consumeMana(cost) {
        if (this.hasMana(cost)) {
            this.mana -= cost;
            return true;
        }
        return false;
    }
}

// 战士类 - 只继承基础角色
class Warrior extends GameCharacter {
    constructor(name) {
        super(name, 150);
        this.rage = 0;
    }
    
    attack() {
        console.log(`${this.name} 用剑攻击`);
        this.rage += 5;
        return 15;
    }
    
    useSkill() {
        if (this.rage >= 20) {
            this.rage -= 20;
            console.log(`${this.name} 使用战士技能：重击`);
            return 30;
        } else {
            console.log(`${this.name} 怒气不足，无法使用技能`);
            return 0;
        }
    }
}

// 法师类 - 继承基础角色并组合魔法使用者
class Mage extends GameCharacter {
    constructor(name) {
        super(name, 80);
        this.magicUser = new MagicUser(100);
    }
    
    attack() {
        console.log(`${this.name} 用法杖攻击`);
        return 8;
    }
    
    useSkill() {
        console.log(`${this.name} 使用法师技能：魔法护盾`);
        return 0; // 防御技能，不造成伤害
    }
    
    castSpell() {
        if (this.magicUser.consumeMana(10)) {
            console.log(`${this.name} 释放火球术`);
            return 25;
        } else {
            console.log(`${this.name} 魔法值不足`);
            return 0;
        }
    }
}

// 弓箭手类
class Archer extends GameCharacter {
    constructor(name) {
        super(name, 100);
        this.arrows = 30;
    }
    
    attack() {
        if (this.arrows > 0) {
            this.arrows--;
            console.log(`${this.name} 射箭攻击，剩余箭矢: ${this.arrows}`);
            return 12;
        } else {
            console.log(`${this.name} 箭矢用完了`);
            return 0;
        }
    }
    
    useSkill() {
        if (this.arrows >= 3) {
            this.arrows -= 3;
            console.log(`${this.name} 使用弓箭手技能：连环射击`);
            return 20;
        } else {
            console.log(`${this.name} 箭矢不足，无法使用技能`);
            return 0;
        }
    }
}

// 现在客户端代码更加清晰
function basicBattleSystem(characters) {
    characters.forEach(character => {
        character.attack();
        character.useSkill();
    });
}

function magicBattleSystem(magicUsers) {
    magicUsers.forEach(user => {
        if (user instanceof Mage) {
            user.castSpell();
        }
    });
}

// 使用
const warrior = new Warrior('亚瑟');
const mage = new Mage('梅林');
const archer = new Archer('罗宾');

// 所有角色都能参与基础战斗
basicBattleSystem([warrior, mage, archer]); // 正常工作

// 只有法师参与魔法战斗
magicBattleSystem([mage]); // 正常工作
```

---

## 🚗 实战案例2：交通工具系统

### 需求描述
设计一个交通工具管理系统，包含汽车、自行车、船只等。

### ❌ 违反LSP的设计

```javascript
class Vehicle {
    constructor(name) {
        this.name = name;
        this.speed = 0;
    }
    
    start() {
        console.log(`${this.name} 启动了`);
    }
    
    accelerate() {
        this.speed += 10;
        console.log(`${this.name} 加速到 ${this.speed} km/h`);
    }
    
    refuel() {
        console.log(`${this.name} 加油中...`);
    }
    
    honk() {
        console.log(`${this.name} 鸣笛：嘀嘀！`);
    }
}

class Car extends Vehicle {
    refuel() {
        console.log(`${this.name} 加汽油中...`);
    }
}

class Bicycle extends Vehicle {
    refuel() {
        throw new Error('自行车不需要加油！'); // ❌ 违反LSP
    }
    
    honk() {
        console.log(`${this.name} 按铃：叮铃铃！`);
    }
}

class Boat extends Vehicle {
    honk() {
        throw new Error('船只不能在陆地上鸣笛！'); // ❌ 违反LSP
    }
    
    refuel() {
        console.log(`${this.name} 加柴油中...`);
    }
}

// 客户端代码期望所有交通工具都有相同行为
function vehicleService(vehicles) {
    vehicles.forEach(vehicle => {
        vehicle.start();
        vehicle.accelerate();
        vehicle.refuel();  // 对自行车会出错
        vehicle.honk();    // 对船只会出错
    });
}
```

### ✅ 遵循LSP的正确设计

```javascript
// 基础交通工具类 - 只包含所有交通工具都有的能力
class Vehicle {
    constructor(name) {
        this.name = name;
        this.speed = 0;
    }
    
    start() {
        console.log(`${this.name} 启动了`);
    }
    
    accelerate() {
        this.speed += 10;
        console.log(`${this.name} 加速到 ${this.speed} km/h`);
    }
    
    stop() {
        this.speed = 0;
        console.log(`${this.name} 停止了`);
    }
}

// 需要燃料的交通工具
class FueledVehicle extends Vehicle {
    constructor(name, fuelType) {
        super(name);
        this.fuelType = fuelType;
        this.fuelLevel = 50;
    }
    
    refuel() {
        this.fuelLevel = 100;
        console.log(`${this.name} 加${this.fuelType}中...`);
    }
    
    getFuelLevel() {
        return this.fuelLevel;
    }
}

// 有喇叭的交通工具
class HornVehicle extends Vehicle {
    honk() {
        console.log(`${this.name} 鸣笛：嘀嘀！`);
    }
}

// 陆地交通工具
class LandVehicle extends Vehicle {
    constructor(name) {
        super(name);
    }
}

// 水上交通工具
class WaterVehicle extends Vehicle {
    constructor(name) {
        super(name);
    }
    
    sail() {
        console.log(`${this.name} 在水上航行`);
    }
}

// 汽车 - 陆地 + 燃料 + 喇叭
class Car extends LandVehicle {
    constructor(name) {
        super(name);
        this.fuelSystem = new FueledVehicle(name, '汽油');
        this.hornSystem = new HornVehicle(name);
    }
    
    refuel() {
        this.fuelSystem.refuel();
    }
    
    honk() {
        this.hornSystem.honk();
    }
}

// 自行车 - 只是陆地交通工具
class Bicycle extends LandVehicle {
    constructor(name) {
        super(name);
    }
    
    ring() {
        console.log(`${this.name} 按铃：叮铃铃！`);
    }
}

// 船只 - 水上 + 燃料
class Boat extends WaterVehicle {
    constructor(name) {
        super(name);
        this.fuelSystem = new FueledVehicle(name, '柴油');
    }
    
    refuel() {
        this.fuelSystem.refuel();
    }
    
    soundHorn() {
        console.log(`${this.name} 鸣汽笛：呜呜！`);
    }
}

// 现在客户端代码更加明确
function basicVehicleService(vehicles) {
    vehicles.forEach(vehicle => {
        vehicle.start();
        vehicle.accelerate();
        vehicle.stop();
    });
}

function fueledVehicleService(vehicles) {
    vehicles.forEach(vehicle => {
        if (vehicle.refuel) {
            vehicle.refuel();
        }
    });
}

function landVehicleService(vehicles) {
    vehicles.forEach(vehicle => {
        if (vehicle instanceof LandVehicle) {
            if (vehicle.honk) {
                vehicle.honk();
            }
            if (vehicle.ring) {
                vehicle.ring();
            }
        }
    });
}

// 使用
const car = new Car('汽车');
const bicycle = new Bicycle('自行车');
const boat = new Boat('游艇');

// 所有交通工具都能进行基础操作
basicVehicleService([car, bicycle, boat]); // 正常工作

// 只有需要燃料的交通工具加油
fueledVehicleService([car, boat]); // 正常工作

// 只有陆地交通工具进行陆地操作
landVehicleService([car, bicycle]); // 正常工作
```

---

## 📊 实战案例3：数据存储系统

### 需求描述
设计一个数据存储系统，支持不同类型的存储：文件存储、数据库存储、缓存存储等。

### ❌ 违反LSP的设计

```javascript
class DataStorage {
    save(key, data) {
        throw new Error('子类必须实现save方法');
    }
    
    load(key) {
        throw new Error('子类必须实现load方法');
    }
    
    delete(key) {
        throw new Error('子类必须实现delete方法');
    }
    
    backup() {
        console.log('创建备份...');
    }
    
    restore() {
        console.log('恢复备份...');
    }
}

class FileStorage extends DataStorage {
    save(key, data) {
        console.log(`保存到文件: ${key}`);
    }
    
    load(key) {
        console.log(`从文件加载: ${key}`);
        return `文件数据: ${key}`;
    }
    
    delete(key) {
        console.log(`删除文件: ${key}`);
    }
}

class CacheStorage extends DataStorage {
    constructor() {
        super();
        this.cache = new Map();
    }
    
    save(key, data) {
        this.cache.set(key, data);
        console.log(`保存到缓存: ${key}`);
    }
    
    load(key) {
        const data = this.cache.get(key);
        console.log(`从缓存加载: ${key}`);
        return data;
    }
    
    delete(key) {
        this.cache.delete(key);
        console.log(`从缓存删除: ${key}`);
    }
    
    backup() {
        throw new Error('缓存不支持备份！'); // ❌ 违反LSP
    }
    
    restore() {
        throw new Error('缓存不支持恢复！'); // ❌ 违反LSP
    }
}

// 客户端代码期望所有存储都支持备份
function dataManager(storages) {
    storages.forEach(storage => {
        storage.save('test', 'data');
        storage.load('test');
        storage.backup();  // 对缓存会出错
        storage.restore(); // 对缓存会出错
    });
}
```

### ✅ 遵循LSP的正确设计

```javascript
// 基础存储接口 - 只包含所有存储都支持的操作
class DataStorage {
    save(key, data) {
        throw new Error('子类必须实现save方法');
    }
    
    load(key) {
        throw new Error('子类必须实现load方法');
    }
    
    delete(key) {
        throw new Error('子类必须实现delete方法');
    }
    
    exists(key) {
        throw new Error('子类必须实现exists方法');
    }
}

// 支持备份的存储
class BackupableStorage extends DataStorage {
    backup() {
        throw new Error('子类必须实现backup方法');
    }
    
    restore() {
        throw new Error('子类必须实现restore方法');
    }
}

// 持久化存储
class PersistentStorage extends BackupableStorage {
    // 持久化存储的通用方法
}

// 临时存储
class TemporaryStorage extends DataStorage {
    clear() {
        throw new Error('子类必须实现clear方法');
    }
}

// 文件存储 - 支持备份的持久化存储
class FileStorage extends PersistentStorage {
    save(key, data) {
        console.log(`保存到文件: ${key}`);
    }
    
    load(key) {
        console.log(`从文件加载: ${key}`);
        return `文件数据: ${key}`;
    }
    
    delete(key) {
        console.log(`删除文件: ${key}`);
    }
    
    exists(key) {
        console.log(`检查文件是否存在: ${key}`);
        return true;
    }
    
    backup() {
        console.log('创建文件备份...');
    }
    
    restore() {
        console.log('恢复文件备份...');
    }
}

// 数据库存储 - 支持备份的持久化存储
class DatabaseStorage extends PersistentStorage {
    save(key, data) {
        console.log(`保存到数据库: ${key}`);
    }
    
    load(key) {
        console.log(`从数据库加载: ${key}`);
        return `数据库数据: ${key}`;
    }
    
    delete(key) {
        console.log(`从数据库删除: ${key}`);
    }
    
    exists(key) {
        console.log(`检查数据库记录是否存在: ${key}`);
        return true;
    }
    
    backup() {
        console.log('创建数据库备份...');
    }
    
    restore() {
        console.log('恢复数据库备份...');
    }
}

// 缓存存储 - 临时存储
class CacheStorage extends TemporaryStorage {
    constructor() {
        super();
        this.cache = new Map();
    }
    
    save(key, data) {
        this.cache.set(key, data);
        console.log(`保存到缓存: ${key}`);
    }
    
    load(key) {
        const data = this.cache.get(key);
        console.log(`从缓存加载: ${key}`);
        return data;
    }
    
    delete(key) {
        this.cache.delete(key);
        console.log(`从缓存删除: ${key}`);
    }
    
    exists(key) {
        return this.cache.has(key);
    }
    
    clear() {
        this.cache.clear();
        console.log('清空缓存');
    }
}

// 现在客户端代码更加明确
function basicDataManager(storages) {
    storages.forEach(storage => {
        storage.save('test', 'data');
        storage.load('test');
        storage.delete('test');
    });
}

function backupManager(storages) {
    storages.forEach(storage => {
        if (storage instanceof BackupableStorage) {
            storage.backup();
            storage.restore();
        }
    });
}

function cacheManager(storages) {
    storages.forEach(storage => {
        if (storage instanceof TemporaryStorage) {
            storage.clear();
        }
    });
}

// 使用
const fileStorage = new FileStorage();
const dbStorage = new DatabaseStorage();
const cacheStorage = new CacheStorage();

// 所有存储都支持基础操作
basicDataManager([fileStorage, dbStorage, cacheStorage]); // 正常工作

// 只有支持备份的存储进行备份操作
backupManager([fileStorage, dbStorage]); // 正常工作

// 只有临时存储进行清空操作
cacheManager([cacheStorage]); // 正常工作
```

---

## 🎯 LSP实践总结

### 关键设计原则

1. **契约一致性**：子类必须遵守父类的契约
2. **行为兼容性**：子类的行为必须与父类兼容
3. **可替换性**：子类必须能够完全替换父类

### 常见解决方案

1. **重新设计继承结构**：将不兼容的行为分离到不同的继承分支
2. **使用组合替代继承**：通过组合实现功能复用
3. **接口隔离**：将大接口拆分为小接口
4. **策略模式**：将变化的行为封装为策略

### 检查方法

1. **替换测试**：用子类替换父类，程序是否正常工作
2. **异常检查**：子类是否抛出父类不会抛出的异常
3. **行为检查**：子类是否改变了方法的预期行为
4. **契约检查**：子类是否违反了父类的前置/后置条件

记住：**LSP不仅仅是语法上的继承，更重要的是语义上的兼容！**
