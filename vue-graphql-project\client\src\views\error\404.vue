<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-icon">
        <el-icon :size="120" color="#e6a23c">
          <QuestionFilled />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h1 class="error-code">404</h1>
        <h2 class="error-title">页面未找到</h2>
        <p class="error-description">
          抱歉，您访问的页面不存在。可能是页面已被删除、移动或URL输入错误。
        </p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
          <el-button @click="goHome">
            <el-icon><House /></el-icon>
            回到首页
          </el-button>
          <el-button @click="refresh">
            <el-icon><Refresh /></el-icon>
            刷新页面
          </el-button>
        </div>
      </div>
    </div>
    
    <div class="suggestions">
      <h3>您可以尝试：</h3>
      <ul>
        <li>检查URL是否正确</li>
        <li>返回上一页面</li>
        <li>访问网站首页</li>
        <li>使用搜索功能查找内容</li>
      </ul>
    </div>
    
    <div class="error-footer">
      <p>如果问题持续存在，请联系技术支持</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { QuestionFilled, ArrowLeft, House, Refresh } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/')
}

const refresh = () => {
  window.location.reload()
}
</script>

<style scoped lang="scss">
.error-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 500px;
  margin-bottom: 40px;
}

.error-icon {
  margin-bottom: 30px;
  animation: bounce 2s infinite;
}

.error-code {
  font-size: 72px;
  font-weight: bold;
  color: #e6a23c;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 32px;
  color: #303133;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.error-description {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 40px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.suggestions {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  max-width: 400px;
  
  h3 {
    color: #303133;
    margin: 0 0 15px 0;
    font-size: 18px;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      color: #606266;
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

.error-footer {
  text-align: center;
  
  p {
    color: #909399;
    font-size: 14px;
    margin: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@media (max-width: 768px) {
  .error-code {
    font-size: 56px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 200px;
    }
  }
  
  .suggestions {
    margin: 0 20px 20px 20px;
  }
}
</style>
