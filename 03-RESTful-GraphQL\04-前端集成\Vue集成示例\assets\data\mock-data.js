/**
 * Mock数据模块
 * 提供RESTful API和GraphQL示例的模拟数据
 */

// 用户数据
export const mockUsers = [
    {
        id: 1,
        name: '张三',
        email: '<PERSON><PERSON><PERSON>@example.com',
        role: 'admin',
        bio: '系统管理员，负责平台的整体运营和维护。',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=1',
        created_at: '2023-10-01T08:00:00Z',
        updated_at: '2023-10-21T10:30:00Z',
        last_login: '2023-10-21T10:30:00Z',
        status: 'active'
    },
    {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        role: 'user',
        bio: '前端开发工程师，专注于Vue.js和React开发。',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=2',
        created_at: '2023-10-02T09:15:00Z',
        updated_at: '2023-10-21T09:45:00Z',
        last_login: '2023-10-21T09:45:00Z',
        status: 'active'
    },
    {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        role: 'moderator',
        bio: '社区版主，维护良好的社区氛围。',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=3',
        created_at: '2023-10-03T10:30:00Z',
        updated_at: '2023-10-21T08:20:00Z',
        last_login: '2023-10-21T08:20:00Z',
        status: 'active'
    },
    {
        id: 4,
        name: '赵六',
        email: '<EMAIL>',
        role: 'user',
        bio: '后端开发工程师，专注于Node.js和Python开发。',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=4',
        created_at: '2023-10-04T11:45:00Z',
        updated_at: '2023-10-20T16:30:00Z',
        last_login: '2023-10-20T16:30:00Z',
        status: 'active'
    },
    {
        id: 5,
        name: '孙七',
        email: '<EMAIL>',
        role: 'user',
        bio: 'UI/UX设计师，专注于用户体验设计。',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=5',
        created_at: '2023-10-05T14:20:00Z',
        updated_at: '2023-10-19T12:15:00Z',
        last_login: '2023-10-19T12:15:00Z',
        status: 'inactive'
    }
];

// 文章数据
export const mockPosts = [
    {
        id: 1,
        title: 'GraphQL入门指南',
        content: 'GraphQL是一种强大的查询语言，它提供了一种更高效、强大和灵活的替代REST的方案...',
        author_id: 1,
        category_id: 1,
        status: 'published',
        tags: ['GraphQL', 'API', '前端'],
        created_at: '2023-10-21T08:00:00Z',
        updated_at: '2023-10-21T08:00:00Z',
        published_at: '2023-10-21T08:00:00Z',
        views: 1250,
        likes: 89
    },
    {
        id: 2,
        title: 'Vue.js 3.0 新特性详解',
        content: 'Vue.js 3.0带来了许多令人兴奋的新特性，包括Composition API、更好的TypeScript支持...',
        author_id: 2,
        category_id: 2,
        status: 'published',
        tags: ['Vue.js', '前端', 'JavaScript'],
        created_at: '2023-10-20T10:30:00Z',
        updated_at: '2023-10-20T10:30:00Z',
        published_at: '2023-10-20T10:30:00Z',
        views: 2100,
        likes: 156
    },
    {
        id: 3,
        title: 'RESTful API 设计最佳实践',
        content: 'RESTful API设计需要遵循一定的原则和最佳实践，本文将详细介绍如何设计优秀的RESTful API...',
        author_id: 1,
        category_id: 1,
        status: 'draft',
        tags: ['REST', 'API', '后端'],
        created_at: '2023-10-19T15:45:00Z',
        updated_at: '2023-10-21T09:20:00Z',
        published_at: null,
        views: 0,
        likes: 0
    }
];

// 评论数据
export const mockComments = [
    {
        id: 1,
        content: '很好的文章！对GraphQL的介绍很详细。',
        author_id: 2,
        post_id: 1,
        parent_id: null,
        created_at: '2023-10-21T09:00:00Z',
        updated_at: '2023-10-21T09:00:00Z'
    },
    {
        id: 2,
        content: '感谢分享，学到了很多。',
        author_id: 3,
        post_id: 1,
        parent_id: null,
        created_at: '2023-10-21T10:15:00Z',
        updated_at: '2023-10-21T10:15:00Z'
    },
    {
        id: 3,
        content: '同意，GraphQL确实比REST更灵活。',
        author_id: 4,
        post_id: 1,
        parent_id: 1,
        created_at: '2023-10-21T11:30:00Z',
        updated_at: '2023-10-21T11:30:00Z'
    }
];

// 任务数据
export const mockTasks = [
    {
        id: 1,
        title: '学习Vue.js基础',
        description: '掌握Vue.js的基本概念和语法，包括组件、指令、生命周期等核心知识点。',
        priority: 'high',
        status: 'completed',
        dueDate: '2023-10-25',
        createdAt: '2023-10-20T08:00:00Z',
        updatedAt: '2023-10-24T16:30:00Z',
        completedAt: '2023-10-24T16:30:00Z',
        assignee: 'zhangsan',
        tags: ['学习', 'Vue.js', '前端']
    },
    {
        id: 2,
        title: '实现RESTful API',
        description: '设计和实现用户管理的RESTful API接口，包括用户的增删改查操作。',
        priority: 'medium',
        status: 'progress',
        dueDate: '2023-10-28',
        createdAt: '2023-10-21T09:15:00Z',
        updatedAt: '2023-10-23T14:20:00Z',
        completedAt: null,
        assignee: 'lisi',
        tags: ['开发', 'API', '后端']
    },
    {
        id: 3,
        title: '学习GraphQL',
        description: '了解GraphQL的查询语法和实际应用，对比GraphQL与RESTful API的优缺点。',
        priority: 'high',
        status: 'todo',
        dueDate: '2023-10-30',
        createdAt: '2023-10-22T10:30:00Z',
        updatedAt: '2023-10-22T10:30:00Z',
        completedAt: null,
        assignee: 'wangwu',
        tags: ['学习', 'GraphQL', 'API']
    },
    {
        id: 4,
        title: '优化数据库查询',
        description: '分析现有数据库查询性能，优化慢查询，添加必要的索引。',
        priority: 'medium',
        status: 'todo',
        dueDate: '2023-11-02',
        createdAt: '2023-10-23T11:45:00Z',
        updatedAt: '2023-10-23T11:45:00Z',
        completedAt: null,
        assignee: 'zhaoliu',
        tags: ['优化', '数据库', '性能']
    },
    {
        id: 5,
        title: '设计用户界面',
        description: '设计任务管理系统的用户界面，包括任务列表、创建任务、编辑任务等页面。',
        priority: 'low',
        status: 'progress',
        dueDate: '2023-11-05',
        createdAt: '2023-10-24T13:20:00Z',
        updatedAt: '2023-10-25T09:10:00Z',
        completedAt: null,
        assignee: 'sunqi',
        tags: ['设计', 'UI', '用户体验']
    }
];

// 分类数据
export const mockCategories = [
    {
        id: 1,
        name: 'API开发',
        description: 'API设计和开发相关文章',
        slug: 'api-development',
        created_at: '2023-10-01T00:00:00Z'
    },
    {
        id: 2,
        name: '前端技术',
        description: '前端开发技术和框架',
        slug: 'frontend',
        created_at: '2023-10-01T00:00:00Z'
    },
    {
        id: 3,
        name: '后端技术',
        description: '后端开发技术和架构',
        slug: 'backend',
        created_at: '2023-10-01T00:00:00Z'
    }
];

// GraphQL Schema字符串
export const graphqlSchema = `type User {
  id: ID!
  name: String!
  email: String!
  role: UserRole!
  status: UserStatus!
  bio: String
  avatar: String
  posts: [Post!]!
  createdAt: DateTime!
  updatedAt: DateTime!
  lastLogin: DateTime
}

type Post {
  id: ID!
  title: String!
  content: String!
  author: User!
  category: Category!
  status: PostStatus!
  tags: [String!]!
  comments: [Comment!]!
  createdAt: DateTime!
  updatedAt: DateTime!
  publishedAt: DateTime
  views: Int!
  likes: Int!
}

type Comment {
  id: ID!
  content: String!
  author: User!
  post: Post!
  parent: Comment
  replies: [Comment!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Task {
  id: ID!
  title: String!
  description: String
  priority: TaskPriority!
  status: TaskStatus!
  dueDate: Date
  assignee: String
  tags: [String!]!
  createdAt: DateTime!
  updatedAt: DateTime!
  completedAt: DateTime
}

type Category {
  id: ID!
  name: String!
  description: String
  slug: String!
  posts: [Post!]!
  createdAt: DateTime!
}

enum UserRole {
  ADMIN
  USER
  MODERATOR
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum TaskPriority {
  HIGH
  MEDIUM
  LOW
}

enum TaskStatus {
  TODO
  PROGRESS
  COMPLETED
}

type Query {
  users(limit: Int, offset: Int, filter: UserFilter): [User!]!
  user(id: ID!): User
  posts(limit: Int, offset: Int, filter: PostFilter): [Post!]!
  post(id: ID!): Post
  tasks(limit: Int, offset: Int, filter: TaskFilter): [Task!]!
  task(id: ID!): Task
  search(query: String!): [SearchResult!]!
}

type Mutation {
  createUser(input: CreateUserInput!): User!
  updateUser(id: ID!, input: UpdateUserInput!): User!
  deleteUser(id: ID!): Boolean!
  createPost(input: CreatePostInput!): Post!
  updatePost(id: ID!, input: UpdatePostInput!): Post!
  deletePost(id: ID!): Boolean!
  createTask(input: CreateTaskInput!): Task!
  updateTask(id: ID!, input: UpdateTaskInput!): Task!
  deleteTask(id: ID!): Boolean!
}

type Subscription {
  userCreated: User!
  postCreated: Post!
  taskCreated: Task!
  commentAdded(postId: ID!): Comment!
}

union SearchResult = User | Post | Comment

input UserFilter {
  role: UserRole
  status: UserStatus
  search: String
}

input PostFilter {
  status: PostStatus
  categoryId: ID
  authorId: ID
  search: String
}

input TaskFilter {
  status: TaskStatus
  priority: TaskPriority
  assignee: String
  search: String
}

input CreateUserInput {
  name: String!
  email: String!
  password: String!
  role: UserRole = USER
  bio: String
}

input UpdateUserInput {
  name: String
  email: String
  bio: String
  role: UserRole
  status: UserStatus
}

input CreatePostInput {
  title: String!
  content: String!
  categoryId: ID!
  tags: [String!]
  status: PostStatus = DRAFT
}

input UpdatePostInput {
  title: String
  content: String
  categoryId: ID
  tags: [String!]
  status: PostStatus
}

input CreateTaskInput {
  title: String!
  description: String
  priority: TaskPriority!
  dueDate: Date
  assignee: String
  tags: [String!]
}

input UpdateTaskInput {
  title: String
  description: String
  priority: TaskPriority
  status: TaskStatus
  dueDate: Date
  assignee: String
  tags: [String!]
}

scalar DateTime
scalar Date`;

// 导出所有数据
const mockData = {
    users: mockUsers,
    posts: mockPosts,
    comments: mockComments,
    tasks: mockTasks,
    categories: mockCategories,
    schema: graphqlSchema
};

// 全局访问
window.mockData = mockData;

// ES6模块导出（兼容性）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = mockData;
}

export default mockData;
