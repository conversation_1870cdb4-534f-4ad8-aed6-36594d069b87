# Apollo Server Express 订阅模式详解

## 1. 基础订阅实现

### PubSub 配置

```typescript
// src/pubsub/PubSubManager.ts
import { PubSub } from 'graphql-subscriptions';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import Redis from 'ioredis';

// 订阅事件常量
export const SUBSCRIPTION_EVENTS = {
  // 任务相关
  TASK_CREATED: 'TASK_CREATED',
  TASK_UPDATED: 'TASK_UPDATED',
  TASK_DELETED: 'TASK_DELETED',
  TASK_ASSIGNED: 'TASK_ASSIGNED',
  
  // 项目相关
  PROJECT_CREATED: 'PROJECT_CREATED',
  PROJECT_UPDATED: 'PROJECT_UPDATED',
  PROJECT_MEMBER_ADDED: 'PROJECT_MEMBER_ADDED',
  PROJECT_MEMBER_REMOVED: 'PROJECT_MEMBER_REMOVED',
  
  // 用户相关
  USER_ONLINE: 'USER_ONLINE',
  USER_OFFLINE: 'USER_OFFLINE',
  USER_TYPING: 'USER_TYPING',
  
  // 通知相关
  NOTIFICATION_SENT: 'NOTIFICATION_SENT',
  NOTIFICATION_READ: 'NOTIFICATION_READ',
  
  // 评论相关
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
} as const;

// 创建 PubSub 实例
export const createPubSub = () => {
  if (process.env.REDIS_URL) {
    // 生产环境使用 Redis PubSub
    const options = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
    };

    return new RedisPubSub({
      publisher: new Redis(options),
      subscriber: new Redis(options),
    });
  } else {
    // 开发环境使用内存 PubSub
    return new PubSub();
  }
};

export const pubsub = createPubSub();

// 发布事件的工具函数
export class EventPublisher {
  // 任务事件
  static publishTaskCreated(task: Task, projectId: string) {
    pubsub.publish(SUBSCRIPTION_EVENTS.TASK_CREATED, {
      taskCreated: task,
      projectId,
    });
  }

  static publishTaskUpdated(task: Task, changes: Partial<Task>) {
    pubsub.publish(SUBSCRIPTION_EVENTS.TASK_UPDATED, {
      taskUpdated: task,
      changes,
    });
  }

  static publishTaskAssigned(task: Task, assigneeId: string, assignedBy: string) {
    pubsub.publish(SUBSCRIPTION_EVENTS.TASK_ASSIGNED, {
      taskAssigned: task,
      assigneeId,
      assignedBy,
    });
  }

  // 项目事件
  static publishProjectMemberAdded(project: Project, newMember: User, addedBy: User) {
    pubsub.publish(SUBSCRIPTION_EVENTS.PROJECT_MEMBER_ADDED, {
      projectMemberAdded: {
        project,
        newMember,
        addedBy,
      },
    });
  }

  // 用户状态事件
  static publishUserOnline(user: User) {
    pubsub.publish(SUBSCRIPTION_EVENTS.USER_ONLINE, {
      userStatusChanged: {
        user,
        status: 'ONLINE',
        timestamp: new Date(),
      },
    });
  }

  static publishUserTyping(userId: string, taskId: string, isTyping: boolean) {
    pubsub.publish(SUBSCRIPTION_EVENTS.USER_TYPING, {
      userTyping: {
        userId,
        taskId,
        isTyping,
        timestamp: new Date(),
      },
    });
  }

  // 通知事件
  static publishNotification(notification: Notification) {
    pubsub.publish(SUBSCRIPTION_EVENTS.NOTIFICATION_SENT, {
      notificationSent: notification,
    });
  }

  // 评论事件
  static publishCommentAdded(comment: Comment, taskId: string) {
    pubsub.publish(SUBSCRIPTION_EVENTS.COMMENT_ADDED, {
      commentAdded: comment,
      taskId,
    });
  }
}
```

## 2. 高级订阅 Resolvers

### 带过滤的订阅

```typescript
// src/resolvers/subscriptionResolvers.ts
import { withFilter } from 'graphql-subscriptions';
import { AuthenticationError, ForbiddenError } from 'apollo-server-express';
import { pubsub, SUBSCRIPTION_EVENTS } from '../pubsub/PubSubManager';

export const subscriptionResolvers = {
  Subscription: {
    // 任务创建订阅（按项目过滤）
    taskCreated: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.TASK_CREATED]),
        (payload, variables, context) => {
          // 认证检查
          if (!context.user) {
            throw new AuthenticationError('Authentication required');
          }

          const { projectId } = payload;
          const project = db.getProjectById(projectId);
          
          // 权限检查：只有项目成员才能订阅
          return project && (
            project.ownerId === context.user.id ||
            project.memberIds.includes(context.user.id)
          );
        }
      ),
    },

    // 任务更新订阅（按任务ID过滤）
    taskUpdated: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.TASK_UPDATED]),
        (payload, variables, context) => {
          if (!context.user) return false;

          const task = payload.taskUpdated;
          
          // 检查用户是否有权限查看此任务
          if (variables.taskId && task.id !== variables.taskId) {
            return false;
          }

          const project = db.getProjectById(task.projectId);
          return project && (
            project.ownerId === context.user.id ||
            project.memberIds.includes(context.user.id) ||
            task.assigneeId === context.user.id ||
            task.creatorId === context.user.id
          );
        }
      ),
    },

    // 项目成员添加订阅
    projectMemberAdded: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.PROJECT_MEMBER_ADDED]),
        (payload, variables, context) => {
          if (!context.user) return false;

          const { project, newMember } = payload.projectMemberAdded;
          
          // 只有项目成员和新成员能收到通知
          return (
            project.ownerId === context.user.id ||
            project.memberIds.includes(context.user.id) ||
            newMember.id === context.user.id
          );
        }
      ),
    },

    // 用户在线状态订阅（只订阅团队成员）
    userStatusChanged: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([
          SUBSCRIPTION_EVENTS.USER_ONLINE,
          SUBSCRIPTION_EVENTS.USER_OFFLINE,
        ]),
        (payload, variables, context) => {
          if (!context.user) return false;

          const { user: statusUser } = payload.userStatusChanged;
          
          // 不订阅自己的状态变化
          if (statusUser.id === context.user.id) return false;

          // 检查是否有共同项目
          const userProjects = db.getProjectsByUserId(context.user.id);
          const statusUserProjects = db.getProjectsByUserId(statusUser.id);

          return userProjects.some(up =>
            statusUserProjects.some(sup => sup.id === up.id)
          );
        }
      ),
    },

    // 个人通知订阅
    notificationReceived: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.NOTIFICATION_SENT]),
        (payload, variables, context) => {
          if (!context.user) return false;
          return payload.notificationSent.userId === context.user.id;
        }
      ),
    },

    // 实时打字状态订阅
    userTyping: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.USER_TYPING]),
        (payload, variables, context) => {
          if (!context.user) return false;

          const { userId, taskId } = payload.userTyping;
          
          // 不订阅自己的打字状态
          if (userId === context.user.id) return false;

          // 检查是否有权限查看该任务
          const task = db.getTaskById(taskId);
          if (!task) return false;

          const project = db.getProjectById(task.projectId);
          return project && (
            project.ownerId === context.user.id ||
            project.memberIds.includes(context.user.id)
          );
        }
      ),
    },

    // 评论添加订阅
    commentAdded: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.COMMENT_ADDED]),
        (payload, variables, context) => {
          if (!context.user) return false;

          const { taskId } = payload;
          
          // 如果指定了任务ID，只订阅该任务的评论
          if (variables.taskId && taskId !== variables.taskId) {
            return false;
          }

          const task = db.getTaskById(taskId);
          if (!task) return false;

          const project = db.getProjectById(task.projectId);
          return project && (
            project.ownerId === context.user.id ||
            project.memberIds.includes(context.user.id)
          );
        }
      ),
    },

    // 项目活动流订阅
    projectActivity: {
      subscribe: withFilter(
        () => pubsub.asyncIterator([
          SUBSCRIPTION_EVENTS.TASK_CREATED,
          SUBSCRIPTION_EVENTS.TASK_UPDATED,
          SUBSCRIPTION_EVENTS.TASK_ASSIGNED,
          SUBSCRIPTION_EVENTS.COMMENT_ADDED,
          SUBSCRIPTION_EVENTS.PROJECT_MEMBER_ADDED,
        ]),
        (payload, variables, context) => {
          if (!context.user) return false;

          const { projectId } = variables;
          if (!projectId) return false;

          const project = db.getProjectById(projectId);
          return project && (
            project.ownerId === context.user.id ||
            project.memberIds.includes(context.user.id)
          );
        }
      ),
      resolve: (payload) => {
        // 根据不同的事件类型返回统一的活动格式
        const eventType = Object.keys(payload)[0];
        const eventData = payload[eventType];

        return {
          id: generateId(),
          type: eventType,
          data: eventData,
          timestamp: new Date(),
        };
      },
    },
  },
};
```

## 3. 订阅生命周期管理

### 连接管理

```typescript
// src/subscriptions/ConnectionManager.ts
export class SubscriptionConnectionManager {
  private connections = new Map<string, ConnectionInfo>();

  // 连接信息接口
  interface ConnectionInfo {
    userId: string;
    connectionId: string;
    connectedAt: Date;
    lastActivity: Date;
    subscriptions: Set<string>;
  }

  // 添加连接
  addConnection(connectionId: string, userId: string) {
    this.connections.set(connectionId, {
      userId,
      connectionId,
      connectedAt: new Date(),
      lastActivity: new Date(),
      subscriptions: new Set(),
    });

    console.log(`User ${userId} connected with connection ${connectionId}`);
    
    // 发布用户上线事件
    EventPublisher.publishUserOnline(db.getUserById(userId));
  }

  // 移除连接
  removeConnection(connectionId: string) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      console.log(`User ${connection.userId} disconnected`);
      
      // 检查用户是否还有其他连接
      const userConnections = Array.from(this.connections.values())
        .filter(conn => conn.userId === connection.userId && conn.connectionId !== connectionId);

      if (userConnections.length === 0) {
        // 用户完全离线
        const user = db.getUserById(connection.userId);
        if (user) {
          pubsub.publish(SUBSCRIPTION_EVENTS.USER_OFFLINE, {
            userStatusChanged: {
              user,
              status: 'OFFLINE',
              timestamp: new Date(),
            },
          });
        }
      }

      this.connections.delete(connectionId);
    }
  }

  // 更新活动时间
  updateActivity(connectionId: string) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastActivity = new Date();
    }
  }

  // 添加订阅
  addSubscription(connectionId: string, subscriptionName: string) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.subscriptions.add(subscriptionName);
    }
  }

  // 移除订阅
  removeSubscription(connectionId: string, subscriptionName: string) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.subscriptions.delete(subscriptionName);
    }
  }

  // 获取用户的所有连接
  getUserConnections(userId: string): ConnectionInfo[] {
    return Array.from(this.connections.values())
      .filter(conn => conn.userId === userId);
  }

  // 获取在线用户列表
  getOnlineUsers(): string[] {
    const onlineUsers = new Set<string>();
    this.connections.forEach(conn => {
      onlineUsers.add(conn.userId);
    });
    return Array.from(onlineUsers);
  }

  // 清理非活跃连接
  cleanupInactiveConnections(timeoutMinutes: number = 30) {
    const timeout = timeoutMinutes * 60 * 1000;
    const now = new Date();

    this.connections.forEach((connection, connectionId) => {
      if (now.getTime() - connection.lastActivity.getTime() > timeout) {
        console.log(`Cleaning up inactive connection: ${connectionId}`);
        this.removeConnection(connectionId);
      }
    });
  }
}

export const connectionManager = new SubscriptionConnectionManager();

// 定期清理非活跃连接
setInterval(() => {
  connectionManager.cleanupInactiveConnections(30);
}, 5 * 60 * 1000); // 每5分钟检查一次
```

## 4. WebSocket 服务器配置

### 高级 WebSocket 设置

```typescript
// src/subscriptions/WebSocketServer.ts
import { useServer } from 'graphql-ws/lib/use/ws';
import { WebSocketServer } from 'ws';
import { makeExecutableSchema } from '@graphql-tools/schema';
import jwt from 'jsonwebtoken';
import { connectionManager } from './ConnectionManager';

export const setupWebSocketServer = (httpServer: any, schema: any) => {
  const wsServer = new WebSocketServer({
    server: httpServer,
    path: '/graphql',
    // WebSocket 配置
    perMessageDeflate: {
      zlibDeflateOptions: {
        level: 6,
        chunkSize: 1024,
      },
      threshold: 1024,
      concurrencyLimit: 10,
    },
  });

  const serverCleanup = useServer({
    schema,
    
    // 连接认证
    onConnect: async (ctx) => {
      const token = ctx.connectionParams?.authorization as string;
      
      try {
        if (!token) {
          throw new Error('No authorization token provided');
        }

        const cleanToken = token.replace('Bearer ', '');
        const decoded = jwt.verify(cleanToken, process.env.JWT_SECRET!) as { userId: string };
        const user = db.getUserById(decoded.userId);

        if (!user) {
          throw new Error('Invalid user');
        }

        // 生成连接ID
        const connectionId = generateId();
        
        // 添加到连接管理器
        connectionManager.addConnection(connectionId, user.id);

        console.log(`WebSocket connected: User ${user.username} (${connectionId})`);
        
        return {
          user,
          connectionId,
        };
      } catch (error) {
        console.error('WebSocket connection failed:', error);
        throw new Error('Authentication failed');
      }
    },

    // 连接断开
    onDisconnect: (ctx) => {
      const connectionId = ctx.connectionId;
      if (connectionId) {
        connectionManager.removeConnection(connectionId);
      }
      console.log('WebSocket disconnected');
    },

    // 订阅开始
    onSubscribe: (ctx, message) => {
      const connectionId = ctx.connectionId;
      const operationName = message.payload?.operationName;
      
      if (connectionId && operationName) {
        connectionManager.addSubscription(connectionId, operationName);
        connectionManager.updateActivity(connectionId);
      }

      console.log(`Subscription started: ${operationName}`);
    },

    // 订阅结束
    onComplete: (ctx, message) => {
      const connectionId = ctx.connectionId;
      const operationName = message.payload?.operationName;
      
      if (connectionId && operationName) {
        connectionManager.removeSubscription(connectionId, operationName);
      }

      console.log(`Subscription completed: ${operationName}`);
    },

    // 错误处理
    onError: (ctx, message, errors) => {
      console.error('WebSocket error:', errors);
    },

    // 上下文创建
    context: async (ctx) => {
      const token = ctx.connectionParams?.authorization as string || '';
      let user = null;

      if (token) {
        try {
          const cleanToken = token.replace('Bearer ', '');
          const decoded = jwt.verify(cleanToken, process.env.JWT_SECRET!) as { userId: string };
          user = db.getUserById(decoded.userId);
        } catch (error) {
          console.error('Token verification failed:', error);
        }
      }

      return {
        user,
        connectionId: ctx.connectionId,
        loaders: createDataLoaders(),
      };
    },
  }, wsServer);

  return serverCleanup;
};
```
