/**
 * Vue.js + RESTful API 集成示例
 * 主要应用逻辑
 */

import { mockUsers } from '../data/mock-data.js';

const { createApp, ref, computed, onMounted } = Vue;

createApp({
    setup() {
        // 响应式数据
        const users = ref([]);
        const form = ref({
            name: '',
            email: '',
            role: '',
            bio: ''
        });
        const editingUser = ref(null);
        const loading = ref(false);
        const error = ref('');
        const success = ref('');
        const searchQuery = ref('');
        const filterRole = ref('');
        const currentPage = ref(1);
        const pageSize = ref(6);
        const totalRequests = ref(0);
        const responseTime = ref(0);

        // API基础配置
        const api = axios.create({
            baseURL: '/api',
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // 请求拦截器
        api.interceptors.request.use(
            config => {
                totalRequests.value++;
                config.metadata = { startTime: new Date() };
                return config;
            },
            error => Promise.reject(error)
        );

        // 响应拦截器
        api.interceptors.response.use(
            response => {
                const endTime = new Date();
                const duration = endTime - response.config.metadata.startTime;
                responseTime.value = Math.round((responseTime.value + duration) / 2);
                return response;
            },
            error => {
                handleApiError(error);
                return Promise.reject(error);
            }
        );

        // 计算属性
        const activeUsers = computed(() => {
            return users.value.filter(user => user.status !== 'inactive').length;
        });

        const filteredUsers = computed(() => {
            let filtered = users.value;

            // 搜索过滤
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                filtered = filtered.filter(user => 
                    user.name.toLowerCase().includes(query) ||
                    user.email.toLowerCase().includes(query)
                );
            }

            // 角色过滤
            if (filterRole.value) {
                filtered = filtered.filter(user => user.role === filterRole.value);
            }

            return filtered;
        });

        const totalPages = computed(() => {
            return Math.ceil(filteredUsers.value.length / pageSize.value);
        });

        const paginatedUsers = computed(() => {
            const start = (currentPage.value - 1) * pageSize.value;
            const end = start + pageSize.value;
            return filteredUsers.value.slice(start, end);
        });

        const visiblePages = computed(() => {
            const pages = [];
            const total = totalPages.value;
            const current = currentPage.value;
            
            let start = Math.max(1, current - 2);
            let end = Math.min(total, current + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        });

        // 方法
        const handleApiError = (error) => {
            if (error.response) {
                error.value = `请求失败: ${error.response.status} ${error.response.statusText}`;
            } else if (error.request) {
                error.value = '网络错误，请检查网络连接';
            } else {
                error.value = '请求配置错误';
            }
        };

        const showSuccess = (message) => {
            success.value = message;
            setTimeout(() => {
                success.value = '';
            }, 3000);
        };

        const simulateApiCall = async (operation, data = null) => {
            loading.value = true;
            error.value = '';
            
            try {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
                
                switch (operation) {
                    case 'GET':
                        return { data: [...mockUsers] };
                    case 'POST':
                        const newUser = {
                            id: Date.now(),
                            ...data,
                            avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`,
                            created_at: new Date().toISOString(),
                            status: 'active'
                        };
                        mockUsers.push(newUser);
                        return { data: newUser };
                    case 'PUT':
                        const index = mockUsers.findIndex(u => u.id === data.id);
                        if (index !== -1) {
                            mockUsers[index] = { 
                                ...mockUsers[index], 
                                ...data,
                                updated_at: new Date().toISOString()
                            };
                            return { data: mockUsers[index] };
                        }
                        throw new Error('用户不存在');
                    case 'DELETE':
                        const deleteIndex = mockUsers.findIndex(u => u.id === data);
                        if (deleteIndex !== -1) {
                            mockUsers.splice(deleteIndex, 1);
                            return { data: { success: true } };
                        }
                        throw new Error('用户不存在');
                    default:
                        throw new Error('不支持的操作');
                }
            } catch (err) {
                throw err;
            } finally {
                loading.value = false;
            }
        };

        const fetchUsers = async () => {
            try {
                const response = await simulateApiCall('GET');
                users.value = response.data;
            } catch (err) {
                error.value = '获取用户列表失败: ' + err.message;
            }
        };

        const createUser = async () => {
            try {
                // 验证表单
                if (!form.value.name || !form.value.email || !form.value.role) {
                    error.value = '请填写所有必需字段';
                    return;
                }

                const response = await simulateApiCall('POST', form.value);
                users.value.push(response.data);
                resetForm();
                showSuccess('用户创建成功！');
            } catch (err) {
                error.value = '创建用户失败: ' + err.message;
            }
        };

        const updateUser = async () => {
            try {
                const userData = { ...form.value, id: editingUser.value.id };
                const response = await simulateApiCall('PUT', userData);
                
                const index = users.value.findIndex(u => u.id === editingUser.value.id);
                if (index !== -1) {
                    users.value[index] = response.data;
                }
                
                resetForm();
                showSuccess('用户更新成功！');
            } catch (err) {
                error.value = '更新用户失败: ' + err.message;
            }
        };

        const deleteUser = async (id) => {
            if (!confirm('确定要删除这个用户吗？')) return;
            
            try {
                await simulateApiCall('DELETE', id);
                users.value = users.value.filter(u => u.id !== id);
                showSuccess('用户删除成功！');
            } catch (err) {
                error.value = '删除用户失败: ' + err.message;
            }
        };

        const editUser = (user) => {
            editingUser.value = user;
            form.value = {
                name: user.name,
                email: user.email,
                role: user.role,
                bio: user.bio || ''
            };
        };

        const cancelEdit = () => {
            editingUser.value = null;
            resetForm();
        };

        const resetForm = () => {
            form.value = {
                name: '',
                email: '',
                role: '',
                bio: ''
            };
            editingUser.value = null;
        };

        const refreshUsers = () => {
            fetchUsers();
        };

        const searchUsers = () => {
            currentPage.value = 1; // 重置到第一页
        };

        const filterUsers = () => {
            currentPage.value = 1; // 重置到第一页
        };

        const getRoleLabel = (role) => {
            const roleMap = {
                admin: '管理员',
                user: '普通用户',
                moderator: '版主'
            };
            return roleMap[role] || role;
        };

        const formatDate = (dateString) => {
            return new Date(dateString).toLocaleDateString('zh-CN');
        };

        // 生命周期
        onMounted(() => {
            fetchUsers();
            console.log('🚀 Vue.js + RESTful API 示例已加载');
            console.log('💡 功能包括：');
            console.log('- 用户CRUD操作');
            console.log('- 搜索和过滤');
            console.log('- 分页显示');
            console.log('- 错误处理');
            console.log('- 加载状态');
        });

        return {
            users,
            form,
            editingUser,
            loading,
            error,
            success,
            searchQuery,
            filterRole,
            currentPage,
            pageSize,
            totalRequests,
            responseTime,
            activeUsers,
            filteredUsers,
            totalPages,
            paginatedUsers,
            visiblePages,
            createUser,
            updateUser,
            deleteUser,
            editUser,
            cancelEdit,
            resetForm,
            refreshUsers,
            searchUsers,
            filterUsers,
            getRoleLabel,
            formatDate
        };
    }
}).mount('#app');
