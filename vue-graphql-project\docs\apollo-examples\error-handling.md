# Apollo Server Express 错误处理最佳实践

## 1. 自定义错误类

```typescript
// src/errors/CustomErrors.ts
import { ApolloError } from 'apollo-server-express';

// 业务逻辑错误
export class BusinessLogicError extends ApolloError {
  constructor(message: string, code: string = 'BUSINESS_LOGIC_ERROR') {
    super(message, code);
    this.name = 'BusinessLogicError';
  }
}

// 验证错误
export class ValidationError extends ApolloError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
    this.extensions.field = field;
  }
}

// 资源不存在错误
export class NotFoundError extends ApolloError {
  constructor(resource: string, id?: string) {
    const message = id ? `${resource} with id ${id} not found` : `${resource} not found`;
    super(message, 'NOT_FOUND');
    this.name = 'NotFoundError';
    this.extensions.resource = resource;
    this.extensions.id = id;
  }
}

// 重复资源错误
export class DuplicateError extends ApolloError {
  constructor(resource: string, field: string, value: string) {
    super(`${resource} with ${field} '${value}' already exists`, 'DUPLICATE_ERROR');
    this.name = 'DuplicateError';
    this.extensions.resource = resource;
    this.extensions.field = field;
    this.extensions.value = value;
  }
}

// 速率限制错误
export class RateLimitError extends ApolloError {
  constructor(limit: number, window: string) {
    super(`Rate limit exceeded: ${limit} requests per ${window}`, 'RATE_LIMIT_EXCEEDED');
    this.name = 'RateLimitError';
    this.extensions.limit = limit;
    this.extensions.window = window;
  }
}
```

## 2. 错误处理中间件

```typescript
// src/middleware/errorHandler.ts
import { GraphQLError, GraphQLFormattedError } from 'graphql';
import { ApolloServerPlugin } from 'apollo-server-plugin-base';

// 错误格式化插件
export const errorFormattingPlugin: ApolloServerPlugin = {
  requestDidStart() {
    return {
      willSendResponse(requestContext) {
        if (requestContext.response.errors) {
          requestContext.response.errors = requestContext.response.errors.map(
            (error: GraphQLError) => formatError(error)
          );
        }
      },
    };
  },
};

// 错误格式化函数
export const formatError = (error: GraphQLError): GraphQLFormattedError => {
  // 记录错误
  console.error('GraphQL Error:', {
    message: error.message,
    code: error.extensions?.code,
    path: error.path,
    locations: error.locations,
    stack: error.stack,
  });

  // 开发环境返回完整错误信息
  if (process.env.NODE_ENV === 'development') {
    return {
      message: error.message,
      locations: error.locations,
      path: error.path,
      extensions: {
        code: error.extensions?.code || 'INTERNAL_ERROR',
        ...error.extensions,
        stack: error.stack,
      },
    };
  }

  // 生产环境隐藏敏感信息
  const code = error.extensions?.code || 'INTERNAL_ERROR';
  
  // 对于内部错误，返回通用消息
  if (code === 'INTERNAL_ERROR' || !error.extensions?.code) {
    return {
      message: 'An internal error occurred',
      extensions: {
        code: 'INTERNAL_ERROR',
      },
    };
  }

  // 返回安全的错误信息
  return {
    message: error.message,
    locations: error.locations,
    path: error.path,
    extensions: {
      code,
      field: error.extensions?.field,
      resource: error.extensions?.resource,
    },
  };
};

// 错误监控插件
export const errorMonitoringPlugin: ApolloServerPlugin = {
  requestDidStart() {
    return {
      didEncounterErrors(requestContext) {
        // 发送错误到监控服务（如 Sentry）
        requestContext.errors?.forEach((error) => {
          if (error.extensions?.code === 'INTERNAL_ERROR') {
            // 发送到错误监控服务
            console.error('Critical Error:', error);
            // Sentry.captureException(error);
          }
        });
      },
    };
  },
};
```

## 3. Resolver 中的错误处理

```typescript
// src/resolvers/userResolvers.ts
import {
  AuthenticationError,
  ForbiddenError,
  UserInputError,
} from 'apollo-server-express';
import {
  ValidationError,
  NotFoundError,
  DuplicateError,
  BusinessLogicError,
} from '../errors/CustomErrors';

const userResolvers = {
  Query: {
    user: async (parent, { id }, context) => {
      try {
        // 认证检查
        if (!context.user) {
          throw new AuthenticationError('You must be logged in to view user details');
        }

        // 输入验证
        if (!id || typeof id !== 'string') {
          throw new ValidationError('Valid user ID is required', 'id');
        }

        const user = db.getUserById(id);
        
        if (!user) {
          throw new NotFoundError('User', id);
        }

        // 权限检查
        if (context.user.id !== id && context.user.role !== 'ADMIN') {
          throw new ForbiddenError('You can only view your own profile');
        }

        return user;
      } catch (error) {
        // 重新抛出已知错误
        if (error instanceof ApolloError) {
          throw error;
        }
        
        // 处理未知错误
        console.error('Unexpected error in user query:', error);
        throw new Error('Failed to fetch user');
      }
    },
  },

  Mutation: {
    createUser: async (parent, { input }, context) => {
      try {
        // 权限检查
        if (!context.user || context.user.role !== 'ADMIN') {
          throw new ForbiddenError('Only administrators can create users');
        }

        // 输入验证
        const validationErrors = validateUserInput(input);
        if (validationErrors.length > 0) {
          throw new ValidationError(
            `Validation failed: ${validationErrors.join(', ')}`,
            'input'
          );
        }

        // 检查邮箱是否已存在
        const existingUser = db.getUserByEmail(input.email);
        if (existingUser) {
          throw new DuplicateError('User', 'email', input.email);
        }

        // 检查用户名是否已存在
        const existingUsername = db.getUserByUsername(input.username);
        if (existingUsername) {
          throw new DuplicateError('User', 'username', input.username);
        }

        // 业务逻辑验证
        if (input.role === 'ADMIN' && context.user.role !== 'ADMIN') {
          throw new BusinessLogicError('Only administrators can create admin users');
        }

        const user = await db.createUser(input);
        
        // 发送欢迎邮件
        try {
          await sendWelcomeEmail(user.email);
        } catch (emailError) {
          // 邮件发送失败不应该影响用户创建
          console.warn('Failed to send welcome email:', emailError);
        }

        return user;
      } catch (error) {
        if (error instanceof ApolloError) {
          throw error;
        }
        
        console.error('Unexpected error in createUser:', error);
        throw new Error('Failed to create user');
      }
    },

    updateUser: async (parent, { id, input }, context) => {
      try {
        if (!context.user) {
          throw new AuthenticationError('Authentication required');
        }

        const user = db.getUserById(id);
        if (!user) {
          throw new NotFoundError('User', id);
        }

        // 权限检查
        if (context.user.id !== id && context.user.role !== 'ADMIN') {
          throw new ForbiddenError('You can only update your own profile');
        }

        // 如果更新邮箱，检查是否已存在
        if (input.email && input.email !== user.email) {
          const existingUser = db.getUserByEmail(input.email);
          if (existingUser) {
            throw new DuplicateError('User', 'email', input.email);
          }
        }

        // 角色更新权限检查
        if (input.role && input.role !== user.role) {
          if (context.user.role !== 'ADMIN') {
            throw new ForbiddenError('Only administrators can change user roles');
          }
          
          if (input.role === 'ADMIN' && context.user.id === id) {
            throw new BusinessLogicError('You cannot change your own role');
          }
        }

        const updatedUser = await db.updateUser(id, input);
        return updatedUser;
      } catch (error) {
        if (error instanceof ApolloError) {
          throw error;
        }
        
        console.error('Unexpected error in updateUser:', error);
        throw new Error('Failed to update user');
      }
    },
  },
};

// 输入验证函数
function validateUserInput(input: any): string[] {
  const errors: string[] = [];

  if (!input.username || input.username.length < 3) {
    errors.push('Username must be at least 3 characters long');
  }

  if (!input.email || !isValidEmail(input.email)) {
    errors.push('Valid email address is required');
  }

  if (!input.password || input.password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!input.firstName || input.firstName.trim().length === 0) {
    errors.push('First name is required');
  }

  if (!input.lastName || input.lastName.trim().length === 0) {
    errors.push('Last name is required');
  }

  return errors;
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

## 4. 全局错误处理配置

```typescript
// src/server.ts
import { ApolloServer } from 'apollo-server-express';
import { 
  errorFormattingPlugin, 
  errorMonitoringPlugin, 
  formatError 
} from './middleware/errorHandler';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: createContext,
  formatError, // 全局错误格式化
  plugins: [
    errorFormattingPlugin,
    errorMonitoringPlugin,
    // 请求日志插件
    {
      requestDidStart() {
        return {
          didResolveOperation(requestContext) {
            console.log(`Operation: ${requestContext.request.operationName}`);
          },
          didEncounterErrors(requestContext) {
            console.error('Request errors:', requestContext.errors?.map(e => ({
              message: e.message,
              code: e.extensions?.code,
              path: e.path,
            })));
          },
        };
      },
    },
  ],
  // 开发环境启用调试
  debug: process.env.NODE_ENV === 'development',
  introspection: true,
});
```
