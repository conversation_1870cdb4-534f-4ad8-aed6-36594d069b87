/**
 * 任务管理系统主逻辑
 * 支持RESTful和GraphQL双模式
 */

// 初始化API实例
let restfulAPI, graphqlAPI;

// Vue应用实例
const { createApp, ref, computed, onMounted, watch } = Vue;

const TaskManagerApp = {
    setup() {
        // 响应式数据
        const apiMode = ref('restful'); // 'restful' 或 'graphql'
        const tasks = ref([]);
        const loading = ref(false);
        const error = ref('');
        const searchQuery = ref('');
        const filterStatus = ref('all');
        const filterPriority = ref('all');
        
        // 新任务表单
        const newTask = ref({
            title: '',
            description: '',
            priority: 'medium',
            assignee: ''
        });
        
        // 编辑任务
        const editingTask = ref(null);
        const editForm = ref({
            title: '',
            description: '',
            priority: 'medium',
            status: 'todo'
        });

        // 统计数据
        const stats = computed(() => {
            const total = tasks.value.length;
            const completed = tasks.value.filter(t => t.status === 'completed').length;
            const inProgress = tasks.value.filter(t => t.status === 'progress').length;
            const todo = tasks.value.filter(t => t.status === 'todo').length;
            const highPriority = tasks.value.filter(t => t.priority === 'high').length;
            
            return { total, completed, inProgress, todo, highPriority };
        });

        // 过滤后的任务
        const filteredTasks = computed(() => {
            let filtered = tasks.value;
            
            // 搜索过滤
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                filtered = filtered.filter(task => 
                    task.title.toLowerCase().includes(query) ||
                    task.description.toLowerCase().includes(query) ||
                    task.assignee.toLowerCase().includes(query)
                );
            }
            
            // 状态过滤
            if (filterStatus.value !== 'all') {
                filtered = filtered.filter(task => task.status === filterStatus.value);
            }
            
            // 优先级过滤
            if (filterPriority.value !== 'all') {
                filtered = filtered.filter(task => task.priority === filterPriority.value);
            }
            
            return filtered;
        });

        // 初始化API
        const initializeAPIs = () => {
            restfulAPI = new RESTfulAPI();
            graphqlAPI = new GraphQLAPI();
            
            // 订阅GraphQL事件
            graphqlAPI.subscribe('taskAdded', (task) => {
                if (apiMode.value === 'graphql') {
                    tasks.value.push(task);
                }
            });
            
            graphqlAPI.subscribe('taskUpdated', (task) => {
                if (apiMode.value === 'graphql') {
                    const index = tasks.value.findIndex(t => t.id === task.id);
                    if (index >= 0) {
                        tasks.value[index] = task;
                    }
                }
            });
            
            graphqlAPI.subscribe('taskDeleted', (taskId) => {
                if (apiMode.value === 'graphql') {
                    const index = tasks.value.findIndex(t => t.id == taskId);
                    if (index >= 0) {
                        tasks.value.splice(index, 1);
                    }
                }
            });
        };

        // 加载任务
        const loadTasks = async () => {
            loading.value = true;
            error.value = '';
            
            try {
                if (apiMode.value === 'restful') {
                    const response = await restfulAPI.get('/tasks');
                    tasks.value = response.data;
                } else {
                    const query = `
                        query {
                            tasks {
                                id
                                title
                                description
                                priority
                                status
                                dueDate
                                assignee
                                tags
                                createdAt
                                updatedAt
                            }
                        }
                    `;
                    const response = await graphqlAPI.execute(query);
                    if (response.errors) {
                        throw new Error(response.errors[0].message);
                    }
                    tasks.value = response.data.tasks;
                }
            } catch (err) {
                error.value = `加载任务失败: ${err.message}`;
                console.error('Load tasks error:', err);
            } finally {
                loading.value = false;
            }
        };

        // 创建任务
        const createTask = async () => {
            if (!newTask.value.title.trim()) {
                error.value = '任务标题不能为空';
                return;
            }
            
            loading.value = true;
            error.value = '';
            
            try {
                if (apiMode.value === 'restful') {
                    const response = await restfulAPI.post('/tasks', newTask.value);
                    tasks.value.push(response.data);
                } else {
                    const mutation = `
                        mutation CreateTask($title: String!, $description: String, $priority: String!, $assignee: String) {
                            createTask(title: $title, description: $description, priority: $priority, assignee: $assignee) {
                                id
                                title
                                description
                                priority
                                status
                                dueDate
                                assignee
                                tags
                                createdAt
                                updatedAt
                            }
                        }
                    `;
                    const response = await graphqlAPI.execute(mutation, newTask.value);
                    if (response.errors) {
                        throw new Error(response.errors[0].message);
                    }
                    // GraphQL订阅会自动更新任务列表
                }
                
                // 重置表单
                newTask.value = {
                    title: '',
                    description: '',
                    priority: 'medium',
                    assignee: ''
                };
            } catch (err) {
                error.value = `创建任务失败: ${err.message}`;
                console.error('Create task error:', err);
            } finally {
                loading.value = false;
            }
        };

        // 更新任务
        const updateTask = async (task, updates) => {
            loading.value = true;
            error.value = '';
            
            try {
                if (apiMode.value === 'restful') {
                    const response = await restfulAPI.put(`/tasks/${task.id}`, updates);
                    const index = tasks.value.findIndex(t => t.id === task.id);
                    if (index >= 0) {
                        tasks.value[index] = response.data;
                    }
                } else {
                    const mutation = `
                        mutation UpdateTask($id: ID!, $title: String, $description: String, $priority: String, $status: String) {
                            updateTask(id: $id, title: $title, description: $description, priority: $priority, status: $status) {
                                id
                                title
                                description
                                priority
                                status
                                dueDate
                                assignee
                                tags
                                createdAt
                                updatedAt
                            }
                        }
                    `;
                    const variables = { id: task.id, ...updates };
                    const response = await graphqlAPI.execute(mutation, variables);
                    if (response.errors) {
                        throw new Error(response.errors[0].message);
                    }
                    // GraphQL订阅会自动更新任务列表
                }
            } catch (err) {
                error.value = `更新任务失败: ${err.message}`;
                console.error('Update task error:', err);
            } finally {
                loading.value = false;
            }
        };

        // 删除任务
        const deleteTask = async (task) => {
            if (!confirm(`确定要删除任务"${task.title}"吗？`)) {
                return;
            }
            
            loading.value = true;
            error.value = '';
            
            try {
                if (apiMode.value === 'restful') {
                    await restfulAPI.delete(`/tasks/${task.id}`);
                    const index = tasks.value.findIndex(t => t.id === task.id);
                    if (index >= 0) {
                        tasks.value.splice(index, 1);
                    }
                } else {
                    const mutation = `
                        mutation DeleteTask($id: ID!) {
                            deleteTask(id: $id)
                        }
                    `;
                    const response = await graphqlAPI.execute(mutation, { id: task.id });
                    if (response.errors) {
                        throw new Error(response.errors[0].message);
                    }
                    // GraphQL订阅会自动更新任务列表
                }
            } catch (err) {
                error.value = `删除任务失败: ${err.message}`;
                console.error('Delete task error:', err);
            } finally {
                loading.value = false;
            }
        };

        // 切换API模式
        const switchApiMode = (mode) => {
            apiMode.value = mode;
            loadTasks();
        };

        // 开始编辑任务
        const startEdit = (task) => {
            editingTask.value = task;
            editForm.value = {
                title: task.title,
                description: task.description,
                priority: task.priority,
                status: task.status
            };
        };

        // 保存编辑
        const saveEdit = async () => {
            if (!editForm.value.title.trim()) {
                error.value = '任务标题不能为空';
                return;
            }
            
            await updateTask(editingTask.value, editForm.value);
            editingTask.value = null;
        };

        // 取消编辑
        const cancelEdit = () => {
            editingTask.value = null;
        };

        // 获取优先级颜色
        const getPriorityColor = (priority) => {
            const colors = {
                high: '#ff4757',
                medium: '#ffa502',
                low: '#2ed573'
            };
            return colors[priority] || '#6c757d';
        };

        // 获取状态颜色
        const getStatusColor = (status) => {
            const colors = {
                todo: '#6c757d',
                progress: '#3742fa',
                completed: '#2ed573'
            };
            return colors[status] || '#6c757d';
        };

        // 监听API模式变化
        watch(apiMode, () => {
            console.log(`切换到 ${apiMode.value.toUpperCase()} 模式`);
        });

        // 组件挂载时初始化
        onMounted(() => {
            initializeAPIs();
            loadTasks();
        });

        return {
            // 数据
            apiMode,
            tasks,
            loading,
            error,
            searchQuery,
            filterStatus,
            filterPriority,
            newTask,
            editingTask,
            editForm,
            stats,
            filteredTasks,
            
            // 方法
            loadTasks,
            createTask,
            updateTask,
            deleteTask,
            switchApiMode,
            startEdit,
            saveEdit,
            cancelEdit,
            getPriorityColor,
            getStatusColor
        };
    }
};

// 创建并挂载Vue应用
createApp(TaskManagerApp).mount('#app');
