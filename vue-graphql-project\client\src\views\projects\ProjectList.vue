<template>
  <div class="project-list-container">
    <div class="page-header">
      <div class="header-left">
        <h1>项目管理</h1>
        <p>管理和查看所有项目</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索项目名称或描述"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="项目状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="进行中" value="ACTIVE" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已暂停" value="PAUSED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="priorityFilter" placeholder="优先级" clearable>
            <el-option label="全部" value="" />
            <el-option label="高" value="HIGH" />
            <el-option label="中" value="MEDIUM" />
            <el-option label="低" value="LOW" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 项目列表 -->
    <el-card class="project-grid">
      <el-row :gutter="20">
        <el-col
          v-for="project in filteredProjects"
          :key="project.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
        >
          <div class="project-card" @click="viewProject(project.id)">
            <div class="project-header">
              <h3 class="project-title">{{ project.name }}</h3>
              <el-tag :type="getStatusType(project.status)" size="small">
                {{ getStatusText(project.status) }}
              </el-tag>
            </div>
            
            <p class="project-description">{{ project.description }}</p>
            
            <div class="project-meta">
              <div class="meta-item">
                <el-icon><Calendar /></el-icon>
                <span>{{ formatDate(project.createdAt) }}</span>
              </div>
              <div class="meta-item">
                <el-icon><User /></el-icon>
                <span>{{ project.owner?.username || '未分配' }}</span>
              </div>
            </div>
            
            <div class="project-progress">
              <div class="progress-label">
                <span>进度</span>
                <span>{{ project.progress }}%</span>
              </div>
              <el-progress
                :percentage="project.progress"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
            
            <div class="project-priority">
              <el-tag
                :type="getPriorityType(project.priority)"
                size="small"
                effect="plain"
              >
                {{ getPriorityText(project.priority) }}
              </el-tag>
            </div>
            
            <div class="project-actions">
              <el-button size="small" @click.stop="editProject(project)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click.stop="deleteProject(project.id)"
              >
                删除
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <div v-if="filteredProjects.length === 0" class="empty-state">
        <el-empty description="暂无项目数据" />
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48, 96]"
        :total="totalProjects"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑项目对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingProject ? '编辑项目' : '新建项目'"
      width="600px"
    >
      <el-form
        ref="projectFormRef"
        :model="projectForm"
        :rules="projectRules"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
        </el-form-item>
        
        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入项目描述"
          />
        </el-form-item>
        
        <el-form-item label="项目状态" prop="status">
          <el-select v-model="projectForm.status" placeholder="请选择状态">
            <el-option label="进行中" value="ACTIVE" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已暂停" value="PAUSED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="projectForm.priority" placeholder="请选择优先级">
            <el-option label="高" value="HIGH" />
            <el-option label="中" value="MEDIUM" />
            <el-option label="低" value="LOW" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目进度" prop="progress">
          <el-slider
            v-model="projectForm.progress"
            :min="0"
            :max="100"
            show-input
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProject" :loading="saving">
          {{ editingProject ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Calendar,
  User
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const editingProject = ref(null)
const searchQuery = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const totalProjects = ref(0)

const projectFormRef = ref()
const projectForm = reactive({
  name: '',
  description: '',
  status: 'ACTIVE',
  priority: 'MEDIUM',
  progress: 0
})

// 模拟项目数据
const projects = ref([
  {
    id: '1',
    name: '电商平台重构',
    description: '使用Vue3和GraphQL重构现有电商平台',
    status: 'ACTIVE',
    priority: 'HIGH',
    progress: 75,
    createdAt: '2024-01-15T00:00:00Z',
    owner: { username: '张三' }
  },
  {
    id: '2',
    name: '移动端APP开发',
    description: '开发iOS和Android移动应用',
    status: 'ACTIVE',
    priority: 'MEDIUM',
    progress: 45,
    createdAt: '2024-01-10T00:00:00Z',
    owner: { username: '李四' }
  },
  {
    id: '3',
    name: '数据分析系统',
    description: '构建实时数据分析和可视化系统',
    status: 'COMPLETED',
    priority: 'HIGH',
    progress: 100,
    createdAt: '2024-01-05T00:00:00Z',
    owner: { username: '王五' }
  }
])

// 表单验证规则
const projectRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入项目描述', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择项目状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const filteredProjects = computed(() => {
  let filtered = projects.value

  if (searchQuery.value) {
    filtered = filtered.filter(project =>
      project.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(project => project.status === statusFilter.value)
  }

  if (priorityFilter.value) {
    filtered = filtered.filter(project => project.priority === priorityFilter.value)
  }

  return filtered
})

// 方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    ACTIVE: 'success',
    COMPLETED: 'info',
    PAUSED: 'warning',
    CANCELLED: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    ACTIVE: '进行中',
    COMPLETED: '已完成',
    PAUSED: '已暂停',
    CANCELLED: '已取消'
  }
  return texts[status] || '未知'
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    HIGH: 'danger',
    MEDIUM: 'warning',
    LOW: 'success'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    HIGH: '高优先级',
    MEDIUM: '中优先级',
    LOW: '低优先级'
  }
  return texts[priority] || '未知'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  priorityFilter.value = ''
}

const viewProject = (projectId: string) => {
  router.push(`/projects/${projectId}`)
}

const editProject = (project: any) => {
  editingProject.value = project
  Object.assign(projectForm, project)
  showCreateDialog.value = true
}

const deleteProject = async (projectId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个项目吗？', '确认删除', {
      type: 'warning'
    })
    
    // 这里应该调用 GraphQL mutation 删除项目
    projects.value = projects.value.filter(p => p.id !== projectId)
    ElMessage.success('项目删除成功')
  } catch {
    // 用户取消删除
  }
}

const saveProject = async () => {
  if (!projectFormRef.value) return

  try {
    await projectFormRef.value.validate()
    saving.value = true

    // 这里应该调用 GraphQL mutation 保存项目
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingProject.value) {
      // 更新项目
      const index = projects.value.findIndex(p => p.id === editingProject.value.id)
      if (index !== -1) {
        projects.value[index] = { ...projects.value[index], ...projectForm }
      }
      ElMessage.success('项目更新成功')
    } else {
      // 创建新项目
      const newProject = {
        id: Date.now().toString(),
        ...projectForm,
        createdAt: new Date().toISOString(),
        owner: { username: '当前用户' }
      }
      projects.value.unshift(newProject)
      ElMessage.success('项目创建成功')
    }

    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingProject.value = null
  Object.assign(projectForm, {
    name: '',
    description: '',
    status: 'ACTIVE',
    priority: 'MEDIUM',
    progress: 0
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 生命周期
onMounted(() => {
  totalProjects.value = projects.value.length
})
</script>

<style scoped lang="scss">
.project-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    h1 {
      margin: 0 0 5px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #909399;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
}

.project-grid {
  margin-bottom: 20px;
}

.project-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .project-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.project-description {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #909399;
    font-size: 12px;
  }
}

.project-progress {
  margin-bottom: 15px;

  .progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 12px;
    color: #606266;
  }
}

.project-priority {
  margin-bottom: 15px;
}

.project-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .filter-card .el-row {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
