# Git 冲突解决详解

## ⚔️ 冲突产生的原因

### 📋 什么是冲突？
冲突发生在 Git 无法自动合并两个分支的修改时，通常出现在：

```bash
# 冲突场景：
1. 同一文件的同一行被不同分支修改
2. 一个分支删除文件，另一个分支修改文件
3. 一个分支重命名文件，另一个分支修改文件
4. 二进制文件被不同分支修改
```

### 🔍 冲突类型
```bash
# 1. 内容冲突 (Content Conflict)
# 两个分支修改了同一文件的同一部分

# 2. 删除/修改冲突 (Delete/Modify Conflict)
# 一个分支删除文件，另一个分支修改文件

# 3. 重命名冲突 (Rename Conflict)
# 两个分支将同一文件重命名为不同名称

# 4. 模式冲突 (Mode Conflict)
# 文件权限或类型发生冲突
```

## 🔄 合并冲突解决

### 📝 识别冲突
```bash
# 1. 尝试合并
git merge feature-branch
# Auto-merging src/app.js
# CONFLICT (content): Merge conflict in src/app.js
# Automatic merge failed; fix conflicts and then commit the result.

# 2. 查看冲突状态
git status
# On branch main
# You have unmerged paths.
#   (fix conflicts and run "git commit")
#   (use "git merge --abort" to abort the merge)
# 
# Unmerged paths:
#   (use "git add <file>..." to mark resolution)
#         both modified:   src/app.js

# 3. 查看冲突文件
git diff
# 或者查看具体文件
git diff src/app.js
```

### 🔧 冲突标记解读
```javascript
// 冲突文件内容示例
function calculateTotal(items) {
<<<<<<< HEAD
    // 主分支的实现
    return items.reduce((sum, item) => sum + item.price, 0);
=======
    // 功能分支的实现
    let total = 0;
    for (let item of items) {
        total += item.price * item.quantity;
    }
    return total;
>>>>>>> feature-branch
}

// 冲突标记说明：
// <<<<<<< HEAD - 当前分支（HEAD）的内容开始
// ======= - 分隔符，分隔两个版本
// >>>>>>> feature-branch - 合并分支的内容结束
```

### ✏️ 手动解决冲突
```bash
# 1. 编辑冲突文件，选择保留的内容
# 可以选择：
# - 保留当前分支的内容
# - 保留合并分支的内容
# - 合并两个版本的内容
# - 编写全新的内容

# 2. 删除冲突标记
# 移除 <<<<<<<, =======, >>>>>>> 标记

# 3. 保存文件

# 4. 标记冲突已解决
git add src/app.js

# 5. 查看解决状态
git status
# On branch main
# All conflicts fixed but you are still merging.
#   (use "git commit" to conclude merge)

# 6. 完成合并
git commit
# 或者自定义提交信息
git commit -m "Merge feature-branch: resolve conflicts in app.js"
```

### 🛠️ 使用合并工具
```bash
# 启动默认合并工具
git mergetool

# 配置 VS Code 作为合并工具
git config --global merge.tool vscode
git config --global mergetool.vscode.cmd 'code --wait $MERGED'

# 配置 Vim 作为合并工具
git config --global merge.tool vimdiff

# 配置其他工具
git config --global merge.tool meld      # Meld
git config --global merge.tool kdiff3    # KDiff3
git config --global merge.tool p4merge   # Perforce P4Merge

# 查看可用的合并工具
git mergetool --tool-help
```

### 🔍 查看冲突的不同版本
```bash
# 查看共同祖先版本
git show :1:src/app.js

# 查看当前分支版本
git show :2:src/app.js

# 查看合并分支版本
git show :3:src/app.js

# 比较不同版本
git diff :1:src/app.js :2:src/app.js  # 祖先 vs 当前
git diff :1:src/app.js :3:src/app.js  # 祖先 vs 合并分支
git diff :2:src/app.js :3:src/app.js  # 当前 vs 合并分支
```

## 🔄 变基冲突解决

### 📝 变基冲突处理
```bash
# 1. 开始变基
git checkout feature-branch
git rebase main
# CONFLICT (content): Merge conflict in src/app.js
# error: could not apply abc123... Add new feature

# 2. 查看冲突状态
git status
# interactive rebase in progress; onto def456
# Last command done (1 command done):
#    pick abc123 Add new feature
# No commands remaining.
# You are currently rebasing branch 'feature-branch' on 'def456'.
#   (fix conflicts and then run "git rebase --continue")
#   (use "git rebase --skip" to skip this patch)
#   (use "git rebase --abort" to check out the original branch)

# 3. 解决冲突
# 编辑冲突文件...
git add src/app.js

# 4. 继续变基
git rebase --continue

# 5. 如果还有冲突，重复步骤 3-4
# 直到变基完成
```

### 🎯 变基冲突选项
```bash
# 继续变基（解决冲突后）
git rebase --continue

# 跳过当前提交
git rebase --skip

# 中止变基，回到原始状态
git rebase --abort

# 编辑提交信息（在 continue 之前）
git commit --amend
```

### 🔄 交互式变基冲突
```bash
# 启动交互式变基
git rebase -i HEAD~3

# 如果在 squash 或 fixup 时发生冲突
# 1. 解决冲突
# 2. git add 冲突文件
# 3. git rebase --continue
# 4. 编辑合并后的提交信息（如果需要）
```

## 🚨 特殊冲突类型

### 🗑️ 删除/修改冲突
```bash
# 场景：一个分支删除文件，另一个分支修改文件
git merge feature-branch
# CONFLICT (modify/delete): src/old-file.js deleted in feature-branch and modified in HEAD.

# 解决方案1：保留文件
git add src/old-file.js

# 解决方案2：删除文件
git rm src/old-file.js

# 完成合并
git commit
```

### 🔄 重命名冲突
```bash
# 场景：两个分支将同一文件重命名为不同名称
git merge feature-branch
# CONFLICT (rename/rename): src/utils.js renamed to src/helpers.js in HEAD and to src/utilities.js in feature-branch.

# 解决方案1：选择一个名称
git rm src/utilities.js
git add src/helpers.js

# 解决方案2：保留两个文件
git add src/helpers.js
git add src/utilities.js

# 解决方案3：重命名为新名称
git mv src/helpers.js src/common-utils.js
git rm src/utilities.js
git add src/common-utils.js

# 完成合并
git commit
```

### 📁 目录冲突
```bash
# 场景：一个分支添加文件到目录，另一个分支删除目录
git merge feature-branch
# CONFLICT (file/directory): src/components is a directory in feature-branch but a file in HEAD.

# 解决方案：
# 1. 决定保留文件还是目录
# 2. 相应地删除或重命名冲突项
# 3. 添加解决后的内容
git add .
git commit
```

## 🔧 高级冲突解决技巧

### 🎯 预防冲突
```bash
# 1. 频繁同步主分支
git checkout feature-branch
git fetch origin
git rebase origin/main

# 2. 小而频繁的提交
git add -p  # 部分添加
git commit -m "Small incremental change"

# 3. 使用 .gitattributes 配置合并策略
echo "*.generated merge=ours" >> .gitattributes
echo "package-lock.json merge=ours" >> .gitattributes

# 4. 团队协作规范
# - 避免同时修改同一文件
# - 定期沟通修改计划
# - 使用功能分支隔离开发
```

### 🔍 分析冲突历史
```bash
# 查找引起冲突的提交
git log --merge

# 查看冲突文件的修改历史
git log --oneline -p src/app.js

# 查看两个分支的分歧点
git merge-base main feature-branch

# 查看分歧后的修改
git log --oneline main..feature-branch
git log --oneline feature-branch..main
```

### 🛠️ 自定义合并策略
```bash
# 为特定文件类型设置合并策略
git config merge.ours.driver true
echo "*.generated merge=ours" >> .gitattributes

# 为二进制文件设置策略
echo "*.png binary" >> .gitattributes
echo "*.jpg binary" >> .gitattributes

# 忽略空白字符差异
git config merge.renormalize true
```

## 💡 冲突解决最佳实践

### ✅ 解决冲突的步骤
```bash
# 1. 理解冲突
git status                    # 查看冲突文件
git diff                      # 查看冲突内容
git log --merge               # 查看相关提交

# 2. 分析代码
# - 理解两个版本的意图
# - 考虑业务逻辑
# - 查看相关测试

# 3. 解决冲突
# - 保留正确的逻辑
# - 合并有用的功能
# - 确保代码质量

# 4. 测试验证
npm test                      # 运行测试
npm run lint                  # 检查代码规范
git diff --check              # 检查空白字符

# 5. 提交解决
git add .
git commit -m "Resolve merge conflicts in feature integration"
```

### 🔒 安全解决流程
```bash
# 1. 备份当前状态
git stash push -m "Backup before conflict resolution"

# 2. 在测试分支上解决
git checkout -b conflict-resolution
git merge feature-branch
# 解决冲突...

# 3. 测试解决结果
npm test
npm run build

# 4. 如果测试通过，应用到主分支
git checkout main
git merge conflict-resolution

# 5. 清理
git branch -d conflict-resolution
git stash drop
```

### 📝 冲突解决文档
```bash
# 在提交信息中记录冲突解决
git commit -m "Merge feature-branch: resolve conflicts

Conflicts resolved:
- src/app.js: Combined authentication logic from both branches
- src/utils.js: Kept main branch version, added helper from feature
- package.json: Updated dependencies to latest versions

Testing:
- All unit tests pass
- Integration tests verified
- Manual testing completed

Reviewed-by: @teammate"
```

## 🚨 紧急冲突处理

### 💊 快速解决方案
```bash
# 1. 中止当前合并
git merge --abort
git rebase --abort

# 2. 使用策略性合并
git merge -X ours feature-branch    # 优先当前分支
git merge -X theirs feature-branch  # 优先功能分支

# 3. 重新开始
git reset --hard HEAD~1             # 撤销合并
# 重新规划合并策略
```

### 🔧 工具辅助解决
```bash
# 使用图形化工具
gitk --merge                        # 可视化冲突
git gui                             # 图形界面

# 使用 IDE 集成
code .                              # VS Code
idea .                              # IntelliJ IDEA

# 使用专业合并工具
meld                                # Meld
kdiff3                              # KDiff3
```

---

## 🚀 下一步学习

掌握冲突解决后，建议学习：
1. **分支策略** - 学习不同的工作流程以减少冲突
2. **高级合并** - cherry-pick、subtree 等高级技巧
3. **团队协作** - 建立团队的冲突处理规范

**记住**: 冲突是团队协作中的正常现象，关键是要有系统的解决方法和良好的沟通！ ⚔️
