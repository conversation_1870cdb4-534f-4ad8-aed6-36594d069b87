<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rem响应式布局实战</title>
    <style>
        /* 动态设置根字体大小 */
        html {
            font-size: 16px; /* 默认基准 */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }

        /* 容器布局 */
        .container {
            max-width: 75rem; /* 1200px */
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* 头部导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.1);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            font-size: 1rem;
            transition: opacity 0.3s;
        }

        .nav-link:hover {
            opacity: 0.8;
        }

        /* 移动端菜单按钮 */
        .menu-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* 主要内容区域 */
        .main {
            padding: 2rem 0;
        }

        /* 英雄区域 */
        .hero {
            text-align: center;
            padding: 4rem 0;
            background: white;
            border-radius: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
        }

        .hero-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: #666;
            margin-bottom: 2rem;
        }

        .hero-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            font-size: 1.125rem;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .hero-button:hover {
            transform: translateY(-0.125rem);
        }

        /* 卡片网格布局 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card:hover {
            transform: translateY(-0.25rem);
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        }

        .card-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .card-text {
            color: #666;
            line-height: 1.6;
        }

        /* 特性展示区域 */
        .features {
            background: white;
            border-radius: 1rem;
            padding: 3rem;
            margin: 3rem 0;
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
        }

        .features-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #2c3e50;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(15rem, 1fr));
            gap: 2rem;
        }

        .feature-item {
            text-align: center;
            padding: 1.5rem;
        }

        .feature-number {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .feature-title {
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
            color: #2c3e50;
        }

        .feature-text {
            color: #666;
            font-size: 0.875rem;
        }

        /* 底部 */
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        /* 调试信息面板 */
        .debug-panel {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            z-index: 1000;
            min-width: 12rem;
        }

        .debug-toggle {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1rem;
            z-index: 1001;
        }

        /* 响应式断点 */
        
        /* 大屏幕 (1200px+) */
        @media screen and (min-width: 75rem) {
            html {
                font-size: 18px;
            }
            
            .hero-title {
                font-size: 4rem;
            }
            
            .card-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* 中等屏幕 (768px - 1199px) */
        @media screen and (min-width: 48rem) and (max-width: 74.9375rem) {
            html {
                font-size: 16px;
            }
            
            .card-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 小屏幕 (576px - 767px) */
        @media screen and (min-width: 36rem) and (max-width: 47.9375rem) {
            html {
                font-size: 15px;
            }
            
            .hero-title {
                font-size: 2.5rem;
            }
            
            .nav-menu {
                gap: 1rem;
            }
        }

        /* 超小屏幕 (575px以下) */
        @media screen and (max-width: 35.9375rem) {
            html {
                font-size: 14px;
            }
            
            .container {
                padding: 0 0.75rem;
            }
            
            .hero {
                padding: 2rem 0;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
            
            .nav-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: #667eea;
                flex-direction: column;
                padding: 1rem;
                gap: 1rem;
            }
            
            .nav-menu.active {
                display: flex;
            }
            
            .menu-toggle {
                display: block;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .features {
                padding: 2rem 1rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        /* 横屏适配 */
        @media screen and (orientation: landscape) and (max-height: 30rem) {
            .hero {
                padding: 2rem 0;
            }
            
            .hero-title {
                font-size: 2rem;
            }
        }

        /* 高DPR设备优化 */
        @media (-webkit-min-device-pixel-ratio: 2) {
            .card {
                box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.1);
            }
            
            .card:hover {
                box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.15);
            }
        }

        /* 动画效果 */
        .fade-in {
            opacity: 0;
            transform: translateY(1rem);
            transition: opacity 0.6s, transform 0.6s;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 0.125rem solid #f3f3f3;
            border-top: 0.125rem solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">📱 RemLayout</div>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home" class="nav-link">首页</a></li>
                    <li><a href="#features" class="nav-link">特性</a></li>
                    <li><a href="#examples" class="nav-link">示例</a></li>
                    <li><a href="#docs" class="nav-link">文档</a></li>
                </ul>
                <button class="menu-toggle" id="menuToggle">☰</button>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <section class="hero fade-in">
                <h1 class="hero-title">Rem响应式布局</h1>
                <p class="hero-subtitle">使用rem单位创建完美的响应式设计</p>
                <button class="hero-button" onclick="showDemo()">查看演示 <span class="loading" style="display: none;"></span></button>
            </section>

            <section class="card-grid">
                <div class="card fade-in">
                    <div class="card-icon">📐</div>
                    <h3 class="card-title">灵活缩放</h3>
                    <p class="card-text">基于rem单位的布局可以根据根字体大小进行整体缩放，确保在不同设备上保持一致的比例关系。</p>
                </div>
                
                <div class="card fade-in">
                    <div class="card-icon">📱</div>
                    <h3 class="card-title">移动优先</h3>
                    <p class="card-text">采用移动优先的设计理念，从小屏幕开始设计，然后逐步适配到大屏幕设备。</p>
                </div>
                
                <div class="card fade-in">
                    <div class="card-icon">⚡</div>
                    <h3 class="card-title">性能优化</h3>
                    <p class="card-text">合理使用rem单位和媒体查询，减少重绘和回流，提供流畅的用户体验。</p>
                </div>
            </section>

            <section class="features" id="features">
                <h2 class="features-title">核心特性</h2>
                <div class="features-grid">
                    <div class="feature-item fade-in">
                        <div class="feature-number">1</div>
                        <h4 class="feature-title">统一基准</h4>
                        <p class="feature-text">所有rem值都基于根元素字体大小，便于统一管理和调整</p>
                    </div>
                    
                    <div class="feature-item fade-in">
                        <div class="feature-number">2</div>
                        <h4 class="feature-title">响应式断点</h4>
                        <p class="feature-text">在不同断点设置不同的根字体大小，实现响应式设计</p>
                    </div>
                    
                    <div class="feature-item fade-in">
                        <div class="feature-number">3</div>
                        <h4 class="feature-title">易于维护</h4>
                        <p class="feature-text">修改根字体大小即可调整整个页面的比例，维护成本低</p>
                    </div>
                    
                    <div class="feature-item fade-in">
                        <div class="feature-number">4</div>
                        <h4 class="feature-title">兼容性好</h4>
                        <p class="feature-text">rem单位有良好的浏览器兼容性，支持现代浏览器</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2023 RemLayout. 学习rem响应式布局的最佳实践。</p>
        </div>
    </footer>

    <!-- 调试面板 -->
    <button class="debug-toggle" onclick="toggleDebug()">🔧</button>
    <div class="debug-panel" id="debugPanel" style="display: none;">
        <div><strong>调试信息</strong></div>
        <div>根字体: <span id="rootFontSize">16px</span></div>
        <div>视口: <span id="viewport">-</span></div>
        <div>屏幕: <span id="screen">-</span></div>
        <div>DPR: <span id="dpr">-</span></div>
        <div>断点: <span id="breakpoint">-</span></div>
    </div>

    <script>
        // 移动端菜单切换
        const menuToggle = document.getElementById('menuToggle');
        const navMenu = document.getElementById('navMenu');

        menuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });

        // 滚动动画
        function handleScrollAnimation() {
            const elements = document.querySelectorAll('.fade-in');
            
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('visible');
                }
            });
        }

        window.addEventListener('scroll', handleScrollAnimation);
        window.addEventListener('load', handleScrollAnimation);

        // 调试面板
        let debugVisible = false;

        function toggleDebug() {
            debugVisible = !debugVisible;
            const panel = document.getElementById('debugPanel');
            panel.style.display = debugVisible ? 'block' : 'none';
            
            if (debugVisible) {
                updateDebugInfo();
                setInterval(updateDebugInfo, 1000);
            }
        }

        function updateDebugInfo() {
            const rootFontSize = getComputedStyle(document.documentElement).fontSize;
            const viewport = `${window.innerWidth} × ${window.innerHeight}`;
            const screen = `${window.screen.width} × ${window.screen.height}`;
            const dpr = window.devicePixelRatio;
            const breakpoint = getCurrentBreakpoint();

            document.getElementById('rootFontSize').textContent = rootFontSize;
            document.getElementById('viewport').textContent = viewport;
            document.getElementById('screen').textContent = screen;
            document.getElementById('dpr').textContent = dpr;
            document.getElementById('breakpoint').textContent = breakpoint;
        }

        function getCurrentBreakpoint() {
            const width = window.innerWidth;
            if (width >= 1200) return 'XL (≥1200px)';
            if (width >= 768) return 'LG (768-1199px)';
            if (width >= 576) return 'MD (576-767px)';
            return 'SM (<576px)';
        }

        // 演示功能
        function showDemo() {
            const button = document.querySelector('.hero-button');
            const loading = button.querySelector('.loading');
            
            loading.style.display = 'inline-block';
            button.disabled = true;
            
            // 模拟加载
            setTimeout(() => {
                loading.style.display = 'none';
                button.disabled = false;
                alert('🎉 演示完成！\n\n当前页面就是一个完整的rem响应式布局示例。\n\n尝试：\n1. 调整浏览器窗口大小\n2. 使用开发者工具模拟不同设备\n3. 点击右下角的调试按钮查看详细信息');
            }, 2000);
        }

        // 动态字体大小调整（仅用于演示）
        function adjustFontSize() {
            const width = window.innerWidth;
            let fontSize = 16;
            
            if (width >= 1200) {
                fontSize = 18;
            } else if (width >= 768) {
                fontSize = 16;
            } else if (width >= 576) {
                fontSize = 15;
            } else {
                fontSize = 14;
            }
            
            // 平滑过渡
            document.documentElement.style.transition = 'font-size 0.3s ease';
            document.documentElement.style.fontSize = fontSize + 'px';
            
            setTimeout(() => {
                document.documentElement.style.transition = '';
            }, 300);
        }

        // 窗口大小改变时调整字体
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(adjustFontSize, 100);
        });

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            adjustFontSize();
            console.log('📱 Rem响应式布局演示页面已加载');
            console.log('💡 提示：');
            console.log('1. 调整窗口大小观察布局变化');
            console.log('2. 点击右下角调试按钮查看详细信息');
            console.log('3. 在不同设备上测试效果');
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
