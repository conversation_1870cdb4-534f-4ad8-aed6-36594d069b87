@echo off
echo ========================================
echo Vue GraphQL Project 启动脚本
echo ========================================
echo.

echo 检查 Node.js 环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

echo 检查 npm 环境...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 npm，请检查 Node.js 安装
    pause
    exit /b 1
)

echo.
echo 正在安装依赖...
call npm run install:all
if %errorlevel% neq 0 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 启动开发服务器...
echo 服务端: http://localhost:4000/graphql
echo 客户端: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务器
echo.

call npm run dev

pause
