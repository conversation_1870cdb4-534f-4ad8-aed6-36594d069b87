# Git 实用脚本集合

## 📋 概述

本目录包含了各种实用的 Git 脚本，帮助自动化常见的 Git 操作，提高开发效率。所有脚本都经过测试，可以直接使用或根据需要进行修改。

## 📁 脚本分类

### 🔧 仓库管理脚本
- `repo-cleanup.sh` - 仓库清理和优化
- `repo-stats.sh` - 仓库统计信息
- `backup-repo.sh` - 仓库备份脚本
- `health-check.sh` - 仓库健康检查

### 🌿 分支管理脚本
- `branch-cleanup.sh` - 清理已合并分支
- `branch-sync.sh` - 批量同步分支
- `create-feature.sh` - 创建功能分支
- `finish-feature.sh` - 完成功能开发

### 📊 统计分析脚本
- `contributor-stats.sh` - 贡献者统计
- `commit-analysis.sh` - 提交分析
- `file-history.sh` - 文件历史分析
- `performance-test.sh` - 性能测试

### 🚀 部署相关脚本
- `release-prepare.sh` - 发布准备
- `tag-release.sh` - 创建发布标签
- `deploy-check.sh` - 部署前检查
- `rollback.sh` - 回滚脚本

### 🛠️ 工具脚本
- `git-aliases.sh` - Git 别名设置
- `setup-hooks.sh` - 钩子脚本安装
- `config-setup.sh` - 配置初始化
- `migration-helper.sh` - 迁移助手

## 🚀 快速开始

### 安装脚本
```bash
# 克隆脚本仓库
git clone https://github.com/your-org/git-scripts.git
cd git-scripts

# 添加到 PATH
echo 'export PATH="$PATH:$(pwd)"' >> ~/.bashrc
source ~/.bashrc

# 或者创建符号链接
sudo ln -s $(pwd)/*.sh /usr/local/bin/
```

### 使用示例
```bash
# 清理仓库
./repo-cleanup.sh

# 查看仓库统计
./repo-stats.sh

# 创建功能分支
./create-feature.sh JIRA-123 "用户登录功能"

# 清理已合并分支
./branch-cleanup.sh
```

## 📝 脚本详细说明

### repo-cleanup.sh
**功能**: 清理和优化 Git 仓库
```bash
#!/bin/bash
# 仓库清理脚本

echo "开始清理 Git 仓库..."

# 垃圾回收
echo "执行垃圾回收..."
git gc --aggressive --prune=now

# 清理远程分支引用
echo "清理远程分支引用..."
git remote prune origin

# 清理未跟踪文件
echo "清理未跟踪文件..."
git clean -fd

# 重新打包对象
echo "重新打包对象..."
git repack -ad

echo "仓库清理完成！"
```

### repo-stats.sh
**功能**: 显示仓库统计信息
```bash
#!/bin/bash
# 仓库统计脚本

echo "=== Git 仓库统计信息 ==="
echo "仓库路径: $(pwd)"
echo "当前分支: $(git branch --show-current)"
echo

# 基本统计
echo "--- 基本信息 ---"
echo "总提交数: $(git rev-list --all --count)"
echo "分支数量: $(git branch -a | wc -l)"
echo "标签数量: $(git tag | wc -l)"
echo "贡献者数: $(git shortlog -sn | wc -l)"

# 仓库大小
echo
echo "--- 仓库大小 ---"
echo "仓库大小: $(du -sh .git | cut -f1)"
echo "对象数量: $(git count-objects | grep 'count' | cut -d' ' -f2)"

# 最近活动
echo
echo "--- 最近活动 ---"
echo "最后提交: $(git log -1 --format='%h - %s (%cr)')"
echo "最活跃贡献者: $(git shortlog -sn | head -1)"
```

### branch-cleanup.sh
**功能**: 清理已合并的分支
```bash
#!/bin/bash
# 分支清理脚本

MAIN_BRANCH=${1:-main}

echo "清理已合并到 $MAIN_BRANCH 的分支..."

# 切换到主分支
git checkout $MAIN_BRANCH
git pull origin $MAIN_BRANCH

# 查找已合并的分支
MERGED_BRANCHES=$(git branch --merged $MAIN_BRANCH | grep -v $MAIN_BRANCH | grep -v develop)

if [ -z "$MERGED_BRANCHES" ]; then
    echo "没有找到已合并的分支"
    exit 0
fi

echo "找到以下已合并的分支:"
echo "$MERGED_BRANCHES"

read -p "是否删除这些分支? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "$MERGED_BRANCHES" | xargs -n 1 git branch -d
    echo "本地分支清理完成"
    
    read -p "是否同时删除远程分支? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "$MERGED_BRANCHES" | sed 's/origin\///' | xargs -n 1 git push --delete origin
        echo "远程分支清理完成"
    fi
else
    echo "取消删除操作"
fi
```

### create-feature.sh
**功能**: 创建标准化的功能分支
```bash
#!/bin/bash
# 功能分支创建脚本

if [ $# -lt 2 ]; then
    echo "用法: $0 <JIRA-ID> <功能描述>"
    echo "示例: $0 PROJ-123 '用户登录功能'"
    exit 1
fi

JIRA_ID=$1
DESCRIPTION=$2
BRANCH_NAME="feature/${JIRA_ID}-$(echo $DESCRIPTION | tr ' ' '-' | tr '[:upper:]' '[:lower:]')"

echo "创建功能分支: $BRANCH_NAME"

# 确保在最新的主分支
git checkout main
git pull origin main

# 创建并切换到功能分支
git checkout -b "$BRANCH_NAME"

# 推送到远程
git push -u origin "$BRANCH_NAME"

echo "功能分支 $BRANCH_NAME 创建成功！"
echo "现在可以开始开发功能了"
```

### contributor-stats.sh
**功能**: 生成贡献者统计报告
```bash
#!/bin/bash
# 贡献者统计脚本

SINCE=${1:-"1 year ago"}

echo "=== 贡献者统计报告 ==="
echo "统计时间: 从 $SINCE 至今"
echo "生成时间: $(date)"
echo

# 提交数量统计
echo "--- 提交数量排行 ---"
git shortlog -sn --since="$SINCE" | head -10

echo
echo "--- 代码行数统计 ---"
git log --since="$SINCE" --pretty=tformat: --numstat | \
awk '{ add += $1; subs += $2; loc += $1 - $2 } END \
{ printf "增加行数: %s\n删除行数: %s\n净增行数: %s\n", add, subs, loc }'

echo
echo "--- 活跃度统计 ---"
git log --since="$SINCE" --format='%an' | sort | uniq -c | sort -nr | head -10

echo
echo "--- 每月提交趋势 ---"
git log --since="$SINCE" --format='%cd' --date=format:'%Y-%m' | sort | uniq -c
```

### performance-test.sh
**功能**: Git 操作性能测试
```bash
#!/bin/bash
# Git 性能测试脚本

echo "=== Git 性能测试 ==="
echo "测试仓库: $(pwd)"
echo "测试时间: $(date)"
echo

# 测试 git status
echo "测试 git status 性能..."
time git status > /dev/null

# 测试 git log
echo "测试 git log 性能..."
time git log --oneline -n 100 > /dev/null

# 测试 git diff
echo "测试 git diff 性能..."
time git diff HEAD~1 > /dev/null

# 测试 git grep
echo "测试 git grep 性能..."
time git grep -n "function" > /dev/null 2>&1

# 仓库大小分析
echo
echo "=== 仓库大小分析 ==="
echo "仓库总大小: $(du -sh .git | cut -f1)"
echo "对象数量: $(git count-objects -v | grep 'count' | cut -d' ' -f2)"
echo "包文件数: $(git count-objects -v | grep 'packs' | cut -d' ' -f2)"

# 大文件检查
echo
echo "=== 大文件检查 ==="
git rev-list --objects --all | \
git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
awk '/^blob/ {print substr($0,6)}' | \
sort --numeric-sort --key=2 | \
tail -5 | \
while read size hash path; do
    echo "$(numfmt --to=iec $size) $path"
done
```

## ⚙️ 配置和自定义

### 环境变量
```bash
# 在 ~/.bashrc 或 ~/.zshrc 中设置
export GIT_SCRIPTS_DIR="/path/to/git-scripts"
export DEFAULT_MAIN_BRANCH="main"
export DEFAULT_REMOTE="origin"
```

### 自定义配置
```bash
# 创建配置文件
cat > ~/.git-scripts-config << 'EOF'
# Git 脚本配置文件
MAIN_BRANCH="main"
DEVELOP_BRANCH="develop"
REMOTE_NAME="origin"
JIRA_URL="https://company.atlassian.net/browse/"
SLACK_WEBHOOK="https://hooks.slack.com/services/..."
EOF
```

## 🔧 安装钩子脚本

### setup-hooks.sh
```bash
#!/bin/bash
# 钩子脚本安装器

HOOKS_DIR=".git/hooks"

if [ ! -d "$HOOKS_DIR" ]; then
    echo "错误: 不在 Git 仓库中"
    exit 1
fi

echo "安装 Git 钩子脚本..."

# 安装 pre-commit 钩子
cat > "$HOOKS_DIR/pre-commit" << 'EOF'
#!/bin/sh
# Pre-commit 钩子

echo "运行 pre-commit 检查..."

# 代码格式检查
if command -v eslint >/dev/null 2>&1; then
    git diff --cached --name-only --diff-filter=ACM | grep '\.js$' | xargs eslint
fi

# 运行测试
if [ -f "package.json" ]; then
    npm test
fi

echo "Pre-commit 检查完成"
EOF

chmod +x "$HOOKS_DIR/pre-commit"

echo "钩子脚本安装完成！"
```

## 📊 使用统计

### 脚本使用频率统计
```bash
# 创建使用统计脚本
cat > usage-stats.sh << 'EOF'
#!/bin/bash
# 统计脚本使用频率

LOG_FILE="$HOME/.git-scripts-usage.log"

# 记录使用
echo "$(date): $0 $*" >> "$LOG_FILE"

# 显示统计
if [ "$1" = "--stats" ]; then
    echo "=== 脚本使用统计 ==="
    awk '{print $3}' "$LOG_FILE" | sort | uniq -c | sort -nr
fi
EOF
```

## 🎯 最佳实践

### 脚本开发规范
1. **错误处理**: 使用 `set -e` 和适当的错误检查
2. **参数验证**: 验证输入参数的有效性
3. **用户确认**: 危险操作前要求用户确认
4. **日志记录**: 记录重要操作和错误信息
5. **文档说明**: 提供清晰的使用说明

### 安全注意事项
1. **权限控制**: 确保脚本有适当的执行权限
2. **路径验证**: 验证文件路径的安全性
3. **输入清理**: 清理和验证用户输入
4. **备份机制**: 重要操作前创建备份

---

这些脚本可以大大提高 Git 操作的效率，建议根据团队需求进行定制和扩展。
