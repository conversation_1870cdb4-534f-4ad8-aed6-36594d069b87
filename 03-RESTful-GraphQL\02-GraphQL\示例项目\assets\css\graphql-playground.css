/* GraphQL Playground 样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #e91e63 0%, #9c27b0 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

.playground-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.panel {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.panel:hover {
    transform: translateY(-2px);
}

.panel h2 {
    color: #e91e63;
    margin-bottom: 20px;
    font-size: 1.4em;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.query-editor {
    width: 100%;
    height: 300px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: #1e1e1e;
    color: #d4d4d4;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.query-editor:focus {
    outline: none;
    border-color: #e91e63;
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
}

.variables-editor {
    width: 100%;
    height: 120px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: #1e1e1e;
    color: #d4d4d4;
    margin-top: 15px;
    transition: border-color 0.3s ease;
}

.variables-editor:focus {
    outline: none;
    border-color: #e91e63;
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
}

.result-display {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    max-height: 500px;
    overflow-y: auto;
    min-height: 200px;
    border: 2px solid #333;
}

.btn {
    background: #e91e63;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    margin: 10px 5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
}

.btn:hover {
    background: #c2185b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #6c757d;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    background: #545b62;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.tabs {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 20px;
}

.tab {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    background: none;
    border: none;
    font-size: 14px;
    color: #666;
}

.tab:hover {
    color: #e91e63;
    background: rgba(233, 30, 99, 0.05);
}

.tab.active {
    color: #e91e63;
    border-bottom-color: #e91e63;
    background: rgba(233, 30, 99, 0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.examples-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.examples-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.example-item {
    margin: 15px 0;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #e91e63;
    transition: transform 0.2s ease;
}

.example-item:hover {
    transform: translateX(5px);
}

.example-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 1.1em;
}

.example-description {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.example-code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin: 8px 0;
    overflow-x: auto;
}

.try-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.3s ease;
}

.try-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #e91e63;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    color: #e74c3c;
    background: #fdf2f2;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
    margin: 15px 0;
}

.success {
    color: #27ae60;
    background: #f0f9f4;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
    margin: 15px 0;
}

.schema-display {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    max-height: 600px;
    overflow-y: auto;
    border: 2px solid #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-number {
    font-size: 1.8em;
    font-weight: bold;
    color: #e91e63;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
}

.docs-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-top: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.docs-section h2 {
    color: #e91e63;
    margin-bottom: 20px;
    text-align: center;
}

.doc-item {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #e91e63;
}

.doc-item h3 {
    color: #333;
    margin-bottom: 15px;
}

.doc-item code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

.doc-item pre {
    background: #1e1e1e;
    color: #f8f8f2;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .playground-layout {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .container {
        padding: 10px;
    }
    
    .panel {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .query-editor {
        height: 250px;
    }
    
    .variables-editor {
        height: 100px;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5em;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .panel {
        padding: 15px;
    }
    
    .tabs {
        flex-wrap: wrap;
    }
    
    .tab {
        flex: 1;
        min-width: 120px;
    }
}
