# GraphQL Resolvers 详解

## 1. 什么是 Resolver？

### 1.1 基本概念
**Resolver（解析器）** 是 GraphQL 的核心，它是一个函数，负责为 Schema 中的每个字段获取数据。

```
简单理解：
Schema 定义了"有什么数据"
Resolver 定义了"如何获取数据"
```

### 1.2 生活化比喻
想象你在餐厅点菜：
- **菜单（Schema）**：告诉你有什么菜可以点
- **厨师（Resolver）**：负责制作你点的菜
- **服务员（GraphQL引擎）**：把你的订单传给厨师，再把菜端给你

```graphql
# Schema（菜单）
type Query {
  user(id: ID!): User     # 菜单上有"用户信息"这道菜
}

type User {
  id: ID!
  name: String!
  email: String!
}
```

```javascript
// Resolver（厨师）
const resolvers = {
  Query: {
    // 这个函数就是"厨师"，负责"制作"用户信息
    user: async (parent, { id }) => {
      return await User.findById(id);  // 从数据库获取用户
    }
  }
};
```

## 2. Resolver 的基本结构

### 2.1 函数签名
每个 Resolver 都是一个函数，接收四个参数：

```javascript
const resolver = (parent, args, context, info) => {
  // 返回数据
};
```

### 2.2 四个参数详解

#### **parent（父对象）**
```javascript
const resolvers = {
  Query: {
    user: (parent, args) => {
      console.log(parent); // null（Query 是根，没有父对象）
      return { id: '1', name: 'John', email: '<EMAIL>' };
    }
  },
  
  User: {
    // parent 就是上面 Query.user 返回的用户对象
    posts: (parent, args) => {
      console.log(parent); // { id: '1', name: 'John', email: '<EMAIL>' }
      return Post.findByUserId(parent.id);  // 使用父对象的 id
    }
  }
};
```

#### **args（参数）**
```javascript
// 查询：user(id: "123", includeEmail: true)
const resolvers = {
  Query: {
    user: (parent, args) => {
      console.log(args); // { id: "123", includeEmail: true }
      return User.findById(args.id);
    }
  }
};
```

#### **context（上下文）**
```javascript
// 服务器配置
const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: ({ req }) => ({
    user: getCurrentUser(req),
    dataSources: {
      userAPI: new UserAPI(),
      postAPI: new PostAPI()
    }
  })
});

// Resolver 中使用
const resolvers = {
  Query: {
    me: (parent, args, context) => {
      console.log(context.user);           // 当前登录用户
      return context.dataSources.userAPI.getMe();
    }
  }
};
```

#### **info（查询信息）**
```javascript
const resolvers = {
  Query: {
    users: (parent, args, context, info) => {
      console.log(info.fieldName);        // "users"
      console.log(info.fieldNodes);       // 查询的 AST 节点
      console.log(info.returnType);       // 返回类型信息
      
      // 可以根据查询的字段优化数据获取
      const requestedFields = getRequestedFields(info);
      return User.findAll({ select: requestedFields });
    }
  }
};
```

## 3. Resolver 的类型

### 3.1 根 Resolver（Root Resolvers）
```javascript
const resolvers = {
  // 查询根 Resolver
  Query: {
    users: () => User.findAll(),
    user: (parent, { id }) => User.findById(id),
    posts: () => Post.findAll()
  },
  
  // 变更根 Resolver
  Mutation: {
    createUser: (parent, { input }) => User.create(input),
    updateUser: (parent, { id, input }) => User.update(id, input),
    deleteUser: (parent, { id }) => User.delete(id)
  },
  
  // 订阅根 Resolver
  Subscription: {
    userCreated: {
      subscribe: () => pubsub.asyncIterator(['USER_CREATED'])
    }
  }
};
```

### 3.2 字段 Resolver（Field Resolvers）
```javascript
const resolvers = {
  // 为 User 类型的字段定义 Resolver
  User: {
    // 如果数据库字段名和 GraphQL 字段名不同
    fullName: (user) => `${user.first_name} ${user.last_name}`,
    
    // 计算字段
    age: (user) => {
      const today = new Date();
      const birthDate = new Date(user.birth_date);
      return today.getFullYear() - birthDate.getFullYear();
    },
    
    // 关联数据
    posts: (user) => Post.findByUserId(user.id),
    
    // 异步获取数据
    avatar: async (user) => {
      return await FileService.getAvatar(user.id);
    }
  }
};
```

## 4. 实际例子：博客系统

### 4.1 Schema 定义
```graphql
type Query {
  posts: [Post!]!
  post(id: ID!): Post
  user(id: ID!): User
}

type Mutation {
  createPost(input: CreatePostInput!): Post!
  updatePost(id: ID!, input: UpdatePostInput!): Post!
}

type Post {
  id: ID!
  title: String!
  content: String!
  author: User!
  comments: [Comment!]!
  likeCount: Int!
  createdAt: String!
}

type User {
  id: ID!
  name: String!
  email: String!
  posts: [Post!]!
  postCount: Int!
}

type Comment {
  id: ID!
  content: String!
  author: User!
  post: Post!
}

input CreatePostInput {
  title: String!
  content: String!
  authorId: ID!
}
```

### 4.2 Resolver 实现
```javascript
const resolvers = {
  // 根查询 Resolver
  Query: {
    // 获取所有文章
    posts: async () => {
      return await Post.findAll();
    },
    
    // 获取单篇文章
    post: async (parent, { id }) => {
      const post = await Post.findById(id);
      if (!post) {
        throw new Error(`文章 ${id} 不存在`);
      }
      return post;
    },
    
    // 获取用户
    user: async (parent, { id }) => {
      return await User.findById(id);
    }
  },
  
  // 变更 Resolver
  Mutation: {
    // 创建文章
    createPost: async (parent, { input }) => {
      // 验证作者是否存在
      const author = await User.findById(input.authorId);
      if (!author) {
        throw new Error('作者不存在');
      }
      
      // 创建文章
      const post = await Post.create({
        title: input.title,
        content: input.content,
        authorId: input.authorId,
        createdAt: new Date().toISOString()
      });
      
      return post;
    },
    
    // 更新文章
    updatePost: async (parent, { id, input }) => {
      const post = await Post.findById(id);
      if (!post) {
        throw new Error('文章不存在');
      }
      
      return await Post.update(id, input);
    }
  },
  
  // Post 类型的字段 Resolver
  Post: {
    // 获取文章作者
    author: async (post) => {
      return await User.findById(post.authorId);
    },
    
    // 获取文章评论
    comments: async (post) => {
      return await Comment.findByPostId(post.id);
    },
    
    // 计算点赞数
    likeCount: async (post) => {
      return await Like.countByPostId(post.id);
    }
  },
  
  // User 类型的字段 Resolver
  User: {
    // 获取用户的文章
    posts: async (user) => {
      return await Post.findByAuthorId(user.id);
    },
    
    // 计算用户文章数量
    postCount: async (user) => {
      return await Post.countByAuthorId(user.id);
    }
  },
  
  // Comment 类型的字段 Resolver
  Comment: {
    // 获取评论作者
    author: async (comment) => {
      return await User.findById(comment.authorId);
    },
    
    // 获取评论所属文章
    post: async (comment) => {
      return await Post.findById(comment.postId);
    }
  }
};
```

## 5. Resolver 的执行流程

### 5.1 查询执行示例
```graphql
# 客户端查询
query {
  post(id: "1") {
    title
    author {
      name
      postCount
    }
    comments {
      content
      author {
        name
      }
    }
  }
}
```

### 5.2 执行步骤
```javascript
// 1. 执行 Query.post resolver
const post = await Post.findById("1");
// 返回：{ id: "1", title: "GraphQL入门", authorId: "123", ... }

// 2. 执行 Post.author resolver
const author = await User.findById(post.authorId);
// 返回：{ id: "123", name: "张三", ... }

// 3. 执行 User.postCount resolver
const postCount = await Post.countByAuthorId(author.id);
// 返回：5

// 4. 执行 Post.comments resolver
const comments = await Comment.findByPostId(post.id);
// 返回：[{ id: "1", content: "很好的文章", authorId: "456" }, ...]

// 5. 为每个评论执行 Comment.author resolver
const commentAuthors = await Promise.all(
  comments.map(comment => User.findById(comment.authorId))
);

// 最终返回组装好的数据
```

## 6. 默认 Resolver

### 6.1 什么是默认 Resolver？
如果你没有为某个字段定义 Resolver，GraphQL 会使用默认的 Resolver：

```javascript
// 默认 Resolver 的逻辑
const defaultResolver = (parent, args, context, info) => {
  return parent[info.fieldName];
};
```

### 6.2 示例
```graphql
type User {
  id: ID!
  name: String!
  email: String!
  age: Int!        # 需要计算
  posts: [Post!]!  # 需要查询数据库
}
```

```javascript
const resolvers = {
  User: {
    // id, name, email 使用默认 Resolver
    // 相当于：
    // id: (user) => user.id,
    // name: (user) => user.name,
    // email: (user) => user.email,
    
    // 只需要定义特殊逻辑的字段
    age: (user) => calculateAge(user.birthDate),
    posts: (user) => Post.findByUserId(user.id)
  }
};
```

## 7. Resolver 中的异步操作

### 7.1 Promise 和 async/await
```javascript
const resolvers = {
  Query: {
    // 使用 async/await
    user: async (parent, { id }) => {
      try {
        const user = await User.findById(id);
        return user;
      } catch (error) {
        throw new Error(`获取用户失败: ${error.message}`);
      }
    },

    // 返回 Promise
    posts: (parent, args) => {
      return Post.findAll()
        .then(posts => posts)
        .catch(error => {
          throw new Error(`获取文章失败: ${error.message}`);
        });
    }
  },

  User: {
    // 并行获取多个数据
    profile: async (user) => {
      const [avatar, settings, stats] = await Promise.all([
        Avatar.findByUserId(user.id),
        Settings.findByUserId(user.id),
        Stats.findByUserId(user.id)
      ]);

      return { avatar, settings, stats };
    }
  }
};
```

### 7.2 处理数据库操作
```javascript
// 使用 Sequelize ORM
const resolvers = {
  Query: {
    users: async () => {
      return await User.findAll({
        include: [
          { model: Post, as: 'posts' },
          { model: Profile, as: 'profile' }
        ]
      });
    }
  },

  User: {
    posts: async (user) => {
      // 如果已经预加载了，直接返回
      if (user.posts) {
        return user.posts;
      }

      // 否则查询数据库
      return await Post.findAll({
        where: { authorId: user.id },
        order: [['createdAt', 'DESC']]
      });
    }
  }
};
```

## 8. 错误处理

### 8.1 基本错误处理
```javascript
const resolvers = {
  Query: {
    user: async (parent, { id }) => {
      // 验证输入
      if (!id) {
        throw new Error('用户ID不能为空');
      }

      try {
        const user = await User.findById(id);

        // 检查是否存在
        if (!user) {
          throw new Error(`用户 ${id} 不存在`);
        }

        return user;
      } catch (error) {
        // 记录错误日志
        console.error('获取用户失败:', error);

        // 重新抛出错误
        throw new Error(`获取用户失败: ${error.message}`);
      }
    }
  }
};
```

### 8.2 自定义错误类型
```javascript
// 自定义错误类
class UserNotFoundError extends Error {
  constructor(userId) {
    super(`用户 ${userId} 不存在`);
    this.extensions = {
      code: 'USER_NOT_FOUND',
      userId: userId
    };
  }
}

class ValidationError extends Error {
  constructor(field, message) {
    super(`字段 ${field} 验证失败: ${message}`);
    this.extensions = {
      code: 'VALIDATION_ERROR',
      field: field
    };
  }
}

// 在 Resolver 中使用
const resolvers = {
  Query: {
    user: async (parent, { id }) => {
      if (!id) {
        throw new ValidationError('id', '不能为空');
      }

      const user = await User.findById(id);
      if (!user) {
        throw new UserNotFoundError(id);
      }

      return user;
    }
  }
};
```

### 8.3 错误响应格式
```json
{
  "data": null,
  "errors": [
    {
      "message": "用户 123 不存在",
      "locations": [{"line": 2, "column": 3}],
      "path": ["user"],
      "extensions": {
        "code": "USER_NOT_FOUND",
        "userId": "123"
      }
    }
  ]
}
```

## 9. 性能优化

### 9.1 N+1 查询问题
```javascript
// ❌ 问题：N+1 查询
const resolvers = {
  Query: {
    posts: () => Post.findAll()  // 1次查询获取所有文章
  },

  Post: {
    // 每篇文章都会执行一次查询！
    author: (post) => User.findById(post.authorId)  // N次查询
  }
};

// ✅ 解决方案：使用 DataLoader
import DataLoader from 'dataloader';

// 创建批量加载函数
const userLoader = new DataLoader(async (userIds) => {
  const users = await User.findByIds(userIds);
  // 返回与输入顺序对应的结果
  return userIds.map(id => users.find(user => user.id === id));
});

const resolvers = {
  Post: {
    author: (post) => userLoader.load(post.authorId)  // 批量加载
  }
};
```

### 9.2 字段级优化
```javascript
const resolvers = {
  Query: {
    users: async (parent, args, context, info) => {
      // 分析查询的字段
      const requestedFields = getRequestedFields(info);

      // 只查询需要的字段
      if (requestedFields.includes('posts')) {
        return User.findAll({ include: ['posts'] });
      } else {
        return User.findAll();
      }
    }
  }
};

// 辅助函数：获取请求的字段
function getRequestedFields(info) {
  const selections = info.fieldNodes[0].selectionSet.selections;
  return selections.map(selection => selection.name.value);
}
```

## 10. 认证和授权

### 10.1 认证检查
```javascript
// 认证中间件
const requireAuth = (resolver) => {
  return (parent, args, context, info) => {
    if (!context.user) {
      throw new Error('需要登录');
    }
    return resolver(parent, args, context, info);
  };
};

const resolvers = {
  Query: {
    // 公开查询
    posts: () => Post.findAll(),

    // 需要认证的查询
    me: requireAuth((parent, args, { user }) => {
      return User.findById(user.id);
    })
  },

  Mutation: {
    // 需要认证的变更
    createPost: requireAuth(async (parent, { input }, { user }) => {
      return await Post.create({
        ...input,
        authorId: user.id
      });
    })
  }
};
```

### 10.2 基于角色的授权
```javascript
// 角色检查中间件
const requireRole = (roles) => (resolver) => {
  return (parent, args, context, info) => {
    if (!context.user) {
      throw new Error('需要登录');
    }

    if (!roles.includes(context.user.role)) {
      throw new Error('权限不足');
    }

    return resolver(parent, args, context, info);
  };
};

const resolvers = {
  Query: {
    // 只有管理员可以查看所有用户
    allUsers: requireRole(['ADMIN'])(() => {
      return User.findAll();
    }),

    // 管理员和版主可以查看举报
    reports: requireRole(['ADMIN', 'MODERATOR'])(() => {
      return Report.findAll();
    })
  },

  User: {
    // 只有用户本人或管理员可以查看邮箱
    email: (user, args, { user: currentUser }) => {
      if (currentUser.id === user.id || currentUser.role === 'ADMIN') {
        return user.email;
      }
      return null;
    }
  }
};
```

## 11. 最佳实践

### 11.1 Resolver 组织结构
```javascript
// 按功能模块组织 Resolver
// resolvers/userResolvers.js
export const userResolvers = {
  Query: {
    user: async (parent, { id }) => User.findById(id),
    users: async () => User.findAll()
  },

  Mutation: {
    createUser: async (parent, { input }) => User.create(input),
    updateUser: async (parent, { id, input }) => User.update(id, input)
  },

  User: {
    posts: (user) => Post.findByUserId(user.id),
    profile: (user) => Profile.findByUserId(user.id)
  }
};

// resolvers/postResolvers.js
export const postResolvers = {
  Query: {
    post: async (parent, { id }) => Post.findById(id),
    posts: async () => Post.findAll()
  },

  Post: {
    author: (post) => User.findById(post.authorId),
    comments: (post) => Comment.findByPostId(post.id)
  }
};

// resolvers/index.js
import { mergeResolvers } from '@graphql-tools/merge';
import { userResolvers } from './userResolvers.js';
import { postResolvers } from './postResolvers.js';

export const resolvers = mergeResolvers([
  userResolvers,
  postResolvers
]);
```

### 11.2 数据源抽象
```javascript
// 数据源类
class UserDataSource {
  async findById(id) {
    return await User.findById(id);
  }

  async findAll() {
    return await User.findAll();
  }

  async create(input) {
    return await User.create(input);
  }
}

// 在 context 中提供数据源
const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: () => ({
    dataSources: {
      users: new UserDataSource(),
      posts: new PostDataSource()
    }
  })
});

// Resolver 中使用数据源
const resolvers = {
  Query: {
    user: (parent, { id }, { dataSources }) => {
      return dataSources.users.findById(id);
    }
  }
};
```

---

**总结：**
Resolver 是 GraphQL 的核心，理解了 Resolver 就理解了 GraphQL 的工作原理。关键要点：
1. Resolver 是获取数据的函数
2. 每个字段都可以有自己的 Resolver
3. 注意性能优化，避免 N+1 问题
4. 合理处理错误和异常
5. 实现适当的认证和授权
