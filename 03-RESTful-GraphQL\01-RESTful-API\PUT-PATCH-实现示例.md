# PUT vs PATCH 实现示例

## 1. Node.js + Express 实现

### 1.1 基础设置

```javascript
const express = require('express');
const app = express();

app.use(express.json());

// 模拟数据库
let users = {
  123: {
    id: 123,
    name: "张三",
    email: "<PERSON><PERSON><PERSON>@example.com",
    phone: "138-0000-0000",
    address: "北京市朝阳区",
    status: "active",
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-10-01T00:00:00Z"
  }
};
```

### 1.2 PUT 实现（完整替换）

```javascript
// PUT - 完整替换用户
app.put('/api/users/:id', (req, res) => {
  const userId = parseInt(req.params.id);
  const userData = req.body;
  
  // 验证必要字段
  const requiredFields = ['name', 'email', 'phone', 'address', 'status'];
  const missingFields = requiredFields.filter(field => !userData[field]);
  
  if (missingFields.length > 0) {
    return res.status(400).json({
      error: 'missing_required_fields',
      message: `缺少必要字段: ${missingFields.join(', ')}`,
      missing_fields: missingFields
    });
  }
  
  // 检查用户是否存在
  const existingUser = users[userId];
  const isCreating = !existingUser;
  
  // 完整替换（保留系统字段）
  users[userId] = {
    id: userId,
    ...userData,
    created_at: existingUser ? existingUser.created_at : new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  res.status(isCreating ? 201 : 200).json({
    message: isCreating ? '用户创建成功' : '用户更新成功',
    data: users[userId]
  });
});
```

### 1.3 PATCH 实现（部分更新）

```javascript
// PATCH - 部分更新用户
app.patch('/api/users/:id', (req, res) => {
  const userId = parseInt(req.params.id);
  const updates = req.body;
  
  // 检查用户是否存在
  if (!users[userId]) {
    return res.status(404).json({
      error: 'user_not_found',
      message: '用户不存在'
    });
  }
  
  // 验证提供的字段
  const allowedFields = ['name', 'email', 'phone', 'address', 'status'];
  const invalidFields = Object.keys(updates).filter(field => 
    !allowedFields.includes(field) && 
    !['id', 'created_at', 'updated_at'].includes(field)
  );
  
  if (invalidFields.length > 0) {
    return res.status(400).json({
      error: 'invalid_fields',
      message: `不允许更新的字段: ${invalidFields.join(', ')}`,
      invalid_fields: invalidFields
    });
  }
  
  // 部分更新
  users[userId] = {
    ...users[userId],
    ...updates,
    updated_at: new Date().toISOString()
  };
  
  res.json({
    message: '用户更新成功',
    data: users[userId],
    updated_fields: Object.keys(updates)
  });
});
```

### 1.4 字段验证中间件

```javascript
// 邮箱验证
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 电话验证
function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/-/g, ''));
}

// 字段验证中间件
function validateUserFields(req, res, next) {
  const { email, phone, status } = req.body;
  
  if (email && !validateEmail(email)) {
    return res.status(400).json({
      error: 'invalid_email',
      message: '邮箱格式不正确'
    });
  }
  
  if (phone && !validatePhone(phone)) {
    return res.status(400).json({
      error: 'invalid_phone',
      message: '电话号码格式不正确'
    });
  }
  
  if (status && !['active', 'inactive', 'suspended'].includes(status)) {
    return res.status(400).json({
      error: 'invalid_status',
      message: '状态值不正确'
    });
  }
  
  next();
}

// 应用验证中间件
app.put('/api/users/:id', validateUserFields, (req, res) => {
  // PUT 实现...
});

app.patch('/api/users/:id', validateUserFields, (req, res) => {
  // PATCH 实现...
});
```

## 2. 前端调用示例

### 2.1 JavaScript 客户端

```javascript
class UserAPI {
  constructor(baseURL) {
    this.baseURL = baseURL;
  }
  
  // PUT - 完整更新用户
  async updateUserComplete(userId, userData) {
    try {
      const response = await fetch(`${this.baseURL}/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message);
      }
      
      return await response.json();
    } catch (error) {
      console.error('PUT 请求失败:', error);
      throw error;
    }
  }
  
  // PATCH - 部分更新用户
  async updateUserPartial(userId, updates) {
    try {
      const response = await fetch(`${this.baseURL}/api/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message);
      }
      
      return await response.json();
    } catch (error) {
      console.error('PATCH 请求失败:', error);
      throw error;
    }
  }
}

// 使用示例
const userAPI = new UserAPI('http://localhost:3000');

// 完整更新用户信息
async function saveUserProfile() {
  const completeUserData = {
    name: "李四",
    email: "<EMAIL>",
    phone: "139-1111-1111",
    address: "上海市浦东新区",
    status: "active"
  };
  
  try {
    const result = await userAPI.updateUserComplete(123, completeUserData);
    console.log('用户信息保存成功:', result);
  } catch (error) {
    console.error('保存失败:', error.message);
  }
}

// 部分更新用户信息
async function updateUserEmail() {
  try {
    const result = await userAPI.updateUserPartial(123, {
      email: "<EMAIL>"
    });
    console.log('邮箱更新成功:', result);
  } catch (error) {
    console.error('更新失败:', error.message);
  }
}
```

### 2.2 Vue.js 组件示例

```vue
<template>
  <div class="user-profile">
    <h2>用户资料</h2>
    
    <!-- 完整表单（PUT） -->
    <form @submit.prevent="saveProfile" class="profile-form">
      <div class="form-group">
        <label>姓名:</label>
        <input v-model="userForm.name" type="text" required>
      </div>
      
      <div class="form-group">
        <label>邮箱:</label>
        <input v-model="userForm.email" type="email" required>
      </div>
      
      <div class="form-group">
        <label>电话:</label>
        <input v-model="userForm.phone" type="tel" required>
      </div>
      
      <div class="form-group">
        <label>地址:</label>
        <input v-model="userForm.address" type="text" required>
      </div>
      
      <button type="submit" :disabled="saving">
        {{ saving ? '保存中...' : '保存资料' }}
      </button>
    </form>
    
    <!-- 快速更新（PATCH） -->
    <div class="quick-updates">
      <h3>快速更新</h3>
      
      <div class="quick-update-item">
        <label>邮箱:</label>
        <input v-model="quickEmail" type="email">
        <button @click="updateEmail" :disabled="updating">更新邮箱</button>
      </div>
      
      <div class="quick-update-item">
        <label>状态:</label>
        <select v-model="quickStatus">
          <option value="active">激活</option>
          <option value="inactive">未激活</option>
          <option value="suspended">暂停</option>
        </select>
        <button @click="updateStatus" :disabled="updating">更新状态</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserProfile',
  data() {
    return {
      userId: 123,
      userForm: {
        name: '',
        email: '',
        phone: '',
        address: '',
        status: 'active'
      },
      quickEmail: '',
      quickStatus: 'active',
      saving: false,
      updating: false
    };
  },
  
  async mounted() {
    await this.loadUserData();
  },
  
  methods: {
    async loadUserData() {
      try {
        const response = await fetch(`/api/users/${this.userId}`);
        const result = await response.json();
        this.userForm = { ...result.data };
        this.quickEmail = result.data.email;
        this.quickStatus = result.data.status;
      } catch (error) {
        console.error('加载用户数据失败:', error);
      }
    },
    
    // PUT - 保存完整资料
    async saveProfile() {
      this.saving = true;
      try {
        const response = await fetch(`/api/users/${this.userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(this.userForm)
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message);
        }
        
        const result = await response.json();
        this.$message.success('资料保存成功');
        console.log('保存结果:', result);
      } catch (error) {
        this.$message.error(`保存失败: ${error.message}`);
      } finally {
        this.saving = false;
      }
    },
    
    // PATCH - 更新邮箱
    async updateEmail() {
      if (!this.quickEmail) return;
      
      this.updating = true;
      try {
        const response = await fetch(`/api/users/${this.userId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: this.quickEmail
          })
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message);
        }
        
        const result = await response.json();
        this.userForm.email = this.quickEmail;
        this.$message.success('邮箱更新成功');
      } catch (error) {
        this.$message.error(`更新失败: ${error.message}`);
      } finally {
        this.updating = false;
      }
    },
    
    // PATCH - 更新状态
    async updateStatus() {
      this.updating = true;
      try {
        const response = await fetch(`/api/users/${this.userId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: this.quickStatus
          })
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message);
        }
        
        const result = await response.json();
        this.userForm.status = this.quickStatus;
        this.$message.success('状态更新成功');
      } catch (error) {
        this.$message.error(`更新失败: ${error.message}`);
      } finally {
        this.updating = false;
      }
    }
  }
};
</script>

<style scoped>
.user-profile {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.profile-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.quick-updates {
  background: #e8f4fd;
  padding: 20px;
  border-radius: 8px;
}

.quick-update-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.quick-update-item label {
  min-width: 60px;
  font-weight: bold;
}

.quick-update-item input,
.quick-update-item select {
  flex: 1;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background: #0056b3;
}
</style>
```

## 3. 测试用例

### 3.1 PUT 测试

```javascript
// PUT 测试用例
describe('PUT /api/users/:id', () => {
  test('应该成功更新完整用户信息', async () => {
    const userData = {
      name: "测试用户",
      email: "<EMAIL>",
      phone: "138-0000-0000",
      address: "测试地址",
      status: "active"
    };
    
    const response = await request(app)
      .put('/api/users/123')
      .send(userData)
      .expect(200);
    
    expect(response.body.data.name).toBe(userData.name);
    expect(response.body.data.email).toBe(userData.email);
  });
  
  test('应该在缺少必要字段时返回错误', async () => {
    const incompleteData = {
      name: "测试用户"
      // 缺少其他必要字段
    };
    
    const response = await request(app)
      .put('/api/users/123')
      .send(incompleteData)
      .expect(400);
    
    expect(response.body.error).toBe('missing_required_fields');
  });
});
```

### 3.2 PATCH 测试

```javascript
// PATCH 测试用例
describe('PATCH /api/users/:id', () => {
  test('应该成功更新单个字段', async () => {
    const update = {
      email: "<EMAIL>"
    };
    
    const response = await request(app)
      .patch('/api/users/123')
      .send(update)
      .expect(200);
    
    expect(response.body.data.email).toBe(update.email);
    expect(response.body.updated_fields).toContain('email');
  });
  
  test('应该在用户不存在时返回404', async () => {
    const update = {
      email: "<EMAIL>"
    };
    
    await request(app)
      .patch('/api/users/999')
      .send(update)
      .expect(404);
  });
});
```

这些示例展示了 PUT 和 PATCH 的具体实现差异，以及在实际项目中如何正确使用它们。
