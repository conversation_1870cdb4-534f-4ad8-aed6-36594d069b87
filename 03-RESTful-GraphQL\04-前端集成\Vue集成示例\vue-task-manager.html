<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理系统 - RESTful vs GraphQL</title>
    <link rel="stylesheet" href="assets/css/task-manager.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="container">
                <h1 class="title">
                    <i class="fas fa-tasks"></i>
                    任务管理系统
                </h1>
                
                <!-- API模式切换 -->
                <div class="api-switcher">
                    <button 
                        :class="['api-btn', { active: apiMode === 'restful' }]"
                        @click="switchApiMode('restful')"
                    >
                        <i class="fas fa-server"></i>
                        RESTful API
                    </button>
                    <button 
                        :class="['api-btn', { active: apiMode === 'graphql' }]"
                        @click="switchApiMode('graphql')"
                    >
                        <i class="fas fa-project-diagram"></i>
                        GraphQL
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="main">
            <div class="container">
                <!-- 错误提示 -->
                <div v-if="error" class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    {{ error }}
                    <button @click="error = ''" class="alert-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- 统计面板 -->
                <div class="stats-panel">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">{{ stats.total }}</div>
                            <div class="stat-label">总任务</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon completed">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">{{ stats.completed }}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon progress">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">{{ stats.inProgress }}</div>
                            <div class="stat-label">进行中</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon high-priority">
                            <i class="fas fa-exclamation"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">{{ stats.highPriority }}</div>
                            <div class="stat-label">高优先级</div>
                        </div>
                    </div>
                </div>

                <!-- 操作面板 -->
                <div class="action-panel">
                    <!-- 搜索和过滤 -->
                    <div class="filters">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input 
                                type="text" 
                                v-model="searchQuery"
                                placeholder="搜索任务..."
                                class="search-input"
                            >
                        </div>
                        
                        <select v-model="filterStatus" class="filter-select">
                            <option value="all">所有状态</option>
                            <option value="todo">待办</option>
                            <option value="progress">进行中</option>
                            <option value="completed">已完成</option>
                        </select>
                        
                        <select v-model="filterPriority" class="filter-select">
                            <option value="all">所有优先级</option>
                            <option value="high">高</option>
                            <option value="medium">中</option>
                            <option value="low">低</option>
                        </select>
                    </div>

                    <!-- 新建任务表单 -->
                    <div class="new-task-form">
                        <h3>
                            <i class="fas fa-plus"></i>
                            新建任务
                        </h3>
                        
                        <div class="form-row">
                            <input 
                                type="text" 
                                v-model="newTask.title"
                                placeholder="任务标题"
                                class="form-input"
                                @keyup.enter="createTask"
                            >
                            
                            <select v-model="newTask.priority" class="form-select">
                                <option value="high">高优先级</option>
                                <option value="medium">中优先级</option>
                                <option value="low">低优先级</option>
                            </select>
                        </div>
                        
                        <div class="form-row">
                            <input 
                                type="text" 
                                v-model="newTask.assignee"
                                placeholder="负责人"
                                class="form-input"
                            >
                            
                            <button 
                                @click="createTask"
                                :disabled="loading || !newTask.title.trim()"
                                class="btn btn-primary"
                            >
                                <i class="fas fa-plus"></i>
                                创建任务
                            </button>
                        </div>
                        
                        <textarea 
                            v-model="newTask.description"
                            placeholder="任务描述（可选）"
                            class="form-textarea"
                            rows="2"
                        ></textarea>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="task-list">
                    <div v-if="loading" class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        加载中...
                    </div>
                    
                    <div v-else-if="filteredTasks.length === 0" class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>暂无任务</p>
                    </div>
                    
                    <div v-else class="task-grid">
                        <div 
                            v-for="task in filteredTasks" 
                            :key="task.id"
                            class="task-card"
                            :class="{ editing: editingTask?.id === task.id }"
                        >
                            <!-- 编辑模式 -->
                            <div v-if="editingTask?.id === task.id" class="edit-form">
                                <input 
                                    type="text" 
                                    v-model="editForm.title"
                                    class="edit-input"
                                    @keyup.enter="saveEdit"
                                >
                                
                                <div class="edit-controls">
                                    <select v-model="editForm.status" class="edit-select">
                                        <option value="todo">待办</option>
                                        <option value="progress">进行中</option>
                                        <option value="completed">已完成</option>
                                    </select>
                                    
                                    <select v-model="editForm.priority" class="edit-select">
                                        <option value="high">高</option>
                                        <option value="medium">中</option>
                                        <option value="low">低</option>
                                    </select>
                                </div>
                                
                                <textarea 
                                    v-model="editForm.description"
                                    class="edit-textarea"
                                    rows="2"
                                ></textarea>
                                
                                <div class="edit-actions">
                                    <button @click="saveEdit" class="btn btn-success btn-sm">
                                        <i class="fas fa-check"></i>
                                        保存
                                    </button>
                                    <button @click="cancelEdit" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-times"></i>
                                        取消
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 显示模式 -->
                            <div v-else class="task-content">
                                <div class="task-header">
                                    <h4 class="task-title">{{ task.title }}</h4>
                                    
                                    <div class="task-badges">
                                        <span 
                                            class="badge priority-badge"
                                            :style="{ backgroundColor: getPriorityColor(task.priority) }"
                                        >
                                            {{ task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低' }}
                                        </span>
                                        
                                        <span 
                                            class="badge status-badge"
                                            :style="{ backgroundColor: getStatusColor(task.status) }"
                                        >
                                            {{ task.status === 'todo' ? '待办' : task.status === 'progress' ? '进行中' : '已完成' }}
                                        </span>
                                    </div>
                                </div>
                                
                                <p v-if="task.description" class="task-description">
                                    {{ task.description }}
                                </p>
                                
                                <div class="task-meta">
                                    <span v-if="task.assignee" class="task-assignee">
                                        <i class="fas fa-user"></i>
                                        {{ task.assignee }}
                                    </span>
                                    
                                    <span class="task-date">
                                        <i class="fas fa-calendar"></i>
                                        {{ new Date(task.createdAt).toLocaleDateString() }}
                                    </span>
                                </div>
                                
                                <div class="task-actions">
                                    <button 
                                        @click="startEdit(task)"
                                        class="btn btn-outline btn-sm"
                                        title="编辑"
                                    >
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    
                                    <button 
                                        @click="deleteTask(task)"
                                        class="btn btn-danger btn-sm"
                                        title="删除"
                                    >
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入依赖 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="assets/data/mock-data.js"></script>
    <script src="assets/js/restful-api.js"></script>
    <script src="assets/js/graphql-api.js"></script>
    <script src="assets/js/task-manager.js"></script>
</body>
</html>
