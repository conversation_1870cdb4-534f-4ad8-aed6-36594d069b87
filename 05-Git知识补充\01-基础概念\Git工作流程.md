# Git 工作流程详解

## 🔄 Git 基本工作流程

### 📊 工作流程图
```
工作目录 (Working Directory)
    ↓ git add
暂存区 (Staging Area)
    ↓ git commit
本地仓库 (Local Repository)
    ↓ git push
远程仓库 (Remote Repository)
```

### 🎯 详细流程说明

#### 1. 初始化或克隆仓库
```bash
# 方式一：初始化新仓库
git init my-project
cd my-project

# 方式二：克隆现有仓库
git clone https://github.com/username/repository.git
cd repository
```

#### 2. 修改文件
```bash
# 创建或修改文件
echo "Hello Git" > README.md
echo "console.log('Hello World');" > app.js
```

#### 3. 查看状态
```bash
# 查看工作目录状态
git status

# 输出示例：
# On branch main
# Untracked files:
#   (use "git add <file>..." to include in what will be committed)
#         README.md
#         app.js
```

#### 4. 添加到暂存区
```bash
# 添加单个文件
git add README.md

# 添加多个文件
git add README.md app.js

# 添加所有文件
git add .

# 添加所有 .js 文件
git add *.js
```

#### 5. 提交到本地仓库
```bash
# 提交暂存区的所有文件
git commit -m "Initial commit: Add README and app.js"

# 提交并添加文件（跳过 git add）
git commit -am "Update existing files"
```

#### 6. 推送到远程仓库
```bash
# 首次推送（设置上游分支）
git push -u origin main

# 后续推送
git push
```

## 🌊 常见工作流程模式

### 🔄 日常开发流程

#### 单人开发流程
```bash
# 1. 拉取最新代码
git pull

# 2. 修改文件
# ... 编辑代码 ...

# 3. 查看修改
git status
git diff

# 4. 添加修改
git add .

# 5. 提交修改
git commit -m "feat: add new feature"

# 6. 推送到远程
git push
```

#### 团队协作流程
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 创建功能分支
git checkout -b feature/user-login

# 3. 开发功能
# ... 编辑代码 ...

# 4. 提交到功能分支
git add .
git commit -m "feat: implement user login"

# 5. 推送功能分支
git push origin feature/user-login

# 6. 创建 Pull Request
# 在 GitHub/GitLab 上创建 PR

# 7. 合并后删除分支
git checkout main
git pull origin main
git branch -d feature/user-login
```

### 🚀 Git Flow 工作流程

Git Flow 是一种分支管理策略，适合有计划发布周期的项目。

#### 分支类型
```
main (master)     - 主分支，存放稳定版本
develop          - 开发分支，集成最新功能
feature/*        - 功能分支，开发新功能
release/*        - 发布分支，准备新版本
hotfix/*         - 热修复分支，修复紧急问题
```

#### 功能开发流程
```bash
# 1. 从 develop 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/shopping-cart

# 2. 开发功能
# ... 编辑代码 ...
git add .
git commit -m "feat: add shopping cart functionality"

# 3. 推送功能分支
git push origin feature/shopping-cart

# 4. 合并到 develop
git checkout develop
git merge feature/shopping-cart
git push origin develop

# 5. 删除功能分支
git branch -d feature/shopping-cart
git push origin --delete feature/shopping-cart
```

#### 发布流程
```bash
# 1. 从 develop 创建发布分支
git checkout develop
git checkout -b release/v1.2.0

# 2. 准备发布（修复 bug，更新版本号）
# ... 修复和准备工作 ...
git commit -am "chore: prepare for v1.2.0 release"

# 3. 合并到 main
git checkout main
git merge release/v1.2.0
git tag v1.2.0
git push origin main --tags

# 4. 合并回 develop
git checkout develop
git merge release/v1.2.0
git push origin develop

# 5. 删除发布分支
git branch -d release/v1.2.0
```

### 🌐 GitHub Flow 工作流程

GitHub Flow 是一种简化的工作流程，适合持续部署的项目。

#### 流程步骤
```bash
# 1. 从 main 创建分支
git checkout main
git pull origin main
git checkout -b feature/new-feature

# 2. 开发和提交
# ... 编辑代码 ...
git add .
git commit -m "feat: implement new feature"

# 3. 推送分支
git push origin feature/new-feature

# 4. 创建 Pull Request
# 在 GitHub 上创建 PR

# 5. 代码审查和讨论
# 团队成员审查代码

# 6. 合并到 main
# 通过 GitHub 界面合并

# 7. 部署
# 自动或手动部署到生产环境

# 8. 删除分支
git checkout main
git pull origin main
git branch -d feature/new-feature
```

## 🔧 工作流程中的常用命令

### 📋 状态查看
```bash
# 查看工作目录状态
git status

# 简洁状态显示
git status -s

# 查看分支状态
git branch -v

# 查看远程分支
git branch -r
```

### 📝 修改查看
```bash
# 查看工作目录的修改
git diff

# 查看暂存区的修改
git diff --staged

# 查看特定文件的修改
git diff filename.txt

# 查看两个提交之间的差异
git diff commit1 commit2
```

### 📚 历史查看
```bash
# 查看提交历史
git log

# 简洁历史显示
git log --oneline

# 图形化历史显示
git log --graph --oneline

# 查看特定文件的历史
git log filename.txt
```

### 🔄 撤销操作
```bash
# 撤销工作目录的修改
git checkout -- filename.txt

# 撤销暂存区的修改
git reset HEAD filename.txt

# 撤销最后一次提交（保留修改）
git reset --soft HEAD~1

# 撤销最后一次提交（丢弃修改）
git reset --hard HEAD~1
```

## 🎯 最佳实践

### ✅ 提交规范
```bash
# 使用有意义的提交信息
git commit -m "feat: add user authentication"
git commit -m "fix: resolve login button styling issue"
git commit -m "docs: update API documentation"

# 提交信息格式
# type(scope): description
# 
# type: feat, fix, docs, style, refactor, test, chore
# scope: 影响的模块或功能
# description: 简洁的描述
```

### 🔄 频繁提交
```bash
# 小步快跑，频繁提交
git add .
git commit -m "feat: add login form validation"

# 继续开发
git add .
git commit -m "feat: add password strength indicator"
```

### 🌿 分支管理
```bash
# 使用描述性的分支名
git checkout -b feature/user-profile-page
git checkout -b bugfix/login-error-handling
git checkout -b hotfix/security-vulnerability

# 及时删除已合并的分支
git branch -d feature/completed-feature
```

### 📥 同步代码
```bash
# 开始工作前先同步
git pull origin main

# 推送前先同步
git pull origin main
git push origin feature-branch
```

## 🚨 常见问题和解决方案

### 问题1：忘记添加文件到暂存区
```bash
# 查看状态
git status

# 添加遗漏的文件
git add forgotten-file.txt
git commit --amend --no-edit
```

### 问题2：提交信息写错
```bash
# 修改最后一次提交信息
git commit --amend -m "correct commit message"
```

### 问题3：推送被拒绝
```bash
# 先拉取远程更新
git pull origin main

# 解决冲突后再推送
git push origin main
```

### 问题4：误提交敏感信息
```bash
# 从历史中完全删除文件
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch sensitive-file.txt' \
--prune-empty --tag-name-filter cat -- --all
```

## 📈 工作流程优化

### 🔧 使用别名提高效率
```bash
# 设置常用别名
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.pl pull
git config --global alias.ps push
```

### 🎯 使用钩子自动化
```bash
# 创建 pre-commit 钩子
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/sh
# 运行代码检查
npm run lint
npm run test
EOF

chmod +x .git/hooks/pre-commit
```

## 🚀 下一步学习

掌握基本工作流程后，建议学习：
1. **基本操作** - 详细的 Git 命令
2. **分支管理** - 高级分支操作
3. **远程仓库** - 多人协作技巧

---

**记住**: 工作流程是 Git 使用的核心，选择适合团队的工作流程并坚持执行是成功的关键！
