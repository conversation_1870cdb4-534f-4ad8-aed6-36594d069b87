import { gql } from '@apollo/client/core'

// 用户片段
export const USER_FRAGMENT = gql`
  fragment UserInfo on User {
    id
    username
    email
    firstName
    lastName
    avatar
    role
    status
    createdAt
    updatedAt
  }
`

// 用户详细信息片段（包含关联数据）
export const USER_DETAIL_FRAGMENT = gql`
  fragment UserDetailInfo on User {
    id
    username
    email
    firstName
    lastName
    avatar
    role
    status
    createdAt
    updatedAt
    tasks {
      id
      title
      status
      priority
      dueDate
      project {
        id
        name
      }
    }
    assignedTasks {
      id
      title
      status
      priority
      dueDate
      project {
        id
        name
      }
    }
    projects {
      id
      name
      status
      priority
    }
  }
`

// 获取用户列表查询
export const GET_USERS_QUERY = gql`
  ${USER_DETAIL_FRAGMENT}
  query GetUsers($pagination: PaginationInput, $search: String) {
    users(pagination: $pagination, search: $search) {
      users {
        ...UserDetailInfo
      }
      pagination {
        page
        limit
        total
        pages
        hasNext
        hasPrev
      }
    }
  }
`

// 获取单个用户查询
export const GET_USER_QUERY = gql`
  ${USER_DETAIL_FRAGMENT}
  query GetUser($id: ID!) {
    user(id: $id) {
      ...UserDetailInfo
    }
  }
`

// 创建用户变更
export const CREATE_USER_MUTATION = gql`
  ${USER_FRAGMENT}
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      ...UserInfo
    }
  }
`

// 更新用户变更
export const UPDATE_USER_MUTATION = gql`
  ${USER_FRAGMENT}
  mutation UpdateUser($id: ID!, $input: UpdateUserInput!) {
    updateUser(id: $id, input: $input) {
      ...UserInfo
    }
  }
`

// 删除用户变更
export const DELETE_USER_MUTATION = gql`
  mutation DeleteUser($id: ID!) {
    deleteUser(id: $id)
  }
`

// 用户统计查询
export const GET_USER_STATS_QUERY = gql`
  query GetUserStats($userId: ID!) {
    user(id: $userId) {
      id
      tasks {
        id
        status
      }
      assignedTasks {
        id
        status
      }
      projects {
        id
        status
      }
    }
  }
`
