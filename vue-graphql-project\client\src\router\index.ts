import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 路由定义
const routes: RouteRecordRaw[] = [
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue'),
        meta: {
            title: '登录',
            requiresAuth: false,
            hideInMenu: true
        }
    },
    {
        path: '/register',
        name: 'Register',
        component: () => import('@/views/auth/Register.vue'),
        meta: {
            title: '注册',
            requiresAuth: false,
            hideInMenu: true
        }
    },
    {
        path: '/',
        component: () => import('@/layouts/MainLayout.vue'),
        meta: {
            requiresAuth: true
        },
        children: [
            {
                path: '',
                name: 'Dashboard',
                component: () => import('@/views/Dashboard.vue'),
                meta: {
                    title: '仪表板',
                    icon: 'Dashboard'
                }
            },
            {
                path: '/projects',
                name: 'Projects',
                component: () => import('@/views/projects/ProjectList.vue'),
                meta: {
                    title: '项目管理',
                    icon: 'FolderOpened'
                }
            },
            {
                path: '/projects/:id',
                name: 'ProjectDetail',
                component: () => import('@/views/projects/ProjectDetail.vue'),
                meta: {
                    title: '项目详情',
                    hideInMenu: true
                }
            },
            {
                path: '/tasks',
                name: 'Tasks',
                component: () => import('@/views/tasks/TaskList.vue'),
                meta: {
                    title: '任务管理',
                    icon: 'List'
                }
            },
            {
                path: '/tasks/:id',
                name: 'TaskDetail',
                component: () => import('@/views/tasks/TaskDetail.vue'),
                meta: {
                    title: '任务详情',
                    hideInMenu: true
                }
            },
            {
                path: '/users',
                name: 'Users',
                component: () => import('@/views/users/UserList.vue'),
                meta: {
                    title: '用户管理',
                    icon: 'User',
                    roles: ['ADMIN', 'MANAGER']
                }
            },
            {
                path: '/members',
                name: 'Members',
                component: () => import('@/views/members/MemberList.vue'),
                meta: {
                    title: '成员信息',
                    icon: 'UserFilled'
                }
            },
            {
                path: '/profile',
                name: 'Profile',
                component: () => import('@/views/Profile.vue'),
                meta: {
                    title: '个人资料',
                    hideInMenu: true
                }
            }
        ]
    },
    {
        path: '/403',
        name: 'Forbidden',
        component: () => import('@/views/error/403.vue'),
        meta: {
            title: '权限不足',
            hideInMenu: true
        }
    },
    {
        path: '/404',
        name: 'NotFound',
        component: () => import('@/views/error/404.vue'),
        meta: {
            title: '页面不存在',
            hideInMenu: true
        }
    },
    {
        path: '/:pathMatch(.*)*',
        redirect: '/404'
    }
]

// 创建路由实例
const router = createRouter({
    history: createWebHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return { top: 0 }
        }
    }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
    NProgress.start()

    const authStore = useAuthStore()
    const appStore = useAppStore()

    // 设置页面标题
    if (to.meta.title) {
        document.title = `${to.meta.title} - Vue GraphQL Project`
    }

    // 检查是否需要认证
    if (to.meta.requiresAuth !== false) {
        if (!authStore.isAuthenticated) {
            // 如果没有token，直接跳转到登录页
            if (!authStore.token) {
                next('/login')
                return
            }

            // 如果有token但没有用户信息，尝试获取用户信息
            const success = await authStore.fetchMe()
            if (!success) {
                next('/login')
                return
            }
        }

        // 检查角色权限
        if (to.meta.roles && Array.isArray(to.meta.roles)) {
            if (!authStore.hasRole(to.meta.roles)) {
                appStore.notifyError('权限不足', '您没有访问此页面的权限')
                next('/403')
                return
            }
        }
    } else {
        // 如果已经登录，访问登录/注册页面时重定向到首页
        if (authStore.isAuthenticated && (to.name === 'Login' || to.name === 'Register')) {
            next('/')
            return
        }
    }

    // 设置面包屑
    const breadcrumbs = generateBreadcrumbs(to)
    appStore.setBreadcrumbs(breadcrumbs)

    next()
})

router.afterEach(() => {
    NProgress.done()
})

// 生成面包屑导航
function generateBreadcrumbs(route: any) {
    const breadcrumbs = []

    // 添加首页
    if (route.name !== 'Dashboard') {
        breadcrumbs.push({ name: '首页', path: '/' })
    }

    // 根据路由生成面包屑
    if (route.matched.length > 1) {
        for (let i = 1; i < route.matched.length; i++) {
            const matched = route.matched[i]
            if (matched.meta.title && !matched.meta.hideInMenu) {
                breadcrumbs.push({
                    name: matched.meta.title,
                    path: matched.path
                })
            }
        }
    }

    // 添加当前页面
    if (route.meta.title) {
        breadcrumbs.push({
            name: route.meta.title
        })
    }

    return breadcrumbs
}

export default router

// 导出路由配置用于菜单生成
export const menuRoutes = routes.find(route => route.path === '/')?.children?.filter(
    route => !route.meta?.hideInMenu
) || []
