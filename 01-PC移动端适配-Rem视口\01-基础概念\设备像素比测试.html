<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备像素比(DPR)测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: #4a5568;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .info-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .label {
            font-weight: 600;
            color: #2d3748;
        }

        .value {
            font-family: 'Courier New', monospace;
            background: #edf2f7;
            padding: 4px 8px;
            border-radius: 4px;
            color: #1a202c;
        }

        .test-section {
            margin: 20px 0;
        }

        .pixel-test {
            border: 1px solid #333;
            margin: 10px 0;
            position: relative;
        }

        .pixel-1 {
            width: 100px;
            height: 1px;
            background: #e53e3e;
        }

        .pixel-2 {
            width: 100px;
            height: 2px;
            background: #38a169;
        }

        .pixel-3 {
            width: 100px;
            height: 3px;
            background: #3182ce;
        }

        .refresh-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }

        .refresh-btn:hover {
            background: #3182ce;
        }

        .explanation {
            background: #fff5cd;
            border-left: 4px solid #f6e05e;
            padding: 15px;
            margin: 20px 0;
        }

        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }

        @media screen and (max-width: 600px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 设备像素比(DPR)测试工具</h1>
            <p>了解你的设备显示特性</p>
        </div>
        
        <div class="content">
            <div class="info-card">
                <h2>🔍 设备信息</h2>
                <div class="info-item">
                    <span class="label">设备像素比 (DPR):</span>
                    <span class="value" id="dpr">-</span>
                </div>
                <div class="info-item">
                    <span class="label">屏幕宽度 (物理像素):</span>
                    <span class="value" id="screenWidth">-</span>
                </div>
                <div class="info-item">
                    <span class="label">屏幕高度 (物理像素):</span>
                    <span class="value" id="screenHeight">-</span>
                </div>
                <div class="info-item">
                    <span class="label">视口宽度 (逻辑像素):</span>
                    <span class="value" id="viewportWidth">-</span>
                </div>
                <div class="info-item">
                    <span class="label">视口高度 (逻辑像素):</span>
                    <span class="value" id="viewportHeight">-</span>
                </div>
                <div class="info-item">
                    <span class="label">用户代理:</span>
                    <span class="value" id="userAgent">-</span>
                </div>
            </div>

            <div class="test-section">
                <h2>📏 像素线条测试</h2>
                <p>观察不同像素高度的线条在你的设备上的显示效果：</p>
                
                <div class="pixel-test">
                    <div class="pixel-1"></div>
                    <small>1px 红色线条</small>
                </div>
                
                <div class="pixel-test">
                    <div class="pixel-2"></div>
                    <small>2px 绿色线条</small>
                </div>
                
                <div class="pixel-test">
                    <div class="pixel-3"></div>
                    <small>3px 蓝色线条</small>
                </div>
            </div>

            <div class="explanation">
                <h3>💡 DPR 说明</h3>
                <p><strong>DPR = 1:</strong> 普通屏幕，1个CSS像素对应1个物理像素</p>
                <p><strong>DPR = 2:</strong> 高清屏幕(如Retina)，1个CSS像素对应4个物理像素</p>
                <p><strong>DPR = 3:</strong> 超高清屏幕，1个CSS像素对应9个物理像素</p>
            </div>

            <div class="test-section">
                <h2>🧪 JavaScript 检测代码</h2>
                <div class="code-block" id="codeExample">
// 获取设备像素比
const dpr = window.devicePixelRatio;

// 获取屏幕尺寸(物理像素)
const screenWidth = screen.width * dpr;
const screenHeight = screen.height * dpr;

// 获取视口尺寸(逻辑像素)
const viewportWidth = window.innerWidth;
const viewportHeight = window.innerHeight;

console.log('DPR:', dpr);
console.log('物理像素:', screenWidth, 'x', screenHeight);
console.log('逻辑像素:', viewportWidth, 'x', viewportHeight);
                </div>
            </div>

            <button class="refresh-btn" onclick="updateDeviceInfo()">🔄 刷新设备信息</button>
            <button class="refresh-btn" onclick="testPixelPerfect()">🔍 测试像素完美</button>
        </div>
    </div>

    <script>
        function updateDeviceInfo() {
            // 获取设备像素比
            const dpr = window.devicePixelRatio || 1;
            
            // 获取屏幕尺寸
            const screenWidth = screen.width;
            const screenHeight = screen.height;
            
            // 获取视口尺寸
            const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
            
            // 获取用户代理
            const userAgent = navigator.userAgent;
            
            // 更新页面显示
            document.getElementById('dpr').textContent = dpr;
            document.getElementById('screenWidth').textContent = screenWidth + ' × ' + dpr + ' = ' + (screenWidth * dpr);
            document.getElementById('screenHeight').textContent = screenHeight + ' × ' + dpr + ' = ' + (screenHeight * dpr);
            document.getElementById('viewportWidth').textContent = viewportWidth + 'px';
            document.getElementById('viewportHeight').textContent = viewportHeight + 'px';
            document.getElementById('userAgent').textContent = userAgent.substring(0, 50) + '...';
            
            // 在控制台输出详细信息
            console.group('📱 设备信息详情');
            console.log('设备像素比 (DPR):', dpr);
            console.log('屏幕尺寸 (逻辑像素):', screenWidth + ' × ' + screenHeight);
            console.log('屏幕尺寸 (物理像素):', (screenWidth * dpr) + ' × ' + (screenHeight * dpr));
            console.log('视口尺寸 (逻辑像素):', viewportWidth + ' × ' + viewportHeight);
            console.log('视口尺寸 (物理像素):', (viewportWidth * dpr) + ' × ' + (viewportHeight * dpr));
            console.log('用户代理:', userAgent);
            console.groupEnd();
        }
        
        function testPixelPerfect() {
            const dpr = window.devicePixelRatio;
            const message = `
当前设备DPR为 ${dpr}

${dpr === 1 ? '✅ 标准屏幕：1px线条显示正常' : 
  dpr === 2 ? '⚠️ 高清屏幕：1px线条可能显示为2个物理像素，看起来较粗' :
  '⚠️ 超高清屏幕：1px线条可能显示为' + dpr + '个物理像素，看起来很粗'}

建议：
${dpr > 1 ? '• 使用 0.5px 或 transform: scale() 来实现真正的1物理像素线条\n• 为高DPR设备提供@2x、@3x图片' : '• 当前设备无需特殊处理'}
            `;
            
            alert(message);
        }
        
        // 页面加载时自动更新信息
        window.addEventListener('load', updateDeviceInfo);
        
        // 窗口大小改变时更新信息
        window.addEventListener('resize', updateDeviceInfo);
        
        // 设备方向改变时更新信息
        window.addEventListener('orientationchange', function() {
            setTimeout(updateDeviceInfo, 100);
        });
    </script>
</body>
</html>
