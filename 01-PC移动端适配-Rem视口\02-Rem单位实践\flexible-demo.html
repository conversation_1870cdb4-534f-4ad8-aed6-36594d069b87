<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 注意：不要手动设置viewport，让flexible.js自动处理 -->
    <title>手淘Flexible.js适配方案演示</title>
    
    <!-- 引入flexible.js -->
    <script src="./flexible.js"></script>
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        /* 使用rem单位进行布局 */
        .container {
            max-width: 10rem; /* 相当于设计稿中的100px (假设设计稿宽度为375px) */
            margin: 0 auto;
            padding: 0.2rem;
            background: white;
        }

        .header {
            height: 1rem; /* 相当于设计稿中的10px */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.32rem; /* 相当于设计稿中的3.2px */
            margin-bottom: 0.2rem;
        }

        .card {
            background: white;
            border-radius: 0.16rem; /* 相当于设计稿中的1.6px */
            padding: 0.3rem;
            margin-bottom: 0.2rem;
            box-shadow: 0 0.04rem 0.16rem rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 0.32rem; /* 相当于设计稿中的3.2px */
            font-weight: bold;
            margin-bottom: 0.2rem;
            color: #333;
        }

        .card-content {
            font-size: 0.28rem; /* 相当于设计稿中的2.8px */
            color: #666;
            line-height: 1.5;
        }

        .button {
            display: inline-block;
            padding: 0.2rem 0.4rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 0.08rem;
            font-size: 0.28rem;
            margin: 0.1rem 0.1rem 0.1rem 0;
            transition: background 0.3s ease;
        }

        .button:hover {
            background: #5a6fd8;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.2rem;
            margin: 0.3rem 0;
        }

        .grid-item {
            background: #f8f9fa;
            padding: 0.3rem;
            text-align: center;
            border-radius: 0.08rem;
            font-size: 0.24rem;
        }

        .debug-info {
            position: fixed;
            top: 0.2rem;
            right: 0.2rem;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 0.2rem;
            border-radius: 0.08rem;
            font-size: 0.24rem;
            font-family: 'Courier New', monospace;
            z-index: 1000;
            max-width: 4rem;
        }

        .debug-info div {
            margin-bottom: 0.1rem;
        }

        /* 1px边框问题的解决方案 */
        .border-1px {
            position: relative;
        }

        .border-1px::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 1px;
            background: #e0e0e0;
            transform-origin: 0 bottom;
        }

        /* 根据DPR调整1px边框 */
        [data-dpr="2"] .border-1px::after {
            transform: scaleY(0.5);
        }

        [data-dpr="3"] .border-1px::after {
            transform: scaleY(0.33);
        }

        .demo-section {
            margin: 0.3rem 0;
        }

        .demo-title {
            font-size: 0.3rem;
            font-weight: bold;
            margin-bottom: 0.2rem;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>手淘Flexible.js演示</h1>
        </div>

        <div class="demo-section">
            <div class="demo-title">基础卡片布局</div>
            <div class="card border-1px">
                <div class="card-title">卡片标题</div>
                <div class="card-content">
                    这是一个使用rem单位布局的卡片。所有尺寸都会根据屏幕宽度自动缩放，
                    保证在不同设备上的视觉效果一致。
                </div>
                <a href="#" class="button">了解更多</a>
                <a href="#" class="button">立即体验</a>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">网格布局</div>
            <div class="grid">
                <div class="grid-item">网格项目 1</div>
                <div class="grid-item">网格项目 2</div>
                <div class="grid-item">网格项目 3</div>
                <div class="grid-item">网格项目 4</div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">1px边框测试</div>
            <div class="card border-1px">
                <div class="card-content">
                    这个卡片底部有1px边框，在高DPR设备上会自动调整为真正的1物理像素。
                    你可以在不同设备上查看效果。
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">响应式测试</div>
            <div class="card">
                <div class="card-content">
                    调整浏览器窗口大小或旋转设备，观察页面如何自动适配。
                    所有元素都会按比例缩放，保持设计稿的比例关系。
                </div>
            </div>
        </div>
    </div>

    <!-- 调试信息面板 -->
    <div class="debug-info" id="debugInfo">
        <div>加载中...</div>
    </div>

    <script>
        // 显示调试信息
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const dpr = window.dpr || window.devicePixelRatio;
            const rem = window.rem || parseFloat(getComputedStyle(document.documentElement).fontSize);
            const screenWidth = window.screen.width;
            const viewportWidth = document.documentElement.clientWidth;
            
            debugInfo.innerHTML = `
                <div>DPR: ${dpr}</div>
                <div>REM: ${rem.toFixed(2)}px</div>
                <div>屏幕: ${screenWidth}px</div>
                <div>视口: ${viewportWidth}px</div>
                <div>缩放: ${(1/dpr).toFixed(2)}</div>
            `;
        }

        // 页面加载完成后更新调试信息
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo();
            
            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                setTimeout(updateDebugInfo, 100);
            });
            
            // 监听设备方向变化
            window.addEventListener('orientationchange', function() {
                setTimeout(updateDebugInfo, 300);
            });
        });

        // 演示flexible的API
        console.log('=== Flexible.js API 演示 ===');
        console.log('当前DPR:', window.dpr);
        console.log('当前REM基准:', window.rem + 'px');
        
        // 等待flexible初始化完成
        setTimeout(() => {
            if (window.lib && window.lib.flexible) {
                console.log('px转rem示例:', window.lib.flexible.px2rem('100px'));
                console.log('rem转px示例:', window.lib.flexible.rem2px('1rem'));
            }
        }, 100);
    </script>
</body>
</html>
