<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL 交互式学习平台</title>
    <link rel="stylesheet" href="assets/css/graphql-playground.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GraphQL 交互式学习平台</h1>
            <p>学习和测试GraphQL查询的完整环境</p>
        </div>

        <div class="playground">
            <div class="query-panel">
                <div class="panel-header">
                    📝 查询编辑器
                </div>
                <div class="panel-content">
                    <textarea id="queryEditor" class="query-editor" placeholder="在这里输入你的GraphQL查询...">query {
  users {
    id
    name
    email
    posts {
      id
      title
    }
  }
}</textarea>
                    <textarea id="variablesEditor" class="variables-editor" placeholder="变量 (JSON格式)">
{
  "userId": 1
}</textarea>
                    <button class="execute-btn" onclick="executeQuery()">▶️ 执行查询</button>
                </div>
            </div>

            <div class="result-panel">
                <div class="panel-header">
                    📊 查询结果
                </div>
                <div class="panel-content">
                    <div id="resultDisplay" class="result-display">点击"执行查询"按钮查看结果...</div>
                </div>
            </div>
        </div>

        <div class="examples-section">
            <h2>📚 示例查询</h2>
            <div class="example-tabs">
                <button class="tab-button active" onclick="showTab('queries')">查询 (Query)</button>
                <button class="tab-button" onclick="showTab('mutations')">修改 (Mutation)</button>
                <button class="tab-button" onclick="showTab('subscriptions')">订阅 (Subscription)</button>
                <button class="tab-button" onclick="showTab('advanced')">高级特性</button>
            </div>

            <div id="queries" class="tab-content active">
                <div class="example-item">
                    <div class="example-title">基础查询 - 获取所有用户</div>
                    <div class="example-code">query {
  users {
    id
    name
    email
  }
}</div>
                    <button class="try-button" onclick="tryExample('basicQuery')">试试看</button>
                </div>

                <div class="example-item">
                    <div class="example-title">带参数查询 - 获取特定用户</div>
                    <div class="example-code">query GetUser($userId: ID!) {
  user(id: $userId) {
    id
    name
    email
    posts {
      id
      title
      content
    }
  }
}</div>
                    <button class="try-button" onclick="tryExample('parameterQuery')">试试看</button>
                </div>

                <div class="example-item">
                    <div class="example-title">嵌套查询 - 用户及其文章和评论</div>
                    <div class="example-code">query {
  user(id: "1") {
    name
    posts {
      title
      comments {
        content
        author {
          name
        }
      }
    }
  }
}</div>
                    <button class="try-button" onclick="tryExample('nestedQuery')">试试看</button>
                </div>
            </div>

            <div id="mutations" class="tab-content">
                <div class="example-item">
                    <div class="example-title">创建用户</div>
                    <div class="example-code">mutation CreateUser($input: CreateUserInput!) {
  createUser(input: $input) {
    id
    name
    email
    createdAt
  }
}</div>
                    <button class="try-button" onclick="tryExample('createUser')">试试看</button>
                </div>

                <div class="example-item">
                    <div class="example-title">更新用户</div>
                    <div class="example-code">mutation UpdateUser($id: ID!, $input: UpdateUserInput!) {
  updateUser(id: $id, input: $input) {
    id
    name
    email
    updatedAt
  }
}</div>
                    <button class="try-button" onclick="tryExample('updateUser')">试试看</button>
                </div>
            </div>

            <div id="subscriptions" class="tab-content">
                <div class="example-item">
                    <div class="example-title">订阅新用户创建</div>
                    <div class="example-code">subscription {
  userCreated {
    id
    name
    email
    createdAt
  }
}</div>
                    <button class="try-button" onclick="tryExample('userSubscription')">试试看</button>
                </div>
            </div>

            <div id="advanced" class="tab-content">
                <div class="example-item">
                    <div class="example-title">使用片段 (Fragments)</div>
                    <div class="example-code">fragment UserInfo on User {
  id
  name
  email
}

query {
  user1: user(id: "1") {
    ...UserInfo
  }
  user2: user(id: "2") {
    ...UserInfo
  }
}</div>
                    <button class="try-button" onclick="tryExample('fragments')">试试看</button>
                </div>

                <div class="example-item">
                    <div class="example-title">内联片段 - 联合类型</div>
                    <div class="example-code">query Search($query: String!) {
  search(query: $query) {
    ... on User {
      id
      name
      email
    }
    ... on Post {
      id
      title
      content
    }
  }
}</div>
                    <button class="try-button" onclick="tryExample('inlineFragments')">试试看</button>
                </div>
            </div>
        </div>

        <div class="schema-section">
            <h2>📋 GraphQL Schema</h2>
            <div id="schemaDisplay" class="schema-display">
type User {
  id: ID!
  name: String!
  email: String!
  posts: [Post!]!
  friends: [User!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Post {
  id: ID!
  title: String!
  content: String!
  author: User!
  comments: [Comment!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Comment {
  id: ID!
  content: String!
  author: User!
  post: Post!
  createdAt: DateTime!
}

type Query {
  users: [User!]!
  user(id: ID!): User
  posts: [Post!]!
  post(id: ID!): Post
  search(query: String!): [SearchResult!]!
}

type Mutation {
  createUser(input: CreateUserInput!): User!
  updateUser(id: ID!, input: UpdateUserInput!): User!
  deleteUser(id: ID!): Boolean!
  createPost(input: CreatePostInput!): Post!
}

type Subscription {
  userCreated: User!
  postCreated: Post!
  commentAdded(postId: ID!): Comment!
}

union SearchResult = User | Post | Comment

input CreateUserInput {
  name: String!
  email: String!
}

input UpdateUserInput {
  name: String
  email: String
}

input CreatePostInput {
  title: String!
  content: String!
  authorId: ID!
}

scalar DateTime
            </div>
        </div>
    </div>

    <!-- 引入外部JavaScript文件 -->
    <script type="module" src="assets/js/graphql-playground.js"></script>
</body>
</html>
