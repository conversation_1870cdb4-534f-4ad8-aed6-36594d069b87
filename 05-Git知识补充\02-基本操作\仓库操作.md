# Git 仓库操作详解

## 🏗️ 仓库初始化

### 📁 创建新仓库

#### 初始化空仓库
```bash
# 在当前目录初始化
git init

# 在指定目录初始化
git init my-project

# 初始化裸仓库（用于服务器）
git init --bare my-repo.git
```

#### 初始化配置
```bash
# 初始化后的基本配置
cd my-project
git config user.name "Your Name"
git config user.email "<EMAIL>"

# 设置默认分支名
git config init.defaultBranch main
```

### 📥 克隆现有仓库

#### 基本克隆
```bash
# 克隆远程仓库
git clone https://github.com/username/repository.git

# 克隆到指定目录
git clone https://github.com/username/repository.git my-local-name

# 克隆指定分支
git clone -b develop https://github.com/username/repository.git
```

#### 高级克隆选项
```bash
# 浅克隆（只克隆最近的提交）
git clone --depth 1 https://github.com/username/repository.git

# 克隆单个分支
git clone --single-branch --branch main https://github.com/username/repository.git

# 克隆时不检出文件
git clone --no-checkout https://github.com/username/repository.git
```

## 📊 仓库状态查看

### 🔍 git status - 查看工作目录状态

#### 基本用法
```bash
# 查看详细状态
git status

# 简洁状态显示
git status -s
git status --short

# 显示被忽略的文件
git status --ignored
```

#### 状态输出解读
```bash
# 详细状态示例
On branch main                    # 当前分支
Your branch is up to date with 'origin/main'.  # 分支同步状态

Changes to be committed:          # 暂存区的修改
  (use "git reset HEAD <file>..." to unstage)
        new file:   README.md
        modified:   app.js

Changes not staged for commit:    # 工作目录的修改
  (use "git add <file>..." to update what will be committed)
  (use "git checkout -- <file>..." to discard changes)
        modified:   style.css

Untracked files:                  # 未跟踪的文件
  (use "git add <file>..." to include in what will be committed)
        config.json
```

#### 简洁状态符号
```bash
# git status -s 输出符号含义
M  modified.txt     # 已修改并暂存
 M modified2.txt    # 已修改但未暂存
MM both-modified.txt # 暂存后又修改
A  added.txt        # 新添加到暂存区
?? untracked.txt    # 未跟踪文件
!! ignored.txt      # 被忽略的文件
```

### 📋 git log - 查看提交历史

#### 基本用法
```bash
# 查看提交历史
git log

# 简洁显示
git log --oneline

# 显示最近 n 次提交
git log -n 5
git log -5
```

#### 高级显示选项
```bash
# 图形化显示分支
git log --graph --oneline

# 显示统计信息
git log --stat

# 显示详细差异
git log -p

# 自定义格式
git log --pretty=format:"%h - %an, %ar : %s"
```

#### 过滤和搜索
```bash
# 按作者过滤
git log --author="John Doe"

# 按时间过滤
git log --since="2023-01-01"
git log --until="2023-12-31"
git log --since="2 weeks ago"

# 按提交信息搜索
git log --grep="fix"

# 按文件过滤
git log -- filename.txt
git log --follow -- filename.txt  # 跟踪重命名
```

### 🔍 git show - 查看提交详情

#### 基本用法
```bash
# 查看最新提交
git show

# 查看指定提交
git show commit-hash
git show HEAD~1

# 查看指定文件在某次提交中的内容
git show commit-hash:filename.txt
```

#### 显示选项
```bash
# 只显示统计信息
git show --stat

# 只显示文件名
git show --name-only

# 显示差异但不显示内容
git show --name-status
```

## 🔄 工作目录操作

### 📂 git ls-files - 列出文件

#### 基本用法
```bash
# 列出所有跟踪的文件
git ls-files

# 列出暂存区的文件
git ls-files --staged

# 列出修改的文件
git ls-files --modified

# 列出删除的文件
git ls-files --deleted
```

### 🧹 git clean - 清理工作目录

#### 基本用法
```bash
# 预览将要删除的文件
git clean -n

# 删除未跟踪的文件
git clean -f

# 删除未跟踪的文件和目录
git clean -fd

# 交互式清理
git clean -i
```

#### 清理选项
```bash
# 包括被忽略的文件
git clean -fx

# 只删除被忽略的文件
git clean -fX

# 清理子模块
git clean -f --recurse-submodules
```

## 🔧 仓库配置

### ⚙️ git config - 配置管理

#### 查看配置
```bash
# 查看所有配置
git config --list

# 查看特定配置
git config user.name
git config user.email

# 查看配置来源
git config --list --show-origin
```

#### 设置配置
```bash
# 仓库级配置
git config user.name "Project Name"
git config user.email "<EMAIL>"

# 全局配置
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 系统级配置
git config --system core.editor vim
```

#### 删除配置
```bash
# 删除配置项
git config --unset user.name
git config --global --unset user.email
```

### 📝 .gitignore - 忽略文件

#### 创建 .gitignore
```bash
# 创建 .gitignore 文件
cat > .gitignore << EOF
# 依赖目录
node_modules/
vendor/

# 构建输出
dist/
build/
*.min.js

# 日志文件
*.log
logs/

# 环境配置
.env
.env.local

# 编辑器配置
.vscode/
.idea/

# 操作系统文件
.DS_Store
Thumbs.db
EOF
```

#### .gitignore 语法
```bash
# 忽略特定文件
filename.txt

# 忽略特定扩展名
*.log
*.tmp

# 忽略目录
directory/
/root-directory/

# 不忽略特定文件
!important.log

# 忽略除了特定文件外的所有文件
/*
!README.md
!src/
```

#### 应用 .gitignore 到已跟踪文件
```bash
# 停止跟踪已跟踪的文件
git rm --cached filename.txt

# 停止跟踪目录
git rm -r --cached directory/

# 重新应用 .gitignore
git add .gitignore
git commit -m "Update .gitignore"
```

## 🔍 仓库检查和维护

### 🔧 git fsck - 文件系统检查

#### 基本检查
```bash
# 检查仓库完整性
git fsck

# 详细检查
git fsck --full

# 检查未引用的对象
git fsck --unreachable
```

### 🧹 git gc - 垃圾回收

#### 基本用法
```bash
# 运行垃圾回收
git gc

# 强制垃圾回收
git gc --aggressive

# 自动垃圾回收
git gc --auto
```

### 📊 git count-objects - 统计对象

#### 查看仓库大小
```bash
# 统计对象数量
git count-objects

# 详细统计
git count-objects -v

# 人类可读格式
git count-objects -vH
```

## 🔄 仓库同步

### 📥 git fetch - 获取远程更新

#### 基本用法
```bash
# 获取默认远程仓库的更新
git fetch

# 获取指定远程仓库的更新
git fetch origin

# 获取所有远程仓库的更新
git fetch --all
```

### 📥 git pull - 拉取并合并

#### 基本用法
```bash
# 拉取并合并
git pull

# 拉取指定分支
git pull origin main

# 使用变基方式拉取
git pull --rebase
```

### 📤 git push - 推送更新

#### 基本用法
```bash
# 推送到默认远程分支
git push

# 推送到指定远程分支
git push origin main

# 首次推送并设置上游
git push -u origin main
```

## 💡 实用技巧

### 🔍 查找大文件
```bash
# 查找大文件
git rev-list --objects --all | \
git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
sed -n 's/^blob //p' | \
sort --numeric-sort --key=2 | \
tail -10
```

### 📊 仓库统计
```bash
# 统计代码行数
git ls-files | xargs wc -l

# 统计提交数量
git rev-list --count HEAD

# 统计贡献者
git shortlog -sn
```

### 🔄 仓库迁移
```bash
# 镜像克隆
git clone --mirror old-repo.git

# 推送到新仓库
cd old-repo.git
git remote set-url origin new-repo.git
git push --mirror
```

## 🚀 下一步学习

掌握仓库操作后，建议学习：
1. **文件操作** - add、commit、rm、mv 等
2. **历史查看** - log、show、diff 等
3. **撤销操作** - reset、revert、checkout 等

---

**记住**: 仓库操作是 Git 的基础，熟练掌握这些命令将大大提高您的工作效率！
