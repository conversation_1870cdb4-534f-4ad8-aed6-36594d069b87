# 依赖目录
node_modules
client/node_modules
server/node_modules

# 构建输出
client/dist
server/dist

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 文件
.vscode
.idea
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# Git 文件
.git
.gitignore

# 文档文件
README.md
*.md

# 测试文件
coverage
.nyc_output

# 临时文件
tmp
temp

# 缓存文件
.cache
.parcel-cache
