# 重载、重写、覆写概念详解

## 🎯 概念总览

### 术语对照表

| 中文术语 | 英文术语 | 核心特征 | 发生时机 |
|----------|----------|----------|----------|
| **重载** | Overloading | 同名不同参数 | 编译时 |
| **重写/覆写** | Overriding | 子类重新实现父类方法 | 运行时 |

**注意**：重写和覆写是同一个概念的不同翻译，都指 Override。

---

## 🔄 重载 (Overloading)

### 核心概念

**重载**：在同一个类中，方法名相同但参数不同的多个方法。

### 关键特征

1. **同一个类中**：重载发生在同一个类内部
2. **方法名相同**：所有重载方法必须有相同的名字
3. **参数不同**：参数的数量、类型或顺序必须不同
4. **编译时决定**：编译器根据参数决定调用哪个方法
5. **返回值可以不同**：但不能仅凭返回值区分重载

### JavaScript中的重载

JavaScript本身不支持传统的方法重载，但可以通过参数检查模拟：

```javascript
class Calculator {
    // 模拟重载：根据参数数量和类型执行不同逻辑
    add(...args) {
        if (args.length === 2 && typeof args[0] === 'number' && typeof args[1] === 'number') {
            // 两个数字相加
            return args[0] + args[1];
        } else if (args.length === 3 && typeof args[0] === 'number' && typeof args[1] === 'number' && typeof args[2] === 'number') {
            // 三个数字相加
            return args[0] + args[1] + args[2];
        } else if (args.length === 2 && typeof args[0] === 'string' && typeof args[1] === 'string') {
            // 字符串拼接
            return args[0] + args[1];
        } else if (args.length === 1 && Array.isArray(args[0])) {
            // 数组求和
            return args[0].reduce((sum, num) => sum + num, 0);
        } else {
            throw new Error('不支持的参数类型或数量');
        }
    }
}

// 使用示例
const calc = new Calculator();

console.log(calc.add(1, 2));           // 3 (两个数字)
console.log(calc.add(1, 2, 3));        // 6 (三个数字)
console.log(calc.add("Hello", "World")); // "HelloWorld" (字符串)
console.log(calc.add([1, 2, 3, 4]));   // 10 (数组求和)
```

### TypeScript中的重载

TypeScript支持更正式的方法重载：

```typescript
class Calculator {
    // 重载声明
    add(a: number, b: number): number;
    add(a: string, b: string): string;
    add(a: number[]): number;
    
    // 实际实现
    add(a: any, b?: any): any {
        if (typeof a === 'number' && typeof b === 'number') {
            return a + b;
        } else if (typeof a === 'string' && typeof b === 'string') {
            return a + b;
        } else if (Array.isArray(a)) {
            return a.reduce((sum, num) => sum + num, 0);
        }
        throw new Error('不支持的参数类型');
    }
}

// 使用时有类型检查
const calc = new Calculator();
const result1: number = calc.add(1, 2);        // ✅ 正确
const result2: string = calc.add("a", "b");    // ✅ 正确
const result3: number = calc.add([1, 2, 3]);   // ✅ 正确
// const result4: number = calc.add("a", 2);   // ❌ 编译错误
```

### 重载的优势

```javascript
class FileProcessor {
    // 处理不同类型的输入
    process(data) {
        if (typeof data === 'string') {
            // 处理文件路径
            return this.processFilePath(data);
        } else if (data instanceof File) {
            // 处理文件对象
            return this.processFileObject(data);
        } else if (Array.isArray(data)) {
            // 批量处理
            return data.map(item => this.process(item));
        } else {
            throw new Error('不支持的数据类型');
        }
    }
    
    processFilePath(path) {
        console.log(`处理文件路径: ${path}`);
        return `已处理路径: ${path}`;
    }
    
    processFileObject(file) {
        console.log(`处理文件对象: ${file.name}`);
        return `已处理文件: ${file.name}`;
    }
}

// 使用
const processor = new FileProcessor();
processor.process('/path/to/file.txt');        // 处理路径
processor.process(new File([''], 'test.txt'));  // 处理文件对象
processor.process(['/file1.txt', '/file2.txt']); // 批量处理
```

---

## 🔄 重写/覆写 (Overriding)

### 核心概念

**重写/覆写**：子类重新实现父类的方法，改变或扩展其行为。

### 关键特征

1. **继承关系**：发生在父类和子类之间
2. **方法签名相同**：方法名、参数列表必须完全相同
3. **运行时决定**：根据对象的实际类型决定调用哪个方法
4. **多态性**：是实现多态的重要机制
5. **可以调用父类方法**：通过super关键字

### JavaScript中的重写

```javascript
// 父类
class Animal {
    constructor(name) {
        this.name = name;
    }
    
    makeSound() {
        console.log(`${this.name} 发出声音`);
    }
    
    move() {
        console.log(`${this.name} 在移动`);
    }
    
    introduce() {
        console.log(`我是 ${this.name}`);
        this.makeSound();
    }
}

// 子类1 - 重写父类方法
class Dog extends Animal {
    makeSound() {
        console.log(`${this.name} 汪汪叫`);
    }
    
    move() {
        console.log(`${this.name} 在跑步`);
    }
    
    // 新增方法
    wagTail() {
        console.log(`${this.name} 摇尾巴`);
    }
}

// 子类2 - 重写父类方法
class Cat extends Animal {
    makeSound() {
        console.log(`${this.name} 喵喵叫`);
    }
    
    move() {
        console.log(`${this.name} 在悄悄走路`);
    }
    
    // 新增方法
    climb() {
        console.log(`${this.name} 在爬树`);
    }
}

// 子类3 - 扩展父类方法
class Bird extends Animal {
    makeSound() {
        // 调用父类方法
        super.makeSound();
        console.log(`${this.name} 还会唱歌`);
    }
    
    move() {
        console.log(`${this.name} 在飞翔`);
    }
    
    fly() {
        console.log(`${this.name} 展翅高飞`);
    }
}

// 使用示例
const animals = [
    new Dog('旺财'),
    new Cat('咪咪'),
    new Bird('小鸟')
];

// 多态性体现：同样的方法调用，不同的行为
animals.forEach(animal => {
    console.log('--- 动物介绍 ---');
    animal.introduce(); // 调用重写后的方法
    animal.move();      // 调用重写后的方法
    console.log('');
});

// 输出：
// --- 动物介绍 ---
// 我是 旺财
// 旺财 汪汪叫
// 旺财 在跑步
//
// --- 动物介绍 ---
// 我是 咪咪
// 咪咪 喵喵叫
// 咪咪 在悄悄走路
//
// --- 动物介绍 ---
// 我是 小鸟
// 小鸟 发出声音
// 小鸟 还会唱歌
// 小鸟 在飞翔
```

### 重写的高级用法

```javascript
class Shape {
    constructor(color) {
        this.color = color;
    }
    
    draw() {
        console.log(`绘制一个${this.color}的形状`);
    }
    
    getArea() {
        throw new Error('子类必须实现getArea方法');
    }
    
    getInfo() {
        return `颜色: ${this.color}, 面积: ${this.getArea()}`;
    }
}

class Rectangle extends Shape {
    constructor(color, width, height) {
        super(color); // 调用父类构造函数
        this.width = width;
        this.height = height;
    }
    
    // 重写抽象方法
    getArea() {
        return this.width * this.height;
    }
    
    // 重写绘制方法
    draw() {
        super.draw(); // 调用父类方法
        console.log(`这是一个 ${this.width}x${this.height} 的长方形`);
    }
}

class Circle extends Shape {
    constructor(color, radius) {
        super(color);
        this.radius = radius;
    }
    
    // 重写抽象方法
    getArea() {
        return Math.PI * this.radius * this.radius;
    }
    
    // 重写绘制方法
    draw() {
        super.draw();
        console.log(`这是一个半径为 ${this.radius} 的圆形`);
    }
}

// 使用
const shapes = [
    new Rectangle('红色', 5, 3),
    new Circle('蓝色', 4)
];

shapes.forEach(shape => {
    shape.draw();
    console.log(shape.getInfo());
    console.log('---');
});
```

---

## 🔍 重载 vs 重写对比

### 详细对比表

| 特征 | 重载 (Overloading) | 重写 (Overriding) |
|------|-------------------|-------------------|
| **发生位置** | 同一个类中 | 父类和子类之间 |
| **方法名** | 相同 | 相同 |
| **参数列表** | 必须不同 | 必须相同 |
| **返回值** | 可以不同 | 通常相同（协变返回类型除外） |
| **决定时机** | 编译时 | 运行时 |
| **目的** | 提供多种调用方式 | 改变或扩展行为 |
| **多态性** | 不涉及 | 实现多态 |
| **继承关系** | 不需要 | 必须有 |

### 实际应用场景

#### 重载适用场景

```javascript
class DataConverter {
    // 重载：处理不同类型的数据转换
    convert(data) {
        if (typeof data === 'string') {
            return this.convertString(data);
        } else if (typeof data === 'number') {
            return this.convertNumber(data);
        } else if (Array.isArray(data)) {
            return this.convertArray(data);
        } else if (typeof data === 'object') {
            return this.convertObject(data);
        }
    }
    
    convertString(str) {
        return str.toUpperCase();
    }
    
    convertNumber(num) {
        return num.toString();
    }
    
    convertArray(arr) {
        return arr.map(item => this.convert(item));
    }
    
    convertObject(obj) {
        const result = {};
        for (const [key, value] of Object.entries(obj)) {
            result[key] = this.convert(value);
        }
        return result;
    }
}
```

#### 重写适用场景

```javascript
class PaymentProcessor {
    processPayment(amount) {
        console.log(`处理支付: ${amount}元`);
        return { success: true, amount };
    }
}

class CreditCardProcessor extends PaymentProcessor {
    processPayment(amount) {
        console.log('验证信用卡信息...');
        const result = super.processPayment(amount);
        console.log('信用卡支付完成');
        return { ...result, method: 'credit_card' };
    }
}

class AlipayProcessor extends PaymentProcessor {
    processPayment(amount) {
        console.log('跳转到支付宝...');
        const result = super.processPayment(amount);
        console.log('支付宝支付完成');
        return { ...result, method: 'alipay' };
    }
}

// 多态使用
function handlePayment(processor, amount) {
    return processor.processPayment(amount); // 运行时决定调用哪个方法
}

const creditCard = new CreditCardProcessor();
const alipay = new AlipayProcessor();

handlePayment(creditCard, 100); // 调用信用卡处理器
handlePayment(alipay, 200);     // 调用支付宝处理器
```

---

## 🎯 实践建议

### 1. 何时使用重载

- **API设计**：为用户提供多种调用方式
- **参数灵活性**：支持不同类型或数量的参数
- **向后兼容**：在不破坏现有代码的情况下添加新功能

### 2. 何时使用重写

- **行为定制**：子类需要不同的行为实现
- **多态实现**：需要在运行时根据对象类型调用不同方法
- **扩展功能**：在父类基础上添加新功能

### 3. 最佳实践

#### 重载最佳实践

```javascript
class Logger {
    log(message, level = 'info', timestamp = new Date()) {
        // 使用默认参数代替重载
        console.log(`[${timestamp.toISOString()}] ${level.toUpperCase()}: ${message}`);
    }
    
    // 或者使用选项对象模式
    logWithOptions(message, options = {}) {
        const {
            level = 'info',
            timestamp = new Date(),
            format = 'json'
        } = options;
        
        if (format === 'json') {
            console.log(JSON.stringify({ message, level, timestamp }));
        } else {
            console.log(`[${timestamp.toISOString()}] ${level.toUpperCase()}: ${message}`);
        }
    }
}
```

#### 重写最佳实践

```javascript
class Component {
    render() {
        throw new Error('子类必须实现render方法');
    }
    
    mount() {
        console.log('组件挂载前的通用逻辑');
        this.render();
        console.log('组件挂载后的通用逻辑');
    }
}

class Button extends Component {
    constructor(text) {
        super();
        this.text = text;
    }
    
    render() {
        console.log(`渲染按钮: ${this.text}`);
    }
}

class Input extends Component {
    constructor(placeholder) {
        super();
        this.placeholder = placeholder;
    }
    
    render() {
        console.log(`渲染输入框: ${this.placeholder}`);
    }
}
```

---

## 📚 总结

### 记忆口诀

- **重载**："同名不同参，编译时决断"
- **重写**："父子同签名，运行时多态"

### 核心区别

1. **重载**是在同一个类中提供多个同名但参数不同的方法
2. **重写**是子类重新实现父类的方法以改变其行为
3. **重载**在编译时决定调用哪个方法
4. **重写**在运行时根据对象类型决定调用哪个方法

### 实际意义

- **重载**提高了API的易用性和灵活性
- **重写**实现了多态性，是面向对象编程的核心特性之一

理解这些概念有助于更好地设计和使用面向对象的代码结构！
