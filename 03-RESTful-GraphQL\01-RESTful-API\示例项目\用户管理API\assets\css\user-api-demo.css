/* 用户管理API演示样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

.main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.panel {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.panel:hover {
    transform: translateY(-5px);
}

.panel h2 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.api-section {
    margin-bottom: 30px;
}

.api-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.method-tag {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: bold;
    margin-right: 10px;
    text-transform: uppercase;
}

.method-get {
    background: #e8f5e8;
    color: #27ae60;
}

.method-post {
    background: #fff3cd;
    color: #f39c12;
}

.method-put {
    background: #e3f2fd;
    color: #2196f3;
}

.method-delete {
    background: #ffebee;
    color: #e74c3c;
}

.endpoint {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 5px;
    border-left: 4px solid #667eea;
    margin: 10px 0;
}

.btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    margin: 5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn:active {
    transform: translateY(0);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.response-area {
    background: #1e1e1e;
    color: #f8f8f2;
    padding: 20px;
    border-radius: 10px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    margin-top: 20px;
    border: 2px solid #333;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-success {
    background: #27ae60;
}

.status-error {
    background: #e74c3c;
}

.status-pending {
    background: #f39c12;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
}

.api-docs {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-top: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.api-docs h2 {
    color: #667eea;
    margin-bottom: 20px;
    text-align: center;
}

.doc-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.doc-section h3 {
    color: #333;
    margin-bottom: 15px;
}

.doc-section code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

.doc-section pre {
    background: #1e1e1e;
    color: #f8f8f2;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .container {
        padding: 10px;
    }
    
    .panel {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5em;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .panel {
        padding: 15px;
    }
}
