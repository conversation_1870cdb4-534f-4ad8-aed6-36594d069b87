#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT转Markdown工具
将PowerPoint文件转换为Markdown格式
"""

from pptx import Presentation
import os
import sys

def extract_text_from_shape(shape):
    """从形状中提取文本"""
    text = ""
    if hasattr(shape, "text"):
        text = shape.text
    elif hasattr(shape, "text_frame"):
        for paragraph in shape.text_frame.paragraphs:
            for run in paragraph.runs:
                text += run.text
            text += "\n"
    return text

def ppt_to_markdown(ppt_file, output_file):
    """将PPT转换为Markdown"""
    try:
        # 打开PPT文件
        prs = Presentation(ppt_file)
        
        markdown_content = []
        markdown_content.append("# 编码原则\n")
        
        for slide_num, slide in enumerate(prs.slides, 1):
            markdown_content.append(f"## 幻灯片 {slide_num}\n")
            
            # 提取幻灯片中的所有文本
            slide_text = []
            
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    text = shape.text.strip()
                    if text:
                        slide_text.append(text)
                elif hasattr(shape, "text_frame"):
                    text = extract_text_from_shape(shape).strip()
                    if text:
                        slide_text.append(text)
            
            # 处理文本内容
            for text in slide_text:
                lines = text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line:
                        # 如果是标题样式（通常是较短的文本）
                        if len(line) < 50 and not line.endswith('.') and not line.endswith('。'):
                            markdown_content.append(f"### {line}\n")
                        else:
                            # 普通文本，添加为列表项或段落
                            if line.startswith(('•', '-', '*', '1.', '2.', '3.', '4.', '5.')):
                                markdown_content.append(f"- {line.lstrip('•-* ')}\n")
                            else:
                                markdown_content.append(f"{line}\n")
            
            markdown_content.append("\n---\n")
        
        # 写入Markdown文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        
        print(f"转换完成！Markdown文件已保存为: {output_file}")
        return True
        
    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")
        return False

def main():
    ppt_file = "编码原则.pptx"
    output_file = "编码原则.md"
    
    if not os.path.exists(ppt_file):
        print(f"错误: 找不到文件 {ppt_file}")
        return
    
    print(f"开始转换 {ppt_file} 为 Markdown...")
    success = ppt_to_markdown(ppt_file, output_file)
    
    if success:
        print("转换成功完成！")
    else:
        print("转换失败！")

if __name__ == "__main__":
    main()
