# Git 分支基础详解

## 🌿 分支概念

### 📖 什么是分支？

分支是 Git 中最重要的概念之一，它允许您从主开发线上分离出来，在不影响主线的情况下继续工作。

#### 分支的本质
- **轻量级指针**: 分支只是指向某个提交对象的可变指针
- **快速创建**: 创建分支几乎是瞬间完成的
- **独立开发**: 每个分支都有独立的开发历史
- **安全隔离**: 分支间的修改互不影响

#### 分支的优势
```
分支优势：
├── 并行开发 - 多个功能同时开发
├── 实验安全 - 可以安全地尝试新想法
├── 版本管理 - 不同版本的代码管理
├── 团队协作 - 避免代码冲突
└── 发布管理 - 稳定版本和开发版本分离
```

### 🎯 Git 中的特殊指针

#### HEAD 指针
- **定义**: 指向当前所在分支的指针
- **作用**: 标识当前工作的分支
- **位置**: `.git/HEAD` 文件

```bash
# 查看 HEAD 指向
cat .git/HEAD
# 输出: ref: refs/heads/main

# 查看 HEAD 指向的提交
git rev-parse HEAD
```

#### 分支指针
- **定义**: 指向分支最新提交的指针
- **位置**: `.git/refs/heads/` 目录下
- **特点**: 随着新提交自动移动

## 🔧 分支基本操作

### 📋 查看分支

#### 列出分支
```bash
# 列出本地分支
git branch

# 列出所有分支（本地+远程）
git branch -a

# 列出远程分支
git branch -r

# 详细信息显示
git branch -v
git branch -vv  # 显示跟踪关系
```

#### 分支信息解读
```bash
# git branch -v 输出示例
* main    a1b2c3d [origin/main] Latest commit message
  feature 4e5f6g7 Working on new feature
  hotfix  8h9i0j1 [origin/hotfix: ahead 1] Fix critical bug

# 符号含义：
# * - 当前分支
# [origin/main] - 跟踪的远程分支
# [ahead 1] - 领先远程分支 1 个提交
# [behind 2] - 落后远程分支 2 个提交
```

### ➕ 创建分支

#### 基本创建
```bash
# 创建新分支（基于当前分支）
git branch feature-login

# 创建并切换到新分支
git checkout -b feature-login
# 或者使用新语法
git switch -c feature-login

# 基于指定提交创建分支
git branch feature-login commit-hash
git checkout -b feature-login commit-hash
```

#### 高级创建选项
```bash
# 基于远程分支创建本地分支
git checkout -b local-branch origin/remote-branch

# 创建孤儿分支（没有历史记录）
git checkout --orphan new-branch

# 创建分支并设置上游
git checkout -b feature-login
git push -u origin feature-login
```

### 🔄 切换分支

#### 基本切换
```bash
# 切换到已存在的分支
git checkout main
git checkout feature-login

# 使用新的 switch 命令（Git 2.23+）
git switch main
git switch feature-login

# 切换到上一个分支
git checkout -
git switch -
```

#### 切换前的检查
```bash
# 检查工作目录状态
git status

# 如果有未提交的修改，需要：
# 1. 提交修改
git add .
git commit -m "Work in progress"

# 2. 或者暂存修改
git stash
git checkout other-branch
git stash pop  # 恢复暂存的修改
```

### ❌ 删除分支

#### 安全删除
```bash
# 删除已合并的分支
git branch -d feature-completed

# 强制删除分支（即使未合并）
git branch -D feature-abandoned

# 删除远程分支
git push origin --delete feature-branch
```

#### 批量删除
```bash
# 删除所有已合并的分支
git branch --merged | grep -v "\*\|main\|develop" | xargs -n 1 git branch -d

# 删除所有包含特定模式的分支
git branch | grep "feature/" | xargs git branch -D
```

## 🌳 分支历史和关系

### 📊 可视化分支

#### 图形化显示
```bash
# 简单图形显示
git log --graph --oneline

# 详细图形显示
git log --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset'

# 显示所有分支
git log --graph --oneline --all

# 简化的分支图
git show-branch
```

#### 分支关系
```bash
# 查看分支的合并基础
git merge-base main feature-branch

# 查看分支包含的提交
git log main..feature-branch

# 查看两个分支的差异
git diff main...feature-branch
```

### 🔍 分支比较

#### 提交比较
```bash
# 查看分支 A 有而分支 B 没有的提交
git log branch-a ^branch-b

# 查看两个分支的不同提交
git log --left-right --graph --cherry-pick --oneline branch-a...branch-b

# 统计分支差异
git rev-list --count main..feature-branch
```

#### 文件比较
```bash
# 比较两个分支的文件差异
git diff main feature-branch

# 比较特定文件
git diff main feature-branch -- filename.txt

# 只显示文件名
git diff --name-only main feature-branch
```

## 🏷️ 分支命名规范

### 📝 命名约定

#### 常用分支类型
```bash
# 功能分支
feature/user-authentication
feature/shopping-cart
feat/payment-integration

# 修复分支
bugfix/login-error
fix/memory-leak
hotfix/security-patch

# 发布分支
release/v1.2.0
release/2023-q1

# 实验分支
experiment/new-algorithm
spike/performance-test
```

#### 命名最佳实践
```bash
# 好的分支名
feature/user-profile-page
bugfix/header-navigation-issue
hotfix/critical-security-vulnerability

# 避免的分支名
temp
test
my-branch
branch1
```

### 🎯 分支策略

#### 简单策略
```
main (生产环境)
├── feature/login
├── feature/dashboard
└── hotfix/urgent-fix
```

#### Git Flow 策略
```
main (生产环境)
├── develop (开发环境)
│   ├── feature/user-auth
│   ├── feature/payment
│   └── feature/reporting
├── release/v1.2.0
└── hotfix/critical-bug
```

## 🔧 分支配置

### ⚙️ 分支相关配置

#### 默认分支设置
```bash
# 设置默认分支名
git config --global init.defaultBranch main

# 查看当前默认分支
git config init.defaultBranch
```

#### 推送配置
```bash
# 设置推送策略
git config --global push.default simple

# 自动设置上游分支
git config --global push.autoSetupRemote true
```

#### 分支自动完成
```bash
# 启用分支名自动完成（需要 bash-completion）
# 在 ~/.bashrc 或 ~/.zshrc 中添加
source /usr/share/bash-completion/completions/git
```

## 💡 分支使用技巧

### 🚀 高效工作流

#### 功能开发流程
```bash
# 1. 从主分支创建功能分支
git checkout main
git pull origin main
git checkout -b feature/new-feature

# 2. 开发功能
# ... 编写代码 ...
git add .
git commit -m "feat: implement new feature"

# 3. 推送分支
git push -u origin feature/new-feature

# 4. 创建 Pull Request
# 在 GitHub/GitLab 上创建 PR

# 5. 合并后清理
git checkout main
git pull origin main
git branch -d feature/new-feature
```

#### 快速切换技巧
```bash
# 使用别名快速切换
git config --global alias.co checkout
git config --global alias.sw switch
git config --global alias.br branch

# 快速切换到主分支
git checkout main
# 或
git switch main

# 切换到上一个分支
git checkout -
```

### 🔍 分支调试

#### 查找问题分支
```bash
# 使用 git bisect 查找引入 bug 的提交
git bisect start
git bisect bad HEAD
git bisect good v1.0
# Git 会自动切换到中间的提交进行测试

# 查看分支的第一个提交
git log --reverse feature-branch | head -1

# 查看分支的创建点
git merge-base main feature-branch
```

#### 分支统计
```bash
# 统计分支的提交数
git rev-list --count feature-branch

# 统计分支的代码行数变化
git diff --stat main feature-branch

# 查看分支的贡献者
git shortlog -sn feature-branch
```

## 🚨 常见问题和解决方案

### 问题1：分支切换失败
```bash
# 错误：有未提交的修改
error: Your local changes to the following files would be overwritten by checkout

# 解决方案1：提交修改
git add .
git commit -m "WIP: save current work"

# 解决方案2：暂存修改
git stash
git checkout other-branch
git stash pop
```

### 问题2：分支删除失败
```bash
# 错误：分支未合并
error: The branch 'feature-branch' is not fully merged.

# 解决方案1：强制删除
git branch -D feature-branch

# 解决方案2：先合并再删除
git checkout main
git merge feature-branch
git branch -d feature-branch
```

### 问题3：分支名冲突
```bash
# 错误：分支名已存在
fatal: A branch named 'feature' already exists.

# 解决方案：使用不同的名称
git checkout -b feature-v2
# 或删除现有分支
git branch -D feature
git checkout -b feature
```

## 🚀 下一步学习

掌握分支基础后，建议学习：
1. **分支合并** - merge 和 rebase 策略
2. **冲突解决** - 处理合并冲突
3. **分支策略** - Git Flow、GitHub Flow 等

---

**记住**: 分支是 Git 最强大的功能之一，熟练使用分支将大大提高您的开发效率和代码管理能力！
