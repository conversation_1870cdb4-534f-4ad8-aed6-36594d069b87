# Git 实用指令速查表

## 🚀 日常开发必备指令

### 📁 仓库快速操作
```bash
# 快速初始化并提交
git init && git add . && git commit -m "Initial commit"

# 克隆并进入目录
git clone <url> && cd $(basename $_ .git)

# 查看仓库状态（简洁版）
git status -s

# 查看最近5次提交
git log --oneline -5

# 查看当前分支
git branch --show-current
```

### 💾 快速提交操作
```bash
# 添加所有修改并提交
git add -A && git commit -m "Update files"

# 修改最后一次提交信息
git commit --amend -m "New commit message"

# 快速修复最后一次提交（添加遗漏文件）
git add forgotten-file.txt && git commit --amend --no-edit

# 空提交（触发CI/CD）
git commit --allow-empty -m "Trigger build"

# 提交时跳过钩子
git commit --no-verify -m "Skip hooks"
```

### 🔍 查看和比较
```bash
# 查看文件修改历史
git log -p filename.txt

# 查看谁修改了这行代码
git blame filename.txt

# 查看两个分支的差异
git diff branch1..branch2

# 查看暂存区和工作区的差异
git diff --cached

# 查看最近一次提交的内容
git show HEAD

# 查看文件在某次提交时的内容
git show commit-hash:path/to/file
```

### 🔄 撤销和恢复
```bash
# 撤销工作区的修改
git checkout -- filename.txt

# 撤销暂存区的修改
git reset HEAD filename.txt

# 撤销最后一次提交（保留修改）
git reset --soft HEAD~1

# 撤销最后一次提交（丢弃修改）
git reset --hard HEAD~1

# 恢复删除的文件
git checkout HEAD~1 -- deleted-file.txt

# 创建反向提交
git revert HEAD
```

### 🌿 分支快速操作
```bash
# 创建并切换到新分支
git checkout -b feature/new-feature

# 切换到上一个分支
git checkout -

# 删除本地分支
git branch -d branch-name

# 强制删除本地分支
git branch -D branch-name

# 删除远程分支
git push origin --delete branch-name

# 查看所有分支（包括远程）
git branch -a

# 查看分支合并情况
git branch --merged
git branch --no-merged
```

### 📡 远程仓库操作
```bash
# 查看远程仓库信息
git remote -v

# 添加远程仓库
git remote add upstream https://github.com/original/repo.git

# 获取远程更新但不合并
git fetch origin

# 拉取并变基
git pull --rebase origin main

# 推送并设置上游分支
git push -u origin feature-branch

# 强制推送（危险操作）
git push --force-with-lease origin branch-name
```

## 🔧 高级实用指令

### 🔍 搜索和查找
```bash
# 在代码中搜索文本
git grep "search-term"

# 在提交历史中搜索
git log --grep="bug fix"

# 搜索添加或删除特定文本的提交
git log -S "function_name"

# 查找文件的完整历史（包括重命名）
git log --follow -- filename.txt

# 查找删除的文件
git log --diff-filter=D --summary | grep delete
```

### 📊 统计和分析
```bash
# 查看代码贡献统计
git shortlog -sn

# 查看文件修改频率
git log --name-only --pretty=format: | sort | uniq -c | sort -rg

# 查看每个作者的代码行数
git log --author="Author Name" --pretty=tformat: --numstat | \
awk '{ add += $1; subs += $2; loc += $1 - $2 } END { printf "added lines: %s, removed lines: %s, total lines: %s\n", add, subs, loc }'

# 查看仓库大小
git count-objects -vH

# 查看最大的文件
git rev-list --objects --all | git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | sed -n 's/^blob //p' | sort --numeric-sort --key=2 | tail -10
```

### 🏷️ 标签操作
```bash
# 创建轻量标签
git tag v1.0.0

# 创建带注释的标签
git tag -a v1.0.0 -m "Release version 1.0.0"

# 推送标签到远程
git push origin v1.0.0

# 推送所有标签
git push origin --tags

# 删除本地标签
git tag -d v1.0.0

# 删除远程标签
git push origin --delete v1.0.0

# 查看标签信息
git show v1.0.0
```

### 🧹 清理和维护
```bash
# 清理未跟踪的文件
git clean -fd

# 清理已合并的分支
git branch --merged | grep -v "\*\|main\|master" | xargs -n 1 git branch -d

# 压缩仓库
git gc --aggressive --prune=now

# 检查仓库完整性
git fsck --full

# 查看引用日志
git reflog

# 清理引用日志
git reflog expire --expire=now --all && git gc --prune=now
```

## 🎯 场景化指令组合

### 🚀 开始新功能开发
```bash
# 完整的功能开发流程
git checkout main
git pull origin main
git checkout -b feature/user-authentication
# 开发代码...
git add .
git commit -m "feat: add user authentication"
git push -u origin feature/user-authentication
```

### 🔄 同步远程更新
```bash
# 保持本地分支与远程同步
git fetch origin
git checkout main
git merge origin/main
git checkout feature-branch
git rebase main
```

### 🐛 紧急修复流程
```bash
# 紧急修复流程
git checkout main
git pull origin main
git checkout -b hotfix/critical-bug
# 修复代码...
git add .
git commit -m "fix: resolve critical security issue"
git push -u origin hotfix/critical-bug
# 创建 Pull Request 并合并后
git checkout main
git pull origin main
git branch -d hotfix/critical-bug
```

### 📦 发布版本流程
```bash
# 版本发布流程
git checkout main
git pull origin main
git tag -a v1.2.0 -m "Release version 1.2.0"
git push origin v1.2.0
git push origin main
```

## 💡 实用别名配置

### ⚡ 推荐的 Git 别名
```bash
# 设置常用别名
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'

# 高级别名
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"
git config --global alias.adog "log --all --decorate --oneline --graph"
git config --global alias.pom "push origin main"
git config --global alias.poh "push origin HEAD"
```

## 🚨 紧急救援指令

### 💊 常见问题快速解决
```bash
# 误删除文件恢复
git checkout HEAD -- deleted-file.txt

# 找回误删除的提交
git reflog
git checkout commit-hash

# 撤销已推送的提交
git revert HEAD
git push origin main

# 修改已推送的提交信息（危险）
git commit --amend -m "New message"
git push --force-with-lease origin branch-name

# 合并多个提交
git rebase -i HEAD~3

# 解决合并冲突后继续
git add .
git rebase --continue
# 或
git add .
git merge --continue
```

### 🔧 配置问题解决
```bash
# 重新配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 解决换行符问题
git config --global core.autocrlf true  # Windows
git config --global core.autocrlf input # Mac/Linux

# 设置默认编辑器
git config --global core.editor "code --wait"  # VS Code
git config --global core.editor "vim"          # Vim

# 设置默认分支名
git config --global init.defaultBranch main
```

---

## 📚 使用建议

1. **收藏此页面** - 作为日常开发的快速参考
2. **练习常用指令** - 每天使用几个新指令
3. **设置别名** - 为常用指令创建简短别名
4. **理解原理** - 不仅要会用，还要理解背后的原理
5. **安全第一** - 使用 `--force-with-lease` 而不是 `--force`

**记住**: 熟练掌握这些指令将让你的 Git 操作更加高效和专业！ 🚀
