<template>
  <div class="project-detail-container">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <div class="title-section">
          <h1>{{ project.name }}</h1>
          <el-tag :type="getStatusType(project.status)">
            {{ getStatusText(project.status) }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="editProject">
          <el-icon><Edit /></el-icon>
          编辑项目
        </el-button>
        <el-button type="danger" @click="deleteProject">
          <el-icon><Delete /></el-icon>
          删除项目
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧主要内容 -->
      <el-col :span="16">
        <!-- 项目信息卡片 -->
        <el-card class="project-info-card">
          <template #header>
            <span>项目信息</span>
          </template>
          
          <div class="project-description">
            <h3>项目描述</h3>
            <p>{{ project.description }}</p>
          </div>
          
          <div class="project-progress">
            <h3>项目进度</h3>
            <div class="progress-info">
              <span>{{ project.progress }}% 完成</span>
              <el-progress :percentage="project.progress" :stroke-width="8" />
            </div>
          </div>
          
          <div class="project-details">
            <h3>项目详情</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="项目状态">
                <el-tag :type="getStatusType(project.status)">
                  {{ getStatusText(project.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="优先级">
                <el-tag :type="getPriorityType(project.priority)">
                  {{ getPriorityText(project.priority) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="项目负责人">
                {{ project.owner?.username || '未分配' }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(project.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDate(project.updatedAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="截止时间">
                {{ project.deadline ? formatDate(project.deadline) : '未设置' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 任务列表卡片 -->
        <el-card class="tasks-card">
          <template #header>
            <div class="card-header">
              <span>项目任务</span>
              <el-button type="primary" size="small" @click="addTask">
                <el-icon><Plus /></el-icon>
                添加任务
              </el-button>
            </div>
          </template>
          
          <div class="tasks-list">
            <div
              v-for="task in project.tasks"
              :key="task.id"
              class="task-item"
              @click="viewTask(task.id)"
            >
              <div class="task-content">
                <div class="task-header">
                  <h4>{{ task.title }}</h4>
                  <el-tag :type="getTaskStatusType(task.status)" size="small">
                    {{ getTaskStatusText(task.status) }}
                  </el-tag>
                </div>
                <p class="task-description">{{ task.description }}</p>
                <div class="task-meta">
                  <span class="assignee">
                    <el-icon><User /></el-icon>
                    {{ task.assignee?.username || '未分配' }}
                  </span>
                  <span class="due-date">
                    <el-icon><Calendar /></el-icon>
                    {{ task.dueDate ? formatDate(task.dueDate) : '无截止时间' }}
                  </span>
                </div>
              </div>
              <div class="task-actions">
                <el-button size="small" @click.stop="editTask(task)">编辑</el-button>
                <el-button size="small" type="danger" @click.stop="deleteTask(task.id)">
                  删除
                </el-button>
              </div>
            </div>
            
            <div v-if="!project.tasks?.length" class="empty-tasks">
              <el-empty description="暂无任务" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧侧边栏 -->
      <el-col :span="8">
        <!-- 项目统计 -->
        <el-card class="stats-card">
          <template #header>
            <span>项目统计</span>
          </template>
          
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ project.tasks?.length || 0 }}</div>
              <div class="stat-label">总任务数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ completedTasksCount }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ inProgressTasksCount }}</div>
              <div class="stat-label">进行中</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ pendingTasksCount }}</div>
              <div class="stat-label">待开始</div>
            </div>
          </div>
        </el-card>

        <!-- 团队成员 -->
        <el-card class="team-card">
          <template #header>
            <span>团队成员</span>
          </template>
          
          <div class="team-members">
            <div
              v-for="member in project.team"
              :key="member.id"
              class="member-item"
            >
              <el-avatar :size="40" :src="member.avatar">
                {{ member.username.charAt(0) }}
              </el-avatar>
              <div class="member-info">
                <div class="member-name">{{ member.username }}</div>
                <div class="member-role">{{ member.role }}</div>
              </div>
            </div>
            
            <div v-if="!project.team?.length" class="empty-team">
              <p>暂无团队成员</p>
            </div>
          </div>
        </el-card>

        <!-- 最近活动 -->
        <el-card class="activity-card">
          <template #header>
            <span>最近活动</span>
          </template>
          
          <div class="activity-list">
            <div
              v-for="activity in project.activities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
              <div class="activity-content">{{ activity.description }}</div>
            </div>
            
            <div v-if="!project.activities?.length" class="empty-activity">
              <p>暂无活动记录</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  Delete,
  Plus,
  User,
  Calendar
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)

// 模拟项目数据
const project = ref({
  id: '1',
  name: '电商平台重构',
  description: '使用Vue3和GraphQL重构现有电商平台，提升用户体验和系统性能。这是一个大型项目，需要团队协作完成。',
  status: 'ACTIVE',
  priority: 'HIGH',
  progress: 75,
  createdAt: '2024-01-15T00:00:00Z',
  updatedAt: '2024-01-20T00:00:00Z',
  deadline: '2024-03-15T00:00:00Z',
  owner: { username: '张三' },
  tasks: [
    {
      id: '1',
      title: '用户界面设计',
      description: '设计新的用户界面和用户体验',
      status: 'COMPLETED',
      assignee: { username: '李四' },
      dueDate: '2024-02-01T00:00:00Z'
    },
    {
      id: '2',
      title: 'API接口开发',
      description: '开发GraphQL API接口',
      status: 'IN_PROGRESS',
      assignee: { username: '王五' },
      dueDate: '2024-02-15T00:00:00Z'
    },
    {
      id: '3',
      title: '前端组件开发',
      description: '使用Vue3开发前端组件',
      status: 'PENDING',
      assignee: { username: '赵六' },
      dueDate: '2024-02-28T00:00:00Z'
    }
  ],
  team: [
    { id: '1', username: '张三', role: '项目经理', avatar: '' },
    { id: '2', username: '李四', role: 'UI设计师', avatar: '' },
    { id: '3', username: '王五', role: '后端开发', avatar: '' },
    { id: '4', username: '赵六', role: '前端开发', avatar: '' }
  ],
  activities: [
    {
      id: '1',
      description: '张三更新了项目进度',
      createdAt: '2024-01-20T10:30:00Z'
    },
    {
      id: '2',
      description: '李四完成了用户界面设计任务',
      createdAt: '2024-01-19T15:20:00Z'
    },
    {
      id: '3',
      description: '王五开始了API接口开发任务',
      createdAt: '2024-01-18T09:15:00Z'
    }
  ]
})

// 计算属性
const completedTasksCount = computed(() => {
  return project.value.tasks?.filter(task => task.status === 'COMPLETED').length || 0
})

const inProgressTasksCount = computed(() => {
  return project.value.tasks?.filter(task => task.status === 'IN_PROGRESS').length || 0
})

const pendingTasksCount = computed(() => {
  return project.value.tasks?.filter(task => task.status === 'PENDING').length || 0
})

// 方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    ACTIVE: 'success',
    COMPLETED: 'info',
    PAUSED: 'warning',
    CANCELLED: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    ACTIVE: '进行中',
    COMPLETED: '已完成',
    PAUSED: '已暂停',
    CANCELLED: '已取消'
  }
  return texts[status] || '未知'
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    HIGH: 'danger',
    MEDIUM: 'warning',
    LOW: 'success'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    HIGH: '高优先级',
    MEDIUM: '中优先级',
    LOW: '低优先级'
  }
  return texts[priority] || '未知'
}

const getTaskStatusType = (status: string) => {
  const types: Record<string, string> = {
    PENDING: 'info',
    IN_PROGRESS: 'warning',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return types[status] || 'info'
}

const getTaskStatusText = (status: string) => {
  const texts: Record<string, string> = {
    PENDING: '待开始',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return texts[status] || '未知'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const goBack = () => {
  router.go(-1)
}

const editProject = () => {
  // 跳转到编辑页面或打开编辑对话框
  ElMessage.info('编辑功能开发中')
}

const deleteProject = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这个项目吗？', '确认删除', {
      type: 'warning'
    })
    
    // 这里应该调用 GraphQL mutation 删除项目
    ElMessage.success('项目删除成功')
    router.push('/projects')
  } catch {
    // 用户取消删除
  }
}

const addTask = () => {
  // 添加任务逻辑
  ElMessage.info('添加任务功能开发中')
}

const viewTask = (taskId: string) => {
  router.push(`/tasks/${taskId}`)
}

const editTask = (task: any) => {
  // 编辑任务逻辑
  ElMessage.info('编辑任务功能开发中')
}

const deleteTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      type: 'warning'
    })
    
    // 这里应该调用 GraphQL mutation 删除任务
    project.value.tasks = project.value.tasks?.filter(task => task.id !== taskId) || []
    ElMessage.success('任务删除成功')
  } catch {
    // 用户取消删除
  }
}

// 生命周期
onMounted(() => {
  // 根据路由参数加载项目数据
  const projectId = route.params.id
  // 这里应该调用 GraphQL 查询加载项目详情
  console.log('Loading project:', projectId)
})
</script>

<style scoped lang="scss">
.project-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    .title-section {
      display: flex;
      align-items: center;
      gap: 10px;

      h1 {
        margin: 0;
        color: #303133;
      }
    }
  }

  .header-right {
    display: flex;
    gap: 10px;
  }
}

.project-info-card,
.tasks-card {
  margin-bottom: 20px;

  h3 {
    color: #303133;
    margin: 0 0 15px 0;
    font-size: 16px;
  }
}

.project-description p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 10px;

  span {
    color: #606266;
    font-weight: 500;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tasks-list {
  .task-item {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .task-content {
      flex: 1;

      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        h4 {
          margin: 0;
          color: #303133;
          font-size: 14px;
        }
      }

      .task-description {
        color: #606266;
        margin: 0 0 10px 0;
        font-size: 12px;
      }

      .task-meta {
        display: flex;
        gap: 15px;
        font-size: 12px;
        color: #909399;

        span {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .task-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.empty-tasks {
  text-align: center;
  padding: 40px;
}

.stats-card,
.team-card,
.activity-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;

  .stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 12px;
      color: #909399;
    }
  }
}

.team-members {
  .member-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .member-info {
      .member-name {
        font-weight: 500;
        color: #303133;
      }

      .member-role {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.activity-list {
  .activity-item {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .activity-time {
      font-size: 12px;
      color: #909399;
      margin-bottom: 5px;
    }

    .activity-content {
      color: #606266;
      font-size: 14px;
    }
  }
}

.empty-team,
.empty-activity {
  text-align: center;
  padding: 20px;
  color: #909399;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .el-row {
    flex-direction: column;
  }

  .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    .task-actions {
      align-self: flex-end;
    }
  }
}
</style>
