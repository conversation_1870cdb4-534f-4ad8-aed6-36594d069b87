# DIP实践案例与模式

## 🎯 DIP核心理解

### 简单比喻：电器与插座

#### ❌ 违反DIP的"专用插头"
```
电视机 ──直接连接──> 专用电视插座
洗衣机 ──直接连接──> 专用洗衣机插座  
冰箱 ──直接连接──> 专用冰箱插座

问题：
- 每个电器只能用专用插座
- 换插座需要换电器
- 无法灵活搭配
```

#### ✅ 遵循DIP的"标准插头"
```
电视机 ──标准插头──> 标准接口 <──标准插座── 电源
洗衣机 ──标准插头──> 标准接口 <──标准插座── 电源
冰箱 ──标准插头──> 标准接口 <──标准插座── 电源

优点：
- 所有电器都能用任何插座
- 换插座不影响电器
- 灵活可配置
```

---

## 🏗️ 实战案例1：日志系统

### 需求背景
设计一个应用程序的日志系统，支持多种日志输出方式。

### ❌ 违反DIP的设计

```typescript
// 违反DIP：业务逻辑直接依赖具体日志实现
class FileLogger {
    log(message: string): void {
        console.log(`写入文件: ${message}`);
        // 实际的文件写入逻辑
    }
}

class DatabaseLogger {
    log(message: string): void {
        console.log(`写入数据库: ${message}`);
        // 实际的数据库写入逻辑
    }
}

// 业务服务直接依赖具体日志实现
class UserService {
    private logger: FileLogger; // ❌ 直接依赖具体实现
    
    constructor() {
        this.logger = new FileLogger(); // ❌ 硬编码依赖
    }
    
    createUser(userData: any): void {
        try {
            // 业务逻辑
            console.log('创建用户:', userData.name);
            
            // 记录日志
            this.logger.log(`用户创建成功: ${userData.name}`);
        } catch (error) {
            this.logger.log(`用户创建失败: ${error.message}`);
        }
    }
}

class OrderService {
    private logger: DatabaseLogger; // ❌ 直接依赖具体实现
    
    constructor() {
        this.logger = new DatabaseLogger(); // ❌ 硬编码依赖
    }
    
    processOrder(order: any): void {
        try {
            console.log('处理订单:', order.id);
            this.logger.log(`订单处理成功: ${order.id}`);
        } catch (error) {
            this.logger.log(`订单处理失败: ${error.message}`);
        }
    }
}

// 问题：
// 1. 每个服务都硬编码了日志实现
// 2. 要统一改成数据库日志，需要修改所有服务
// 3. 无法在运行时切换日志方式
// 4. 难以进行单元测试
```

### ✅ 遵循DIP的设计

```typescript
// 第一步：定义抽象接口
interface ILogger {
    log(level: LogLevel, message: string, metadata?: any): void;
    info(message: string, metadata?: any): void;
    warn(message: string, metadata?: any): void;
    error(message: string, metadata?: any): void;
    debug(message: string, metadata?: any): void;
}

enum LogLevel {
    DEBUG = 'debug',
    INFO = 'info',
    WARN = 'warn',
    ERROR = 'error'
}

// 第二步：具体实现依赖抽象
class FileLogger implements ILogger {
    constructor(private filePath: string) {}
    
    log(level: LogLevel, message: string, metadata?: any): void {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        console.log(`写入文件 ${this.filePath}: ${logEntry}`);
        
        if (metadata) {
            console.log(`元数据: ${JSON.stringify(metadata)}`);
        }
    }
    
    info(message: string, metadata?: any): void {
        this.log(LogLevel.INFO, message, metadata);
    }
    
    warn(message: string, metadata?: any): void {
        this.log(LogLevel.WARN, message, metadata);
    }
    
    error(message: string, metadata?: any): void {
        this.log(LogLevel.ERROR, message, metadata);
    }
    
    debug(message: string, metadata?: any): void {
        this.log(LogLevel.DEBUG, message, metadata);
    }
}

class DatabaseLogger implements ILogger {
    constructor(private connectionString: string) {}
    
    log(level: LogLevel, message: string, metadata?: any): void {
        const logEntry = {
            timestamp: new Date(),
            level,
            message,
            metadata
        };
        console.log(`写入数据库: ${JSON.stringify(logEntry)}`);
        // 实际的数据库写入逻辑
    }
    
    info(message: string, metadata?: any): void {
        this.log(LogLevel.INFO, message, metadata);
    }
    
    warn(message: string, metadata?: any): void {
        this.log(LogLevel.WARN, message, metadata);
    }
    
    error(message: string, metadata?: any): void {
        this.log(LogLevel.ERROR, message, metadata);
    }
    
    debug(message: string, metadata?: any): void {
        this.log(LogLevel.DEBUG, message, metadata);
    }
}

class ConsoleLogger implements ILogger {
    log(level: LogLevel, message: string, metadata?: any): void {
        const timestamp = new Date().toISOString();
        const colorCode = this.getColorCode(level);
        console.log(`${colorCode}[${timestamp}] [${level.toUpperCase()}] ${message}\x1b[0m`);
        
        if (metadata) {
            console.log(`元数据:`, metadata);
        }
    }
    
    private getColorCode(level: LogLevel): string {
        switch (level) {
            case LogLevel.ERROR: return '\x1b[31m'; // 红色
            case LogLevel.WARN: return '\x1b[33m';  // 黄色
            case LogLevel.INFO: return '\x1b[36m';  // 青色
            case LogLevel.DEBUG: return '\x1b[90m'; // 灰色
            default: return '';
        }
    }
    
    info(message: string, metadata?: any): void {
        this.log(LogLevel.INFO, message, metadata);
    }
    
    warn(message: string, metadata?: any): void {
        this.log(LogLevel.WARN, message, metadata);
    }
    
    error(message: string, metadata?: any): void {
        this.log(LogLevel.ERROR, message, metadata);
    }
    
    debug(message: string, metadata?: any): void {
        this.log(LogLevel.DEBUG, message, metadata);
    }
}

// 组合日志器：同时输出到多个目标
class CompositeLogger implements ILogger {
    constructor(private loggers: ILogger[]) {}
    
    log(level: LogLevel, message: string, metadata?: any): void {
        this.loggers.forEach(logger => logger.log(level, message, metadata));
    }
    
    info(message: string, metadata?: any): void {
        this.loggers.forEach(logger => logger.info(message, metadata));
    }
    
    warn(message: string, metadata?: any): void {
        this.loggers.forEach(logger => logger.warn(message, metadata));
    }
    
    error(message: string, metadata?: any): void {
        this.loggers.forEach(logger => logger.error(message, metadata));
    }
    
    debug(message: string, metadata?: any): void {
        this.loggers.forEach(logger => logger.debug(message, metadata));
    }
}

// 第三步：高层模块依赖抽象
class UserService {
    constructor(private logger: ILogger) {} // ✅ 依赖抽象接口
    
    createUser(userData: { name: string; email: string }): void {
        try {
            this.logger.info('开始创建用户', { userData });
            
            // 业务逻辑
            if (!userData.name || !userData.email) {
                throw new Error('用户数据不完整');
            }
            
            console.log('创建用户:', userData.name);
            
            this.logger.info('用户创建成功', { 
                userName: userData.name,
                userEmail: userData.email 
            });
            
        } catch (error) {
            this.logger.error('用户创建失败', { 
                error: error.message,
                userData 
            });
            throw error;
        }
    }
    
    deleteUser(userId: string): void {
        try {
            this.logger.info('开始删除用户', { userId });
            
            // 业务逻辑
            console.log('删除用户:', userId);
            
            this.logger.info('用户删除成功', { userId });
            
        } catch (error) {
            this.logger.error('用户删除失败', { 
                error: error.message,
                userId 
            });
            throw error;
        }
    }
}

class OrderService {
    constructor(private logger: ILogger) {} // ✅ 依赖抽象接口
    
    processOrder(order: { id: string; amount: number }): void {
        try {
            this.logger.info('开始处理订单', { orderId: order.id });
            
            // 业务逻辑
            if (order.amount <= 0) {
                throw new Error('订单金额无效');
            }
            
            console.log('处理订单:', order.id);
            
            this.logger.info('订单处理成功', { 
                orderId: order.id,
                amount: order.amount 
            });
            
        } catch (error) {
            this.logger.error('订单处理失败', { 
                error: error.message,
                order 
            });
            throw error;
        }
    }
}

// 第四步：依赖注入和配置
class LoggerFactory {
    static createLogger(config: LoggerConfig): ILogger {
        switch (config.type) {
            case 'file':
                return new FileLogger(config.filePath || 'app.log');
                
            case 'database':
                return new DatabaseLogger(config.connectionString || 'default');
                
            case 'console':
                return new ConsoleLogger();
                
            case 'composite':
                const loggers = config.loggers?.map(loggerConfig => 
                    this.createLogger(loggerConfig)
                ) || [];
                return new CompositeLogger(loggers);
                
            default:
                throw new Error(`不支持的日志类型: ${config.type}`);
        }
    }
}

interface LoggerConfig {
    type: 'file' | 'database' | 'console' | 'composite';
    filePath?: string;
    connectionString?: string;
    loggers?: LoggerConfig[];
}

// 应用程序配置
class Application {
    private userService: UserService;
    private orderService: OrderService;
    
    constructor(loggerConfig: LoggerConfig) {
        // 创建日志器
        const logger = LoggerFactory.createLogger(loggerConfig);
        
        // 注入依赖
        this.userService = new UserService(logger);
        this.orderService = new OrderService(logger);
    }
    
    run(): void {
        // 测试用户服务
        try {
            this.userService.createUser({ name: '张三', email: '<EMAIL>' });
            this.userService.deleteUser('user_123');
        } catch (error) {
            console.log('用户操作出错');
        }
        
        // 测试订单服务
        try {
            this.orderService.processOrder({ id: 'order_456', amount: 100 });
        } catch (error) {
            console.log('订单操作出错');
        }
    }
}

// 使用示例
function main(): void {
    console.log('=== 使用控制台日志 ===');
    const consoleApp = new Application({
        type: 'console'
    });
    consoleApp.run();
    
    console.log('\n=== 使用文件日志 ===');
    const fileApp = new Application({
        type: 'file',
        filePath: 'application.log'
    });
    fileApp.run();
    
    console.log('\n=== 使用组合日志（控制台+文件+数据库）===');
    const compositeApp = new Application({
        type: 'composite',
        loggers: [
            { type: 'console' },
            { type: 'file', filePath: 'app.log' },
            { type: 'database', connectionString: 'mongodb://localhost:27017/logs' }
        ]
    });
    compositeApp.run();
}

main();
```

---

## 🔧 DIP的实现模式

### 1. 依赖注入模式

#### 构造函数注入（推荐）
```typescript
class EmailService {
    constructor(
        private emailProvider: IEmailProvider,
        private templateEngine: ITemplateEngine,
        private logger: ILogger
    ) {}
    
    sendEmail(to: string, subject: string, template: string, data: any): void {
        try {
            const content = this.templateEngine.render(template, data);
            this.emailProvider.send(to, subject, content);
            this.logger.info('邮件发送成功', { to, subject });
        } catch (error) {
            this.logger.error('邮件发送失败', { error: error.message, to, subject });
            throw error;
        }
    }
}
```

#### 属性注入
```typescript
class EmailService {
    public emailProvider: IEmailProvider;
    public templateEngine: ITemplateEngine;
    public logger: ILogger;
    
    sendEmail(to: string, subject: string, template: string, data: any): void {
        if (!this.emailProvider || !this.templateEngine || !this.logger) {
            throw new Error('依赖未注入');
        }
        
        // 使用注入的依赖
    }
}
```

#### 方法注入
```typescript
class EmailService {
    sendEmail(
        to: string,
        subject: string,
        template: string,
        data: any,
        emailProvider: IEmailProvider,
        templateEngine: ITemplateEngine,
        logger: ILogger
    ): void {
        try {
            const content = templateEngine.render(template, data);
            emailProvider.send(to, subject, content);
            logger.info('邮件发送成功', { to, subject });
        } catch (error) {
            logger.error('邮件发送失败', { error: error.message, to, subject });
            throw error;
        }
    }
}
```

### 2. 工厂模式

```typescript
interface IServiceFactory {
    createEmailProvider(): IEmailProvider;
    createTemplateEngine(): ITemplateEngine;
    createLogger(): ILogger;
}

class ProductionServiceFactory implements IServiceFactory {
    createEmailProvider(): IEmailProvider {
        return new SMTPEmailProvider('smtp.gmail.com', 587);
    }
    
    createTemplateEngine(): ITemplateEngine {
        return new HandlebarsTemplateEngine();
    }
    
    createLogger(): ILogger {
        return new CompositeLogger([
            new FileLogger('production.log'),
            new DatabaseLogger('mongodb://prod-db:27017/logs')
        ]);
    }
}

class TestServiceFactory implements IServiceFactory {
    createEmailProvider(): IEmailProvider {
        return new MockEmailProvider();
    }
    
    createTemplateEngine(): ITemplateEngine {
        return new MockTemplateEngine();
    }
    
    createLogger(): ILogger {
        return new ConsoleLogger();
    }
}

class EmailService {
    private emailProvider: IEmailProvider;
    private templateEngine: ITemplateEngine;
    private logger: ILogger;
    
    constructor(factory: IServiceFactory) {
        this.emailProvider = factory.createEmailProvider();
        this.templateEngine = factory.createTemplateEngine();
        this.logger = factory.createLogger();
    }
}
```

### 3. 服务定位器模式

```typescript
class ServiceLocator {
    private static services: Map<string, any> = new Map();
    
    static register<T>(name: string, service: T): void {
        this.services.set(name, service);
    }
    
    static resolve<T>(name: string): T {
        const service = this.services.get(name);
        if (!service) {
            throw new Error(`服务未注册: ${name}`);
        }
        return service;
    }
    
    static isRegistered(name: string): boolean {
        return this.services.has(name);
    }
}

class EmailService {
    private emailProvider: IEmailProvider;
    private templateEngine: ITemplateEngine;
    private logger: ILogger;
    
    constructor() {
        this.emailProvider = ServiceLocator.resolve<IEmailProvider>('emailProvider');
        this.templateEngine = ServiceLocator.resolve<ITemplateEngine>('templateEngine');
        this.logger = ServiceLocator.resolve<ILogger>('logger');
    }
}

// 配置服务
ServiceLocator.register('emailProvider', new SMTPEmailProvider('smtp.gmail.com', 587));
ServiceLocator.register('templateEngine', new HandlebarsTemplateEngine());
ServiceLocator.register('logger', new FileLogger('app.log'));
```

### 4. IoC容器模式

```typescript
class DIContainer {
    private services: Map<string, ServiceDefinition> = new Map();
    private instances: Map<string, any> = new Map();
    
    register<T>(
        name: string,
        factory: () => T,
        lifetime: 'singleton' | 'transient' = 'singleton'
    ): void {
        this.services.set(name, { factory, lifetime });
    }
    
    resolve<T>(name: string): T {
        const definition = this.services.get(name);
        if (!definition) {
            throw new Error(`服务未注册: ${name}`);
        }
        
        if (definition.lifetime === 'singleton') {
            if (!this.instances.has(name)) {
                this.instances.set(name, definition.factory());
            }
            return this.instances.get(name);
        } else {
            return definition.factory();
        }
    }
}

interface ServiceDefinition {
    factory: () => any;
    lifetime: 'singleton' | 'transient';
}

// 配置容器
const container = new DIContainer();

container.register('logger', () => new FileLogger('app.log'), 'singleton');
container.register('emailProvider', () => new SMTPEmailProvider('smtp.gmail.com', 587), 'singleton');
container.register('templateEngine', () => new HandlebarsTemplateEngine(), 'singleton');

container.register('emailService', () => new EmailService(
    container.resolve('emailProvider'),
    container.resolve('templateEngine'),
    container.resolve('logger')
), 'transient');

// 使用
const emailService = container.resolve<EmailService>('emailService');
```

---

## 🧪 单元测试的优势

### 使用Mock对象进行测试

```typescript
describe('EmailService', () => {
    let emailService: EmailService;
    let mockEmailProvider: jest.Mocked<IEmailProvider>;
    let mockTemplateEngine: jest.Mocked<ITemplateEngine>;
    let mockLogger: jest.Mocked<ILogger>;
    
    beforeEach(() => {
        // 创建mock对象
        mockEmailProvider = {
            send: jest.fn()
        };
        
        mockTemplateEngine = {
            render: jest.fn()
        };
        
        mockLogger = {
            log: jest.fn(),
            info: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
            debug: jest.fn()
        };
        
        // 注入mock依赖
        emailService = new EmailService(
            mockEmailProvider,
            mockTemplateEngine,
            mockLogger
        );
    });
    
    it('should send email successfully', () => {
        // 设置mock行为
        mockTemplateEngine.render.mockReturnValue('<h1>Hello World</h1>');
        mockEmailProvider.send.mockResolvedValue(undefined);
        
        // 执行测试
        emailService.sendEmail(
            '<EMAIL>',
            'Test Subject',
            'welcome-template',
            { name: 'John' }
        );
        
        // 验证调用
        expect(mockTemplateEngine.render).toHaveBeenCalledWith('welcome-template', { name: 'John' });
        expect(mockEmailProvider.send).toHaveBeenCalledWith(
            '<EMAIL>',
            'Test Subject',
            '<h1>Hello World</h1>'
        );
        expect(mockLogger.info).toHaveBeenCalledWith(
            '邮件发送成功',
            { to: '<EMAIL>', subject: 'Test Subject' }
        );
    });
    
    it('should handle email sending failure', () => {
        // 设置mock行为
        mockTemplateEngine.render.mockReturnValue('<h1>Hello World</h1>');
        mockEmailProvider.send.mockRejectedValue(new Error('SMTP连接失败'));
        
        // 执行测试并验证异常
        expect(() => {
            emailService.sendEmail(
                '<EMAIL>',
                'Test Subject',
                'welcome-template',
                { name: 'John' }
            );
        }).toThrow('SMTP连接失败');
        
        // 验证错误日志
        expect(mockLogger.error).toHaveBeenCalledWith(
            '邮件发送失败',
            expect.objectContaining({
                error: 'SMTP连接失败',
                to: '<EMAIL>',
                subject: 'Test Subject'
            })
        );
    });
});
```

---

## 🎯 DIP最佳实践

### 1. 接口设计原则

#### 保持接口简洁
```typescript
// ✅ 好的接口设计
interface IEmailProvider {
    send(to: string, subject: string, content: string): Promise<void>;
}

// ❌ 过于复杂的接口
interface IEmailProvider {
    send(to: string, subject: string, content: string): Promise<void>;
    sendBulk(emails: Email[]): Promise<void>;
    validateEmail(email: string): boolean;
    getDeliveryStatus(messageId: string): DeliveryStatus;
    configureServer(config: ServerConfig): void;
}
```

#### 按职责分离接口
```typescript
// ✅ 按职责分离
interface IEmailSender {
    send(to: string, subject: string, content: string): Promise<void>;
}

interface IEmailValidator {
    validate(email: string): boolean;
}

interface IEmailTracker {
    getDeliveryStatus(messageId: string): DeliveryStatus;
}
```

### 2. 依赖管理

#### 避免循环依赖
```typescript
// ❌ 循环依赖
class UserService {
    constructor(private orderService: OrderService) {}
}

class OrderService {
    constructor(private userService: UserService) {} // 循环依赖
}

// ✅ 通过接口解决
interface IUserService {
    getUser(id: string): User;
}

interface IOrderService {
    getOrdersByUser(userId: string): Order[];
}

class UserService implements IUserService {
    constructor(private orderService: IOrderService) {}
    
    getUser(id: string): User {
        // 实现
    }
}

class OrderService implements IOrderService {
    constructor(private userService: IUserService) {}
    
    getOrdersByUser(userId: string): Order[] {
        // 实现
    }
}
```

### 3. 配置管理

```typescript
// 环境配置
interface AppConfig {
    database: {
        type: 'mysql' | 'postgresql' | 'mongodb';
        connectionString: string;
    };
    email: {
        provider: 'smtp' | 'sendgrid' | 'ses';
        config: any;
    };
    logging: {
        level: 'debug' | 'info' | 'warn' | 'error';
        outputs: ('console' | 'file' | 'database')[];
    };
}

class ApplicationBootstrap {
    static bootstrap(config: AppConfig): Application {
        // 根据配置创建依赖
        const database = DatabaseFactory.create(config.database);
        const emailProvider = EmailProviderFactory.create(config.email);
        const logger = LoggerFactory.create(config.logging);
        
        // 创建应用
        return new Application(database, emailProvider, logger);
    }
}
```

---

## 📚 总结

### DIP的核心价值

1. **灵活性**: 可以轻松替换依赖的实现
2. **可测试性**: 可以使用mock对象进行单元测试
3. **可维护性**: 高层模块不依赖低层模块的变化
4. **可扩展性**: 添加新实现不需要修改现有代码

### 实现要点

1. **定义清晰的抽象接口**
2. **通过依赖注入管理依赖关系**
3. **使用工厂模式或IoC容器**
4. **保持接口简洁和专一**

### 记忆口诀

**"高低不直连，抽象做桥梁，注入解耦合，测试更轻松"**

DIP是实现松耦合架构的关键，它让我们的代码更加灵活、可测试和可维护！
