# 上下文注入使用指南

## 📖 概述

本文档展示如何在 `executionDidStart` 钩子中注入上下文数据，以及如何在 resolvers 中使用这些数据来提升性能和功能。

## 🎯 核心概念

### 为什么在 executionDidStart 中注入数据？

1. **时机最佳**: 查询已解析和验证完成，即将执行 resolvers
2. **作用域正确**: 数据在整个请求生命周期中可用
3. **性能优化**: 可以初始化 DataLoader 等性能优化工具
4. **资源管理**: 可以在 executionDidEnd 中清理资源

## 🛠️ 实际应用示例

### 1. DataLoader 批量加载

**问题**: N+1 查询问题
```graphql
query {
  users {
    id
    name
    tasks {  # 每个用户都会触发一次数据库查询
      title
    }
  }
}
```

**解决方案**: 使用 DataLoader
```typescript
// 在 executionDidStart 中初始化
requestContext.context.loaders = {
  userTasksLoader: new DataLoader(async (userIds) => {
    // 一次查询获取所有用户的任务
    console.log(`📊 Batch loading tasks for ${userIds.length} users`);
    return userIds.map(userId => 
      db.tasks.filter(task => task.assigneeId === userId)
    );
  })
};

// 在 resolver 中使用
User: {
  tasks: async (parent, args, context) => {
    // 批量加载，避免 N+1 问题
    return await context.loaders.userTasksLoader.load(parent.id);
  }
}
```

### 2. 请求级别缓存

**场景**: 同一请求中多次访问相同数据
```typescript
// 在 executionDidStart 中初始化缓存
requestContext.context.cache = new Map();

// 在 resolver 中使用缓存
user: async (parent, { id }, context) => {
  const cacheKey = `user_${id}`;
  
  // 检查缓存
  if (context.cache.has(cacheKey)) {
    context.stats.cacheHits++;
    return context.cache.get(cacheKey);
  }
  
  // 加载数据并缓存
  const user = await context.loaders.userLoader.load(id);
  context.cache.set(cacheKey, user);
  context.stats.cacheMisses++;
  
  return user;
}
```

### 3. 权限检查

**场景**: 基于用户角色的权限控制
```typescript
// 在 executionDidStart 中注入权限
if (requestContext.context.user) {
  const userRole = requestContext.context.user.role;
  requestContext.context.permissions = getPermissionsByRole(userRole);
}

// 在 resolver 中检查权限
tasks: async (parent, args, context) => {
  if (!context.permissions?.includes('read:tasks')) {
    throw new Error('Insufficient permissions');
  }
  
  return await loadTasks(context);
}
```

### 4. 请求追踪

**场景**: 分布式系统中的请求追踪
```typescript
// 在 executionDidStart 中生成追踪ID
requestContext.context.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 在 resolver 中使用追踪ID
console.log(`[${context.requestId}] Loading user: ${id}`);

// 在错误处理中使用
didEncounterErrors(requestContext) {
  const { requestId } = requestContext.context;
  console.error(`❌ [${requestId}] Error occurred:`, error);
}
```

### 5. 性能统计

**场景**: 监控 resolver 性能和调用次数
```typescript
// 在 executionDidStart 中初始化统计
requestContext.context.stats = {
  resolverCalls: 0,
  dbQueries: 0,
  cacheHits: 0,
  cacheMisses: 0
};

// 在 resolver 中更新统计
user: async (parent, args, context) => {
  context.stats.resolverCalls++;
  // ... resolver 逻辑
}

// 在 executionDidEnd 中输出统计
executionDidEnd() {
  console.log(`📊 Request stats:`, requestContext.context.stats);
}
```

## 🚀 在你的项目中使用

### 1. 安装依赖

```bash
npm install dataloader
npm install --save-dev @types/dataloader
```

### 2. 导入插件

```typescript
// server.ts
import { contextInjectionPlugin } from './plugins/contextInjectionPlugin';

const server = new ApolloServer({
  schema,
  context: createContext,
  plugins: [
    contextInjectionPlugin,  // 添加上下文注入插件
    // ... 其他插件
  ]
});
```

### 3. 在 Resolvers 中使用

```typescript
// resolvers.ts
export const resolvers = {
  Query: {
    user: async (parent, { id }, context) => {
      // 🎯 使用 DataLoader
      return await context.loaders.userLoader.load(id);
    },
    
    tasks: async (parent, args, context) => {
      // 🎯 检查权限
      if (!context.permissions?.includes('read:tasks')) {
        throw new Error('Insufficient permissions');
      }
      
      // 🎯 使用缓存
      const cacheKey = `tasks_${context.user?.id}`;
      if (context.cache.has(cacheKey)) {
        return context.cache.get(cacheKey);
      }
      
      // 🎯 加载数据
      const tasks = await context.loaders.userTasksLoader.load(context.user.id);
      context.cache.set(cacheKey, tasks);
      
      return tasks;
    }
  },
  
  User: {
    tasks: async (parent, args, context) => {
      // 🎯 批量加载，解决 N+1 问题
      return await context.loaders.userTasksLoader.load(parent.id);
    }
  }
};
```

## 📊 性能对比

### 没有 DataLoader (N+1 问题)
```
查询 10 个用户的任务:
- 1 次查询获取用户列表
- 10 次查询获取每个用户的任务
- 总计: 11 次数据库查询
```

### 使用 DataLoader
```
查询 10 个用户的任务:
- 1 次查询获取用户列表  
- 1 次批量查询获取所有用户的任务
- 总计: 2 次数据库查询
```

## 🎯 最佳实践

### 1. 资源清理
```typescript
executionDidEnd() {
  // 清理 DataLoader 缓存
  Object.values(context.loaders).forEach(loader => {
    if (loader.clearAll) loader.clearAll();
  });
  
  // 清理请求缓存
  context.cache.clear();
}
```

### 2. 错误处理
```typescript
userLoader: new DataLoader(async (userIds) => {
  try {
    const users = await db.users.findMany({ where: { id: { in: userIds } } });
    return userIds.map(id => users.find(user => user.id === id) || null);
  } catch (error) {
    console.error('DataLoader error:', error);
    return userIds.map(() => null);
  }
})
```

### 3. 缓存策略
```typescript
// 短期缓存 - 请求级别
context.cache.set(key, value);

// 长期缓存 - 应用级别
// 使用 Redis 或内存缓存
```

### 4. 权限粒度
```typescript
// 粗粒度权限
if (!context.permissions?.includes('read:users')) {
  throw new Error('Access denied');
}

// 细粒度权限
if (!canAccessUser(context.user, targetUserId)) {
  throw new Error('Cannot access this user');
}
```

## 🔗 相关资源

- [DataLoader 官方文档](https://github.com/graphql/dataloader)
- [GraphQL N+1 问题解决方案](https://www.apollographql.com/blog/graphql/explaining-graphql-connections/)
- [Apollo Server 插件指南](./apollo-server-plugins-guide.md)
