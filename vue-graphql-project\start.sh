#!/bin/bash

echo "========================================"
echo "Vue GraphQL Project 启动脚本"
echo "========================================"
echo

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

# 检查 npm
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到 npm，请检查 Node.js 安装"
    exit 1
fi

echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"
echo

echo "正在安装依赖..."
npm run install:all
if [ $? -ne 0 ]; then
    echo "错误: 依赖安装失败"
    exit 1
fi

echo
echo "启动开发服务器..."
echo "服务端: http://localhost:4000/graphql"
echo "客户端: http://localhost:3000"
echo
echo "按 Ctrl+C 停止服务器"
echo

npm run dev
