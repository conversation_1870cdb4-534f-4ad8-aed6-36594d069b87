# DPR + 响应式 Rem 解决方案

## 📋 方案概述

本项目提供了多种基于 DPR (Device Pixel Ratio) 的响应式 rem 计算方案，完美解决 PC 和移动端兼容性问题，支持高清屏适配和 1px 边框等常见需求。

## 📁 文件说明

### JavaScript 方案

1. **`dpr-responsive-rem-complete.js`** - 完整版方案
   - 功能最全面，支持所有特性
   - 适合复杂项目和精确控制需求
   - 支持多种 DPR 策略

2. **`dpr-responsive-rem-simple.js`** - 简化版方案
   - 轻量级实现，性能优先
   - 适合简单项目和快速开发
   - 基础 DPR 支持

3. **`dpr-responsive-rem-react.js`** - React Hook 方案
   - React 项目专用
   - Hook 风格，TypeScript 友好
   - CSS-in-JS 支持

4. **`dpr-responsive-rem-vue.js`** - Vue 3 组合式 API 方案
   - Vue 3 项目专用
   - 组合式 API，响应式数据
   - 插件形式安装

### CSS 样式

5. **`dpr-responsive-rem.css`** - 配套 CSS 样式
   - DPR 相关样式处理
   - 1px 边框解决方案
   - 响应式组件和工具类

## 🚀 快速开始

### 方案一：完整版 JavaScript 方案

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>DPR 响应式方案</title>
    <link rel="stylesheet" href="dpr-responsive-rem.css">
</head>
<body>
    <div class="container">
        <h1 class="text-lg">响应式标题</h1>
        <div class="card">
            <div class="card-body">
                <p class="text-base">这是一个响应式卡片</p>
            </div>
        </div>
    </div>
    
    <script src="dpr-responsive-rem-complete.js"></script>
    <script>
        // 使用全局工具函数
        console.log('当前 DPR:', window.dprRemCalculator.dpr);
        console.log('设备类型:', window.dprRemCalculator.getDeviceType(window.innerWidth));
        
        // px 转 rem
        const remValue = window.px2rem(100);
        console.log('100px =', remValue, 'rem');
    </script>
</body>
</html>
```

### 方案二：React 项目使用

```jsx
// App.jsx
import React from 'react';
import { useResponsiveRem, useDPR } from './dpr-responsive-rem-react';
import './dpr-responsive-rem.css';

function App() {
    const { deviceType, px2rem, styles } = useResponsiveRem({
        mobileDesignWidth: 750,
        enableDPR: true
    });
    
    const dpr = useDPR();
    
    return (
        <div style={styles.container}>
            <h1 style={styles.text}>
                设备类型: {deviceType}, DPR: {dpr}
            </h1>
            <button style={styles.button}>
                响应式按钮
            </button>
            <div style={styles.hairlineBorder}>
                1px 边框测试
            </div>
        </div>
    );
}

export default App;
```

### 方案三：Vue 3 项目使用

```javascript
// main.js
import { createApp } from 'vue';
import { DPRResponsiveRemPlugin } from './dpr-responsive-rem-vue';
import App from './App.vue';
import './dpr-responsive-rem.css';

const app = createApp(App);
app.use(DPRResponsiveRemPlugin, {
    mobileDesignWidth: 750,
    enableDPR: true
});
app.mount('#app');
```

```vue
<!-- App.vue -->
<template>
  <div :style="styles.container">
    <h1 :style="styles.text">
      设备类型: {{ deviceType }}, DPR: {{ dpr }}
    </h1>
    <button :style="styles.button">
      响应式按钮
    </button>
    <div :style="styles.hairlineBorder">
      1px 边框测试
    </div>
  </div>
</template>

<script>
import { useResponsiveRem, useDPR } from './dpr-responsive-rem-vue';

export default {
  setup() {
    const { deviceType, styles } = useResponsiveRem();
    const { dpr } = useDPR();
    
    return {
      deviceType,
      dpr,
      styles
    };
  }
};
</script>
```

## ⚙️ 配置选项

### 基础配置

```javascript
const config = {
    // 设计稿宽度
    mobileDesignWidth: 750,    // 移动端设计稿宽度
    pcDesignWidth: 1920,       // PC端设计稿宽度
    
    // 基准字体大小
    mobileBaseSize: 75,        // 移动端基准字体
    pcBaseSize: 16,            // PC端基准字体
    
    // 断点设置
    breakpoints: {
        mobile: 768,           // 移动端最大宽度
        tablet: 1024,          // 平板最大宽度
        desktop: 1920          // 桌面端最大宽度
    },
    
    // DPR 相关
    enableDPR: true,           // 是否启用 DPR
    maxDPR: 3,                 // 最大 DPR 支持
    dprStrategy: 'viewport',   // DPR 策略: 'scale' | 'viewport' | 'both'
    
    // 限制
    maxWidth: 2560,            // 最大宽度
    minWidth: 320              // 最小宽度
};
```

### DPR 策略说明

1. **`viewport`** (推荐)
   - 通过设置 viewport 缩放实现高清适配
   - 完美解决 1px 边框问题
   - 性能较好

2. **`scale`**
   - 通过字体大小缩放实现适配
   - 兼容性更好
   - 某些情况下可能有精度问题

3. **`both`**
   - 综合使用 viewport 和字体缩放
   - 效果最好但复杂度较高
   - 适合对视觉效果要求极高的项目

## 🎯 使用场景

### 适用项目类型

1. **移动端为主的 H5 项目**
   - 微信公众号、小程序 H5
   - 移动端官网、活动页面
   - 混合 App 内嵌页面

2. **响应式网站**
   - 需要同时支持 PC 和移动端
   - 对视觉效果要求较高
   - 需要完美的 1px 边框

3. **高清屏适配要求高的项目**
   - 设计稿精度要求高
   - 需要支持 Retina 屏幕
   - 图标和图片需要高清显示

### 设备支持

- **移动端**: iOS 8+, Android 4.4+
- **桌面端**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **DPR 支持**: 1x, 2x, 3x 屏幕

## 🔧 高级用法

### 自定义样式

```css
/* 使用 CSS 变量 */
.my-component {
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    border: var(--hairline-width) solid #ddd;
    border-radius: var(--border-radius-medium);
}

/* 设备特定样式 */
.device-mobile .my-component {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

.device-desktop .my-component {
    background: #f8f9fa;
}

/* DPR 特定样式 */
[data-dpr="3"] .high-quality-image {
    background-image: url('<EMAIL>');
}
```

### JavaScript API

```javascript
// 获取当前信息
const info = window.dprRemCalculator.getInfo();
console.log(info);
// {
//   dpr: 2,
//   deviceType: 'mobile',
//   remBase: 50,
//   deviceWidth: 375,
//   config: {...}
// }

// px 转 rem
const remValue = window.px2rem(100, 'mobile');

// 获取实际像素值
const actualPx = window.getActualPx(100);

// 监听设备变化
window.addEventListener('resize', () => {
    console.log('设备信息已更新');
});
```

## 📊 性能优化

### 最佳实践

1. **防抖处理**: 所有 resize 事件都使用防抖处理
2. **缓存计算**: 重复计算结果会被缓存
3. **按需加载**: 只在需要时设置 viewport
4. **事件清理**: 组件卸载时自动清理事件监听

### 性能对比

| 方案 | 包大小 | 初始化时间 | 内存占用 | 兼容性 |
|------|--------|------------|----------|--------|
| 完整版 | ~8KB | ~2ms | 低 | 优秀 |
| 简化版 | ~3KB | ~1ms | 极低 | 优秀 |
| React版 | ~5KB | ~1ms | 低 | 现代浏览器 |
| Vue版 | ~6KB | ~1ms | 低 | 现代浏览器 |

## 🐛 常见问题

### Q: 为什么在某些 Android 设备上效果不理想？

A: 某些 Android 设备的 DPR 值可能不准确，建议在配置中限制最大 DPR 值：

```javascript
const config = {
    maxDPR: 2, // 限制最大 DPR 为 2
    // ...其他配置
};
```

### Q: 如何处理第三方组件的样式冲突？

A: 使用 CSS 作用域或者在第三方组件外层添加重置样式：

```css
.third-party-wrapper {
    font-size: 16px !important;
}

.third-party-wrapper * {
    box-sizing: content-box !important;
}
```

### Q: 在 iframe 中使用时有什么注意事项？

A: iframe 中需要重新初始化，并且要注意 viewport 设置可能会影响父页面：

```javascript
// 在 iframe 中禁用 viewport 设置
const config = {
    enableDPR: false, // 禁用 DPR
    // ...其他配置
};
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的 DPR 适配
- 提供完整版和简化版方案

### v1.1.0
- 新增 React Hook 支持
- 新增 Vue 3 组合式 API 支持
- 优化性能和兼容性

### v1.2.0
- 新增配套 CSS 样式文件
- 完善 1px 边框解决方案
- 新增响应式组件库

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

选择适合你项目的方案，开始享受完美的响应式开发体验吧！ 🚀
