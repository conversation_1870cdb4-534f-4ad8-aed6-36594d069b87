/**
 * GraphQL Playground 模拟数据
 */

// 用户数据
export const users = [
    {
        id: "1",
        name: "张三",
        email: "z<PERSON><PERSON>@example.com",
        role: "admin",
        status: "active",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=zhang",
        createdAt: "2023-10-01T08:00:00Z",
        posts: [
            {
                id: "1",
                title: "GraphQL入门指南",
                content: "GraphQL是一种强大的查询语言，它提供了一种更高效、强大和灵活的替代REST的方案...",
                status: "published",
                createdAt: "2023-10-21T08:00:00Z",
                tags: ["GraphQL", "API", "前端"]
            },
            {
                id: "2",
                title: "现代前端开发实践",
                content: "探讨现代前端开发的最佳实践和工具链...",
                status: "draft",
                createdAt: "2023-10-20T15:30:00Z",
                tags: ["前端", "开发", "实践"]
            }
        ]
    },
    {
        id: "2",
        name: "李四",
        email: "<EMAIL>",
        role: "user",
        status: "active",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=li",
        createdAt: "2023-10-02T09:15:00Z",
        posts: [
            {
                id: "3",
                title: "Vue.js 3.0 新特性详解",
                content: "Vue.js 3.0带来了许多令人兴奋的新特性，包括Composition API、更好的TypeScript支持...",
                status: "published",
                createdAt: "2023-10-19T10:20:00Z",
                tags: ["Vue.js", "前端", "JavaScript"]
            }
        ]
    },
    {
        id: "3",
        name: "王五",
        email: "<EMAIL>",
        role: "moderator",
        status: "active",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=wang",
        createdAt: "2023-10-03T10:30:00Z",
        posts: []
    }
];

// 评论数据
export const comments = [
    {
        id: "1",
        content: "很好的文章！对GraphQL的介绍很详细。",
        authorId: "2",
        postId: "1",
        createdAt: "2023-10-21T09:00:00Z"
    },
    {
        id: "2",
        content: "感谢分享，学到了很多。",
        authorId: "3",
        postId: "1",
        createdAt: "2023-10-21T10:15:00Z"
    },
    {
        id: "3",
        content: "Vue 3的Composition API确实很强大！",
        authorId: "1",
        postId: "3",
        createdAt: "2023-10-19T11:30:00Z"
    }
];

// GraphQL Schema
export const graphqlSchema = `type User {
  id: ID!
  name: String!
  email: String!
  role: UserRole!
  status: UserStatus!
  avatar: String
  posts: [Post!]!
  createdAt: DateTime!
}

type Post {
  id: ID!
  title: String!
  content: String!
  author: User!
  status: PostStatus!
  tags: [String!]!
  comments: [Comment!]!
  createdAt: DateTime!
}

type Comment {
  id: ID!
  content: String!
  author: User!
  post: Post!
  createdAt: DateTime!
}

enum UserRole {
  ADMIN
  USER
  MODERATOR
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

type Query {
  users(limit: Int, role: UserRole): [User!]!
  user(id: ID!): User
  posts(limit: Int, status: PostStatus): [Post!]!
  post(id: ID!): Post
  comments(postId: ID): [Comment!]!
  search(query: String!): [SearchResult!]!
}

type Mutation {
  createUser(input: CreateUserInput!): User!
  updateUser(id: ID!, input: UpdateUserInput!): User!
  deleteUser(id: ID!): Boolean!
  createPost(input: CreatePostInput!): Post!
  updatePost(id: ID!, input: UpdatePostInput!): Post!
  deletePost(id: ID!): Boolean!
  createComment(input: CreateCommentInput!): Comment!
}

type Subscription {
  userCreated: User!
  postCreated: Post!
  commentAdded(postId: ID!): Comment!
}

union SearchResult = User | Post | Comment

input CreateUserInput {
  name: String!
  email: String!
  role: UserRole = USER
}

input UpdateUserInput {
  name: String
  email: String
  role: UserRole
  status: UserStatus
}

input CreatePostInput {
  title: String!
  content: String!
  tags: [String!]
  status: PostStatus = DRAFT
}

input UpdatePostInput {
  title: String
  content: String
  tags: [String!]
  status: PostStatus
}

input CreateCommentInput {
  content: String!
  postId: ID!
}

scalar DateTime`;

// 示例查询
export const exampleQueries = [
    {
        id: 1,
        title: "获取所有用户",
        description: "获取系统中的所有用户基本信息",
        query: `query GetUsers {
  users {
    id
    name
    email
    role
    status
  }
}`,
        variables: "{}"
    },
    {
        id: 2,
        title: "获取用户及其文章",
        description: "获取指定用户的详细信息，包括其发布的文章",
        query: `query GetUserWithPosts($userId: ID!) {
  user(id: $userId) {
    id
    name
    email
    posts {
      id
      title
      status
      createdAt
      tags
    }
  }
}`,
        variables: `{
  "userId": "1"
}`
    },
    {
        id: 3,
        title: "获取文章及评论",
        description: "获取文章详情，包括作者信息和所有评论",
        query: `query GetPostWithComments($postId: ID!) {
  post(id: $postId) {
    id
    title
    content
    author {
      name
      avatar
    }
    comments {
      id
      content
      author {
        name
      }
      createdAt
    }
  }
}`,
        variables: `{
  "postId": "1"
}`
    },
    {
        id: 4,
        title: "创建新用户",
        description: "使用Mutation创建一个新的用户账户",
        query: `mutation CreateUser($input: CreateUserInput!) {
  createUser(input: $input) {
    id
    name
    email
    role
    createdAt
  }
}`,
        variables: `{
  "input": {
    "name": "新用户",
    "email": "<EMAIL>",
    "role": "USER"
  }
}`
    },
    {
        id: 5,
        title: "搜索内容",
        description: "使用Union类型搜索用户、文章和评论",
        query: `query Search($query: String!) {
  search(query: $query) {
    ... on User {
      __typename
      id
      name
      email
    }
    ... on Post {
      __typename
      id
      title
      author {
        name
      }
    }
    ... on Comment {
      __typename
      id
      content
      author {
        name
      }
    }
  }
}`,
        variables: `{
  "query": "GraphQL"
}`
    },
    {
        id: 6,
        title: "订阅新评论",
        description: "使用Subscription实时监听新评论",
        query: `subscription OnCommentAdded($postId: ID!) {
  commentAdded(postId: $postId) {
    id
    content
    author {
      name
      avatar
    }
    createdAt
  }
}`,
        variables: `{
  "postId": "1"
}`
    }
];

// GraphQL操作类型
export const operationTypes = {
    query: {
        name: "Query",
        description: "查询操作，用于获取数据",
        color: "#28a745",
        icon: "🔍"
    },
    mutation: {
        name: "Mutation", 
        description: "变更操作，用于修改数据",
        color: "#ffc107",
        icon: "✏️"
    },
    subscription: {
        name: "Subscription",
        description: "订阅操作，用于实时数据推送",
        color: "#17a2b8",
        icon: "📡"
    }
};

// 错误类型
export const errorTypes = {
    VALIDATION_ERROR: "数据验证错误",
    NOT_FOUND: "资源不存在",
    PERMISSION_DENIED: "权限不足",
    INTERNAL_ERROR: "服务器内部错误"
};

export default {
    users,
    comments,
    graphqlSchema,
    exampleQueries,
    operationTypes,
    errorTypes
};
