/**
 * 用户管理API演示主逻辑
 */

import { users, apiEndpoints, responseTemplates, statusCodes, sampleRequests } from '../data/api-data.js';

class UserApiDemo {
    constructor() {
        this.users = [...users];
        this.requestCount = 0;
        this.successCount = 0;
        this.errorCount = 0;
        this.totalResponseTime = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.displayApiDocs();
        console.log('🚀 用户管理API演示已初始化');
    }

    bindEvents() {
        // 绑定按钮事件
        document.getElementById('getUsersBtn').addEventListener('click', () => this.getUsers());
        document.getElementById('getUserBtn').addEventListener('click', () => this.getUser());
        document.getElementById('createUserBtn').addEventListener('click', () => this.createUser());
        document.getElementById('updateUserBtn').addEventListener('click', () => this.updateUser());
        document.getElementById('deleteUserBtn').addEventListener('click', () => this.deleteUser());
        
        // 填充示例数据按钮
        document.getElementById('fillCreateData').addEventListener('click', () => this.fillCreateForm());
        document.getElementById('fillUpdateData').addEventListener('click', () => this.fillUpdateForm());
    }

    async simulateApiCall(method, endpoint, data = null, delay = 500) {
        const startTime = Date.now();
        this.requestCount++;
        
        // 显示加载状态
        this.showLoading(true);
        this.updateRequestStatus('pending', `${method} ${endpoint}`);
        
        try {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, delay + Math.random() * 500));
            
            let response;
            
            switch (method) {
                case 'GET':
                    if (endpoint.includes('/users/')) {
                        const id = parseInt(endpoint.split('/').pop());
                        response = this.handleGetUser(id);
                    } else {
                        response = this.handleGetUsers();
                    }
                    break;
                    
                case 'POST':
                    response = this.handleCreateUser(data);
                    break;
                    
                case 'PUT':
                    const updateId = parseInt(endpoint.split('/').pop());
                    response = this.handleUpdateUser(updateId, data);
                    break;
                    
                case 'DELETE':
                    const deleteId = parseInt(endpoint.split('/').pop());
                    response = this.handleDeleteUser(deleteId);
                    break;
                    
                default:
                    throw new Error('不支持的HTTP方法');
            }
            
            const responseTime = Date.now() - startTime;
            this.totalResponseTime += responseTime;
            this.successCount++;
            
            this.displayResponse(response, responseTime);
            this.updateRequestStatus('success', `${method} ${endpoint} - ${responseTime}ms`);
            
            return response;
            
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.totalResponseTime += responseTime;
            this.errorCount++;
            
            const errorResponse = {
                ...responseTemplates.error,
                message: error.message,
                timestamp: new Date().toISOString()
            };
            
            this.displayResponse(errorResponse, responseTime);
            this.updateRequestStatus('error', `${method} ${endpoint} - ${error.message}`);
            
            throw error;
        } finally {
            this.showLoading(false);
            this.updateStats();
        }
    }

    handleGetUsers() {
        return {
            ...responseTemplates.success,
            data: this.users,
            pagination: {
                page: 1,
                limit: 10,
                total: this.users.length,
                pages: Math.ceil(this.users.length / 10)
            },
            timestamp: new Date().toISOString()
        };
    }

    handleGetUser(id) {
        const user = this.users.find(u => u.id === id);
        if (!user) {
            throw new Error('用户不存在');
        }
        
        return {
            ...responseTemplates.success,
            data: user,
            timestamp: new Date().toISOString()
        };
    }

    handleCreateUser(userData) {
        // 验证必需字段
        if (!userData.name || !userData.email || !userData.role) {
            throw new Error('缺少必需字段：name, email, role');
        }
        
        // 检查邮箱是否已存在
        if (this.users.some(u => u.email === userData.email)) {
            throw new Error('邮箱地址已存在');
        }
        
        const newUser = {
            id: Math.max(...this.users.map(u => u.id)) + 1,
            name: userData.name,
            email: userData.email,
            role: userData.role,
            status: 'active',
            avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.name}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        this.users.push(newUser);
        
        return {
            ...responseTemplates.success,
            code: 201,
            message: '用户创建成功',
            data: newUser,
            timestamp: new Date().toISOString()
        };
    }

    handleUpdateUser(id, userData) {
        const userIndex = this.users.findIndex(u => u.id === id);
        if (userIndex === -1) {
            throw new Error('用户不存在');
        }
        
        // 如果更新邮箱，检查是否已存在
        if (userData.email && userData.email !== this.users[userIndex].email) {
            if (this.users.some(u => u.email === userData.email && u.id !== id)) {
                throw new Error('邮箱地址已存在');
            }
        }
        
        const updatedUser = {
            ...this.users[userIndex],
            ...userData,
            updated_at: new Date().toISOString()
        };
        
        this.users[userIndex] = updatedUser;
        
        return {
            ...responseTemplates.success,
            message: '用户更新成功',
            data: updatedUser,
            timestamp: new Date().toISOString()
        };
    }

    handleDeleteUser(id) {
        const userIndex = this.users.findIndex(u => u.id === id);
        if (userIndex === -1) {
            throw new Error('用户不存在');
        }
        
        this.users.splice(userIndex, 1);
        
        return {
            ...responseTemplates.success,
            message: '用户删除成功',
            timestamp: new Date().toISOString()
        };
    }

    async getUsers() {
        try {
            await this.simulateApiCall('GET', '/api/v1/users');
        } catch (error) {
            console.error('获取用户列表失败:', error);
        }
    }

    async getUser() {
        const userId = document.getElementById('getUserId').value;
        if (!userId) {
            alert('请输入用户ID');
            return;
        }
        
        try {
            await this.simulateApiCall('GET', `/api/v1/users/${userId}`);
        } catch (error) {
            console.error('获取用户失败:', error);
        }
    }

    async createUser() {
        const userData = {
            name: document.getElementById('createName').value,
            email: document.getElementById('createEmail').value,
            role: document.getElementById('createRole').value
        };
        
        if (!userData.name || !userData.email || !userData.role) {
            alert('请填写所有必需字段');
            return;
        }
        
        try {
            await this.simulateApiCall('POST', '/api/v1/users', userData);
            this.clearCreateForm();
        } catch (error) {
            console.error('创建用户失败:', error);
        }
    }

    async updateUser() {
        const userId = document.getElementById('updateUserId').value;
        const userData = {
            name: document.getElementById('updateName').value,
            email: document.getElementById('updateEmail').value,
            role: document.getElementById('updateRole').value
        };
        
        if (!userId) {
            alert('请输入用户ID');
            return;
        }
        
        // 移除空值
        Object.keys(userData).forEach(key => {
            if (!userData[key]) delete userData[key];
        });
        
        if (Object.keys(userData).length === 0) {
            alert('请至少填写一个要更新的字段');
            return;
        }
        
        try {
            await this.simulateApiCall('PUT', `/api/v1/users/${userId}`, userData);
            this.clearUpdateForm();
        } catch (error) {
            console.error('更新用户失败:', error);
        }
    }

    async deleteUser() {
        const userId = document.getElementById('deleteUserId').value;
        if (!userId) {
            alert('请输入用户ID');
            return;
        }
        
        if (!confirm('确定要删除这个用户吗？')) {
            return;
        }
        
        try {
            await this.simulateApiCall('DELETE', `/api/v1/users/${userId}`);
            document.getElementById('deleteUserId').value = '';
        } catch (error) {
            console.error('删除用户失败:', error);
        }
    }

    fillCreateForm() {
        const sample = sampleRequests.createUser;
        document.getElementById('createName').value = sample.name;
        document.getElementById('createEmail').value = sample.email;
        document.getElementById('createRole').value = sample.role;
    }

    fillUpdateForm() {
        const sample = sampleRequests.updateUser;
        document.getElementById('updateUserId').value = '1';
        document.getElementById('updateName').value = sample.name;
        document.getElementById('updateEmail').value = sample.email;
        document.getElementById('updateRole').value = sample.role;
    }

    clearCreateForm() {
        document.getElementById('createName').value = '';
        document.getElementById('createEmail').value = '';
        document.getElementById('createRole').value = '';
    }

    clearUpdateForm() {
        document.getElementById('updateUserId').value = '';
        document.getElementById('updateName').value = '';
        document.getElementById('updateEmail').value = '';
        document.getElementById('updateRole').value = '';
    }

    displayResponse(response, responseTime) {
        const responseArea = document.getElementById('responseArea');
        const formattedResponse = JSON.stringify(response, null, 2);
        responseArea.textContent = formattedResponse;
        
        // 添加响应时间信息
        const timeInfo = `\n\n// 响应时间: ${responseTime}ms\n// 时间戳: ${new Date().toLocaleString()}`;
        responseArea.textContent += timeInfo;
    }

    showLoading(show) {
        const loadingElements = document.querySelectorAll('.loading');
        loadingElements.forEach(el => {
            el.style.display = show ? 'inline-block' : 'none';
        });
    }

    updateRequestStatus(status, message) {
        const statusElement = document.getElementById('requestStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');
        
        indicator.className = `status-indicator status-${status}`;
        text.textContent = message;
    }

    updateStats() {
        document.getElementById('totalRequests').textContent = this.requestCount;
        document.getElementById('successRequests').textContent = this.successCount;
        document.getElementById('errorRequests').textContent = this.errorCount;
        
        const avgResponseTime = this.requestCount > 0 
            ? Math.round(this.totalResponseTime / this.requestCount) 
            : 0;
        document.getElementById('avgResponseTime').textContent = avgResponseTime;
    }

    displayApiDocs() {
        // API文档显示逻辑可以在这里实现
        console.log('API文档已准备就绪');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.userApiDemo = new UserApiDemo();
});
