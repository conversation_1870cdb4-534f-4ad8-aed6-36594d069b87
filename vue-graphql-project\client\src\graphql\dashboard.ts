import { gql } from '@apollo/client/core'

// 仪表板查询
export const GET_DASHBOARD_STATS_QUERY = gql`
  query GetDashboardStats {
    dashboardStats {
      taskStats {
        total
        todo
        inProgress
        inReview
        testing
        done
        cancelled
      }
      projectStats {
        total
        planning
        active
        onHold
        completed
        cancelled
      }
      userCount
      recentTasks {
        id
        title
        status
        priority
        dueDate
        project {
          id
          name
        }
        assignee {
          id
          username
          firstName
          lastName
          avatar
        }
      }
      recentProjects {
        id
        name
        status
        priority
        owner {
          id
          username
          firstName
          lastName
          avatar
        }
      }
    }
  }
`

// 用户查询
export const GET_USERS_QUERY = gql`
  query GetUsers($pagination: PaginationInput, $search: String) {
    users(pagination: $pagination, search: $search) {
      users {
        id
        username
        email
        firstName
        lastName
        avatar
        role
        status
        createdAt
      }
      pagination {
        page
        limit
        total
        pages
        hasNext
        hasPrev
      }
    }
  }
`

export const GET_USER_QUERY = gql`
  query GetUser($id: ID!) {
    user(id: $id) {
      id
      username
      email
      firstName
      lastName
      avatar
      role
      status
      createdAt
      updatedAt
      tasks {
        id
        title
        status
        priority
        project {
          id
          name
        }
      }
      assignedTasks {
        id
        title
        status
        priority
        project {
          id
          name
        }
      }
      projects {
        id
        name
        status
        priority
      }
    }
  }
`
