# RESTful API 基础概念详解

## 1. REST 简介

### 1.1 什么是 REST？
**REST** (Representational State Transfer，表现层状态转移) 是一种软件架构风格，由Roy Fielding在2000年的博士论文中提出。它定义了一组约束条件和原则，用于创建可扩展的Web服务。

### 1.2 REST 的核心思想
- **资源(Resource)**: 网络上的一个实体，可以是文档、图片、服务等
- **表现层(Representation)**: 资源的具体表现形式，如JSON、XML、HTML等
- **状态转移(State Transfer)**: 通过HTTP方法对资源进行操作，实现状态的转移

## 2. REST 的六大约束原则

### 2.1 客户端-服务器架构 (Client-Server)
- **分离关注点**: 客户端负责用户界面，服务器负责数据存储
- **独立演化**: 客户端和服务器可以独立开发和部署
- **提高可移植性**: 客户端可以运行在不同平台上

```
客户端 (前端应用) ←→ 服务器 (API服务)
     ↓                    ↓
  用户界面              数据存储
  用户体验              业务逻辑
```

### 2.2 无状态性 (Stateless)
- **每个请求独立**: 服务器不保存客户端状态信息
- **请求包含完整信息**: 每个请求必须包含处理所需的所有信息
- **提高可靠性**: 服务器故障不会影响客户端状态

```javascript
// ❌ 有状态的请求 (不符合REST)
// 第一个请求
POST /login
{ "username": "john", "password": "123456" }

// 第二个请求依赖第一个请求的状态
GET /profile  // 服务器需要记住用户已登录

// ✅ 无状态的请求 (符合REST)
GET /profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2.3 可缓存性 (Cacheable)
- **响应可缓存**: 服务器响应应该明确标识是否可缓存
- **提高性能**: 减少客户端-服务器交互次数
- **减少延迟**: 客户端可以重用缓存的响应

```http
HTTP/1.1 200 OK
Cache-Control: max-age=3600, public
ETag: "abc123"
Last-Modified: Wed, 21 Oct 2023 07:28:00 GMT

{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

### 2.4 统一接口 (Uniform Interface)
REST的核心约束，包含四个子约束：

#### 2.4.1 资源标识 (Resource Identification)
- 每个资源都有唯一的URI标识
- URI应该是名词，不是动词

```
✅ 好的URI设计
GET /users/123          # 获取ID为123的用户
GET /users/123/posts    # 获取用户123的所有文章
GET /posts/456/comments # 获取文章456的所有评论

❌ 不好的URI设计
GET /getUser?id=123     # 使用了动词
GET /user_posts/123     # 不够RESTful
POST /createUser        # URI包含动词
```

#### 2.4.2 通过表现层操作资源 (Resource Manipulation through Representations)
- 客户端通过资源的表现层来操作资源
- 表现层包含足够的信息来修改或删除资源

```json
// 用户资源的JSON表现层
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "created_at": "2023-10-21T07:28:00Z",
  "updated_at": "2023-10-21T07:28:00Z"
}
```

#### 2.4.3 自描述消息 (Self-descriptive Messages)
- 每个消息包含足够的信息来描述如何处理消息
- 包含媒体类型、缓存指令等元数据

```http
GET /users/123 HTTP/1.1
Host: api.example.com
Accept: application/json
Authorization: Bearer token123

HTTP/1.1 200 OK
Content-Type: application/json; charset=utf-8
Cache-Control: max-age=300
Content-Length: 156

{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

#### 2.4.4 超媒体作为应用状态引擎 (HATEOAS)
- 客户端通过服务器提供的链接来发现可用的操作
- 应用状态通过超媒体链接来驱动

```json
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "_links": {
    "self": { "href": "/users/123" },
    "posts": { "href": "/users/123/posts" },
    "edit": { "href": "/users/123", "method": "PUT" },
    "delete": { "href": "/users/123", "method": "DELETE" }
  }
}
```

### 2.5 分层系统 (Layered System)
- **层次化架构**: 客户端不需要知道是否直接连接到最终服务器
- **中间层**: 可以添加负载均衡器、缓存、安全层等
- **提高可扩展性**: 每一层只关注自己的职责

```
客户端 → 负载均衡器 → API网关 → 微服务 → 数据库
         ↓           ↓        ↓
       缓存层      安全层   业务层
```

### 2.6 按需代码 (Code on Demand) - 可选
- **动态功能**: 服务器可以向客户端发送可执行代码
- **扩展客户端功能**: 如JavaScript、Java Applet等
- **可选约束**: 这是唯一的可选约束

## 3. RESTful API 设计原则

### 3.1 资源设计原则
```
资源集合: /users          # 所有用户
单个资源: /users/123       # ID为123的用户
子资源:   /users/123/posts # 用户123的文章
```

### 3.2 HTTP 方法使用
| 方法 | 用途 | 示例 | 幂等性 | 安全性 |
|------|------|------|--------|--------|
| GET | 获取资源 | `GET /users/123` | ✅ | ✅ |
| POST | 创建资源 | `POST /users` | ❌ | ❌ |
| PUT | 更新/替换资源 | `PUT /users/123` | ✅ | ❌ |
| PATCH | 部分更新资源 | `PATCH /users/123` | ❌ | ❌ |
| DELETE | 删除资源 | `DELETE /users/123` | ✅ | ❌ |

### 3.3 状态码使用
```
2xx 成功
200 OK              # 请求成功
201 Created         # 资源创建成功
204 No Content      # 请求成功但无返回内容

4xx 客户端错误
400 Bad Request     # 请求格式错误
401 Unauthorized    # 未认证
403 Forbidden       # 无权限
404 Not Found       # 资源不存在

5xx 服务器错误
500 Internal Server Error  # 服务器内部错误
503 Service Unavailable    # 服务不可用
```

## 4. RESTful API 示例

### 4.1 用户管理 API
```javascript
// 获取所有用户
GET /api/v1/users
Response: 200 OK
[
  { "id": 1, "name": "John", "email": "<EMAIL>" },
  { "id": 2, "name": "Jane", "email": "<EMAIL>" }
]

// 获取单个用户
GET /api/v1/users/1
Response: 200 OK
{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "created_at": "2023-10-21T07:28:00Z"
}

// 创建用户
POST /api/v1/users
Content-Type: application/json
{
  "name": "New User",
  "email": "<EMAIL>"
}
Response: 201 Created
{
  "id": 3,
  "name": "New User",
  "email": "<EMAIL>",
  "created_at": "2023-10-21T08:00:00Z"
}

// 更新用户
PUT /api/v1/users/1
Content-Type: application/json
{
  "name": "John Smith",
  "email": "<EMAIL>"
}
Response: 200 OK
{
  "id": 1,
  "name": "John Smith",
  "email": "<EMAIL>",
  "updated_at": "2023-10-21T08:15:00Z"
}

// 删除用户
DELETE /api/v1/users/1
Response: 204 No Content
```

## 5. REST vs 其他架构风格

### 5.1 REST vs SOAP
| 特性 | REST | SOAP |
|------|------|------|
| 协议 | HTTP | HTTP/SMTP/TCP |
| 数据格式 | JSON/XML | XML |
| 复杂度 | 简单 | 复杂 |
| 性能 | 高 | 较低 |
| 缓存 | 支持 | 不支持 |

### 5.2 REST vs RPC
| 特性 | REST | RPC |
|------|------|------|
| 设计理念 | 资源导向 | 操作导向 |
| URL风格 | `/users/123` | `/getUser?id=123` |
| HTTP方法 | 充分利用 | 主要用POST |
| 缓存 | 容易 | 困难 |

## 6. 最佳实践总结

### 6.1 设计建议
- 使用名词而不是动词
- 保持URL简洁明了
- 使用复数形式表示集合
- 提供版本控制
- 返回有意义的HTTP状态码

### 6.2 常见错误
- 在URL中使用动词
- 不正确的HTTP方法使用
- 不一致的命名约定
- 缺乏错误处理
- 忽略缓存策略

---

**学习检验**:
1. 解释REST的六大约束原则
2. 设计一个博客系统的RESTful API
3. 说明HATEOAS的作用和实现方式
4. 对比REST与SOAP的优缺点
