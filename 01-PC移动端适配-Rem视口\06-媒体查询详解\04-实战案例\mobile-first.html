<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动优先设计示例</title>
    <style>
        /* 移动优先基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        /* 移动端基础布局 */
        .container {
            width: 100%;
            padding: 0 1rem;
            margin: 0 auto;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.25rem;
            font-weight: bold;
        }

        .nav-menu {
            display: none; /* 移动端默认隐藏菜单 */
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #007bff;
            flex-direction: column;
            padding: 1rem;
            list-style: none;
        }

        .nav-menu.active {
            display: flex;
        }

        .nav-menu li {
            margin: 0.5rem 0;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            display: block;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .nav-menu a:hover {
            background: rgba(255,255,255,0.1);
        }

        .menu-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.25rem;
        }

        /* 主要内容区域 - 移动端单列布局 */
        .main {
            padding: 1rem 0;
        }

        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 2rem 1rem;
            margin-bottom: 2rem;
            border-radius: 8px;
        }

        .hero h1 {
            font-size: 1.75rem;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 卡片布局 - 移动端单列 */
        .card-grid {
            display: grid;
            grid-template-columns: 1fr; /* 移动端单列 */
            gap: 1rem;
            margin: 2rem 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card h3 {
            color: #007bff;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .card p {
            color: #666;
            font-size: 0.9rem;
        }

        /* 特性列表 - 移动端垂直布局 */
        .features {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .features h2 {
            text-align: center;
            margin-bottom: 1.5rem;
            color: #333;
            font-size: 1.5rem;
        }

        .feature-list {
            display: flex;
            flex-direction: column; /* 移动端垂直排列 */
            gap: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .feature-content h4 {
            margin-bottom: 0.5rem;
            color: #333;
            font-size: 1rem;
        }

        .feature-content p {
            color: #666;
            font-size: 0.85rem;
        }

        /* 响应式断点 */

        /* 小平板 (576px+) */
        @media (min-width: 576px) {
            .container {
                max-width: 540px;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .card-grid {
                grid-template-columns: repeat(2, 1fr); /* 小平板双列 */
            }
        }

        /* 平板 (768px+) */
        @media (min-width: 768px) {
            .container {
                max-width: 720px;
            }

            .nav-menu {
                display: flex !important; /* 平板显示水平菜单 */
                position: static;
                flex-direction: row;
                background: transparent;
                padding: 0;
                gap: 2rem;
            }

            .nav-menu li {
                margin: 0;
            }

            .menu-toggle {
                display: none; /* 隐藏汉堡菜单 */
            }

            .hero {
                padding: 3rem 2rem;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .feature-list {
                flex-direction: row; /* 平板水平排列 */
                flex-wrap: wrap;
            }

            .feature-item {
                flex: 1;
                min-width: 250px;
            }
        }

        /* 大平板/小桌面 (992px+) */
        @media (min-width: 992px) {
            .container {
                max-width: 960px;
            }

            .card-grid {
                grid-template-columns: repeat(3, 1fr); /* 大屏三列 */
            }

            .hero {
                padding: 4rem 2rem;
            }

            .hero h1 {
                font-size: 3rem;
            }

            .hero p {
                font-size: 1.25rem;
            }
        }

        /* 桌面 (1200px+) */
        @media (min-width: 1200px) {
            .container {
                max-width: 1140px;
            }

            .card-grid {
                grid-template-columns: repeat(4, 1fr); /* 桌面四列 */
            }
        }

        /* 超大屏幕 (1400px+) */
        @media (min-width: 1400px) {
            .container {
                max-width: 1320px;
            }
        }

        /* 方向特定样式 */
        
        /* 移动端横屏优化 */
        @media (max-width: 767px) and (orientation: landscape) {
            .hero {
                padding: 1.5rem 1rem; /* 横屏减少垂直间距 */
            }

            .hero h1 {
                font-size: 1.5rem;
            }

            .card-grid {
                grid-template-columns: repeat(2, 1fr); /* 横屏显示两列 */
                gap: 0.75rem;
            }

            .card {
                padding: 1rem;
            }

            .features {
                padding: 1rem;
            }

            .feature-list {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .feature-item {
                flex: 1;
                min-width: 200px;
            }
        }

        /* 小屏幕横屏特殊处理 */
        @media (max-height: 500px) and (orientation: landscape) {
            .header {
                padding: 0.5rem 0;
            }

            .hero {
                padding: 1rem;
            }

            .hero h1 {
                font-size: 1.25rem;
                margin-bottom: 0.5rem;
            }

            .main {
                padding: 0.5rem 0;
            }
        }

        /* 打印样式 */
        @media print {
            .header, .menu-toggle {
                display: none !important;
            }

            .hero {
                background: white !important;
                color: black !important;
                border: 1px solid black !important;
            }

            .card-grid {
                grid-template-columns: 1fr !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                page-break-inside: avoid;
            }

            .feature-list {
                flex-direction: column !important;
            }
        }

        /* 调试信息 */
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            z-index: 1000;
            display: none;
        }

        .debug-info.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        <div>屏幕: <span id="screenSize"></span></div>
        <div>方向: <span id="orientation"></span></div>
        <div>断点: <span id="breakpoint"></span></div>
    </div>

    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">MobileFirst</div>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#features">特性</a></li>
                    <li><a href="#about">关于</a></li>
                    <li><a href="#contact">联系</a></li>
                </ul>
                <button class="menu-toggle" id="menuToggle">☰</button>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <section class="hero">
                <h1>移动优先设计</h1>
                <p>从最小屏幕开始设计，逐步增强到更大屏幕，确保在所有设备上都有良好的用户体验。</p>
            </section>

            <section class="features">
                <h2>设计原则</h2>
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon">📱</div>
                        <div class="feature-content">
                            <h4>移动优先</h4>
                            <p>从320px开始设计，确保在最小屏幕上功能完整</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📈</div>
                        <div class="feature-content">
                            <h4>渐进增强</h4>
                            <p>随着屏幕增大，逐步添加更多功能和视觉效果</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🎯</div>
                        <div class="feature-content">
                            <h4>内容优先</h4>
                            <p>确保核心内容在任何设备上都清晰可见</p>
                        </div>
                    </div>
                </div>
            </section>

            <section class="card-grid">
                <div class="card">
                    <h3>320px - 575px</h3>
                    <p>超小屏幕：单列布局，垂直导航，简化内容</p>
                </div>
                <div class="card">
                    <h3>576px - 767px</h3>
                    <p>小屏幕：双列卡片，保持垂直导航</p>
                </div>
                <div class="card">
                    <h3>768px - 991px</h3>
                    <p>中等屏幕：水平导航，三列布局开始出现</p>
                </div>
                <div class="card">
                    <h3>992px - 1199px</h3>
                    <p>大屏幕：完整的多列布局，更多空白空间</p>
                </div>
                <div class="card">
                    <h3>1200px+</h3>
                    <p>超大屏幕：最大化利用空间，四列或更多</p>
                </div>
                <div class="card">
                    <h3>横屏适配</h3>
                    <p>移动设备横屏时的特殊优化处理</p>
                </div>
            </section>
        </div>
    </main>

    <script>
        // 菜单切换
        const menuToggle = document.getElementById('menuToggle');
        const navMenu = document.getElementById('navMenu');

        menuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });

        // 调试信息
        const debugInfo = document.getElementById('debugInfo');
        const screenSize = document.getElementById('screenSize');
        const orientation = document.getElementById('orientation');
        const breakpoint = document.getElementById('breakpoint');

        function updateDebugInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            screenSize.textContent = `${width}x${height}`;
            orientation.textContent = height > width ? '竖屏' : '横屏';
            
            let bp = 'xs';
            if (width >= 1400) bp = 'xxl';
            else if (width >= 1200) bp = 'xl';
            else if (width >= 992) bp = 'lg';
            else if (width >= 768) bp = 'md';
            else if (width >= 576) bp = 'sm';
            
            breakpoint.textContent = bp;
        }

        // 显示/隐藏调试信息
        let debugVisible = false;
        document.addEventListener('keydown', (e) => {
            if (e.key === 'D' && e.ctrlKey) {
                e.preventDefault();
                debugVisible = !debugVisible;
                debugInfo.classList.toggle('show', debugVisible);
                if (debugVisible) updateDebugInfo();
            }
        });

        // 监听窗口变化
        window.addEventListener('resize', () => {
            if (debugVisible) updateDebugInfo();
            // 窗口变化时关闭菜单
            navMenu.classList.remove('active');
        });

        // 监听方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                if (debugVisible) updateDebugInfo();
                navMenu.classList.remove('active');
            }, 100);
        });

        // 初始化
        updateDebugInfo();

        // 提示用户可以查看调试信息
        console.log('按 Ctrl+D 显示/隐藏调试信息');
    </script>
</body>
</html>
