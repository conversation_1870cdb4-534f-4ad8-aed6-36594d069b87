# Vue 3 + GraphQL 完整项目

一个现代化的全栈项目，使用 Vue 3 + GraphQL + Apollo 构建的任务管理系统。

## 🚀 项目特性

- **前端**: Vue 3 + Composition API + TypeScript
- **构建工具**: Vite (快速开发和构建)
- **GraphQL客户端**: Apollo Client
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **后端**: Apollo Server + Express
- **数据库**: 内存数据库 (可扩展为真实数据库)
- **实时功能**: GraphQL Subscriptions
- **类型安全**: 完整的TypeScript支持

## 📁 项目结构

```
vue-graphql-project/
├── client/                     # Vue 3 前端项目
│   ├── src/
│   │   ├── components/         # Vue 组件
│   │   ├── views/             # 页面组件
│   │   ├── stores/            # Pinia 状态管理
│   │   ├── graphql/           # GraphQL 查询和变更
│   │   ├── types/             # TypeScript 类型定义
│   │   ├── utils/             # 工具函数
│   │   └── main.ts            # 应用入口
│   ├── package.json
│   ├── vite.config.ts
│   └── tsconfig.json
├── server/                     # GraphQL 服务端
│   ├── src/
│   │   ├── schema/            # GraphQL Schema 定义
│   │   ├── resolvers/         # GraphQL Resolvers
│   │   ├── models/            # 数据模型
│   │   ├── data/              # 模拟数据
│   │   └── server.ts          # 服务器入口
│   ├── package.json
│   └── tsconfig.json
├── shared/                     # 共享类型和工具
│   └── types/                 # 共享的TypeScript类型
└── README.md
```

## 🛠️ 快速开始

### 方法一：使用启动脚本（推荐）

**Windows:**
```bash
# 双击运行或在命令行执行
start.bat
```

**Linux/macOS:**
```bash
# 添加执行权限并运行
chmod +x start.sh
./start.sh
```

### 方法二：手动启动

**1. 安装所有依赖**
```bash
# 在项目根目录执行
npm run install:all
```

**2. 启动开发服务器**
```bash
# 同时启动前后端
npm run dev

# 或分别启动
npm run dev:server  # 启动GraphQL服务端 (端口: 4000)
npm run dev:client  # 启动Vue前端 (端口: 3000)
```

### 3. 访问应用

- **前端应用**: http://localhost:3000
- **GraphQL Playground**: http://localhost:4000/graphql
- **API信息**: http://localhost:4000/api/info
- **健康检查**: http://localhost:4000/health

### 4. 登录系统

使用以下演示账户登录：

| 角色 | 邮箱 | 密码 |
|------|------|------|
| 管理员 | <EMAIL> | admin123 |
| 项目经理 | <EMAIL> | password123 |
| 开发者 | <EMAIL> | password123 |
| 设计师 | <EMAIL> | password123 |
| 测试员 | <EMAIL> | password123 |

### 5. 测试文档

我们提供了多种格式的测试文档：

- **📋 完整测试指南**: [TESTING.md](./TESTING.md) - 详细的功能测试说明
- **🚀 快速测试卡片**: [QUICK-TEST.md](./QUICK-TEST.md) - 简化的测试流程
- **🌐 在线测试页面**: [test-guide.html](./test-guide.html) - 可在浏览器中查看的测试指南

**推荐**: 直接在浏览器中打开 `test-guide.html` 获得最佳的测试体验！

## 🎯 功能特性

### 用户管理
- ✅ 用户注册和登录
- ✅ 用户信息管理
- ✅ 角色权限控制

### 任务管理
- ✅ 创建、编辑、删除任务
- ✅ 任务状态管理
- ✅ 优先级设置
- ✅ 任务分配
- ✅ 实时更新

### 项目管理
- ✅ 项目创建和管理
- ✅ 项目成员管理
- ✅ 项目进度跟踪

### 实时功能
- ✅ 实时任务更新
- ✅ 实时通知
- ✅ 在线用户状态

## 🔧 技术栈详解

### 前端技术栈
- **Vue 3**: 使用Composition API，提供更好的逻辑复用
- **TypeScript**: 完整的类型安全
- **Vite**: 极快的开发体验
- **Apollo Client**: 强大的GraphQL客户端
- **Element Plus**: 企业级UI组件库
- **Pinia**: 现代化的状态管理
- **Vue Router 4**: 官方路由解决方案

### 后端技术栈
- **Apollo Server**: GraphQL服务器
- **Express**: Node.js Web框架
- **GraphQL**: 查询语言和运行时
- **TypeScript**: 类型安全的JavaScript
- **内存数据库**: 快速原型开发

## 📚 学习路径

### 第一步: 了解项目结构
1. 查看项目目录结构
2. 理解前后端分离架构
3. 了解GraphQL Schema定义

### 第二步: 运行项目
1. 启动后端服务
2. 启动前端应用
3. 体验完整功能

### 第三步: 学习GraphQL
1. 使用GraphQL Playground
2. 编写查询和变更
3. 理解订阅机制

### 第四步: Vue 3 开发
1. 学习Composition API
2. 理解响应式系统
3. 掌握组件通信

### 第五步: 扩展功能
1. 添加新的数据模型
2. 实现新的业务功能
3. 优化性能和用户体验

## 🚀 部署指南

### 开发环境
```bash
# 同时启动前后端
npm run dev
```

### 生产环境
```bash
# 1. 构建项目
npm run build

# 2. 启动生产服务器
npm start

# 或手动构建和启动
cd client && npm run build
cd ../server && npm run build && npm start
```

### Docker 部署（可选）
```bash
# 构建镜像
docker build -t vue-graphql-project .

# 运行容器
docker run -p 4000:4000 vue-graphql-project
```

## 🔧 环境配置

### 服务端环境变量
复制 `server/.env.example` 到 `server/.env` 并修改配置：

```bash
# 服务器配置
PORT=4000
NODE_ENV=development
JWT_SECRET=your-super-secret-jwt-key

# CORS 配置
CORS_ORIGIN=http://localhost:3000
```

### 客户端环境变量
复制 `client/.env.example` 到 `client/.env` 并修改配置：

```bash
# API 配置
VITE_GRAPHQL_URL=http://localhost:4000/graphql
VITE_GRAPHQL_WS_URL=ws://localhost:4000/graphql
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
