import DataLoader from 'dataloader';
import { ApolloServerPlugin } from 'apollo-server-plugin-base';
import { db } from '../data/mockData';
import { User, Task, Project } from '../models';

/**
 * 上下文注入插件
 * 在 executionDidStart 钩子中为每个请求注入共享数据
 */
export const contextInjectionPlugin: ApolloServerPlugin = {
  requestDidStart() {
    return {
      // 🎯 在执行开始前注入上下文数据
      executionDidStart(requestContext) {
        const startTime = Date.now();
        
        // 🎯 1. 生成请求追踪ID
        requestContext.context.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        requestContext.context.startTime = startTime;
        
        // 🎯 2. 初始化 DataLoader 实例（解决 N+1 问题）
        requestContext.context.loaders = {
          // 用户数据加载器
          userLoader: new DataLoader<string, User | null>(async (userIds: readonly string[]) => {
            console.log(`📊 Batch loading ${userIds.length} users`);
            const users = db.users.filter(user => userIds.includes(user.id));
            return userIds.map(id => users.find(user => user.id === id) || null);
          }),
          
          // 任务数据加载器
          taskLoader: new DataLoader<string, Task | null>(async (taskIds: readonly string[]) => {
            console.log(`📊 Batch loading ${taskIds.length} tasks`);
            const tasks = db.tasks.filter(task => taskIds.includes(task.id));
            return taskIds.map(id => tasks.find(task => task.id === id) || null);
          }),
          
          // 项目数据加载器
          projectLoader: new DataLoader<string, Project | null>(async (projectIds: readonly string[]) => {
            console.log(`📊 Batch loading ${projectIds.length} projects`);
            const projects = db.projects.filter(project => projectIds.includes(project.id));
            return projectIds.map(id => projects.find(project => project.id === id) || null);
          }),
          
          // 用户任务加载器（按用户ID批量加载任务）
          userTasksLoader: new DataLoader<string, Task[]>(async (userIds: readonly string[]) => {
            console.log(`📊 Batch loading tasks for ${userIds.length} users`);
            return userIds.map(userId => 
              db.tasks.filter(task => task.assigneeId === userId || task.creatorId === userId)
            );
          }),
          
          // 项目成员加载器
          projectMembersLoader: new DataLoader<string, User[]>(async (projectIds: readonly string[]) => {
            console.log(`📊 Batch loading members for ${projectIds.length} projects`);
            return projectIds.map(projectId => {
              const project = db.projects.find(p => p.id === projectId);
              if (!project) return [];
              return db.users.filter(user => project.memberIds.includes(user.id));
            });
          })
        };
        
        // 🎯 3. 初始化请求级别缓存
        requestContext.context.cache = new Map<string, any>();
        
        // 🎯 4. 初始化请求统计
        requestContext.context.stats = {
          resolverCalls: 0,
          dbQueries: 0,
          cacheHits: 0,
          cacheMisses: 0,
          dataLoaderCalls: 0
        };
        
        // 🎯 5. 注入用户权限信息（如果用户已认证）
        if (requestContext.context.user) {
          // 基于用户角色设置权限
          const userRole = requestContext.context.user.role || 'user';
          requestContext.context.permissions = getPermissionsByRole(userRole);
          
          console.log(`🔐 User ${requestContext.context.user.username} permissions:`, requestContext.context.permissions);
        }
        
        // 🎯 6. 设置操作类型标识
        const operationName = requestContext.request.operationName;
        requestContext.context.operationType = getOperationType(operationName);
        
        // 🎯 7. 初始化功能开关（基于用户或环境）
        requestContext.context.featureFlags = {
          enableAdvancedSearch: true,
          enableRealTimeUpdates: process.env.NODE_ENV === 'development',
          enableBetaFeatures: requestContext.context.user?.role === 'admin'
        };
        
        console.log(`🚀 Context initialized for request: ${requestContext.context.requestId} (${operationName})`);
        
        return {
          // 🎯 执行完成后清理资源
          executionDidEnd(err) {
            const duration = Date.now() - startTime;
            
            // 记录请求统计
            console.log(`📊 Request ${requestContext.context.requestId} stats:`, {
              duration: `${duration}ms`,
              ...requestContext.context.stats
            });
            
            // 🎯 清理 DataLoader 缓存
            if (requestContext.context.loaders) {
              Object.values(requestContext.context.loaders).forEach((loader: any) => {
                if (loader.clearAll) {
                  loader.clearAll();
                }
              });
            }
            
            // 🎯 清理请求缓存
            if (requestContext.context.cache) {
              requestContext.context.cache.clear();
            }
            
            // 🎯 记录慢请求
            if (duration > 1000) {
              console.warn(`🐌 Slow request detected: ${requestContext.context.requestId} took ${duration}ms`);
            }
            
            if (err) {
              console.error(`❌ Request ${requestContext.context.requestId} failed:`, err.message);
            } else {
              console.log(`✅ Request ${requestContext.context.requestId} completed successfully in ${duration}ms`);
            }
          }
        };
      }
    };
  }
};

/**
 * 根据用户角色获取权限列表
 */
function getPermissionsByRole(role: string): string[] {
  const permissions: Record<string, string[]> = {
    admin: [
      'read:users', 'write:users', 'delete:users',
      'read:tasks', 'write:tasks', 'delete:tasks',
      'read:projects', 'write:projects', 'delete:projects',
      'manage:system'
    ],
    manager: [
      'read:users', 'write:users',
      'read:tasks', 'write:tasks', 'delete:tasks',
      'read:projects', 'write:projects'
    ],
    user: [
      'read:tasks', 'write:tasks',
      'read:projects'
    ]
  };
  
  return permissions[role] || permissions.user;
}

/**
 * 根据操作名称确定操作类型
 */
function getOperationType(operationName?: string): 'query' | 'mutation' | 'subscription' | 'unknown' {
  if (!operationName) return 'unknown';
  
  if (operationName.startsWith('get') || operationName.startsWith('list') || operationName.startsWith('search')) {
    return 'query';
  }
  
  if (operationName.startsWith('create') || operationName.startsWith('update') || operationName.startsWith('delete')) {
    return 'mutation';
  }
  
  if (operationName.startsWith('subscribe') || operationName.includes('Subscription')) {
    return 'subscription';
  }
  
  return 'unknown';
}

/**
 * 扩展 Context 类型定义
 */
declare module '../models' {
  interface Context {
    requestId?: string;
    startTime?: number;
    loaders?: {
      userLoader: DataLoader<string, User | null>;
      taskLoader: DataLoader<string, Task | null>;
      projectLoader: DataLoader<string, Project | null>;
      userTasksLoader: DataLoader<string, Task[]>;
      projectMembersLoader: DataLoader<string, User[]>;
    };
    cache?: Map<string, any>;
    stats?: {
      resolverCalls: number;
      dbQueries: number;
      cacheHits: number;
      cacheMisses: number;
      dataLoaderCalls: number;
    };
    permissions?: string[];
    operationType?: 'query' | 'mutation' | 'subscription' | 'unknown';
    featureFlags?: {
      enableAdvancedSearch: boolean;
      enableRealTimeUpdates: boolean;
      enableBetaFeatures: boolean;
    };
  }
}
