# Git 命令分类速查表

## 🚀 按使用场景分类的 Git 命令

### 📁 仓库管理
```bash
# 创建和初始化
git init                          # 初始化新仓库
git init --bare                   # 创建裸仓库
git clone <url>                   # 克隆远程仓库
git clone --depth 1 <url>         # 浅克隆

# 仓库信息
git status                        # 查看仓库状态
git status -s                     # 简洁状态
git remote -v                     # 查看远程仓库
git config --list                 # 查看配置
```

### 📝 文件操作
```bash
# 添加文件
git add <file>                    # 添加指定文件
git add .                         # 添加所有文件
git add -A                        # 添加所有文件（包括删除）
git add -p                        # 交互式添加

# 提交文件
git commit -m "message"           # 提交并添加信息
git commit -am "message"          # 添加并提交已跟踪文件
git commit --amend                # 修改最后一次提交

# 删除和移动
git rm <file>                     # 删除文件
git rm --cached <file>            # 从暂存区删除
git mv <old> <new>                # 重命名/移动文件
```

### 🔍 查看历史
```bash
# 提交历史
git log                           # 查看提交历史
git log --oneline                 # 简洁显示
git log --graph                   # 图形化显示
git log -p                        # 显示差异
git log --stat                    # 显示统计

# 查看差异
git diff                          # 工作区 vs 暂存区
git diff --staged                 # 暂存区 vs 最后提交
git diff HEAD                     # 工作区 vs 最后提交
git diff <commit1> <commit2>      # 两个提交间差异

# 查看详情
git show <commit>                 # 查看提交详情
git blame <file>                  # 查看文件注释
git grep "pattern"                # 在仓库中搜索
```

### 🔄 撤销操作
```bash
# 撤销工作区修改
git checkout -- <file>           # 撤销文件修改
git restore <file>                # 恢复文件（新命令）

# 撤销暂存区修改
git reset HEAD <file>             # 撤销暂存
git restore --staged <file>      # 撤销暂存（新命令）

# 撤销提交
git reset --soft HEAD~1          # 软重置（保留修改）
git reset --hard HEAD~1          # 硬重置（丢弃修改）
git revert <commit>               # 创建反向提交
```

### 🌿 分支管理
```bash
# 分支操作
git branch                        # 查看分支
git branch <name>                 # 创建分支
git checkout <branch>             # 切换分支
git checkout -b <branch>          # 创建并切换分支
git switch <branch>               # 切换分支（新命令）
git switch -c <branch>            # 创建并切换（新命令）

# 分支合并
git merge <branch>                # 合并分支
git merge --no-ff <branch>        # 非快进合并
git rebase <branch>               # 变基合并

# 分支删除
git branch -d <branch>            # 删除已合并分支
git branch -D <branch>            # 强制删除分支
git push origin --delete <branch> # 删除远程分支
```

### 📡 远程操作
```bash
# 远程仓库
git remote add <name> <url>       # 添加远程仓库
git remote remove <name>          # 删除远程仓库
git remote rename <old> <new>     # 重命名远程仓库

# 同步操作
git fetch                         # 获取远程更新
git fetch --all                   # 获取所有远程更新
git pull                          # 拉取并合并
git pull --rebase                 # 拉取并变基
git push                          # 推送到远程
git push -u origin <branch>       # 推送并设置上游
```

### 🏷️ 标签管理
```bash
# 标签操作
git tag                           # 查看标签
git tag <name>                    # 创建轻量标签
git tag -a <name> -m "message"    # 创建注释标签
git tag -d <name>                 # 删除本地标签
git push origin <tag>             # 推送标签
git push origin --tags            # 推送所有标签
git push origin --delete <tag>    # 删除远程标签
```

### 🔧 配置管理
```bash
# 用户配置
git config --global user.name "Name"     # 设置用户名
git config --global user.email "email"   # 设置邮箱
git config --global core.editor "editor" # 设置编辑器

# 别名配置
git config --global alias.st status      # 设置别名
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit

# 查看配置
git config --list                 # 查看所有配置
git config user.name              # 查看特定配置
```

## 🎯 按开发阶段分类

### 🚀 项目开始
```bash
# 新项目
git init
git add README.md
git commit -m "Initial commit"
git remote add origin <url>
git push -u origin main

# 加入现有项目
git clone <url>
cd <project>
git checkout -b feature/my-feature
```

### 💻 日常开发
```bash
# 开发流程
git status                        # 检查状态
git add .                         # 添加修改
git commit -m "feat: add feature" # 提交修改
git push origin feature-branch    # 推送分支

# 同步更新
git fetch origin                  # 获取更新
git checkout main                 # 切换到主分支
git pull origin main              # 拉取最新代码
git checkout feature-branch       # 回到功能分支
git rebase main                   # 变基到最新代码
```

### 🔍 问题排查
```bash
# 查找问题
git log --grep="bug"              # 搜索相关提交
git blame <file>                  # 查看代码作者
git bisect start                  # 开始二分查找
git show <commit>                 # 查看提交详情
git diff <commit1> <commit2>      # 比较提交差异
```

### 🚨 紧急修复
```bash
# 紧急修复流程
git checkout main                 # 切换到主分支
git pull origin main              # 获取最新代码
git checkout -b hotfix/urgent-fix # 创建修复分支
# 修复代码...
git add .
git commit -m "fix: urgent security issue"
git push -u origin hotfix/urgent-fix
```

### 📦 版本发布
```bash
# 发布流程
git checkout main                 # 切换到主分支
git pull origin main              # 获取最新代码
git tag -a v1.0.0 -m "Release v1.0.0" # 创建标签
git push origin v1.0.0            # 推送标签
git push origin main              # 推送代码
```

## 🔥 高频使用命令

### ⭐ 每天必用
```bash
git status                        # 查看状态
git add .                         # 添加文件
git commit -m "message"           # 提交
git push                          # 推送
git pull                          # 拉取
git checkout <branch>             # 切换分支
git log --oneline                 # 查看历史
```

### 🔄 经常使用
```bash
git diff                          # 查看差异
git branch                        # 查看分支
git merge <branch>                # 合并分支
git reset HEAD <file>             # 撤销暂存
git checkout -- <file>            # 撤销修改
git stash                         # 暂存修改
git stash pop                     # 恢复暂存
```

### 🛠️ 偶尔使用
```bash
git rebase <branch>               # 变基
git cherry-pick <commit>          # 挑选提交
git tag -a <name> -m "message"    # 创建标签
git remote add <name> <url>       # 添加远程
git clean -fd                     # 清理文件
git reflog                        # 查看引用日志
```

## 💡 实用组合命令

### 🚀 快速开发
```bash
# 快速提交
git add . && git commit -m "Update" && git push

# 快速切换并更新
git checkout main && git pull origin main

# 创建功能分支
git checkout main && git pull && git checkout -b feature/new-feature

# 完成功能开发
git add . && git commit -m "feat: complete feature" && git push -u origin HEAD
```

### 🔍 快速查看
```bash
# 查看最近提交
git log --oneline -10

# 查看分支状态
git branch -vv

# 查看远程状态
git remote -v && git branch -r

# 查看修改统计
git diff --stat
```

### 🧹 快速清理
```bash
# 清理已合并分支
git branch --merged | grep -v "\*\|main\|master" | xargs -n 1 git branch -d

# 清理远程跟踪分支
git remote prune origin

# 清理未跟踪文件
git clean -fd

# 重置到远程状态
git fetch origin && git reset --hard origin/main
```

## 🚨 紧急救援命令

### 💊 数据恢复
```bash
# 恢复删除的提交
git reflog                        # 查找丢失的提交
git checkout <commit-hash>        # 恢复到指定提交
git checkout -b recovery-branch   # 创建恢复分支

# 恢复删除的文件
git checkout HEAD~1 -- <file>     # 从历史恢复文件
git show HEAD~1:<file> > <file>   # 恢复文件内容

# 撤销错误操作
git reset --hard HEAD@{1}         # 撤销最近的 reset
git revert <commit>               # 撤销指定提交
```

### 🔧 修复问题
```bash
# 修复提交信息
git commit --amend -m "Correct message"

# 修复作者信息
git commit --amend --author="Name <email>"

# 修复合并冲突
git status                        # 查看冲突文件
# 编辑冲突文件...
git add .                         # 标记已解决
git commit                        # 完成合并

# 中止操作
git merge --abort                 # 中止合并
git rebase --abort                # 中止变基
```

---

## 📚 使用建议

1. **从基础开始** - 先掌握每天必用的命令
2. **理解原理** - 了解每个命令的作用和影响
3. **安全第一** - 重要操作前先备份
4. **多练习** - 在测试仓库中练习各种操作
5. **设置别名** - 为常用命令创建简短别名

**记住**: 这个速查表是你的 Git 工具箱，熟练掌握这些命令将让你的开发效率大大提升！ 🚀
