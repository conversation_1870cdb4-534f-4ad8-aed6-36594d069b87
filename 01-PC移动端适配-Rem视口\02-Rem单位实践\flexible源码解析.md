# 手淘Flexible.js源码深度解析

## 1. 源码概览

手淘flexible.js是阿里巴巴手淘团队开发的移动端适配解决方案，核心思想是通过动态设置viewport和根字体大小来实现不同设备的等比缩放。

## 2. 核心原理

### 2.1 基本流程
```
1. 检测设备像素比(DPR)
2. 动态设置viewport的scale值
3. 计算并设置html的font-size
4. 监听resize和orientationchange事件
5. 提供px与rem的转换方法
```

### 2.2 关键变量说明
```javascript
var dpr = 0;        // 设备像素比
var scale = 0;      // viewport缩放比例
var flexible = {};  // 暴露的API对象
```

## 3. DPR检测逻辑

### 3.1 优先级顺序
1. **已有viewport meta标签** - 从existing meta标签获取scale值
2. **flexible meta标签** - 从flexible配置获取dpr值  
3. **自动检测** - 根据设备类型和devicePixelRatio自动设置

### 3.2 设备适配策略
```javascript
if (isIPhone) {
    // iOS设备：支持2倍和3倍方案
    if (devicePixelRatio >= 3) {
        dpr = 3;
    } else if (devicePixelRatio >= 2) {
        dpr = 2;
    } else {
        dpr = 1;
    }
} else {
    // Android等其他设备：统一使用1倍方案
    dpr = 1;
}
```

**设计考虑**：
- iOS设备屏幕质量较高，支持多倍图方案
- Android设备碎片化严重，统一使用1倍方案避免兼容性问题

## 4. Viewport动态设置

### 4.1 核心代码
```javascript
scale = 1 / dpr;
metaEl.setAttribute('content', 
    'initial-scale=' + scale + 
    ', maximum-scale=' + scale + 
    ', minimum-scale=' + scale + 
    ', user-scalable=no'
);
```

### 4.2 缩放原理
- **DPR=1**: scale=1，无缩放
- **DPR=2**: scale=0.5，页面缩小到50%
- **DPR=3**: scale=0.33，页面缩小到33%

**效果**：通过缩放让1个CSS像素等于1个物理像素，解决高DPR设备上的显示问题。

## 5. REM基准计算

### 5.1 计算公式
```javascript
function refreshRem(){
    var width = docEl.getBoundingClientRect().width;
    
    // 适配平板（限制最大宽度）
    if (width / dpr > 540) {
        width = 540 * dpr;
    }
    
    // 设置根字体大小：屏幕宽度的1/10
    var rem = width / 10;
    docEl.style.fontSize = rem + 'px';
}
```

### 5.2 设计思路
- **10等分方案**：将屏幕宽度分为10等分，1rem = 屏幕宽度/10
- **平板适配**：限制最大宽度为540px，避免平板上元素过大
- **动态更新**：监听resize和orientationchange事件，实时更新

## 6. 事件监听机制

### 6.1 窗口大小变化
```javascript
win.addEventListener('resize', function() {
    clearTimeout(tid);
    tid = setTimeout(refreshRem, 300);
}, false);
```

### 6.2 页面显示事件
```javascript
win.addEventListener('pageshow', function(e) {
    if (e.persisted) {
        clearTimeout(tid);
        tid = setTimeout(refreshRem, 300);
    }
}, false);
```

**防抖处理**：使用setTimeout防止频繁触发，提升性能。

## 7. API方法

### 7.1 px转rem
```javascript
flexible.px2rem = function(d) {
    var val = parseFloat(d) / this.rem;
    if (typeof d === 'string' && d.match(/px$/)) {
        val += 'rem';
    }
    return val;
}
```

### 7.2 rem转px
```javascript
flexible.rem2px = function(d) {
    var val = parseFloat(d) * this.rem;
    if (typeof d === 'string' && d.match(/rem$/)) {
        val += 'px';
    }
    return val;
}
```

## 8. 使用示例

### 8.1 HTML引入
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <!-- 不要手动设置viewport，让flexible自动处理 -->
    <script src="flexible.js"></script>
</head>
```

### 8.2 CSS开发
```css
/* 假设设计稿宽度为750px */
.header {
    height: 1.33rem;    /* 设计稿中100px */
    font-size: 0.4rem;  /* 设计稿中30px */
}

/* 计算方法：设计稿px值 / 75 = rem值 */
/* 因为750px屏幕下，1rem = 75px */
```

### 8.3 JavaScript调用
```javascript
// 获取当前配置
console.log('DPR:', window.dpr);
console.log('REM基准:', window.rem);

// 单位转换
var remValue = window.lib.flexible.px2rem('100px');
var pxValue = window.lib.flexible.rem2px('1rem');
```

## 9. 优势与局限

### 9.1 优势
- ✅ **自动适配**：无需手动计算不同设备尺寸
- ✅ **等比缩放**：保证视觉效果一致性
- ✅ **1px问题**：通过viewport缩放解决高DPR设备1px边框问题
- ✅ **简单易用**：引入即用，学习成本低

### 9.2 局限性
- ❌ **字体缩放**：所有元素等比缩放，可能导致小屏幕字体过小
- ❌ **Android兼容**：Android设备统一1倍方案，未充分利用高分辨率
- ❌ **viewport限制**：修改viewport可能影响第三方组件
- ❌ **调试困难**：缩放后的页面调试相对复杂

## 10. 现代替代方案

随着CSS技术发展，现在有更好的替代方案：

### 10.1 vw/vh方案
```css
/* 直接使用视口单位 */
.container {
    width: 100vw;
    height: 50vh;
    font-size: 4vw;
}
```

### 10.2 CSS clamp()
```css
/* 响应式字体 */
.title {
    font-size: clamp(16px, 4vw, 24px);
}
```

### 10.3 Container Queries
```css
/* 容器查询（未来标准） */
@container (min-width: 400px) {
    .card {
        font-size: 1.2rem;
    }
}
```

## 11. 总结

手淘flexible.js在移动端适配历史上具有重要意义，其核心思想至今仍有参考价值。虽然现在有了更现代的解决方案，但理解其原理有助于深入掌握移动端适配技术。

**学习建议**：
1. 理解DPR和viewport的关系
2. 掌握rem单位的计算原理  
3. 了解事件监听和防抖处理
4. 对比现代方案的优劣
5. 在实际项目中选择合适的适配方案
