# Git 命令速查表

## 📋 概述

本速查表提供了 Git 常用命令的快速参考，按功能分类整理，便于日常开发查阅。

## 🚀 基础操作

### 仓库初始化
```bash
# 初始化新仓库
git init

# 克隆远程仓库
git clone <url>
git clone <url> <directory>

# 克隆指定分支
git clone -b <branch> <url>

# 浅克隆（只获取最新提交）
git clone --depth 1 <url>
```

### 配置设置
```bash
# 设置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 查看配置
git config --list
git config user.name

# 设置编辑器
git config --global core.editor vim

# 设置别名
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
```

## 📁 文件操作

### 添加和提交
```bash
# 查看状态
git status
git status -s  # 简洁模式

# 添加文件到暂存区
git add <file>
git add .                    # 添加所有文件
git add *.js                 # 添加所有 js 文件
git add -A                   # 添加所有变更
git add -p                   # 交互式添加

# 提交更改
git commit -m "commit message"
git commit -am "message"     # 添加并提交已跟踪文件
git commit --amend           # 修改最后一次提交
git commit --amend -m "new message"  # 修改提交信息
```

### 文件管理
```bash
# 移除文件
git rm <file>
git rm --cached <file>       # 从暂存区移除，保留工作区
git rm -r <directory>        # 递归删除目录

# 重命名文件
git mv <old-name> <new-name>

# 忽略文件
echo "*.log" >> .gitignore
git add .gitignore
```

## 🌿 分支操作

### 分支管理
```bash
# 查看分支
git branch                   # 本地分支
git branch -r                # 远程分支
git branch -a                # 所有分支
git branch -v                # 显示最后一次提交

# 创建分支
git branch <branch-name>
git checkout -b <branch-name>  # 创建并切换
git switch -c <branch-name>    # Git 2.23+ 新语法

# 切换分支
git checkout <branch-name>
git switch <branch-name>       # Git 2.23+ 新语法

# 删除分支
git branch -d <branch-name>    # 删除已合并分支
git branch -D <branch-name>    # 强制删除分支
git push origin --delete <branch-name>  # 删除远程分支
```

### 分支合并
```bash
# 合并分支
git merge <branch-name>
git merge --no-ff <branch-name>  # 禁用快进合并
git merge --squash <branch-name> # 压缩合并

# 变基操作
git rebase <branch-name>
git rebase -i HEAD~3         # 交互式变基
git rebase --continue        # 继续变基
git rebase --abort           # 取消变基
```

## 🔍 查看历史

### 日志查看
```bash
# 查看提交历史
git log
git log --oneline            # 单行显示
git log --graph              # 图形化显示
git log --stat               # 显示统计信息
git log -p                   # 显示差异
git log -n 5                 # 显示最近 5 次提交

# 格式化日志
git log --pretty=format:"%h - %an, %ar : %s"
git log --since="2 weeks ago"
git log --until="2023-01-01"
git log --author="John"
git log --grep="fix"

# 查看文件历史
git log <file>
git log -p <file>
git blame <file>             # 查看文件每行的修改者
```

### 差异比较
```bash
# 查看差异
git diff                     # 工作区与暂存区
git diff --cached            # 暂存区与最后提交
git diff HEAD                # 工作区与最后提交
git diff <commit1> <commit2> # 两次提交间差异
git diff <branch1> <branch2> # 两个分支间差异

# 查看特定文件差异
git diff <file>
git diff HEAD~1 <file>
```

## 🔄 远程操作

### 远程仓库
```bash
# 查看远程仓库
git remote
git remote -v                # 显示详细信息
git remote show origin       # 显示远程仓库信息

# 添加远程仓库
git remote add <name> <url>
git remote add origin https://github.com/user/repo.git

# 修改远程仓库
git remote set-url origin <new-url>
git remote rename <old-name> <new-name>
git remote remove <name>
```

### 同步操作
```bash
# 获取更新
git fetch                    # 获取所有远程更新
git fetch origin             # 获取指定远程更新
git fetch origin <branch>    # 获取指定分支更新

# 拉取更新
git pull                     # 获取并合并
git pull origin main         # 从指定远程分支拉取
git pull --rebase            # 使用变基方式拉取

# 推送更新
git push                     # 推送当前分支
git push origin main         # 推送到指定远程分支
git push -u origin main      # 推送并设置上游分支
git push --all               # 推送所有分支
git push --tags              # 推送所有标签
```

## 🏷️ 标签操作

### 标签管理
```bash
# 查看标签
git tag
git tag -l "v1.*"            # 列出匹配的标签
git show <tag-name>          # 显示标签信息

# 创建标签
git tag <tag-name>           # 轻量标签
git tag -a <tag-name> -m "message"  # 注释标签
git tag -a <tag-name> <commit-hash>  # 给特定提交打标签

# 推送标签
git push origin <tag-name>
git push origin --tags       # 推送所有标签

# 删除标签
git tag -d <tag-name>        # 删除本地标签
git push origin --delete <tag-name>  # 删除远程标签
```

## ↩️ 撤销操作

### 撤销修改
```bash
# 撤销工作区修改
git checkout -- <file>
git restore <file>           # Git 2.23+ 新语法

# 撤销暂存区修改
git reset HEAD <file>
git restore --staged <file>  # Git 2.23+ 新语法

# 撤销提交
git reset --soft HEAD~1      # 撤销提交，保留暂存区
git reset --mixed HEAD~1     # 撤销提交和暂存区
git reset --hard HEAD~1      # 撤销所有修改

# 创建反向提交
git revert <commit-hash>
git revert HEAD              # 撤销最后一次提交
```

### 数据恢复
```bash
# 查看引用日志
git reflog
git reflog <branch-name>

# 恢复删除的分支
git branch <branch-name> <commit-hash>

# 恢复删除的提交
git cherry-pick <commit-hash>

# 查找丢失的提交
git fsck --lost-found
```

## 🔧 高级操作

### 储藏操作
```bash
# 储藏当前工作
git stash
git stash save "message"
git stash -u                 # 包含未跟踪文件

# 查看储藏
git stash list
git stash show
git stash show -p            # 显示详细差异

# 应用储藏
git stash apply              # 应用最新储藏
git stash apply stash@{2}    # 应用指定储藏
git stash pop                # 应用并删除储藏

# 删除储藏
git stash drop stash@{0}
git stash clear              # 清空所有储藏
```

### 子模块操作
```bash
# 添加子模块
git submodule add <url> <path>

# 初始化子模块
git submodule init
git submodule update
git submodule update --init --recursive

# 更新子模块
git submodule update --remote
git submodule foreach git pull origin main

# 删除子模块
git submodule deinit <path>
git rm <path>
```

### 工作树操作
```bash
# 创建工作树
git worktree add <path> <branch>
git worktree add ../feature-branch feature

# 列出工作树
git worktree list

# 删除工作树
git worktree remove <path>
git worktree prune           # 清理无效工作树
```

## 🛠️ 维护操作

### 仓库维护
```bash
# 垃圾回收
git gc                       # 基本垃圾回收
git gc --aggressive          # 激进垃圾回收
git gc --auto                # 自动垃圾回收

# 检查仓库
git fsck                     # 检查对象完整性
git fsck --full              # 完整检查

# 统计信息
git count-objects            # 对象统计
git count-objects -v         # 详细统计

# 清理操作
git clean -n                 # 预览清理
git clean -f                 # 清理未跟踪文件
git clean -fd                # 清理文件和目录
git clean -fX                # 清理忽略的文件
```

### 配置优化
```bash
# 性能优化
git config --global core.preloadindex true
git config --global core.fscache true
git config --global gc.auto 256

# 网络优化
git config --global http.postBuffer 524288000
git config --global http.maxRequestBuffer 100M

# 别名设置
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'
```

## 📊 实用技巧

### 搜索操作
```bash
# 搜索内容
git grep "search-term"
git grep -n "search-term"    # 显示行号
git grep -i "search-term"    # 忽略大小写

# 搜索提交
git log --grep="keyword"
git log -S "function-name"   # 搜索代码变更
git log -G "regex-pattern"   # 正则搜索
```

### 批量操作
```bash
# 批量删除分支
git branch | grep "feature/" | xargs git branch -d

# 批量推送标签
git tag | xargs git push origin

# 批量更新子模块
git submodule foreach git pull origin main
```

---

这个速查表涵盖了 Git 的主要命令和常用操作，建议收藏备用，在日常开发中快速查阅。
