/**
 * Apollo Server Express 高级示例
 * 包含认证、授权、错误处理、订阅等高级功能
 */

import express from 'express';
import { ApolloServer, AuthenticationError, ForbiddenError, UserInputError } from 'apollo-server-express';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { useServer } from 'graphql-ws/lib/use/ws';
import { makeExecutableSchema } from '@graphql-tools/schema';
import { PubSub } from 'graphql-subscriptions';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import cors from 'cors';
import { gql } from 'apollo-server-express';

// 配置
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const pubsub = new PubSub();

// 类型定义
interface User {
  id: string;
  username: string;
  email: string;
  password: string;
  role: 'ADMIN' | 'USER';
  createdAt: Date;
}

interface Context {
  user?: User;
  token?: string;
}

// Schema 定义
const typeDefs = gql`
  scalar DateTime

  enum UserRole {
    ADMIN
    USER
  }

  type User {
    id: ID!
    username: String!
    email: String!
    role: UserRole!
    createdAt: DateTime!
  }

  type AuthPayload {
    token: String!
    user: User!
  }

  input RegisterInput {
    username: String!
    email: String!
    password: String!
  }

  input LoginInput {
    email: String!
    password: String!
  }

  type Query {
    # 获取当前用户信息
    me: User
    
    # 获取所有用户（仅管理员）
    users: [User!]! @auth(requires: ADMIN)
    
    # 根据ID获取用户
    user(id: ID!): User @auth
  }

  type Mutation {
    # 用户注册
    register(input: RegisterInput!): AuthPayload!
    
    # 用户登录
    login(input: LoginInput!): AuthPayload!
    
    # 更新用户信息
    updateProfile(username: String, email: String): User! @auth
    
    # 删除用户（仅管理员）
    deleteUser(id: ID!): Boolean! @auth(requires: ADMIN)
  }

  type Subscription {
    # 用户注册通知
    userRegistered: User!
    
    # 用户更新通知
    userUpdated: User!
  }

  directive @auth(requires: UserRole = USER) on FIELD_DEFINITION
`;

// 模拟数据库
let users: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    password: '$2a$10$8K1p/a0dURXAm7ZisDXAv.6P5.2N2Z9Z9Z9Z9Z9Z9Z9Z9Z9Z9Z9Z9', // 'admin123'
    role: 'ADMIN',
    createdAt: new Date()
  }
];

let nextId = 2;

// 工具函数
const generateId = () => (nextId++).toString();

const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, 10);
};

const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash);
};

const generateToken = (userId: string): string => {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
};

const verifyToken = (token: string): { userId: string } | null => {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: string };
  } catch {
    return null;
  }
};

// 认证中间件
const getUser = async (token: string): Promise<User | null> => {
  if (!token) return null;
  
  const cleanToken = token.replace('Bearer ', '');
  const decoded = verifyToken(cleanToken);
  
  if (!decoded) return null;
  
  return users.find(user => user.id === decoded.userId) || null;
};

// 授权指令实现
const authDirective = (next: any, source: any, args: any, context: Context, info: any) => {
  const { requires } = info.directives.auth || {};
  
  if (!context.user) {
    throw new AuthenticationError('Authentication required');
  }
  
  if (requires && context.user.role !== requires) {
    throw new ForbiddenError('Insufficient permissions');
  }
  
  return next();
};

// Resolvers
const resolvers = {
  DateTime: {
    serialize: (value: Date) => value.toISOString(),
    parseValue: (value: string) => new Date(value),
    parseLiteral: (ast: any) => new Date(ast.value)
  },

  Query: {
    me: (parent: any, args: any, context: Context) => {
      if (!context.user) {
        throw new AuthenticationError('Authentication required');
      }
      return context.user;
    },

    users: (parent: any, args: any, context: Context) => {
      if (!context.user || context.user.role !== 'ADMIN') {
        throw new ForbiddenError('Admin access required');
      }
      return users.map(user => ({ ...user, password: undefined }));
    },

    user: (parent: any, { id }: { id: string }, context: Context) => {
      if (!context.user) {
        throw new AuthenticationError('Authentication required');
      }
      
      const user = users.find(u => u.id === id);
      if (!user) {
        throw new UserInputError('User not found');
      }
      
      return { ...user, password: undefined };
    }
  },

  Mutation: {
    register: async (parent: any, { input }: { input: any }) => {
      const { username, email, password } = input;
      
      // 验证输入
      if (!username || username.length < 3) {
        throw new UserInputError('Username must be at least 3 characters');
      }
      
      if (!email.includes('@')) {
        throw new UserInputError('Invalid email format');
      }
      
      if (!password || password.length < 6) {
        throw new UserInputError('Password must be at least 6 characters');
      }
      
      // 检查用户是否已存在
      if (users.find(u => u.email === email)) {
        throw new UserInputError('User with this email already exists');
      }
      
      // 创建新用户
      const hashedPassword = await hashPassword(password);
      const newUser: User = {
        id: generateId(),
        username,
        email,
        password: hashedPassword,
        role: 'USER',
        createdAt: new Date()
      };
      
      users.push(newUser);
      
      // 发布订阅事件
      pubsub.publish('USER_REGISTERED', { 
        userRegistered: { ...newUser, password: undefined } 
      });
      
      // 生成 token
      const token = generateToken(newUser.id);
      
      return {
        token,
        user: { ...newUser, password: undefined }
      };
    },

    login: async (parent: any, { input }: { input: any }) => {
      const { email, password } = input;
      
      // 查找用户
      const user = users.find(u => u.email === email);
      if (!user) {
        throw new AuthenticationError('Invalid credentials');
      }
      
      // 验证密码
      const isValid = await comparePassword(password, user.password);
      if (!isValid) {
        throw new AuthenticationError('Invalid credentials');
      }
      
      // 生成 token
      const token = generateToken(user.id);
      
      return {
        token,
        user: { ...user, password: undefined }
      };
    },

    updateProfile: async (parent: any, { username, email }: { username?: string; email?: string }, context: Context) => {
      if (!context.user) {
        throw new AuthenticationError('Authentication required');
      }
      
      const userIndex = users.findIndex(u => u.id === context.user!.id);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      // 更新用户信息
      if (username) users[userIndex].username = username;
      if (email) {
        // 检查邮箱是否已被使用
        if (users.find(u => u.email === email && u.id !== context.user!.id)) {
          throw new UserInputError('Email already in use');
        }
        users[userIndex].email = email;
      }
      
      const updatedUser = { ...users[userIndex], password: undefined };
      
      // 发布订阅事件
      pubsub.publish('USER_UPDATED', { userUpdated: updatedUser });
      
      return updatedUser;
    },

    deleteUser: (parent: any, { id }: { id: string }, context: Context) => {
      if (!context.user || context.user.role !== 'ADMIN') {
        throw new ForbiddenError('Admin access required');
      }
      
      const userIndex = users.findIndex(u => u.id === id);
      if (userIndex === -1) {
        throw new UserInputError('User not found');
      }
      
      users.splice(userIndex, 1);
      return true;
    }
  },

  Subscription: {
    userRegistered: {
      subscribe: () => pubsub.asyncIterator(['USER_REGISTERED'])
    },
    
    userUpdated: {
      subscribe: () => pubsub.asyncIterator(['USER_UPDATED'])
    }
  }
};

// 创建上下文
const createContext = async ({ req }: { req: express.Request }): Promise<Context> => {
  const token = req.headers.authorization || '';
  const user = await getUser(token);
  
  return { user, token };
};

// 启动服务器
async function startAdvancedServer() {
  const app = express();
  
  // CORS 配置
  app.use(cors({
    origin: ['http://localhost:3000', 'http://localhost:5173'],
    credentials: true
  }));
  
  // 创建可执行 Schema
  const schema = makeExecutableSchema({
    typeDefs,
    resolvers
  });
  
  // 创建 Apollo Server
  const server = new ApolloServer({
    schema,
    context: createContext,
    introspection: true,
    formatError: (error) => {
      console.error('GraphQL Error:', error);
      return error;
    }
  });
  
  await server.start();
  server.applyMiddleware({ app, path: '/graphql', cors: false });
  
  // HTTP 服务器
  const httpServer = createServer(app);
  
  // WebSocket 服务器
  const wsServer = new WebSocketServer({
    server: httpServer,
    path: '/graphql'
  });
  
  // GraphQL WebSocket 服务器
  useServer({
    schema,
    context: async (ctx) => {
      const token = ctx.connectionParams?.authorization as string || '';
      const user = await getUser(token);
      return { user, token };
    }
  }, wsServer);
  
  const PORT = process.env.PORT || 4001;
  httpServer.listen(PORT, () => {
    console.log(`🚀 Advanced server ready at http://localhost:${PORT}/graphql`);
    console.log(`🔌 Subscriptions ready at ws://localhost:${PORT}/graphql`);
  });
}

export { startAdvancedServer, typeDefs, resolvers };
