# 单一职责原则详解与实例

## 🎯 概念深入理解

### 核心定义

**单一职责原则 (Single Responsibility Principle, SRP)**：一个类应该只有一个引起它变化的原因。

### 多角度理解

#### 1. 从"变化原因"角度

```
职责 = 变化的原因
- 如果一个类因为多个不同的原因需要修改，说明它承担了多个职责
- 每个职责都是一个变化的轴心
```

#### 2. 从"用户群体"角度

```
职责 = 服务的用户群体
- 不同的用户群体有不同的需求
- 一个类应该只服务于一个用户群体
```

#### 3. 从"内聚性"角度

```
职责 = 高度相关的功能集合
- 类内的方法应该为了同一个目标而协作
- 如果方法之间没有关联，可能承担了多个职责
```

---

## 📚 详细实例分析

### 实例 1: 员工管理系统

#### ❌ 违反 SRP 的设计

```javascript
class Employee {
	constructor(name, id, salary, department) {
		this.name = name
		this.id = id
		this.salary = salary
		this.department = department
	}

	// 职责1: 员工基本信息管理
	getName() {
		return this.name
	}
	getId() {
		return this.id
	}
	getSalary() {
		return this.salary
	}
	getDepartment() {
		return this.department
	}

	// 职责2: 薪资计算
	calculatePay() {
		if (this.department === 'IT') {
			return this.salary * 1.2 // IT部门有20%奖金
		} else if (this.department === 'Sales') {
			return this.salary * 1.15 // 销售部门有15%奖金
		}
		return this.salary
	}

	// 职责3: 工作时间管理
	calculateWorkHours() {
		// 复杂的工时计算逻辑
		return 40 // 简化示例
	}

	// 职责4: 数据持久化
	save() {
		database.execute(`
            INSERT INTO employees (name, id, salary, department) 
            VALUES ('${this.name}', ${this.id}, ${this.salary}, '${this.department}')
        `)
	}

	// 职责5: 报表生成
	generatePayrollReport() {
		return `员工: ${this.name}, 部门: ${
			this.department
		}, 应付薪资: ${this.calculatePay()}`
	}

	// 职责6: 邮件通知
	sendPayslip() {
		const payslip = this.generatePayrollReport()
		emailService.send(this.email, '工资单', payslip)
	}
}
```

**问题分析**：

1. **HR 部门**修改员工信息格式 → 需要修改 Employee 类
2. **财务部门**修改薪资计算规则 → 需要修改 Employee 类
3. **IT 部门**修改数据库结构 → 需要修改 Employee 类
4. **管理层**修改报表格式 → 需要修改 Employee 类

#### ✅ 遵循 SRP 的设计

```javascript
// 职责1: 员工基本信息管理
class Employee {
	constructor(name, id, salary, department) {
		this.name = name
		this.id = id
		this.salary = salary
		this.department = department
	}

	getName() {
		return this.name
	}
	getId() {
		return this.id
	}
	getSalary() {
		return this.salary
	}
	getDepartment() {
		return this.department
	}
}

// 职责2: 薪资计算
class PayrollCalculator {
	static calculatePay(employee) {
		const baseSalary = employee.getSalary()
		const department = employee.getDepartment()

		switch (department) {
			case 'IT':
				return baseSalary * 1.2
			case 'Sales':
				return baseSalary * 1.15
			default:
				return baseSalary
		}
	}
}

// 职责3: 工作时间管理
class TimeTracker {
	static calculateWorkHours(employeeId) {
		// 复杂的工时计算逻辑
		return 40
	}
}

// 职责4: 数据持久化
class EmployeeRepository {
	static save(employee) {
		database.execute(
			`
            INSERT INTO employees (name, id, salary, department) 
            VALUES (?, ?, ?, ?)
        `,
			[
				employee.getName(),
				employee.getId(),
				employee.getSalary(),
				employee.getDepartment(),
			]
		)
	}

	static findById(id) {
		// 查询逻辑
	}
}

// 职责5: 报表生成
class PayrollReportGenerator {
	static generateReport(employee) {
		const pay = PayrollCalculator.calculatePay(employee)
		return `员工: ${employee.getName()}, 部门: ${employee.getDepartment()}, 应付薪资: ${pay}`
	}
}

// 职责6: 邮件通知
class PayslipNotificationService {
	static sendPayslip(employee) {
		const payslip = PayrollReportGenerator.generateReport(employee)
		emailService.send(employee.email, '工资单', payslip)
	}
}
```

---

### 实例 2: 电商订单系统

#### ❌ 违反 SRP 的设计

```javascript
class Order {
	constructor(customerId, items) {
		this.id = Date.now()
		this.customerId = customerId
		this.items = items
		this.status = 'pending'
		this.createdAt = new Date()
	}

	// 职责1: 订单数据管理
	getId() {
		return this.id
	}
	getCustomerId() {
		return this.customerId
	}
	getItems() {
		return this.items
	}
	getStatus() {
		return this.status
	}

	// 职责2: 价格计算
	calculateTotal() {
		let total = 0
		for (const item of this.items) {
			total += item.price * item.quantity
		}

		// 应用折扣
		if (total > 100) {
			total *= 0.9 // 9折优惠
		}

		// 计算税费
		total += total * 0.08 // 8%税费

		return total
	}

	// 职责3: 库存管理
	checkInventory() {
		for (const item of this.items) {
			const stock = inventoryService.getStock(item.productId)
			if (stock < item.quantity) {
				throw new Error(`商品 ${item.productId} 库存不足`)
			}
		}
	}

	updateInventory() {
		for (const item of this.items) {
			inventoryService.reduceStock(item.productId, item.quantity)
		}
	}

	// 职责4: 支付处理
	processPayment(paymentInfo) {
		const total = this.calculateTotal()

		if (paymentInfo.type === 'credit_card') {
			return creditCardService.charge(paymentInfo.cardNumber, total)
		} else if (paymentInfo.type === 'paypal') {
			return paypalService.charge(paymentInfo.email, total)
		}

		throw new Error('不支持的支付方式')
	}

	// 职责5: 订单状态管理
	confirm() {
		this.status = 'confirmed'
		this.confirmedAt = new Date()
	}

	ship() {
		this.status = 'shipped'
		this.shippedAt = new Date()
	}

	complete() {
		this.status = 'completed'
		this.completedAt = new Date()
	}

	// 职责6: 通知服务
	sendConfirmationEmail() {
		const customer = customerService.getById(this.customerId)
		const emailContent = `您的订单 ${
			this.id
		} 已确认，总金额: ${this.calculateTotal()}`
		emailService.send(customer.email, '订单确认', emailContent)
	}

	sendShippingNotification() {
		const customer = customerService.getById(this.customerId)
		emailService.send(
			customer.email,
			'订单发货',
			`您的订单 ${this.id} 已发货`
		)
	}

	// 职责7: 数据持久化
	save() {
		database.orders.insert(this)
	}

	// 职责8: 报表和分析
	generateInvoice() {
		return {
			orderId: this.id,
			customerId: this.customerId,
			items: this.items,
			total: this.calculateTotal(),
			createdAt: this.createdAt,
		}
	}
}
```

#### ✅ 遵循 SRP 的设计

```javascript
// 职责1: 订单数据管理
class Order {
	constructor(customerId, items) {
		this.id = Date.now()
		this.customerId = customerId
		this.items = items
		this.status = 'pending'
		this.createdAt = new Date()
	}

	getId() {
		return this.id
	}
	getCustomerId() {
		return this.customerId
	}
	getItems() {
		return this.items
	}
	getStatus() {
		return this.status
	}

	setStatus(status) {
		this.status = status
	}
}

// 职责2: 价格计算
class OrderPriceCalculator {
	static calculateTotal(order) {
		let total = 0
		for (const item of order.getItems()) {
			total += item.price * item.quantity
		}
		return total
	}

	static applyDiscounts(total) {
		if (total > 100) {
			return total * 0.9
		}
		return total
	}

	static calculateTax(total) {
		return total * 0.08
	}

	static getFinalTotal(order) {
		let total = this.calculateTotal(order)
		total = this.applyDiscounts(total)
		total += this.calculateTax(total)
		return total
	}
}

// 职责3: 库存管理
class InventoryManager {
	static checkAvailability(order) {
		for (const item of order.getItems()) {
			const stock = inventoryService.getStock(item.productId)
			if (stock < item.quantity) {
				throw new Error(`商品 ${item.productId} 库存不足`)
			}
		}
	}

	static reserveItems(order) {
		for (const item of order.getItems()) {
			inventoryService.reserve(item.productId, item.quantity)
		}
	}

	static updateStock(order) {
		for (const item of order.getItems()) {
			inventoryService.reduceStock(item.productId, item.quantity)
		}
	}
}

// 职责4: 支付处理
class PaymentProcessor {
	static process(order, paymentInfo) {
		const total = OrderPriceCalculator.getFinalTotal(order)

		const strategy = PaymentStrategyFactory.create(paymentInfo.type)
		return strategy.charge(paymentInfo, total)
	}
}

class PaymentStrategyFactory {
	static create(type) {
		switch (type) {
			case 'credit_card':
				return new CreditCardPaymentStrategy()
			case 'paypal':
				return new PayPalPaymentStrategy()
			default:
				throw new Error('不支持的支付方式')
		}
	}
}

// 职责5: 订单状态管理
class OrderStatusManager {
	static confirm(order) {
		order.setStatus('confirmed')
		order.confirmedAt = new Date()
	}

	static ship(order) {
		order.setStatus('shipped')
		order.shippedAt = new Date()
	}

	static complete(order) {
		order.setStatus('completed')
		order.completedAt = new Date()
	}
}

// 职责6: 通知服务
class OrderNotificationService {
	static sendConfirmation(order) {
		const customer = customerService.getById(order.getCustomerId())
		const total = OrderPriceCalculator.getFinalTotal(order)
		const content = `您的订单 ${order.getId()} 已确认，总金额: ${total}`
		emailService.send(customer.email, '订单确认', content)
	}

	static sendShippingNotification(order) {
		const customer = customerService.getById(order.getCustomerId())
		const content = `您的订单 ${order.getId()} 已发货`
		emailService.send(customer.email, '订单发货', content)
	}
}

// 职责7: 数据持久化
class OrderRepository {
	static save(order) {
		return database.orders.insert(order)
	}

	static findById(id) {
		return database.orders.findById(id)
	}

	static findByCustomerId(customerId) {
		return database.orders.findByCustomerId(customerId)
	}
}

// 职责8: 报表和分析
class OrderReportGenerator {
	static generateInvoice(order) {
		return {
			orderId: order.getId(),
			customerId: order.getCustomerId(),
			items: order.getItems(),
			total: OrderPriceCalculator.getFinalTotal(order),
			createdAt: order.createdAt,
		}
	}

	static generateSalesReport(orders) {
		// 销售报表逻辑
	}
}

// 职责9: 订单处理协调器
class OrderService {
	constructor(
		inventoryManager,
		paymentProcessor,
		statusManager,
		notificationService,
		repository
	) {
		this.inventoryManager = inventoryManager
		this.paymentProcessor = paymentProcessor
		this.statusManager = statusManager
		this.notificationService = notificationService
		this.repository = repository
	}

	async processOrder(order, paymentInfo) {
		try {
			// 检查库存
			this.inventoryManager.checkAvailability(order)

			// 预留库存
			this.inventoryManager.reserveItems(order)

			// 处理支付
			const paymentResult = await this.paymentProcessor.process(
				order,
				paymentInfo
			)

			if (paymentResult.success) {
				// 确认订单
				this.statusManager.confirm(order)

				// 更新库存
				this.inventoryManager.updateStock(order)

				// 保存订单
				await this.repository.save(order)

				// 发送确认邮件
				this.notificationService.sendConfirmation(order)

				return { success: true, orderId: order.getId() }
			} else {
				throw new Error('支付失败')
			}
		} catch (error) {
			// 释放预留库存
			this.inventoryManager.releaseReservation(order)
			throw error
		}
	}
}
```

---

## 🎯 更多实例场景

### 实例 3: 用户认证系统

#### ❌ 违反 SRP

```javascript
class UserAuthenticator {
	// 混合了认证、授权、日志、缓存等多个职责
	authenticate(username, password) {
		// 验证逻辑
		// 权限检查
		// 日志记录
		// 缓存管理
		// 会话管理
	}
}
```

#### ✅ 遵循 SRP

```javascript
class UserAuthenticator {
	authenticate(username, password) {
		/* 只负责认证 */
	}
}

class UserAuthorizer {
	authorize(user, resource) {
		/* 只负责授权 */
	}
}

class AuthenticationLogger {
	log(event) {
		/* 只负责日志 */
	}
}

class SessionManager {
	createSession(user) {
		/* 只负责会话 */
	}
}
```

### 实例 4: 文件处理系统

#### ❌ 违反 SRP

```javascript
class FileProcessor {
	// 混合了文件读取、解析、验证、转换、保存等职责
	processFile(filePath) {
		// 读取文件
		// 解析内容
		// 验证格式
		// 数据转换
		// 保存结果
		// 发送通知
	}
}
```

#### ✅ 遵循 SRP

```javascript
class FileReader {
	read(filePath) {
		/* 只负责读取 */
	}
}

class FileParser {
	parse(content) {
		/* 只负责解析 */
	}
}

class DataValidator {
	validate(data) {
		/* 只负责验证 */
	}
}

class DataTransformer {
	transform(data) {
		/* 只负责转换 */
	}
}

class FileWriter {
	write(data, path) {
		/* 只负责写入 */
	}
}
```

---

## 🚀 实践指导原则

### 1. 识别职责的方法

#### 方法一：变化原因分析

```
问自己：这个类会因为什么原因需要修改？
- 如果有多个不相关的原因，需要拆分
```

#### 方法二：用户群体分析

```
问自己：这个类服务于哪些用户群体？
- HR部门、财务部门、IT部门等
- 不同群体的需求变化应该影响不同的类
```

#### 方法三：内聚性分析

```
问自己：类中的方法是否为了同一个目标协作？
- 如果方法之间没有关联，可能需要拆分
```

### 2. 重构策略

#### 渐进式重构

```javascript
// 第一步：识别职责
// 第二步：提取最明显的职责
// 第三步：逐步细化
// 第四步：验证和调整
```

#### 保持平衡

```
不要过度拆分：
- 考虑项目规模
- 考虑团队能力
- 考虑维护成本
```

---

## 🔍 更多概念和实例

### 概念 5: 日志系统设计

#### ❌ 违反 SRP 的设计

```javascript
class Logger {
	constructor() {
		this.logs = []
	}

	// 职责1: 日志记录
	log(level, message) {
		const logEntry = {
			timestamp: new Date(),
			level: level,
			message: message,
		}
		this.logs.push(logEntry)
	}

	// 职责2: 日志格式化
	formatLog(logEntry) {
		return `[${logEntry.timestamp.toISOString()}] ${logEntry.level.toUpperCase()}: ${
			logEntry.message
		}`
	}

	// 职责3: 日志输出到控制台
	printToConsole() {
		this.logs.forEach((log) => {
			console.log(this.formatLog(log))
		})
	}

	// 职责4: 日志保存到文件
	saveToFile(filename) {
		const content = this.logs.map((log) => this.formatLog(log)).join('\n')
		fs.writeFileSync(filename, content)
	}

	// 职责5: 日志发送到远程服务器
	sendToServer(endpoint) {
		const payload = {
			logs: this.logs.map((log) => this.formatLog(log)),
		}
		fetch(endpoint, {
			method: 'POST',
			body: JSON.stringify(payload),
		})
	}

	// 职责6: 日志过滤
	filterByLevel(level) {
		return this.logs.filter((log) => log.level === level)
	}

	// 职责7: 日志统计
	getStatistics() {
		const stats = {}
		this.logs.forEach((log) => {
			stats[log.level] = (stats[log.level] || 0) + 1
		})
		return stats
	}
}
```

#### ✅ 遵循 SRP 的设计

```javascript
// 职责1: 日志条目数据结构
class LogEntry {
	constructor(level, message, timestamp = new Date()) {
		this.level = level
		this.message = message
		this.timestamp = timestamp
	}
}

// 职责2: 日志存储
class LogStorage {
	constructor() {
		this.logs = []
	}

	add(logEntry) {
		this.logs.push(logEntry)
	}

	getAll() {
		return [...this.logs]
	}

	clear() {
		this.logs = []
	}
}

// 职责3: 日志格式化
class LogFormatter {
	static format(logEntry) {
		return `[${logEntry.timestamp.toISOString()}] ${logEntry.level.toUpperCase()}: ${
			logEntry.message
		}`
	}

	static formatJson(logEntry) {
		return JSON.stringify(logEntry)
	}
}

// 职责4: 日志输出策略
class ConsoleLogWriter {
	write(logEntry) {
		console.log(LogFormatter.format(logEntry))
	}
}

class FileLogWriter {
	constructor(filename) {
		this.filename = filename
	}

	write(logEntry) {
		const content = LogFormatter.format(logEntry) + '\n'
		fs.appendFileSync(this.filename, content)
	}
}

class RemoteLogWriter {
	constructor(endpoint) {
		this.endpoint = endpoint
	}

	async write(logEntry) {
		await fetch(this.endpoint, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: LogFormatter.formatJson(logEntry),
		})
	}
}

// 职责5: 日志过滤
class LogFilter {
	static byLevel(logs, level) {
		return logs.filter((log) => log.level === level)
	}

	static byTimeRange(logs, startTime, endTime) {
		return logs.filter(
			(log) => log.timestamp >= startTime && log.timestamp <= endTime
		)
	}
}

// 职责6: 日志统计
class LogAnalyzer {
	static getStatistics(logs) {
		const stats = {}
		logs.forEach((log) => {
			stats[log.level] = (stats[log.level] || 0) + 1
		})
		return stats
	}

	static getErrorRate(logs) {
		const total = logs.length
		const errors = logs.filter((log) => log.level === 'error').length
		return total > 0 ? (errors / total) * 100 : 0
	}
}

// 职责7: 日志管理器（协调各个组件）
class Logger {
	constructor() {
		this.storage = new LogStorage()
		this.writers = []
	}

	addWriter(writer) {
		this.writers.push(writer)
	}

	log(level, message) {
		const logEntry = new LogEntry(level, message)
		this.storage.add(logEntry)

		// 写入到所有配置的输出目标
		this.writers.forEach((writer) => writer.write(logEntry))
	}

	getLogs() {
		return this.storage.getAll()
	}
}

// 使用示例
const logger = new Logger()
logger.addWriter(new ConsoleLogWriter())
logger.addWriter(new FileLogWriter('app.log'))
logger.addWriter(new RemoteLogWriter('https://api.example.com/logs'))

logger.log('info', '应用启动')
logger.log('error', '数据库连接失败')
```

### 概念 6: 数据验证系统

#### ❌ 违反 SRP 的设计

```javascript
class UserValidator {
	// 混合了多种验证规则和验证逻辑
	validateUser(userData) {
		const errors = []

		// 邮箱验证
		if (!userData.email) {
			errors.push('邮箱不能为空')
		} else if (!this.isValidEmail(userData.email)) {
			errors.push('邮箱格式不正确')
		} else if (this.isEmailExists(userData.email)) {
			errors.push('邮箱已存在')
		}

		// 密码验证
		if (!userData.password) {
			errors.push('密码不能为空')
		} else if (userData.password.length < 8) {
			errors.push('密码长度至少8位')
		} else if (!this.hasUpperCase(userData.password)) {
			errors.push('密码必须包含大写字母')
		} else if (!this.hasNumber(userData.password)) {
			errors.push('密码必须包含数字')
		}

		// 年龄验证
		if (!userData.age) {
			errors.push('年龄不能为空')
		} else if (userData.age < 18) {
			errors.push('年龄必须大于18岁')
		} else if (userData.age > 120) {
			errors.push('年龄不能超过120岁')
		}

		// 手机号验证
		if (!userData.phone) {
			errors.push('手机号不能为空')
		} else if (!this.isValidPhone(userData.phone)) {
			errors.push('手机号格式不正确')
		}

		return {
			isValid: errors.length === 0,
			errors: errors,
		}
	}

	// 各种验证方法混在一起
	isValidEmail(email) {
		/* ... */
	}
	isEmailExists(email) {
		/* ... */
	}
	hasUpperCase(password) {
		/* ... */
	}
	hasNumber(password) {
		/* ... */
	}
	isValidPhone(phone) {
		/* ... */
	}
}
```

#### ✅ 遵循 SRP 的设计

```javascript
// 职责1: 邮箱验证
class EmailValidator {
	static isValidFormat(email) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
		return emailRegex.test(email)
	}

	static async isUnique(email) {
		// 检查数据库中是否已存在
		const existingUser = await userRepository.findByEmail(email)
		return !existingUser
	}
}

// 职责2: 密码验证
class PasswordValidator {
	static hasMinLength(password, minLength = 8) {
		return password.length >= minLength
	}

	static hasUpperCase(password) {
		return /[A-Z]/.test(password)
	}

	static hasLowerCase(password) {
		return /[a-z]/.test(password)
	}

	static hasNumber(password) {
		return /\d/.test(password)
	}

	static hasSpecialChar(password) {
		return /[!@#$%^&*(),.?":{}|<>]/.test(password)
	}
}

// 职责3: 年龄验证
class AgeValidator {
	static isValidAge(age) {
		return age >= 18 && age <= 120
	}

	static isAdult(age) {
		return age >= 18
	}
}

// 职责4: 手机号验证
class PhoneValidator {
	static isValidFormat(phone) {
		const phoneRegex = /^1[3-9]\d{9}$/
		return phoneRegex.test(phone)
	}
}

// 职责5: 验证规则定义
class ValidationRule {
	constructor(field, validator, message) {
		this.field = field
		this.validator = validator
		this.message = message
	}

	async validate(value) {
		const isValid = await this.validator(value)
		return {
			isValid,
			message: isValid ? null : this.message,
		}
	}
}

// 职责6: 验证结果收集
class ValidationResult {
	constructor() {
		this.errors = []
	}

	addError(field, message) {
		this.errors.push({ field, message })
	}

	isValid() {
		return this.errors.length === 0
	}

	getErrors() {
		return this.errors
	}
}

// 职责7: 验证器协调
class UserValidator {
	constructor() {
		this.rules = [
			new ValidationRule('email', (email) => !!email, '邮箱不能为空'),
			new ValidationRule(
				'email',
				EmailValidator.isValidFormat,
				'邮箱格式不正确'
			),
			new ValidationRule('email', EmailValidator.isUnique, '邮箱已存在'),

			new ValidationRule('password', (pwd) => !!pwd, '密码不能为空'),
			new ValidationRule(
				'password',
				(pwd) => PasswordValidator.hasMinLength(pwd),
				'密码长度至少8位'
			),
			new ValidationRule(
				'password',
				PasswordValidator.hasUpperCase,
				'密码必须包含大写字母'
			),
			new ValidationRule(
				'password',
				PasswordValidator.hasNumber,
				'密码必须包含数字'
			),

			new ValidationRule('age', (age) => !!age, '年龄不能为空'),
			new ValidationRule(
				'age',
				AgeValidator.isValidAge,
				'年龄必须在18-120之间'
			),

			new ValidationRule('phone', (phone) => !!phone, '手机号不能为空'),
			new ValidationRule(
				'phone',
				PhoneValidator.isValidFormat,
				'手机号格式不正确'
			),
		]
	}

	async validate(userData) {
		const result = new ValidationResult()

		for (const rule of this.rules) {
			const value = userData[rule.field]
			const validation = await rule.validate(value)

			if (!validation.isValid) {
				result.addError(rule.field, validation.message)
			}
		}

		return result
	}
}
```

---

## 🎯 实际应用场景总结

### 1. Web 应用开发

-   **控制器**：只处理 HTTP 请求和响应
-   **服务层**：只处理业务逻辑
-   **数据访问层**：只处理数据库操作
-   **验证层**：只处理数据验证

### 2. 游戏开发

-   **渲染器**：只负责图形渲染
-   **物理引擎**：只负责物理计算
-   **音频管理器**：只负责音频播放
-   **输入处理器**：只负责用户输入

### 3. 数据处理系统

-   **数据读取器**：只负责数据读取
-   **数据解析器**：只负责数据解析
-   **数据转换器**：只负责数据转换
-   **数据写入器**：只负责数据写入

## 🎉 总结

单一职责原则是面向对象设计的基础，它帮助我们：

1. **提高代码的可维护性**：每个类职责单一，修改影响范围小
2. **提高代码的可测试性**：职责单一的类更容易编写单元测试
3. **提高代码的可复用性**：单一职责的类更容易在其他场景中复用
4. **降低代码的耦合度**：职责分离减少了类之间的依赖关系

记住：**一个类应该只有一个引起它变化的原因**
