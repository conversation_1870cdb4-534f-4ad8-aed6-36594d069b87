# 编码原则实例代码

## 目录
1. [OAOO/DRY 原则示例](#oaoodry-原则示例)
2. [KISS 原则示例](#kiss-原则示例)
3. [SOLID 原则示例](#solid-原则示例)

---

## OAOO/DRY 原则示例

### ❌ 违反 OAOO 原则的代码

```javascript
// 重复的验证逻辑
function validateUserEmail(email) {
    if (!email) return false;
    if (!email.includes('@')) return false;
    if (email.length < 5) return false;
    return true;
}

function validateAdminEmail(email) {
    if (!email) return false;
    if (!email.includes('@')) return false;
    if (email.length < 5) return false;
    // 额外的管理员验证
    if (!email.endsWith('@company.com')) return false;
    return true;
}

// 重复的数据库连接代码
function getUserById(id) {
    const connection = mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: 'password',
        database: 'myapp'
    });
    // 查询逻辑...
}

function getOrderById(id) {
    const connection = mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: 'password',
        database: 'myapp'
    });
    // 查询逻辑...
}
```

### ✅ 遵循 OAOO 原则的代码

```javascript
// 抽取通用的邮箱验证逻辑
class EmailValidator {
    static isValidBasic(email) {
        if (!email) return false;
        if (!email.includes('@')) return false;
        if (email.length < 5) return false;
        return true;
    }
    
    static isValidUser(email) {
        return this.isValidBasic(email);
    }
    
    static isValidAdmin(email) {
        return this.isValidBasic(email) && email.endsWith('@company.com');
    }
}

// 抽取数据库连接配置
class DatabaseConnection {
    static getConnection() {
        return mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || 'password',
            database: process.env.DB_NAME || 'myapp'
        });
    }
}

// 使用抽取的组件
function getUserById(id) {
    const connection = DatabaseConnection.getConnection();
    // 查询逻辑...
}

function getOrderById(id) {
    const connection = DatabaseConnection.getConnection();
    // 查询逻辑...
}
```

---

## KISS 原则示例

### ❌ 违反 KISS 原则的代码

```javascript
// 过度复杂的状态管理
class ComplexUserManager {
    constructor() {
        this.users = new Map();
        this.userStates = new WeakMap();
        this.observers = [];
        this.middleware = [];
        this.plugins = new Set();
    }
    
    addUser(userData) {
        // 复杂的中间件链
        let processedData = userData;
        for (const middleware of this.middleware) {
            processedData = middleware.process(processedData);
        }
        
        // 复杂的状态管理
        const userState = {
            status: 'pending',
            metadata: new Map(),
            history: [],
            flags: new Set()
        };
        
        // 复杂的插件系统
        for (const plugin of this.plugins) {
            plugin.beforeAdd(processedData, userState);
        }
        
        // 实际添加用户...
    }
}

// 过度抽象的工具函数
function createAdvancedProcessor(config) {
    return {
        process: (data) => {
            const pipeline = config.steps.map(step => {
                return createStepProcessor(step.type, step.options);
            });
            
            return pipeline.reduce((acc, processor) => {
                return processor.transform(acc, config.context);
            }, data);
        }
    };
}
```

### ✅ 遵循 KISS 原则的代码

```javascript
// 简单直接的用户管理
class SimpleUserManager {
    constructor() {
        this.users = [];
    }
    
    addUser(userData) {
        // 简单的验证
        if (!userData.email || !userData.name) {
            throw new Error('Email and name are required');
        }
        
        // 创建用户对象
        const user = {
            id: Date.now(),
            email: userData.email,
            name: userData.name,
            createdAt: new Date()
        };
        
        this.users.push(user);
        return user;
    }
    
    getUserById(id) {
        return this.users.find(user => user.id === id);
    }
    
    removeUser(id) {
        const index = this.users.findIndex(user => user.id === id);
        if (index !== -1) {
            this.users.splice(index, 1);
            return true;
        }
        return false;
    }
}

// 简单的数据处理函数
function processUserData(userData) {
    // 清理数据
    const cleaned = {
        email: userData.email?.trim().toLowerCase(),
        name: userData.name?.trim(),
        age: parseInt(userData.age) || 0
    };
    
    // 验证数据
    if (!cleaned.email || !cleaned.name) {
        throw new Error('Invalid user data');
    }
    
    return cleaned;
}
```

---

## SOLID 原则示例

### S - 单一职责原则

#### ❌ 违反 SRP 的代码

```javascript
// 一个类承担了太多职责
class User {
    constructor(name, email) {
        this.name = name;
        this.email = email;
    }
    
    // 用户数据验证
    validate() {
        if (!this.email.includes('@')) {
            throw new Error('Invalid email');
        }
    }
    
    // 数据库操作
    save() {
        const query = `INSERT INTO users (name, email) VALUES ('${this.name}', '${this.email}')`;
        database.execute(query);
    }
    
    // 邮件发送
    sendWelcomeEmail() {
        const emailContent = `Welcome ${this.name}!`;
        emailService.send(this.email, emailContent);
    }
    
    // 报表生成
    generateReport() {
        return `User Report: ${this.name} - ${this.email}`;
    }
}
```

#### ✅ 遵循 SRP 的代码

```javascript
// 每个类只负责一个职责
class User {
    constructor(name, email) {
        this.name = name;
        this.email = email;
    }
}

class UserValidator {
    static validate(user) {
        if (!user.email.includes('@')) {
            throw new Error('Invalid email');
        }
        return true;
    }
}

class UserRepository {
    static save(user) {
        const query = `INSERT INTO users (name, email) VALUES (?, ?)`;
        return database.execute(query, [user.name, user.email]);
    }
}

class UserEmailService {
    static sendWelcomeEmail(user) {
        const emailContent = `Welcome ${user.name}!`;
        return emailService.send(user.email, emailContent);
    }
}

class UserReportGenerator {
    static generateReport(user) {
        return `User Report: ${user.name} - ${user.email}`;
    }
}
```

### O - 开闭原则

#### ✅ 遵循 OCP 的代码

```javascript
// 定义抽象基类
class PaymentProcessor {
    process(amount) {
        throw new Error('Must implement process method');
    }
}

// 具体实现 - 对扩展开放
class CreditCardProcessor extends PaymentProcessor {
    process(amount) {
        console.log(`Processing $${amount} via Credit Card`);
        // 信用卡处理逻辑
    }
}

class PayPalProcessor extends PaymentProcessor {
    process(amount) {
        console.log(`Processing $${amount} via PayPal`);
        // PayPal处理逻辑
    }
}

class AlipayProcessor extends PaymentProcessor {
    process(amount) {
        console.log(`Processing $${amount} via Alipay`);
        // 支付宝处理逻辑
    }
}

// 支付管理器 - 对修改封闭
class PaymentManager {
    constructor() {
        this.processors = new Map();
    }
    
    registerProcessor(type, processor) {
        this.processors.set(type, processor);
    }
    
    processPayment(type, amount) {
        const processor = this.processors.get(type);
        if (!processor) {
            throw new Error(`Unknown payment type: ${type}`);
        }
        return processor.process(amount);
    }
}

// 使用示例
const paymentManager = new PaymentManager();
paymentManager.registerProcessor('credit', new CreditCardProcessor());
paymentManager.registerProcessor('paypal', new PayPalProcessor());
paymentManager.registerProcessor('alipay', new AlipayProcessor());
```

### L - 里氏替换原则

#### ✅ 遵循 LSP 的代码

```javascript
class Bird {
    fly() {
        console.log('Flying...');
    }
}

class FlyingBird extends Bird {
    fly() {
        console.log('Flying high in the sky');
    }
}

// 不能飞的鸟应该有不同的基类
class FlightlessBird {
    walk() {
        console.log('Walking...');
    }
}

class Penguin extends FlightlessBird {
    walk() {
        console.log('Penguin walking on ice');
    }
    
    swim() {
        console.log('Penguin swimming');
    }
}

// 正确的继承关系
class Eagle extends FlyingBird {
    fly() {
        console.log('Eagle soaring majestically');
    }
}
```

### I - 接口隔离原则

#### ✅ 遵循 ISP 的代码

```javascript
// 小而专一的接口
class Readable {
    read() {
        throw new Error('Must implement read method');
    }
}

class Writable {
    write(data) {
        throw new Error('Must implement write method');
    }
}

class Seekable {
    seek(position) {
        throw new Error('Must implement seek method');
    }
}

// 只实现需要的接口
class FileReader extends Readable {
    read() {
        console.log('Reading from file');
    }
}

class FileWriter extends Writable {
    write(data) {
        console.log(`Writing: ${data}`);
    }
}

class RandomAccessFile extends Readable {
    read() {
        console.log('Reading from random access file');
    }
}

// 组合多个接口
class FullAccessFile {
    constructor() {
        this.readable = new FileReader();
        this.writable = new FileWriter();
    }
    
    read() {
        return this.readable.read();
    }
    
    write(data) {
        return this.writable.write(data);
    }
}
```

### D - 依赖倒置原则

#### ✅ 遵循 DIP 的代码

```javascript
// 定义抽象接口
class Logger {
    log(message) {
        throw new Error('Must implement log method');
    }
}

class Database {
    save(data) {
        throw new Error('Must implement save method');
    }
}

// 具体实现
class ConsoleLogger extends Logger {
    log(message) {
        console.log(`[LOG] ${message}`);
    }
}

class FileLogger extends Logger {
    log(message) {
        // 写入文件的逻辑
        console.log(`[FILE] ${message}`);
    }
}

class MySQLDatabase extends Database {
    save(data) {
        console.log('Saving to MySQL:', data);
    }
}

class MongoDatabase extends Database {
    save(data) {
        console.log('Saving to MongoDB:', data);
    }
}

// 高层模块依赖抽象
class UserService {
    constructor(logger, database) {
        this.logger = logger;
        this.database = database;
    }
    
    createUser(userData) {
        this.logger.log('Creating new user');
        
        // 业务逻辑
        const user = {
            id: Date.now(),
            ...userData,
            createdAt: new Date()
        };
        
        this.database.save(user);
        this.logger.log(`User created: ${user.id}`);
        
        return user;
    }
}

// 依赖注入
const logger = new ConsoleLogger();
const database = new MySQLDatabase();
const userService = new UserService(logger, database);

// 可以轻松切换实现
const fileLogger = new FileLogger();
const mongoDatabase = new MongoDatabase();
const userServiceV2 = new UserService(fileLogger, mongoDatabase);
```

---

## 总结

这些代码示例展示了如何在实际开发中应用编码原则：

1. **OAOO/DRY**: 通过抽取公共逻辑避免重复代码
2. **KISS**: 保持代码简单直接，避免过度设计
3. **SOLID**: 通过良好的面向对象设计提高代码的可维护性和扩展性

记住，这些原则的目标是让代码更易于理解、测试、维护和扩展。在实际应用中，要根据具体情况灵活运用，避免教条主义。
