# Apollo Server Express 基础使用指南

## 目录

1. [Apollo Server Express 简介](#apollo-server-express-简介)
2. [核心概念](#核心概念)
3. [基础设置](#基础设置)
4. [Schema 定义](#schema-定义)
5. [Resolvers 实现](#resolvers-实现)
6. [中间件和认证](#中间件和认证)
7. [实际应用示例](#实际应用示例)
8. [最佳实践](#最佳实践)

## Apollo Server Express 简介

Apollo Server Express 是一个用于构建 GraphQL API 的强大工具，它将 Apollo Server 与 Express.js 框架集成，提供了：

-   **类型安全的 GraphQL API**
-   **内置的 GraphQL Playground**
-   **中间件支持**
-   **认证和授权**
-   **实时订阅功能**
-   **缓存机制**

### 主要优势

1. **统一的数据层**：单一端点处理所有数据请求
2. **强类型系统**：编译时类型检查
3. **灵活的查询**：客户端可以精确请求所需数据
4. **实时功能**：内置订阅支持
5. **开发工具**：丰富的调试和测试工具

## 核心概念

### 1. GraphQL Schema

Schema 定义了 API 的结构，包括：

-   **Types（类型）**：数据结构定义
-   **Queries（查询）**：读取操作
-   **Mutations（变更）**：写入操作
-   **Subscriptions（订阅）**：实时数据推送

### 2. Resolvers

Resolvers 是实际执行 GraphQL 操作的函数：

-   每个字段都有对应的 resolver
-   负责从数据源获取数据
-   可以是异步函数

### 3. Context

Context 在所有 resolvers 之间共享：

-   包含认证信息
-   数据库连接
-   请求相关数据

## 基础设置

### 1. 安装依赖

```bash
# 核心依赖
yarn add apollo-server-express express graphql

# TypeScript 支持
yarn add -D @types/express typescript ts-node-dev

# 其他常用依赖
yarn add cors dotenv jsonwebtoken bcryptjs
yarn add -D @types/cors @types/jsonwebtoken @types/bcryptjs
```

### 2. 基本服务器设置

```typescript
import express from 'express'
import { ApolloServer } from 'apollo-server-express'
import { typeDefs } from './schema/typeDefs'
import { resolvers } from './resolvers'

async function startServer() {
	// 创建 Express 应用
	const app = express()

	// 创建 Apollo Server
	const server = new ApolloServer({
		typeDefs,
		resolvers,
		context: ({ req }) => ({
			// 上下文数据
			user: req.user,
			token: req.headers.authorization,
		}),
	})

	// 启动服务器
	await server.start()

	// 应用中间件
	server.applyMiddleware({ app, path: '/graphql' })

	// 启动 HTTP 服务器
	const PORT = process.env.PORT || 4000
	app.listen(PORT, () => {
		console.log(
			`🚀 Server ready at http://localhost:${PORT}${server.graphqlPath}`
		)
	})
}

startServer().catch((error) => {
	console.error('Failed to start server:', error)
})
```

## Schema 定义

### 1. 基本类型定义

```graphql
# 标量类型
scalar DateTime

# 对象类型
type User {
	id: ID!
	username: String!
	email: String!
	createdAt: DateTime!
}

# 枚举类型
enum UserRole {
	ADMIN
	USER
	MODERATOR
}

# 输入类型
input CreateUserInput {
	username: String!
	email: String!
	password: String!
}
```

### 2. 查询和变更定义

```graphql
type Query {
	# 获取所有用户
	users: [User!]!

	# 根据ID获取用户
	user(id: ID!): User

	# 分页查询
	usersPaginated(
		page: Int = 1
		limit: Int = 10
		filter: UserFilter
	): UserConnection!
}

type Mutation {
	# 创建用户
	createUser(input: CreateUserInput!): User!

	# 更新用户
	updateUser(id: ID!, input: UpdateUserInput!): User!

	# 删除用户
	deleteUser(id: ID!): Boolean!

	# 用户登录
	login(input: LoginInput!): AuthPayload!
}

type Subscription {
	# 用户创建订阅
	userCreated: User!

	# 用户更新订阅
	userUpdated(userId: ID!): User!
}
```

## Resolvers 实现

### 1. 基本 Resolver 结构

```typescript
export const resolvers = {
	// 查询解析器
	Query: {
		users: async () => {
			return await db.getUsers()
		},

		user: async (parent, { id }) => {
			return await db.getUserById(id)
		},

		usersPaginated: async (parent, { page, limit, filter }) => {
			const offset = (page - 1) * limit
			const users = await db.getUsersPaginated(offset, limit, filter)
			const total = await db.getUsersCount(filter)

			return {
				users,
				pageInfo: {
					currentPage: page,
					totalPages: Math.ceil(total / limit),
					hasNextPage: page * limit < total,
					hasPreviousPage: page > 1,
				},
			}
		},
	},

	// 变更解析器
	Mutation: {
		createUser: async (parent, { input }, context) => {
			// 检查认证
			if (!context.user) {
				throw new Error('Authentication required')
			}

			// 创建用户
			const user = await db.createUser(input)

			// 发布订阅事件
			pubsub.publish('USER_CREATED', { userCreated: user })

			return user
		},

		login: async (parent, { input }) => {
			const { email, password } = input

			// 验证用户
			const user = await db.getUserByEmail(email)
			if (!user) {
				throw new Error('Invalid credentials')
			}

			// 验证密码
			const isValid = await bcrypt.compare(password, user.password)
			if (!isValid) {
				throw new Error('Invalid credentials')
			}

			// 生成 JWT
			const token = jwt.sign({ userId: user.id }, JWT_SECRET, {
				expiresIn: '7d',
			})

			return { token, user }
		},
	},

	// 订阅解析器
	Subscription: {
		userCreated: {
			subscribe: () => pubsub.asyncIterator(['USER_CREATED']),
		},

		userUpdated: {
			subscribe: (parent, { userId }) =>
				pubsub.asyncIterator([`USER_UPDATED_${userId}`]),
		},
	},

	// 字段解析器
	User: {
		// 解析关联数据
		tasks: async (parent) => {
			return await db.getTasksByUserId(parent.id)
		},

		projects: async (parent) => {
			return await db.getProjectsByUserId(parent.id)
		},
	},

	// 自定义标量
	DateTime: DateTimeScalar,
}
```

### 2. 错误处理

```typescript
import {
	AuthenticationError,
	ForbiddenError,
	UserInputError,
} from 'apollo-server-express'

const resolvers = {
	Mutation: {
		createUser: async (parent, { input }, context) => {
			// 认证检查
			if (!context.user) {
				throw new AuthenticationError('You must be logged in')
			}

			// 权限检查
			if (context.user.role !== 'ADMIN') {
				throw new ForbiddenError('Insufficient permissions')
			}

			// 输入验证
			if (!input.email.includes('@')) {
				throw new UserInputError('Invalid email format')
			}

			try {
				return await db.createUser(input)
			} catch (error) {
				throw new Error('Failed to create user')
			}
		},
	},
}
```

## 中间件和认证

### 1. 认证中间件

```typescript
const getUser = async (token: string): Promise<User | null> => {
	try {
		if (!token) return null

		const cleanToken = token.replace('Bearer ', '')
		const decoded = jwt.verify(cleanToken, JWT_SECRET) as { userId: string }

		return await db.getUserById(decoded.userId)
	} catch (error) {
		return null
	}
}

const createContext = async ({ req }): Promise<Context> => {
	const token = req.headers.authorization || ''
	const user = await getUser(token)

	return { user, token }
}
```

### 2. 权限检查

```typescript
const requireAuth = (resolver) => {
	return (parent, args, context, info) => {
		if (!context.user) {
			throw new AuthenticationError('Authentication required')
		}
		return resolver(parent, args, context, info)
	}
}

const requireRole = (role: string) => (resolver) => {
	return (parent, args, context, info) => {
		if (!context.user || context.user.role !== role) {
			throw new ForbiddenError('Insufficient permissions')
		}
		return resolver(parent, args, context, info)
	}
}

// 使用示例
const resolvers = {
	Mutation: {
		createUser: requireRole('ADMIN')(async (parent, { input }) => {
			return await db.createUser(input)
		}),

		updateProfile: requireAuth(async (parent, { input }, context) => {
			return await db.updateUser(context.user.id, input)
		}),
	},
}
```

## 实际应用示例

### 1. 完整的服务器配置

基于您项目中的实际代码，这里是一个完整的 Apollo Server Express 配置示例：

```typescript
import express from 'express'
import { ApolloServer } from 'apollo-server-express'
import { createServer } from 'http'
import { WebSocketServer } from 'ws'
import { useServer } from 'graphql-ws/lib/use/ws'
import { makeExecutableSchema } from '@graphql-tools/schema'
import cors from 'cors'

async function startServer() {
	const app = express()

	// CORS 配置
	app.use(
		cors({
			origin: ['http://localhost:3000', 'http://localhost:5173'],
			credentials: true,
		})
	)

	// 创建可执行 Schema
	const schema = makeExecutableSchema({
		typeDefs,
		resolvers,
	})

	// Apollo Server 配置
	const server = new ApolloServer({
		schema,
		context: createContext,
		introspection: true, // 开发环境启用
		plugins: [
			// 自定义插件
			{
				requestDidStart() {
					return {
						didResolveOperation(requestContext) {
							console.log(
								`GraphQL Operation: ${requestContext.request.operationName}`
							)
						},
						didEncounterErrors(requestContext) {
							console.error(
								'GraphQL Errors:',
								requestContext.errors
							)
						},
					}
				},
			},
		],
	})

	await server.start()
	server.applyMiddleware({ app, path: '/graphql', cors: false })

	// HTTP 服务器
	const httpServer = createServer(app)

	// WebSocket 服务器（用于订阅）
	const wsServer = new WebSocketServer({
		server: httpServer,
		path: '/graphql',
	})

	// GraphQL WebSocket 服务器
	const serverCleanup = useServer(
		{
			schema,
			context: async (ctx) => {
				const token =
					(ctx.connectionParams?.authorization as string) || ''
				const user = await getUser(token)
				return { user, token }
			},
		},
		wsServer
	)

	// 启动服务器
	const PORT = process.env.PORT || 4000
	httpServer.listen(PORT, () => {
		console.log(`🚀 Server ready at http://localhost:${PORT}/graphql`)
	})
}
```

### 2. 数据层集成

```typescript
// 模拟数据库操作
class Database {
	private users: User[] = []
	private projects: Project[] = []
	private tasks: Task[] = []

	// 用户操作
	async getUsers(): Promise<User[]> {
		return this.users
	}

	async getUserById(id: string): Promise<User | null> {
		return this.users.find((user) => user.id === id) || null
	}

	async createUser(input: CreateUserInput): Promise<User> {
		const hashedPassword = await bcrypt.hash(input.password, 10)
		const user: User = {
			id: generateId(),
			...input,
			password: hashedPassword,
			role: UserRole.USER,
			status: UserStatus.ACTIVE,
			createdAt: new Date(),
			updatedAt: new Date(),
		}

		this.users.push(user)
		return user
	}

	async updateUser(id: string, input: UpdateUserInput): Promise<User | null> {
		const userIndex = this.users.findIndex((user) => user.id === id)
		if (userIndex === -1) return null

		this.users[userIndex] = {
			...this.users[userIndex],
			...input,
			updatedAt: new Date(),
		}

		return this.users[userIndex]
	}

	// 项目操作
	async getProjects(): Promise<Project[]> {
		return this.projects
	}

	async getProjectsByUserId(userId: string): Promise<Project[]> {
		return this.projects.filter(
			(project) =>
				project.ownerId === userId || project.memberIds.includes(userId)
		)
	}

	// 任务操作
	async getTasksByUserId(userId: string): Promise<Task[]> {
		return this.tasks.filter(
			(task) => task.assigneeId === userId || task.creatorId === userId
		)
	}
}

export const db = new Database()
```

### 3. 高级查询示例

```typescript
const resolvers = {
	Query: {
		// 复杂查询：获取用户的仪表板数据
		dashboardStats: async (parent, args, context) => {
			if (!context.user) {
				throw new AuthenticationError('Authentication required')
			}

			const userId = context.user.id
			const tasks = await db.getTasksByUserId(userId)
			const projects = await db.getProjectsByUserId(userId)

			return {
				taskStats: {
					total: tasks.length,
					done: tasks.filter((t) => t.status === TaskStatus.DONE)
						.length,
					inProgress: tasks.filter(
						(t) => t.status === TaskStatus.IN_PROGRESS
					).length,
					todo: tasks.filter((t) => t.status === TaskStatus.TODO)
						.length,
				},
				projectStats: {
					total: projects.length,
					active: projects.filter(
						(p) => p.status === ProjectStatus.ACTIVE
					).length,
					completed: projects.filter(
						(p) => p.status === ProjectStatus.COMPLETED
					).length,
				},
				recentTasks: tasks
					.sort(
						(a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()
					)
					.slice(0, 5),
			}
		},

		// 搜索功能
		search: async (parent, { query, type }) => {
			const results = []

			if (!type || type === 'USER') {
				const users = await db.searchUsers(query)
				results.push(
					...users.map((user) => ({ ...user, __typename: 'User' }))
				)
			}

			if (!type || type === 'PROJECT') {
				const projects = await db.searchProjects(query)
				results.push(
					...projects.map((project) => ({
						...project,
						__typename: 'Project',
					}))
				)
			}

			if (!type || type === 'TASK') {
				const tasks = await db.searchTasks(query)
				results.push(
					...tasks.map((task) => ({ ...task, __typename: 'Task' }))
				)
			}

			return results
		},
	},
}
```

## 最佳实践

### 1. 项目结构

```
server/
├── src/
│   ├── schema/
│   │   ├── typeDefs.ts          # GraphQL Schema 定义
│   │   └── index.ts
│   ├── resolvers/
│   │   ├── user.ts              # 用户相关解析器
│   │   ├── project.ts           # 项目相关解析器
│   │   ├── task.ts              # 任务相关解析器
│   │   └── index.ts             # 解析器汇总
│   ├── models/
│   │   ├── User.ts              # 类型定义
│   │   ├── Project.ts
│   │   └── index.ts
│   ├── data/
│   │   ├── database.ts          # 数据库操作
│   │   └── mockData.ts          # 模拟数据
│   ├── middleware/
│   │   ├── auth.ts              # 认证中间件
│   │   └── validation.ts        # 验证中间件
│   ├── utils/
│   │   ├── helpers.ts           # 工具函数
│   │   └── constants.ts         # 常量定义
│   └── server.ts                # 服务器入口
├── package.json
└── tsconfig.json
```

### 2. 错误处理策略

```typescript
// 自定义错误类
class ValidationError extends Error {
	constructor(message: string, field?: string) {
		super(message)
		this.name = 'ValidationError'
		this.field = field
	}
}

// 全局错误处理
const server = new ApolloServer({
	typeDefs,
	resolvers,
	formatError: (error) => {
		// 记录错误
		console.error('GraphQL Error:', error)

		// 开发环境返回详细错误信息
		if (process.env.NODE_ENV === 'development') {
			return error
		}

		// 生产环境隐藏敏感信息
		if (error.message.includes('database')) {
			return new Error('Internal server error')
		}

		return error
	},
})
```

### 3. 性能优化

```typescript
// DataLoader 解决 N+1 查询问题
import DataLoader from 'dataloader'

const createLoaders = () => ({
	userLoader: new DataLoader(async (userIds: string[]) => {
		const users = await db.getUsersByIds(userIds)
		return userIds.map((id) => users.find((user) => user.id === id))
	}),

	tasksByUserLoader: new DataLoader(async (userIds: string[]) => {
		const tasks = await db.getTasksByUserIds(userIds)
		return userIds.map((id) =>
			tasks.filter((task) => task.assigneeId === id)
		)
	}),
})

// 在 context 中提供 loaders
const createContext = async ({ req }) => {
	const user = await getUser(req.headers.authorization)
	const loaders = createLoaders()

	return { user, loaders }
}

// 在 resolver 中使用
const resolvers = {
	User: {
		tasks: async (parent, args, { loaders }) => {
			return await loaders.tasksByUserLoader.load(parent.id)
		},
	},
}
```

### 4. 安全考虑

```typescript
// 查询复杂度限制
import { createComplexityLimitRule } from 'graphql-query-complexity'

const server = new ApolloServer({
	typeDefs,
	resolvers,
	validationRules: [
		createComplexityLimitRule(1000), // 限制查询复杂度
	],
	plugins: [
		// 查询深度限制
		{
			requestDidStart() {
				return {
					didResolveOperation({ request, document }) {
						const depth = getQueryDepth(document)
						if (depth > 10) {
							throw new Error('Query too deep')
						}
					},
				}
			},
		},
	],
})

// 速率限制
import rateLimit from 'express-rate-limit'

app.use(
	'/graphql',
	rateLimit({
		windowMs: 15 * 60 * 1000, // 15 分钟
		max: 100, // 限制每个 IP 100 次请求
	})
)
```

### 5. 测试策略

```typescript
// 单元测试示例
import { createTestClient } from 'apollo-server-testing'

describe('User Resolvers', () => {
	let server: ApolloServer
	let query: any, mutate: any

	beforeEach(() => {
		server = new ApolloServer({
			typeDefs,
			resolvers,
			context: () => ({ user: mockUser }),
		})
		;({ query, mutate } = createTestClient(server))
	})

	test('should get users', async () => {
		const GET_USERS = gql`
			query GetUsers {
				users {
					id
					username
					email
				}
			}
		`

		const result = await query({ query: GET_USERS })

		expect(result.errors).toBeUndefined()
		expect(result.data.users).toHaveLength(2)
	})

	test('should create user', async () => {
		const CREATE_USER = gql`
			mutation CreateUser($input: CreateUserInput!) {
				createUser(input: $input) {
					id
					username
					email
				}
			}
		`

		const result = await mutate({
			mutation: CREATE_USER,
			variables: {
				input: {
					username: 'testuser',
					email: '<EMAIL>',
					password: 'password123',
				},
			},
		})

		expect(result.errors).toBeUndefined()
		expect(result.data.createUser.username).toBe('testuser')
	})
})
```

## 高级特性和扩展

### 1. DataLoader 实现（解决 N+1 查询问题）

```typescript
// src/loaders/index.ts
import DataLoader from 'dataloader'
import { db } from '../data/mockData'
import { User, Task, Project, Comment } from '../models'

export interface Loaders {
	userLoader: DataLoader<string, User | null>
	tasksByUserLoader: DataLoader<string, Task[]>
	projectsByUserLoader: DataLoader<string, Project[]>
	commentsByTaskLoader: DataLoader<string, Comment[]>
	tasksByProjectLoader: DataLoader<string, Task[]>
}

export const createLoaders = (): Loaders => ({
	// 用户加载器
	userLoader: new DataLoader<string, User | null>(
		async (userIds: readonly string[]) => {
			const users = db.getUsers()
			return userIds.map(
				(id) => users.find((user) => user.id === id) || null
			)
		}
	),

	// 用户任务加载器
	tasksByUserLoader: new DataLoader<string, Task[]>(
		async (userIds: readonly string[]) => {
			const tasks = db.getTasks()
			return userIds.map((userId) =>
				tasks.filter(
					(task) =>
						task.assigneeId === userId || task.creatorId === userId
				)
			)
		}
	),

	// 用户项目加载器
	projectsByUserLoader: new DataLoader<string, Project[]>(
		async (userIds: readonly string[]) => {
			const projects = db.getProjects()
			return userIds.map((userId) =>
				projects.filter(
					(project) =>
						project.ownerId === userId ||
						project.memberIds.includes(userId)
				)
			)
		}
	),

	// 任务评论加载器
	commentsByTaskLoader: new DataLoader<string, Comment[]>(
		async (taskIds: readonly string[]) => {
			const comments = db.getComments()
			return taskIds.map((taskId) =>
				comments.filter((comment) => comment.taskId === taskId)
			)
		}
	),

	// 项目任务加载器
	tasksByProjectLoader: new DataLoader<string, Task[]>(
		async (projectIds: readonly string[]) => {
			const tasks = db.getTasks()
			return projectIds.map((projectId) =>
				tasks.filter((task) => task.projectId === projectId)
			)
		}
	),
})

// 在 server.ts 中集成 DataLoader
const createContext = async ({
	req,
}: {
	req: express.Request
}): Promise<Context> => {
	const token = req.headers.authorization || ''
	const user = await getUser(token)
	const loaders = createLoaders() // 为每个请求创建新的 loader 实例

	return {
		token,
		user,
		loaders,
	}
}
```

### 2. 缓存策略

```typescript
// src/cache/redis.ts
import Redis from 'ioredis'

class CacheService {
	private redis: Redis

	constructor() {
		this.redis = new Redis({
			host: process.env.REDIS_HOST || 'localhost',
			port: parseInt(process.env.REDIS_PORT || '6379'),
			retryDelayOnFailover: 100,
			maxRetriesPerRequest: 3,
		})
	}

	async get<T>(key: string): Promise<T | null> {
		try {
			const value = await this.redis.get(key)
			return value ? JSON.parse(value) : null
		} catch (error) {
			console.error('Cache get error:', error)
			return null
		}
	}

	async set(key: string, value: any, ttl: number = 3600): Promise<void> {
		try {
			await this.redis.setex(key, ttl, JSON.stringify(value))
		} catch (error) {
			console.error('Cache set error:', error)
		}
	}

	async del(key: string): Promise<void> {
		try {
			await this.redis.del(key)
		} catch (error) {
			console.error('Cache delete error:', error)
		}
	}

	async invalidatePattern(pattern: string): Promise<void> {
		try {
			const keys = await this.redis.keys(pattern)
			if (keys.length > 0) {
				await this.redis.del(...keys)
			}
		} catch (error) {
			console.error('Cache invalidate error:', error)
		}
	}
}

export const cache = new CacheService()

// 在 resolvers 中使用缓存
const resolvers = {
	Query: {
		users: async () => {
			const cacheKey = 'users:all'
			let users = await cache.get<User[]>(cacheKey)

			if (!users) {
				users = db.getUsers()
				await cache.set(cacheKey, users, 300) // 缓存5分钟
			}

			return { users }
		},

		dashboardStats: async (parent, args, context) => {
			if (!context.user) {
				throw new AuthenticationError('Authentication required')
			}

			const cacheKey = `dashboard:${context.user.id}`
			let stats = await cache.get(cacheKey)

			if (!stats) {
				// 计算统计数据
				const tasks = await context.loaders.tasksByUserLoader.load(
					context.user.id
				)
				const projects =
					await context.loaders.projectsByUserLoader.load(
						context.user.id
					)

				stats = {
					taskStats: {
						total: tasks.length,
						done: tasks.filter((t) => t.status === TaskStatus.DONE)
							.length,
						inProgress: tasks.filter(
							(t) => t.status === TaskStatus.IN_PROGRESS
						).length,
						todo: tasks.filter((t) => t.status === TaskStatus.TODO)
							.length,
					},
					projectStats: {
						total: projects.length,
						active: projects.filter(
							(p) => p.status === ProjectStatus.ACTIVE
						).length,
						completed: projects.filter(
							(p) => p.status === ProjectStatus.COMPLETED
						).length,
					},
				}

				await cache.set(cacheKey, stats, 60) // 缓存1分钟
			}

			return stats
		},
	},

	Mutation: {
		createTask: async (parent, { input }, context) => {
			const task = await db.createTask(input)

			// 清除相关缓存
			await cache.invalidatePattern(`dashboard:*`)
			await cache.invalidatePattern(`tasks:*`)
			await cache.invalidatePattern(`projects:${input.projectId}:*`)

			return task
		},
	},
}
```

### 3. 实时订阅高级用法

```typescript
// src/subscriptions/index.ts
import { PubSub } from 'graphql-subscriptions'
import { withFilter } from 'graphql-subscriptions'

const pubsub = new PubSub()

// 订阅事件常量
export const SUBSCRIPTION_EVENTS = {
	TASK_CREATED: 'TASK_CREATED',
	TASK_UPDATED: 'TASK_UPDATED',
	TASK_DELETED: 'TASK_DELETED',
	PROJECT_UPDATED: 'PROJECT_UPDATED',
	USER_ONLINE: 'USER_ONLINE',
	USER_OFFLINE: 'USER_OFFLINE',
	NOTIFICATION_SENT: 'NOTIFICATION_SENT',
} as const

export const subscriptionResolvers = {
	Subscription: {
		// 任务创建订阅（按项目过滤）
		taskCreated: {
			subscribe: withFilter(
				() => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.TASK_CREATED]),
				(payload, variables, context) => {
					// 只有项目成员才能收到通知
					const task = payload.taskCreated
					const project = db.getProjectById(task.projectId)

					return (
						project &&
						(project.ownerId === context.user?.id ||
							project.memberIds.includes(context.user?.id))
					)
				}
			),
		},

		// 任务更新订阅（按任务ID过滤）
		taskUpdated: {
			subscribe: withFilter(
				() => pubsub.asyncIterator([SUBSCRIPTION_EVENTS.TASK_UPDATED]),
				(payload, variables) => {
					return payload.taskUpdated.id === variables.taskId
				}
			),
		},

		// 用户在线状态订阅
		userStatusChanged: {
			subscribe: withFilter(
				() =>
					pubsub.asyncIterator([
						SUBSCRIPTION_EVENTS.USER_ONLINE,
						SUBSCRIPTION_EVENTS.USER_OFFLINE,
					]),
				(payload, variables, context) => {
					// 只有团队成员才能看到状态变化
					const user = payload.userStatusChanged
					const userProjects = db.getProjectsByUserId(
						context.user?.id
					)
					const targetUserProjects = db.getProjectsByUserId(user.id)

					// 检查是否有共同项目
					return userProjects.some((up) =>
						targetUserProjects.some((tup) => tup.id === up.id)
					)
				}
			),
		},

		// 个人通知订阅
		notificationReceived: {
			subscribe: withFilter(
				() =>
					pubsub.asyncIterator([
						SUBSCRIPTION_EVENTS.NOTIFICATION_SENT,
					]),
				(payload, variables, context) => {
					return (
						payload.notificationReceived.userId === context.user?.id
					)
				}
			),
		},
	},
}

// 发布订阅事件的工具函数
export const publishTaskCreated = (task: Task) => {
	pubsub.publish(SUBSCRIPTION_EVENTS.TASK_CREATED, { taskCreated: task })
}

export const publishTaskUpdated = (task: Task) => {
	pubsub.publish(SUBSCRIPTION_EVENTS.TASK_UPDATED, { taskUpdated: task })
}

export const publishUserOnline = (user: User) => {
	pubsub.publish(SUBSCRIPTION_EVENTS.USER_ONLINE, {
		userStatusChanged: { ...user, isOnline: true },
	})
}

export const publishNotification = (notification: any) => {
	pubsub.publish(SUBSCRIPTION_EVENTS.NOTIFICATION_SENT, {
		notificationReceived: notification,
	})
}
```

### 4. 高级认证和授权

```typescript
// src/auth/permissions.ts
import { rule, shield, and, or, not } from 'graphql-shield'
import { AuthenticationError, ForbiddenError } from 'apollo-server-express'
import { Context } from '../models'

// 基础规则
const isAuthenticated = rule({ cache: 'contextual' })(
	async (parent, args, context: Context) => {
		return context.user !== null
	}
)

const isAdmin = rule({ cache: 'contextual' })(
	async (parent, args, context: Context) => {
		return context.user?.role === 'ADMIN'
	}
)

const isManager = rule({ cache: 'contextual' })(
	async (parent, args, context: Context) => {
		return (
			context.user?.role === 'MANAGER' || context.user?.role === 'ADMIN'
		)
	}
)

// 资源所有权规则
const isProjectOwner = rule({ cache: 'strict' })(
	async (parent, args, context: Context) => {
		const project = db.getProjectById(args.id || args.projectId)
		return project?.ownerId === context.user?.id
	}
)

const isProjectMember = rule({ cache: 'strict' })(
	async (parent, args, context: Context) => {
		const project = db.getProjectById(args.id || args.projectId)
		return (
			project &&
			(project.ownerId === context.user?.id ||
				project.memberIds.includes(context.user?.id))
		)
	}
)

const isTaskAssignee = rule({ cache: 'strict' })(
	async (parent, args, context: Context) => {
		const task = db.getTaskById(args.id || args.taskId)
		return (
			task?.assigneeId === context.user?.id ||
			task?.creatorId === context.user?.id
		)
	}
)

// 权限配置
export const permissions = shield(
	{
		Query: {
			users: isManager,
			user: isAuthenticated,
			projects: isAuthenticated,
			project: isProjectMember,
			tasks: isAuthenticated,
			task: or(isTaskAssignee, isProjectMember),
			dashboardStats: isAuthenticated,
		},
		Mutation: {
			createUser: isAdmin,
			updateUser: or(isAdmin, isOwner),
			deleteUser: isAdmin,
			createProject: isAuthenticated,
			updateProject: or(isProjectOwner, isManager),
			deleteProject: or(isProjectOwner, isAdmin),
			createTask: isProjectMember,
			updateTask: or(isTaskAssignee, isProjectOwner, isManager),
			deleteTask: or(isTaskAssignee, isProjectOwner, isManager),
		},
		Subscription: {
			taskCreated: isAuthenticated,
			taskUpdated: isAuthenticated,
			userStatusChanged: isAuthenticated,
			notificationReceived: isAuthenticated,
		},
	},
	{
		allowExternalErrors: true,
		fallbackError: new ForbiddenError('Access denied'),
	}
)

// 自定义权限检查函数
export const checkPermission = {
	canEditTask: (task: Task, user: User): boolean => {
		if (!user) return false
		if (user.role === 'ADMIN') return true
		if (task.assigneeId === user.id || task.creatorId === user.id)
			return true

		const project = db.getProjectById(task.projectId)
		return project?.ownerId === user.id
	},

	canViewProject: (project: Project, user: User): boolean => {
		if (!user) return false
		if (user.role === 'ADMIN') return true
		return (
			project.ownerId === user.id || project.memberIds.includes(user.id)
		)
	},

	canManageProject: (project: Project, user: User): boolean => {
		if (!user) return false
		if (user.role === 'ADMIN' || user.role === 'MANAGER') return true
		return project.ownerId === user.id
	},
}
```

## 总结

Apollo Server Express 提供了构建现代 GraphQL API 的完整解决方案。通过合理的架构设计、错误处理、性能优化和安全措施，可以构建出高质量、可维护的 GraphQL 服务。

关键要点：

1. **模块化设计**：分离 schema、resolvers 和业务逻辑
2. **类型安全**：充分利用 TypeScript 的类型系统
3. **错误处理**：提供清晰的错误信息和适当的错误类型
4. **性能优化**：使用 DataLoader 解决 N+1 问题
5. **安全防护**：实施认证、授权和查询限制
6. **测试覆盖**：编写全面的单元测试和集成测试
7. **缓存策略**：合理使用缓存提升性能
8. **实时功能**：充分利用订阅功能
9. **权限控制**：细粒度的权限管理

通过这些实践，您可以构建出既强大又安全的 GraphQL API 服务。
