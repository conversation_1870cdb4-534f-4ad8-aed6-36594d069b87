# 策略模式实践案例

## 🎮 完整实战案例：游戏角色攻击系统

让我们通过一个游戏角色攻击系统来深入理解策略模式。

### 需求描述
游戏中有不同的角色，每个角色可以使用不同的武器进行攻击：
- 剑：近战攻击，伤害高
- 弓箭：远程攻击，伤害中等
- 魔法：范围攻击，伤害根据魔法值计算

### ❌ 没有策略模式的实现

```javascript
class GameCharacter {
    constructor(name, weaponType) {
        this.name = name;
        this.weaponType = weaponType;
        this.health = 100;
        this.mana = 50;
    }
    
    attack(target) {
        let damage = 0;
        
        if (this.weaponType === 'sword') {
            // 剑的攻击逻辑
            damage = 30;
            console.log(`${this.name} 用剑砍击 ${target.name}，造成 ${damage} 点伤害！`);
        } else if (this.weaponType === 'bow') {
            // 弓箭的攻击逻辑
            damage = 20;
            console.log(`${this.name} 用弓箭射击 ${target.name}，造成 ${damage} 点伤害！`);
        } else if (this.weaponType === 'magic') {
            // 魔法的攻击逻辑
            if (this.mana >= 10) {
                damage = this.mana * 0.8;
                this.mana -= 10;
                console.log(`${this.name} 释放魔法攻击 ${target.name}，造成 ${damage} 点伤害！`);
            } else {
                console.log(`${this.name} 魔法值不足，无法攻击！`);
                return;
            }
        } else {
            console.log(`${this.name} 没有武器，无法攻击！`);
            return;
        }
        
        target.takeDamage(damage);
    }
    
    takeDamage(damage) {
        this.health -= damage;
        console.log(`${this.name} 受到 ${damage} 点伤害，剩余生命值: ${this.health}`);
    }
}

// 使用
const warrior = new GameCharacter('战士', 'sword');
const archer = new GameCharacter('弓箭手', 'bow');
const mage = new GameCharacter('法师', 'magic');

warrior.attack(archer); // 战士用剑砍击弓箭手，造成30点伤害！
```

**问题**：
- 每次添加新武器都要修改attack方法
- 武器逻辑混在角色类中
- 难以测试单个武器的逻辑
- 代码越来越复杂

### ✅ 使用策略模式的实现

#### 第一步：定义攻击策略接口

```javascript
// 所有武器策略都需要实现attack方法
// attack(attacker, target) => 执行攻击逻辑
```

#### 第二步：实现具体的武器策略

```javascript
// 剑攻击策略
class SwordAttackStrategy {
    attack(attacker, target) {
        const damage = 30;
        console.log(`${attacker.name} 用剑砍击 ${target.name}，造成 ${damage} 点伤害！`);
        target.takeDamage(damage);
    }
}

// 弓箭攻击策略
class BowAttackStrategy {
    attack(attacker, target) {
        const damage = 20;
        console.log(`${attacker.name} 用弓箭射击 ${target.name}，造成 ${damage} 点伤害！`);
        target.takeDamage(damage);
    }
}

// 魔法攻击策略
class MagicAttackStrategy {
    attack(attacker, target) {
        if (attacker.mana >= 10) {
            const damage = attacker.mana * 0.8;
            attacker.mana -= 10;
            console.log(`${attacker.name} 释放魔法攻击 ${target.name}，造成 ${damage} 点伤害！`);
            target.takeDamage(damage);
        } else {
            console.log(`${attacker.name} 魔法值不足，无法攻击！`);
        }
    }
}

// 新增：火球术策略
class FireballStrategy {
    attack(attacker, target) {
        if (attacker.mana >= 15) {
            const damage = 35;
            attacker.mana -= 15;
            console.log(`${attacker.name} 释放火球术攻击 ${target.name}，造成 ${damage} 点火焰伤害！`);
            target.takeDamage(damage);
        } else {
            console.log(`${attacker.name} 魔法值不足，无法释放火球术！`);
        }
    }
}
```

#### 第三步：重构角色类

```javascript
class GameCharacter {
    constructor(name) {
        this.name = name;
        this.health = 100;
        this.mana = 50;
        this.attackStrategy = null; // 当前使用的攻击策略
    }
    
    // 设置攻击策略（切换武器）
    setAttackStrategy(strategy) {
        this.attackStrategy = strategy;
        console.log(`${this.name} 切换了武器！`);
    }
    
    // 执行攻击
    attack(target) {
        if (!this.attackStrategy) {
            console.log(`${this.name} 没有装备武器，无法攻击！`);
            return;
        }
        
        this.attackStrategy.attack(this, target);
    }
    
    takeDamage(damage) {
        this.health -= damage;
        console.log(`${this.name} 受到 ${damage} 点伤害，剩余生命值: ${this.health}`);
    }
    
    // 恢复魔法值
    restoreMana(amount) {
        this.mana += amount;
        console.log(`${this.name} 恢复了 ${amount} 点魔法值，当前魔法值: ${this.mana}`);
    }
}
```

#### 第四步：使用策略模式

```javascript
// 创建角色
const warrior = new GameCharacter('战士');
const archer = new GameCharacter('弓箭手');
const mage = new GameCharacter('法师');

// 创建武器策略
const swordStrategy = new SwordAttackStrategy();
const bowStrategy = new BowAttackStrategy();
const magicStrategy = new MagicAttackStrategy();
const fireballStrategy = new FireballStrategy();

// 为角色装备武器
warrior.setAttackStrategy(swordStrategy);
archer.setAttackStrategy(bowStrategy);
mage.setAttackStrategy(magicStrategy);

// 进行战斗
warrior.attack(archer);  // 战士用剑砍击弓箭手
archer.attack(mage);     // 弓箭手用弓箭射击法师
mage.attack(warrior);    // 法师释放魔法攻击战士

// 法师切换到火球术
mage.setAttackStrategy(fireballStrategy);
mage.attack(warrior);    // 法师释放火球术攻击战士

// 战士也可以使用魔法（如果有魔法值）
warrior.restoreMana(20);
warrior.setAttackStrategy(magicStrategy);
warrior.attack(archer);  // 战士释放魔法攻击弓箭手
```

---

## 🏪 实战案例2：电商促销系统

### 需求描述
电商平台有多种促销策略：
- 满减：满100减20
- 打折：全场8折
- 买赠：买2送1
- 会员专享：VIP额外9折

### 策略模式实现

```javascript
// 满减策略
class FullReductionStrategy {
    constructor(threshold, reduction) {
        this.threshold = threshold; // 满多少
        this.reduction = reduction; // 减多少
    }
    
    calculate(originalPrice) {
        if (originalPrice >= this.threshold) {
            const finalPrice = originalPrice - this.reduction;
            return {
                finalPrice: Math.max(finalPrice, 0),
                description: `满${this.threshold}减${this.reduction}，优惠${this.reduction}元`
            };
        }
        return {
            finalPrice: originalPrice,
            description: `未达到满${this.threshold}减${this.reduction}条件`
        };
    }
}

// 打折策略
class DiscountStrategy {
    constructor(discountRate) {
        this.discountRate = discountRate; // 折扣率，如0.8表示8折
    }
    
    calculate(originalPrice) {
        const finalPrice = originalPrice * this.discountRate;
        const discount = originalPrice - finalPrice;
        return {
            finalPrice: finalPrice,
            description: `${this.discountRate * 10}折优惠，节省${discount.toFixed(2)}元`
        };
    }
}

// 买赠策略
class BuyGetFreeStrategy {
    constructor(buyCount, freeCount, unitPrice) {
        this.buyCount = buyCount;   // 买几个
        this.freeCount = freeCount; // 送几个
        this.unitPrice = unitPrice; // 单价
    }
    
    calculate(quantity) {
        const totalSets = Math.floor(quantity / this.buyCount);
        const freeItems = totalSets * this.freeCount;
        const payItems = quantity - freeItems;
        const finalPrice = payItems * this.unitPrice;
        
        return {
            finalPrice: finalPrice,
            description: `买${this.buyCount}送${this.freeCount}，共送${freeItems}件商品`
        };
    }
}

// 促销管理器
class PromotionManager {
    constructor() {
        this.strategies = new Map();
    }
    
    addStrategy(name, strategy) {
        this.strategies.set(name, strategy);
    }
    
    applyPromotion(strategyName, ...args) {
        const strategy = this.strategies.get(strategyName);
        if (!strategy) {
            throw new Error(`未找到促销策略: ${strategyName}`);
        }
        return strategy.calculate(...args);
    }
    
    // 比较所有策略，找出最优惠的
    findBestPromotion(originalPrice) {
        let bestResult = {
            finalPrice: originalPrice,
            description: '无优惠',
            strategyName: 'none'
        };
        
        for (const [name, strategy] of this.strategies) {
            try {
                const result = strategy.calculate(originalPrice);
                if (result.finalPrice < bestResult.finalPrice) {
                    bestResult = { ...result, strategyName: name };
                }
            } catch (error) {
                console.log(`策略 ${name} 计算失败:`, error.message);
            }
        }
        
        return bestResult;
    }
}

// 使用示例
const promotionManager = new PromotionManager();

// 添加各种促销策略
promotionManager.addStrategy('满减', new FullReductionStrategy(100, 20));
promotionManager.addStrategy('打折', new DiscountStrategy(0.8));
promotionManager.addStrategy('会员折扣', new DiscountStrategy(0.75));

// 计算促销价格
console.log('原价150元的商品：');
console.log(promotionManager.applyPromotion('满减', 150));
// { finalPrice: 130, description: '满100减20，优惠20元' }

console.log(promotionManager.applyPromotion('打折', 150));
// { finalPrice: 120, description: '8折优惠，节省30.00元' }

// 找出最优惠的策略
console.log('最优惠策略：', promotionManager.findBestPromotion(150));
// { finalPrice: 112.5, description: '7.5折优惠，节省37.50元', strategyName: '会员折扣' }
```

---

## 🔧 策略模式的变体

### 1. 工厂模式 + 策略模式

```javascript
// 策略工厂
class AttackStrategyFactory {
    static createStrategy(weaponType) {
        switch (weaponType) {
            case 'sword':
                return new SwordAttackStrategy();
            case 'bow':
                return new BowAttackStrategy();
            case 'magic':
                return new MagicAttackStrategy();
            case 'fireball':
                return new FireballStrategy();
            default:
                throw new Error(`未知的武器类型: ${weaponType}`);
        }
    }
}

// 使用
const character = new GameCharacter('勇者');
const strategy = AttackStrategyFactory.createStrategy('sword');
character.setAttackStrategy(strategy);
```

### 2. 配置驱动的策略模式

```javascript
// 通过配置文件定义策略
const strategyConfig = {
    'beginner': { damage: 10, cost: 0 },
    'intermediate': { damage: 20, cost: 5 },
    'expert': { damage: 35, cost: 15 }
};

class ConfigurableAttackStrategy {
    constructor(config) {
        this.damage = config.damage;
        this.cost = config.cost;
    }
    
    attack(attacker, target) {
        if (attacker.mana >= this.cost) {
            attacker.mana -= this.cost;
            console.log(`造成 ${this.damage} 点伤害！`);
            target.takeDamage(this.damage);
        } else {
            console.log('能量不足！');
        }
    }
}

// 根据配置创建策略
function createStrategyFromConfig(level) {
    const config = strategyConfig[level];
    return new ConfigurableAttackStrategy(config);
}
```

---

## 📋 策略模式最佳实践

### 1. 命名规范
```javascript
// 策略类命名：XxxStrategy
class PaymentStrategy { }
class CreditCardPaymentStrategy { }
class AlipayPaymentStrategy { }

// 策略管理器命名：XxxManager 或 XxxContext
class PaymentManager { }
class PaymentContext { }
```

### 2. 错误处理
```javascript
class StrategyManager {
    executeStrategy(strategyName, ...args) {
        try {
            const strategy = this.getStrategy(strategyName);
            return strategy.execute(...args);
        } catch (error) {
            console.error(`策略执行失败: ${error.message}`);
            // 可以返回默认结果或重新抛出错误
            return this.getDefaultResult();
        }
    }
}
```

### 3. 策略验证
```javascript
class ValidatedStrategy {
    execute(data) {
        // 执行前验证
        if (!this.validate(data)) {
            throw new Error('输入数据无效');
        }
        
        return this.doExecute(data);
    }
    
    validate(data) {
        // 子类实现具体验证逻辑
        return true;
    }
    
    doExecute(data) {
        // 子类实现具体执行逻辑
        throw new Error('子类必须实现doExecute方法');
    }
}
```

---

## 🎯 总结

策略模式是一个非常实用的设计模式，它的核心思想是：

### 关键概念
1. **策略**：完成同一任务的不同方法
2. **上下文**：使用策略的环境
3. **可替换**：策略之间可以互相替换

### 使用场景
- 有多种算法或方法完成同一任务
- 需要在运行时选择不同的行为
- 想要避免复杂的条件语句

### 主要优点
- 代码更清晰，易于维护
- 易于扩展新的策略
- 符合开闭原则
- 每个策略可以独立测试

记住：**策略模式就像是您的工具箱，根据不同的任务选择合适的工具！**
