/**
 * Apollo Server Express 基础示例
 * 这是一个最简单的 Apollo Server Express 设置示例
 */

import express from 'express';
import { ApolloServer } from 'apollo-server-express';
import { gql } from 'apollo-server-express';

// 1. 定义 GraphQL Schema
const typeDefs = gql`
  # 用户类型
  type User {
    id: ID!
    name: String!
    email: String!
    age: Int
  }

  # 查询类型
  type Query {
    # 获取所有用户
    users: [User!]!
    
    # 根据ID获取用户
    user(id: ID!): User
    
    # 问候语
    hello(name: String): String!
  }

  # 变更类型
  type Mutation {
    # 创建用户
    createUser(name: String!, email: String!, age: Int): User!
    
    # 更新用户
    updateUser(id: ID!, name: String, email: String, age: Int): User
    
    # 删除用户
    deleteUser(id: ID!): Boolean!
  }
`;

// 2. 模拟数据
let users = [
  { id: '1', name: '张三', email: '<EMAIL>', age: 25 },
  { id: '2', name: '李四', email: '<EMAIL>', age: 30 },
  { id: '3', name: '王五', email: '<EMAIL>', age: 28 }
];

let nextId = 4;

// 3. 定义 Resolvers
const resolvers = {
  Query: {
    // 获取所有用户
    users: () => users,
    
    // 根据ID获取用户
    user: (parent: any, { id }: { id: string }) => {
      return users.find(user => user.id === id);
    },
    
    // 问候语
    hello: (parent: any, { name }: { name?: string }) => {
      return `Hello, ${name || 'World'}!`;
    }
  },

  Mutation: {
    // 创建用户
    createUser: (parent: any, { name, email, age }: { name: string; email: string; age?: number }) => {
      const newUser = {
        id: nextId.toString(),
        name,
        email,
        age: age || null
      };
      
      users.push(newUser);
      nextId++;
      
      return newUser;
    },
    
    // 更新用户
    updateUser: (parent: any, { id, name, email, age }: { id: string; name?: string; email?: string; age?: number }) => {
      const userIndex = users.findIndex(user => user.id === id);
      
      if (userIndex === -1) {
        return null;
      }
      
      // 更新用户信息
      if (name !== undefined) users[userIndex].name = name;
      if (email !== undefined) users[userIndex].email = email;
      if (age !== undefined) users[userIndex].age = age;
      
      return users[userIndex];
    },
    
    // 删除用户
    deleteUser: (parent: any, { id }: { id: string }) => {
      const userIndex = users.findIndex(user => user.id === id);
      
      if (userIndex === -1) {
        return false;
      }
      
      users.splice(userIndex, 1);
      return true;
    }
  }
};

// 4. 创建并启动服务器
async function startServer() {
  // 创建 Express 应用
  const app = express();
  
  // 创建 Apollo Server
  const server = new ApolloServer({
    typeDefs,
    resolvers,
    // 开发环境启用 GraphQL Playground
    introspection: true,
    playground: true
  });

  // 启动 Apollo Server
  await server.start();

  // 将 Apollo GraphQL 中间件应用到 Express 应用
  server.applyMiddleware({ app, path: '/graphql' });

  // 添加一个简单的健康检查端点
  app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'Server is running!' });
  });

  // 启动 HTTP 服务器
  const PORT = process.env.PORT || 4000;
  app.listen(PORT, () => {
    console.log(`🚀 Server ready at http://localhost:${PORT}${server.graphqlPath}`);
    console.log(`🎮 GraphQL Playground available at http://localhost:${PORT}${server.graphqlPath}`);
    console.log(`💚 Health check at http://localhost:${PORT}/health`);
    
    console.log('\n📝 Try these queries in GraphQL Playground:');
    console.log('1. Query all users:');
    console.log('   query { users { id name email age } }');
    console.log('\n2. Get specific user:');
    console.log('   query { user(id: "1") { id name email age } }');
    console.log('\n3. Create new user:');
    console.log('   mutation { createUser(name: "新用户", email: "<EMAIL>", age: 25) { id name email age } }');
    console.log('\n4. Update user:');
    console.log('   mutation { updateUser(id: "1", name: "更新的名字") { id name email age } }');
    console.log('\n5. Delete user:');
    console.log('   mutation { deleteUser(id: "1") }');
  });
}

// 启动服务器
startServer().catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  process.exit(0);
});

export { typeDefs, resolvers };
