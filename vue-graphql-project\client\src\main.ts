import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { DefaultApolloClient } from '@vue/apollo-composable'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import apolloClient from './apollo'
import { useAuthStore } from './stores/auth'
import { useAppStore } from './stores/app'

// 创建Vue应用
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()
app.use(pinia)

// 使用路由
app.use(router)

// 使用Element Plus
app.use(ElementPlus)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 提供Apollo客户端
app.provide(DefaultApolloClient, apolloClient)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  
  // 获取应用store
  const appStore = useAppStore()
  appStore.notifyError('应用错误', '发生了意外错误，请刷新页面重试')
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Global warning:', msg, trace)
}

// 初始化应用
async function initApp() {
  // 初始化应用状态
  const appStore = useAppStore()
  appStore.initApp()
  
  // 初始化认证状态
  const authStore = useAuthStore()
  await authStore.initAuth()
  
  // 挂载应用
  app.mount('#app')
  
  console.log('🚀 Vue GraphQL App initialized successfully!')
}

// 启动应用
initApp().catch(error => {
  console.error('Failed to initialize app:', error)
  app.mount('#app') // 即使初始化失败也要挂载应用
})
