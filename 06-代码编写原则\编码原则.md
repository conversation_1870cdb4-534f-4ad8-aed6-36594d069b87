# 编码原则

## 幻灯片 1

### 编码原则

---

## 幻灯片 2

### 编码原则

### OAOO

### KISS

### SOLID

---

## 幻灯片 3

### 编码原则 - OAOO

### OAOO - Once and only once

### 一次且仅一次

### 又称为 Don't repeat yourself（不要重复你自己，简称 DRY）

在编码中都强调减少重复的代码，使代码更加简洁和易于维护。OAOO 强调一个算法或者操作只应该出现在代码中一次，DRY 原则则是指避免重复代码的出现，无论是逻辑、数据结构，还是函数的实现。

https://zh.wikipedia.org/wiki/%E4%B8%80%E6%AC%A1%E4%B8%94%E4%BB%85%E4%B8%80%E6%AC%A1

---

## 幻灯片 4

### 编码原则 - KISS

### KISS - Keep It Simple, Stupid

### 保持简洁和单纯

旨在鼓励开发者编写简洁、易懂、易维护的代码。遵循 KISS 原则通常意味着避免过度复杂化设计，避免引入不必要的抽象和复杂的实现。

https://zh.wikipedia.org/wiki/KISS%E5%8E%9F%E5%88%99

---

## 幻灯片 5

### 编码原则 - SOLID

### S - Single Responsibility Principle 单一职责原则

### O - The Open/Closed Principle 开闭原则

### L - Liskov Substitution principle 里氏替换原则

### I - interface-segregation principles 接口隔离原则

### D - Dependency inversion principle 依赖反转原则

---

## 幻灯片 6

### 编码原则 - SOLID - 单一职责原则

### Single responsibility principle 单一功能原则

强调一个类或模块应该只有一个责任（即，只有一个原因引起变化）。在实际编码中，遵循单一功能原则可以使代码更易于理解、测试、维护，并且更具扩展性。

https://zh.wikipedia.org/wiki/%E5%8D%95%E4%B8%80%E5%8A%9F%E8%83%BD%E5%8E%9F%E5%88%99

---

## 幻灯片 7

### 编码原则 - SOLID - 开闭原则

### The Open/Closed Principle

### 开闭原则

核心思想是：对扩展开放，对修改封闭。即我们应该能够通过扩展现有代码而不是修改已有的代码来实现新的功能或需求。

### 遵循开闭原则的代码具有以下特点：

可以在不修改现有代码的情况下添加新功能。

新的功能和变化可以通过扩展（如继承或接口）来实现，而不需要修改现有的类或方法。

https://zh.wikipedia.org/wiki/%E5%BC%80%E9%97%AD%E5%8E%9F%E5%88%99

---

## 幻灯片 8

### 编码原则 - SOLID - 里氏替换原则

### Liskov Substitution principle

### 里氏替换原则

核心思想是：子类对象应该能够替换父类对象，并且程序的行为不会出现错误。换句话说，如果一个类是另一个类的子类，那么在程序中使用子类对象时，应该不会影响程序的正常运行。

遵循里氏替换原则可以确保系统的扩展性和灵活性。违反该原则通常会导致程序行为不一致或不正确。

https://zh.wikipedia.org/wiki/%E9%87%8C%E6%B0%8F%E6%9B%BF%E6%8D%A2%E5%8E%9F%E5%88%99

---

## 幻灯片 9

### 编码原则 - SOLID - 接口隔离原则

### interface-segregation principles

### 接口隔离原则

核心思想是：客户端不应该依赖于它不需要的接口。换句话说，应该将大的接口拆分成多个小的、具体的接口，而不是让一个类依赖于一个庞大且不必要的接口。

接口隔离原则的主要目的是减少不必要的依赖，确保系统的灵活性和扩展性。遵循接口隔离原则可以使得类只实现它所需要的接口，避免不相关的接口引入额外的复杂度。

https://zh.wikipedia.org/wiki/%E6%8E%A5%E5%8F%A3%E9%9A%94%E7%A6%BB%E5%8E%9F%E5%88%99

---

## 幻灯片 10

### 编码原则 - SOLID - 依赖反转原则

### Dependency inversion principle

### 依赖反转原则

核心思想是：高层模块不应依赖于低层模块，二者应该依赖于抽象；抽象不应依赖于细节，细节应该依赖于抽象。简单来说，我们应该通过抽象（接口或抽象类）来解耦高层模块与低层模块的依赖关系，从而提高系统的灵活性和可扩展性。

### 原则解读：

高层模块：通常是处理业务逻辑的模块。

低层模块：通常是实现业务逻辑的具体模块，如数据库、外部 API 等。

抽象：通常是接口或抽象类，表示行为的规范。

依赖反转原则的目标是让高层模块依赖于抽象接口而不是低层模块的实现细节。

https://zh.wikipedia.org/wiki/%E4%BE%9D%E8%B5%96%E5%8F%8D%E8%BD%AC%E5%8E%9F%E5%88%99

---

## 幻灯片 11

### EOF

---
