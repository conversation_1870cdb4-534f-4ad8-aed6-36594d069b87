# GraphQL Resolvers 常见问题解答

## 1. 基础概念问题

### Q1: 为什么需要 Resolver？Schema 不是已经定义了数据结构吗？
**A:** Schema 只是定义了数据的"形状"，但没有说明数据从哪里来。

```graphql
# Schema 只是说"有一个 user 字段，返回 User 类型"
type Query {
  user(id: ID!): User
}

type User {
  id: ID!
  name: String!
}
```

```javascript
// Resolver 告诉 GraphQL "如何获取这个数据"
const resolvers = {
  Query: {
    user: (parent, { id }) => {
      // 这里可以从数据库、API、文件等任何地方获取数据
      return database.users.findById(id);
    }
  }
};
```

**类比：** Schema 像菜单，Resolver 像厨师。菜单告诉你有什么菜，厨师告诉你怎么做这道菜。

### Q2: 什么时候需要写 Resolver，什么时候不需要？
**A:** 

**不需要写 Resolver 的情况：**
```javascript
// 如果数据库字段名和 GraphQL 字段名一致
const user = { id: '1', name: '<PERSON>', email: '<EMAIL>' };

// 这些字段会自动使用默认 Resolver
type User {
  id: ID!      # user.id
  name: String! # user.name
  email: String! # user.email
}
```

**需要写 Resolver 的情况：**
```javascript
const resolvers = {
  User: {
    // 1. 字段名不匹配
    fullName: (user) => `${user.first_name} ${user.last_name}`,
    
    // 2. 需要计算
    age: (user) => calculateAge(user.birth_date),
    
    // 3. 需要查询其他数据
    posts: (user) => Post.findByUserId(user.id),
    
    // 4. 需要格式化
    createdAt: (user) => formatDate(user.created_at)
  }
};
```

### Q3: Resolver 的执行顺序是怎样的？
**A:** GraphQL 按照查询的结构执行 Resolver：

```graphql
query {
  user(id: "1") {    # 1. 先执行 Query.user
    name             # 2. 然后执行 User.name (默认 resolver)
    posts {          # 3. 执行 User.posts
      title          # 4. 为每个 post 执行 Post.title
      author {       # 5. 为每个 post 执行 Post.author
        name         # 6. 为每个 author 执行 User.name
      }
    }
  }
}
```

**执行流程：**
```javascript
// 1. Query.user(parent=null, args={id:"1"})
// 2. User.posts(parent=user对象)
// 3. Post.author(parent=post对象) - 为每个post执行
// 4. User.name(parent=author对象) - 为每个author执行
```

## 2. 参数和上下文问题

### Q4: parent 参数什么时候有值，什么时候是 null？
**A:** 

```javascript
const resolvers = {
  Query: {
    user: (parent, args) => {
      console.log(parent); // null - Query 是根，没有父对象
      return { id: '1', name: 'John' };
    }
  },
  
  User: {
    posts: (parent, args) => {
      console.log(parent); // { id: '1', name: 'John' } - 来自 Query.user
      return Post.findByUserId(parent.id);
    }
  },
  
  Post: {
    author: (parent, args) => {
      console.log(parent); // post 对象 - 来自 User.posts
      return User.findById(parent.authorId);
    }
  }
};
```

**规律：**
- 根字段（Query、Mutation、Subscription）的 parent 总是 null
- 其他字段的 parent 是上一级 Resolver 返回的对象

### Q5: context 应该放什么内容？
**A:** context 用于在所有 Resolver 之间共享数据：

```javascript
// 服务器配置
const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: ({ req }) => ({
    // 1. 当前用户信息
    user: getCurrentUser(req),
    
    // 2. 数据库连接
    db: database,
    
    // 3. 外部 API 客户端
    apis: {
      userService: new UserService(),
      emailService: new EmailService()
    },
    
    // 4. 请求信息
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    
    // 5. 缓存和加载器
    loaders: {
      user: new DataLoader(batchLoadUsers),
      post: new DataLoader(batchLoadPosts)
    }
  })
});

// Resolver 中使用
const resolvers = {
  Query: {
    me: (parent, args, { user, db }) => {
      if (!user) throw new Error('未登录');
      return db.users.findById(user.id);
    }
  }
};
```

### Q6: 如何在 Resolver 之间传递数据？
**A:** 

**方法1：通过 parent 传递**
```javascript
const resolvers = {
  Query: {
    user: () => ({
      id: '1',
      name: 'John',
      // 可以添加额外数据传递给子 Resolver
      _metadata: { source: 'database', timestamp: Date.now() }
    })
  },
  
  User: {
    posts: (parent) => {
      console.log(parent._metadata); // 可以访问父级传递的数据
      return Post.findByUserId(parent.id);
    }
  }
};
```

**方法2：通过 context 传递**
```javascript
const resolvers = {
  Query: {
    user: (parent, args, context) => {
      // 在 context 中存储数据
      context.currentUserId = args.id;
      return User.findById(args.id);
    }
  },
  
  User: {
    posts: (parent, args, context) => {
      // 从 context 中读取数据
      console.log(context.currentUserId);
      return Post.findByUserId(parent.id);
    }
  }
};
```

## 3. 性能问题

### Q7: 什么是 N+1 问题？如何识别？
**A:** N+1 问题是指查询 N 个对象时，需要执行 1+N 次数据库查询。

**问题示例：**
```javascript
// 查询：获取所有文章及其作者
query {
  posts {        # 1次查询：获取所有文章
    title
    author {     # N次查询：为每篇文章查询作者
      name
    }
  }
}

// 有问题的 Resolver
const resolvers = {
  Query: {
    posts: () => Post.findAll()  // 1次查询，返回100篇文章
  },
  
  Post: {
    author: (post) => User.findById(post.authorId)  // 100次查询！
  }
};
```

**如何识别：**
```javascript
// 在 Resolver 中添加日志
const resolvers = {
  Post: {
    author: (post) => {
      console.log(`查询作者 ${post.authorId}`); // 会打印很多次
      return User.findById(post.authorId);
    }
  }
};
```

**解决方案：使用 DataLoader**
```javascript
import DataLoader from 'dataloader';

const userLoader = new DataLoader(async (userIds) => {
  console.log('批量查询用户:', userIds); // 只打印一次
  const users = await User.findByIds(userIds);
  return userIds.map(id => users.find(user => user.id === id));
});

const resolvers = {
  Post: {
    author: (post) => userLoader.load(post.authorId)  // 批量加载
  }
};
```

### Q8: DataLoader 如何工作？
**A:** DataLoader 收集一个事件循环中的所有请求，然后批量执行：

```javascript
// 创建 DataLoader
const userLoader = new DataLoader(async (userIds) => {
  // 这个函数接收一批 ID，返回对应的数据
  console.log('批量查询:', userIds); // ['1', '2', '3']
  
  const users = await User.findByIds(userIds);
  
  // 必须返回与输入顺序对应的数组
  return userIds.map(id => users.find(user => user.id === id));
});

// 使用 DataLoader
const resolvers = {
  Post: {
    author: (post) => {
      // 这些调用会被收集起来，批量执行
      return userLoader.load(post.authorId);
    }
  }
};

// 执行过程：
// 1. post1.author -> userLoader.load('1')
// 2. post2.author -> userLoader.load('2') 
// 3. post3.author -> userLoader.load('1')
// 4. 事件循环结束时，批量执行：batchLoadFn(['1', '2', '1'])
// 5. 返回结果分发给各个调用者
```

### Q9: 如何优化深层嵌套查询？
**A:** 

**问题：**
```graphql
query {
  users {
    posts {
      comments {
        author {
          posts {
            comments {
              # 无限嵌套...
            }
          }
        }
      }
    }
  }
}
```

**解决方案：**

1. **查询深度限制**
```javascript
import depthLimit from 'graphql-depth-limit';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  validationRules: [depthLimit(10)] // 限制查询深度
});
```

2. **查询复杂度分析**
```javascript
import { costAnalysis } from 'graphql-cost-analysis';

const server = new ApolloServer({
  plugins: [
    costAnalysis({
      maximumCost: 1000,
      defaultCost: 1,
      scalarCost: 1,
      objectCost: 1,
      listFactor: 10
    })
  ]
});
```

3. **智能预加载**
```javascript
const resolvers = {
  Query: {
    users: async (parent, args, context, info) => {
      // 分析查询结构，决定预加载什么数据
      const selections = getSelections(info);
      
      if (selections.includes('posts')) {
        return User.findAll({ include: ['posts'] });
      }
      
      return User.findAll();
    }
  }
};
```

## 4. 错误处理问题

### Q10: 如何在 Resolver 中处理错误？
**A:** 

**基本错误处理：**
```javascript
const resolvers = {
  Query: {
    user: async (parent, { id }) => {
      try {
        const user = await User.findById(id);
        
        if (!user) {
          throw new Error(`用户 ${id} 不存在`);
        }
        
        return user;
      } catch (error) {
        // 记录错误
        console.error('查询用户失败:', error);
        
        // 重新抛出错误
        throw error;
      }
    }
  }
};
```

**自定义错误类型：**
```javascript
class UserNotFoundError extends Error {
  constructor(userId) {
    super(`用户 ${userId} 不存在`);
    this.extensions = {
      code: 'USER_NOT_FOUND',
      userId: userId
    };
  }
}

const resolvers = {
  Query: {
    user: async (parent, { id }) => {
      const user = await User.findById(id);
      
      if (!user) {
        throw new UserNotFoundError(id);
      }
      
      return user;
    }
  }
};
```

### Q11: 如何处理部分失败的情况？
**A:** GraphQL 支持部分成功的响应：

```javascript
const resolvers = {
  User: {
    posts: async (user) => {
      try {
        return await Post.findByUserId(user.id);
      } catch (error) {
        // 记录错误但不阻止其他字段
        console.error('获取用户文章失败:', error);
        return []; // 返回空数组而不是抛出错误
      }
    },
    
    profile: async (user) => {
      try {
        return await Profile.findByUserId(user.id);
      } catch (error) {
        console.error('获取用户资料失败:', error);
        return null; // 返回 null 而不是抛出错误
      }
    }
  }
};

// 响应可能是：
{
  "data": {
    "user": {
      "name": "John",
      "posts": [],      // 失败了但返回空数组
      "profile": null   // 失败了但返回 null
    }
  },
  "errors": [
    // 错误信息仍然会在这里
  ]
}
```

## 5. 最佳实践问题

### Q12: 如何组织大型项目的 Resolver？
**A:** 

**按功能模块组织：**
```
resolvers/
├── index.js          # 合并所有 resolver
├── userResolvers.js  # 用户相关
├── postResolvers.js  # 文章相关
├── commentResolvers.js # 评论相关
└── shared/
    ├── auth.js       # 认证中间件
    └── validation.js # 验证函数
```

```javascript
// userResolvers.js
export const userResolvers = {
  Query: {
    user: async (parent, { id }) => User.findById(id),
    users: async () => User.findAll()
  },
  
  User: {
    posts: (user) => Post.findByUserId(user.id)
  }
};

// index.js
import { mergeResolvers } from '@graphql-tools/merge';
import { userResolvers } from './userResolvers.js';
import { postResolvers } from './postResolvers.js';

export const resolvers = mergeResolvers([
  userResolvers,
  postResolvers
]);
```

### Q13: 如何测试 Resolver？
**A:** 

```javascript
// userResolvers.test.js
import { userResolvers } from './userResolvers.js';

describe('User Resolvers', () => {
  test('Query.user 应该返回用户', async () => {
    // 模拟数据库
    const mockUser = { id: '1', name: 'John' };
    User.findById = jest.fn().mockResolvedValue(mockUser);
    
    // 调用 resolver
    const result = await userResolvers.Query.user(null, { id: '1' });
    
    // 断言
    expect(result).toEqual(mockUser);
    expect(User.findById).toHaveBeenCalledWith('1');
  });
  
  test('User.posts 应该返回用户的文章', async () => {
    const mockPosts = [{ id: '1', title: 'Test' }];
    Post.findByUserId = jest.fn().mockResolvedValue(mockPosts);
    
    const user = { id: '1', name: 'John' };
    const result = await userResolvers.User.posts(user);
    
    expect(result).toEqual(mockPosts);
    expect(Post.findByUserId).toHaveBeenCalledWith('1');
  });
});
```

---

**总结：**
理解 Resolver 的关键是要明白它们是 GraphQL 的"数据获取层"。每个字段都可能需要一个 Resolver 来说明如何获取数据。掌握了 Resolver 的工作原理，就掌握了 GraphQL 的核心！
