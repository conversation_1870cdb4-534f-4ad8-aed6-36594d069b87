# HEAD 方法详解

## 1. HEAD 方法概述

HEAD 方法是 HTTP 协议中一个重要但经常被忽视的方法。它的作用是获取资源的**元数据信息**，而不返回实际的响应体内容。

### 1.1 核心特点

| 特性 | 描述 |
|------|------|
| **响应体** | 无响应体，只返回头部信息 |
| **幂等性** | ✅ 幂等 |
| **安全性** | ✅ 安全（不修改服务器状态） |
| **缓存** | ✅ 可缓存 |
| **用途** | 获取资源元数据、检查资源状态 |

### 1.2 与 GET 的关系

HEAD 方法与 GET 方法几乎相同，唯一的区别是：
- **GET**: 返回完整的响应（头部 + 响应体）
- **HEAD**: 只返回响应头部，不返回响应体

```http
# GET 请求
GET /api/users/123 HTTP/1.1
Host: example.com

# 响应
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 156
Last-Modified: Wed, 21 Oct 2023 07:28:00 GMT

{
  "id": 123,
  "name": "张三",
  "email": "<EMAIL>"
}

# HEAD 请求
HEAD /api/users/123 HTTP/1.1
Host: example.com

# 响应（无响应体）
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 156
Last-Modified: Wed, 21 Oct 2023 07:28:00 GMT
```

## 2. HEAD 方法的主要用途

### 2.1 检查资源是否存在

```javascript
// 检查用户是否存在
HEAD /api/users/123

// 响应状态码说明：
// 200 OK - 用户存在
// 404 Not Found - 用户不存在
// 403 Forbidden - 用户存在但无权限访问
```

### 2.2 获取资源大小

```javascript
// 检查文件大小
HEAD /api/files/document.pdf

// 响应头
HTTP/1.1 200 OK
Content-Type: application/pdf
Content-Length: 2048576  // 文件大小：2MB
Last-Modified: Wed, 21 Oct 2023 07:28:00 GMT
```

### 2.3 检查资源修改时间

```javascript
// 检查资源最后修改时间
HEAD /api/articles/456

// 响应头
HTTP/1.1 200 OK
Content-Type: application/json
Last-Modified: Wed, 21 Oct 2023 07:28:00 GMT
ETag: "abc123def456"
```

### 2.4 验证缓存有效性

```javascript
// 客户端检查缓存是否过期
HEAD /api/users/123
If-Modified-Since: Wed, 21 Oct 2023 07:28:00 GMT

// 如果资源未修改
HTTP/1.1 304 Not Modified

// 如果资源已修改
HTTP/1.1 200 OK
Last-Modified: Thu, 22 Oct 2023 08:30:00 GMT
```

### 2.5 检查服务器支持的功能

```javascript
// 检查资源支持的操作
HEAD /api/users/123

// 响应头
HTTP/1.1 200 OK
Allow: GET, PUT, PATCH, DELETE
Accept-Ranges: bytes
```

## 3. 实际应用场景

### 3.1 文件下载前的预检查

```javascript
// 场景：大文件下载前检查
async function checkFileBeforeDownload(fileUrl) {
  try {
    const response = await fetch(fileUrl, { method: 'HEAD' });
    
    if (!response.ok) {
      throw new Error('文件不存在');
    }
    
    const fileSize = parseInt(response.headers.get('Content-Length'));
    const fileType = response.headers.get('Content-Type');
    const lastModified = response.headers.get('Last-Modified');
    
    console.log(`文件大小: ${(fileSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`文件类型: ${fileType}`);
    console.log(`最后修改: ${lastModified}`);
    
    // 检查文件是否太大
    if (fileSize > 100 * 1024 * 1024) { // 100MB
      const confirm = window.confirm('文件较大，确定要下载吗？');
      if (!confirm) return false;
    }
    
    return true;
  } catch (error) {
    console.error('文件检查失败:', error);
    return false;
  }
}

// 使用示例
async function downloadFile(fileUrl) {
  const canDownload = await checkFileBeforeDownload(fileUrl);
  if (canDownload) {
    // 执行实际下载
    window.location.href = fileUrl;
  }
}
```

### 3.2 资源监控和健康检查

```javascript
// 场景：API 健康检查
async function checkAPIHealth(endpoints) {
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const startTime = Date.now();
      const response = await fetch(endpoint, { 
        method: 'HEAD',
        timeout: 5000 
      });
      const responseTime = Date.now() - startTime;
      
      results.push({
        endpoint,
        status: response.status,
        responseTime,
        available: response.ok
      });
    } catch (error) {
      results.push({
        endpoint,
        status: 'ERROR',
        responseTime: -1,
        available: false,
        error: error.message
      });
    }
  }
  
  return results;
}

// 使用示例
const endpoints = [
  '/api/users',
  '/api/articles',
  '/api/files'
];

checkAPIHealth(endpoints).then(results => {
  console.table(results);
});
```

### 3.3 缓存策略实现

```javascript
// 场景：智能缓存管理
class SmartCache {
  constructor() {
    this.cache = new Map();
  }
  
  async get(url) {
    const cached = this.cache.get(url);
    
    if (cached) {
      // 使用 HEAD 检查缓存是否过期
      const isValid = await this.validateCache(url, cached.etag);
      if (isValid) {
        console.log('使用缓存数据');
        return cached.data;
      }
    }
    
    // 缓存无效或不存在，获取新数据
    console.log('获取新数据');
    return await this.fetchFresh(url);
  }
  
  async validateCache(url, etag) {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: {
          'If-None-Match': etag
        }
      });
      
      // 304 表示缓存仍然有效
      return response.status === 304;
    } catch (error) {
      console.error('缓存验证失败:', error);
      return false;
    }
  }
  
  async fetchFresh(url) {
    const response = await fetch(url);
    const data = await response.json();
    const etag = response.headers.get('ETag');
    
    // 更新缓存
    this.cache.set(url, {
      data,
      etag,
      timestamp: Date.now()
    });
    
    return data;
  }
}

// 使用示例
const cache = new SmartCache();
const userData = await cache.get('/api/users/123');
```

### 3.4 断点续传支持检查

```javascript
// 场景：检查服务器是否支持断点续传
async function checkResumeSupport(fileUrl) {
  try {
    const response = await fetch(fileUrl, { method: 'HEAD' });
    
    const acceptRanges = response.headers.get('Accept-Ranges');
    const contentLength = response.headers.get('Content-Length');
    
    return {
      supportsResume: acceptRanges === 'bytes',
      fileSize: parseInt(contentLength),
      etag: response.headers.get('ETag')
    };
  } catch (error) {
    console.error('检查断点续传支持失败:', error);
    return {
      supportsResume: false,
      fileSize: 0,
      etag: null
    };
  }
}

// 使用示例
async function downloadWithResume(fileUrl) {
  const resumeInfo = await checkResumeSupport(fileUrl);
  
  if (resumeInfo.supportsResume) {
    console.log('服务器支持断点续传');
    // 实现断点续传逻辑
  } else {
    console.log('服务器不支持断点续传，使用普通下载');
    // 实现普通下载逻辑
  }
}
```

## 4. 服务端实现示例

### 4.1 Node.js + Express 实现

```javascript
const express = require('express');
const app = express();

// HEAD 方法实现
app.head('/api/users/:id', async (req, res) => {
  const userId = req.params.id;
  
  try {
    // 检查用户是否存在（不需要返回完整数据）
    const userExists = await checkUserExists(userId);
    
    if (!userExists) {
      return res.status(404).end();
    }
    
    // 获取用户元数据
    const userMeta = await getUserMetadata(userId);
    
    // 设置响应头
    res.set({
      'Content-Type': 'application/json',
      'Content-Length': userMeta.contentLength,
      'Last-Modified': userMeta.lastModified,
      'ETag': userMeta.etag,
      'Cache-Control': 'max-age=300'
    });
    
    // HEAD 方法不返回响应体
    res.status(200).end();
    
  } catch (error) {
    console.error('HEAD 请求处理失败:', error);
    res.status(500).end();
  }
});

// 对应的 GET 方法（保持一致性）
app.get('/api/users/:id', async (req, res) => {
  const userId = req.params.id;
  
  try {
    const user = await getUserById(userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'user_not_found',
        message: '用户不存在'
      });
    }
    
    // 设置相同的响应头
    res.set({
      'Content-Type': 'application/json',
      'Last-Modified': user.updated_at,
      'ETag': user.etag,
      'Cache-Control': 'max-age=300'
    });
    
    res.json(user);
    
  } catch (error) {
    console.error('GET 请求处理失败:', error);
    res.status(500).json({
      error: 'internal_error',
      message: '服务器内部错误'
    });
  }
});

// 文件 HEAD 方法实现
app.head('/api/files/:filename', async (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(__dirname, 'uploads', filename);
  
  try {
    const stats = await fs.stat(filePath);
    
    res.set({
      'Content-Type': getContentType(filename),
      'Content-Length': stats.size,
      'Last-Modified': stats.mtime.toUTCString(),
      'Accept-Ranges': 'bytes',
      'ETag': generateETag(stats)
    });
    
    res.status(200).end();
    
  } catch (error) {
    if (error.code === 'ENOENT') {
      res.status(404).end();
    } else {
      res.status(500).end();
    }
  }
});

// 辅助函数
async function checkUserExists(userId) {
  // 实现用户存在性检查逻辑
  // 返回 true/false
}

async function getUserMetadata(userId) {
  // 获取用户元数据，不需要完整数据
  // 返回 { contentLength, lastModified, etag }
}

function getContentType(filename) {
  // 根据文件扩展名返回 MIME 类型
}

function generateETag(stats) {
  // 生成 ETag
  return `"${stats.size}-${stats.mtime.getTime()}"`;
}
```

## 5. 最佳实践

### 5.1 HEAD 与 GET 的一致性

```javascript
// ✅ 正确：HEAD 和 GET 返回相同的头部信息
app.get('/api/resource', (req, res) => {
  res.set('Custom-Header', 'value');
  res.json(data);
});

app.head('/api/resource', (req, res) => {
  res.set('Custom-Header', 'value'); // 保持一致
  res.end();
});

// ❌ 错误：HEAD 和 GET 返回不同的头部信息
app.get('/api/resource', (req, res) => {
  res.set('Custom-Header', 'value1');
  res.json(data);
});

app.head('/api/resource', (req, res) => {
  res.set('Custom-Header', 'value2'); // 不一致！
  res.end();
});
```

### 5.2 性能优化

```javascript
// ✅ 优化：HEAD 请求避免加载完整数据
app.head('/api/users/:id', async (req, res) => {
  // 只检查存在性，不加载完整用户数据
  const exists = await User.exists({ id: req.params.id });
  
  if (exists) {
    res.status(200).end();
  } else {
    res.status(404).end();
  }
});

// ❌ 低效：HEAD 请求加载了完整数据
app.head('/api/users/:id', async (req, res) => {
  const user = await User.findById(req.params.id); // 浪费资源
  
  if (user) {
    res.status(200).end();
  } else {
    res.status(404).end();
  }
});
```

### 5.3 错误处理

```javascript
app.head('/api/resource/:id', async (req, res) => {
  try {
    const exists = await checkResourceExists(req.params.id);
    
    if (!exists) {
      return res.status(404).end();
    }
    
    // 设置适当的头部信息
    res.set({
      'Content-Type': 'application/json',
      'Cache-Control': 'max-age=300'
    });
    
    res.status(200).end();
    
  } catch (error) {
    console.error('HEAD 请求错误:', error);
    res.status(500).end(); // HEAD 请求的错误响应也不应有响应体
  }
});
```

## 6. 总结

HEAD 方法是一个非常有用的 HTTP 方法，主要用于：

1. **资源存在性检查** - 快速验证资源是否存在
2. **元数据获取** - 获取文件大小、修改时间等信息
3. **缓存验证** - 检查缓存是否仍然有效
4. **性能优化** - 避免传输不必要的响应体数据
5. **健康检查** - 监控 API 端点的可用性

**关键要点**：
- HEAD 响应必须与对应的 GET 响应头部保持一致
- 不返回响应体，只返回头部信息
- 具有幂等性和安全性
- 适合用于预检查和元数据获取场景

正确使用 HEAD 方法可以显著提高应用的性能和用户体验。
