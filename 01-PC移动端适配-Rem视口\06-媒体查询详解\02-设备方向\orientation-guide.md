# 设备方向适配指南

## 概述

设备方向检测是移动端开发的重要技能，通过 `orientation` 媒体特性可以检测设备是纵向还是横向，从而提供最佳的用户体验。

## 基础概念

### orientation 媒体特性

```css
/* 纵向（竖屏）：高度 > 宽度 */
@media (orientation: portrait) {
    /* 竖屏样式 */
}

/* 横向（横屏）：宽度 > 高度 */
@media (orientation: landscape) {
    /* 横屏样式 */
}
```

### 判断逻辑
- **portrait（纵向）**：`height > width`
- **landscape（横向）**：`width > height`

## 实际应用场景

### 1. 导航栏适配

```css
/* 基础导航样式 */
.navbar {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 1rem;
}

/* 横屏时减少导航高度，节省垂直空间 */
@media (orientation: landscape) and (max-height: 500px) {
    .navbar {
        height: 40px;
        padding: 0 0.5rem;
    }
    
    .navbar .logo {
        font-size: 1.2rem;
    }
}

/* 竖屏时可以使用更多垂直空间 */
@media (orientation: portrait) {
    .navbar {
        height: 70px;
    }
    
    .navbar .menu-toggle {
        display: block; /* 显示汉堡菜单 */
    }
}
```

### 2. 侧边栏布局

```css
.layout {
    display: flex;
}

.sidebar {
    width: 250px;
    background: #f5f5f5;
}

.main-content {
    flex: 1;
}

/* 竖屏时隐藏侧边栏，使用抽屉式导航 */
@media (orientation: portrait) {
    .sidebar {
        position: fixed;
        left: -250px;
        top: 0;
        height: 100vh;
        transition: left 0.3s ease;
        z-index: 1000;
    }
    
    .sidebar.open {
        left: 0;
    }
    
    .main-content {
        width: 100%;
    }
}

/* 横屏时显示侧边栏 */
@media (orientation: landscape) and (min-width: 768px) {
    .sidebar {
        position: static;
        display: block;
    }
}
```

### 3. 表格适配

```css
.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

/* 竖屏时使用卡片式布局 */
@media (orientation: portrait) and (max-width: 767px) {
    table, thead, tbody, th, td, tr {
        display: block;
    }
    
    thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }
    
    tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        padding: 10px;
    }
    
    td {
        border: none;
        position: relative;
        padding-left: 50%;
    }
    
    td:before {
        content: attr(data-label) ": ";
        position: absolute;
        left: 6px;
        width: 45%;
        font-weight: bold;
    }
}
```

### 4. 视频播放器

```css
.video-container {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.video-player {
    width: 100%;
    height: auto;
}

/* 横屏时全屏显示 */
@media (orientation: landscape) and (max-height: 500px) {
    .video-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        max-width: none;
        z-index: 9999;
        background: black;
    }
    
    .video-player {
        height: 100vh;
        object-fit: contain;
    }
}
```

## 移动端特殊处理

### iOS Safari 地址栏处理

```css
/* iOS Safari 在横屏时地址栏会影响视口高度 */
@media (orientation: landscape) {
    .full-height {
        /* 使用 vh 单位时要考虑地址栏 */
        height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    }
}
```

### Android 虚拟键盘处理

```css
/* Android 设备横屏时虚拟键盘会占用大量空间 */
@media (orientation: landscape) and (max-height: 500px) {
    .input-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        z-index: 1000;
    }
}
```

## 结合其他媒体特性

### 尺寸 + 方向

```css
/* 小屏幕竖屏 */
@media (max-width: 767px) and (orientation: portrait) {
    .grid {
        grid-template-columns: 1fr;
    }
}

/* 小屏幕横屏 */
@media (max-width: 767px) and (orientation: landscape) {
    .grid {
        grid-template-columns: 1fr 1fr;
    }
}

/* 大屏幕（不区分方向） */
@media (min-width: 768px) {
    .grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

### 高度 + 方向

```css
/* 横屏且高度较小时的特殊处理 */
@media (orientation: landscape) and (max-height: 450px) {
    .modal {
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .header {
        height: 40px;
    }
    
    .footer {
        height: 30px;
    }
}
```

## JavaScript 配合

```javascript
// 监听方向变化
window.addEventListener('orientationchange', function() {
    // 延迟执行，等待浏览器完成方向切换
    setTimeout(function() {
        // 重新计算布局
        updateLayout();
    }, 100);
});

// 获取当前方向
function getCurrentOrientation() {
    return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
}

// 更新布局
function updateLayout() {
    const orientation = getCurrentOrientation();
    document.body.className = orientation;
}
```

## 最佳实践

1. **优先考虑内容**：确保核心内容在任何方向都能正常显示
2. **渐进增强**：从基础布局开始，逐步添加方向特定的优化
3. **测试充分**：在真实设备上测试横竖屏切换
4. **性能优化**：避免在方向切换时重新加载资源
5. **用户体验**：提供平滑的过渡动画
