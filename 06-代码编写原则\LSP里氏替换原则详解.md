# LSP 里氏替换原则详解

## 🎯 什么是LSP？

### 基本定义
**LSP = Liskov Substitution Principle = 里氏替换原则**

> 子类对象应该能够替换父类对象，并且程序的行为不会出现错误。

### 通俗理解
想象一下现实生活中的例子：
- **汽车**是父类
- **轿车**、**卡车**、**摩托车**是子类
- 如果某个地方需要"汽车"，那么用"轿车"或"卡车"替换应该都能正常工作
- 但如果用"自行车"替换就不行，因为自行车不是汽车的子类

### 编程中的LSP
```javascript
// 如果这段代码能正常工作
function 使用交通工具(vehicle) {
    vehicle.start();    // 启动
    vehicle.move();     // 移动
    vehicle.stop();     // 停止
}

const car = new Car();
使用交通工具(car);  // 正常工作

// 那么用子类替换也应该正常工作
const truck = new Truck();  // Truck继承自Car
使用交通工具(truck);  // 也应该正常工作
```

---

## 🚫 违反LSP的典型例子

### 经典的"正方形-长方形"问题

```javascript
// 长方形类
class Rectangle {
    constructor(width, height) {
        this.width = width;
        this.height = height;
    }
    
    setWidth(width) {
        this.width = width;
    }
    
    setHeight(height) {
        this.height = height;
    }
    
    getWidth() {
        return this.width;
    }
    
    getHeight() {
        return this.height;
    }
    
    getArea() {
        return this.width * this.height;
    }
}

// 正方形类 - 看起来合理的继承
class Square extends Rectangle {
    constructor(side) {
        super(side, side);
    }
    
    // 正方形的宽高必须相等
    setWidth(width) {
        this.width = width;
        this.height = width;  // 强制保持正方形
    }
    
    setHeight(height) {
        this.width = height;  // 强制保持正方形
        this.height = height;
    }
}

// 测试函数 - 期望长方形的行为
function testRectangle(rectangle) {
    rectangle.setWidth(5);
    rectangle.setHeight(4);
    
    // 期望面积是 5 * 4 = 20
    console.log(`面积应该是20，实际是: ${rectangle.getArea()}`);
    
    // 期望宽度是5，高度是4
    console.log(`宽度应该是5，实际是: ${rectangle.getWidth()}`);
    console.log(`高度应该是4，实际是: ${rectangle.getHeight()}`);
}

// 测试
const rectangle = new Rectangle(0, 0);
testRectangle(rectangle);
// 输出：面积应该是20，实际是: 20 ✓
//      宽度应该是5，实际是: 5 ✓  
//      高度应该是4，实际是: 4 ✓

const square = new Square(0);
testRectangle(square);
// 输出：面积应该是20，实际是: 16 ❌
//      宽度应该是5，实际是: 4 ❌
//      高度应该是4，实际是: 4 ❌
```

### 问题分析
1. **行为不一致**：Square改变了Rectangle的预期行为
2. **违反了替换原则**：用Square替换Rectangle会导致错误结果
3. **破坏了客户端代码**：testRectangle函数无法正常工作

---

## ✅ 遵循LSP的正确设计

### 方案1：重新设计继承结构

```javascript
// 抽象基类 - 形状
class Shape {
    getArea() {
        throw new Error('子类必须实现getArea方法');
    }
}

// 长方形 - 继承自Shape
class Rectangle extends Shape {
    constructor(width, height) {
        super();
        this.width = width;
        this.height = height;
    }
    
    setWidth(width) {
        this.width = width;
    }
    
    setHeight(height) {
        this.height = height;
    }
    
    getWidth() {
        return this.width;
    }
    
    getHeight() {
        return this.height;
    }
    
    getArea() {
        return this.width * this.height;
    }
}

// 正方形 - 也继承自Shape，而不是Rectangle
class Square extends Shape {
    constructor(side) {
        super();
        this.side = side;
    }
    
    setSide(side) {
        this.side = side;
    }
    
    getSide() {
        return this.side;
    }
    
    getArea() {
        return this.side * this.side;
    }
}

// 现在测试函数需要针对不同的形状
function testShape(shape) {
    console.log(`形状面积: ${shape.getArea()}`);
}

function testRectangle(rectangle) {
    if (!(rectangle instanceof Rectangle)) {
        throw new Error('需要Rectangle实例');
    }
    
    rectangle.setWidth(5);
    rectangle.setHeight(4);
    console.log(`长方形面积: ${rectangle.getArea()}`); // 20
}

function testSquare(square) {
    if (!(square instanceof Square)) {
        throw new Error('需要Square实例');
    }
    
    square.setSide(5);
    console.log(`正方形面积: ${square.getArea()}`); // 25
}
```

### 方案2：使用组合而非继承

```javascript
// 尺寸接口
class Dimensions {
    getArea() {
        throw new Error('必须实现getArea方法');
    }
}

// 长方形尺寸
class RectangleDimensions extends Dimensions {
    constructor(width, height) {
        super();
        this.width = width;
        this.height = height;
    }
    
    setWidth(width) {
        this.width = width;
    }
    
    setHeight(height) {
        this.height = height;
    }
    
    getArea() {
        return this.width * this.height;
    }
}

// 正方形尺寸
class SquareDimensions extends Dimensions {
    constructor(side) {
        super();
        this.side = side;
    }
    
    setSide(side) {
        this.side = side;
    }
    
    getArea() {
        return this.side * this.side;
    }
}

// 形状类使用组合
class Shape {
    constructor(dimensions) {
        this.dimensions = dimensions;
    }
    
    getArea() {
        return this.dimensions.getArea();
    }
}

// 使用
const rectangle = new Shape(new RectangleDimensions(5, 4));
const square = new Shape(new SquareDimensions(5));

console.log(rectangle.getArea()); // 20
console.log(square.getArea());    // 25
```

---

## 🦅 更多LSP违反的例子

### 例子1：鸟类继承问题

#### ❌ 违反LSP的设计

```javascript
class Bird {
    fly() {
        console.log('鸟儿在飞翔');
    }
    
    eat() {
        console.log('鸟儿在吃食');
    }
}

class Duck extends Bird {
    fly() {
        console.log('鸭子在飞翔');
    }
    
    swim() {
        console.log('鸭子在游泳');
    }
}

class Penguin extends Bird {
    fly() {
        throw new Error('企鹅不会飞！'); // 违反了LSP
    }
    
    swim() {
        console.log('企鹅在游泳');
    }
}

// 测试函数期望所有鸟都能飞
function makeBirdFly(bird) {
    bird.fly(); // 对企鹅会抛出异常
}

const duck = new Duck();
const penguin = new Penguin();

makeBirdFly(duck);    // 正常工作
makeBirdFly(penguin); // 抛出异常！违反了LSP
```

#### ✅ 遵循LSP的设计

```javascript
// 重新设计继承结构
class Bird {
    eat() {
        console.log('鸟儿在吃食');
    }
}

class FlyingBird extends Bird {
    fly() {
        console.log('会飞的鸟在飞翔');
    }
}

class SwimmingBird extends Bird {
    swim() {
        console.log('会游泳的鸟在游泳');
    }
}

class Duck extends FlyingBird {
    fly() {
        console.log('鸭子在飞翔');
    }
    
    swim() {
        console.log('鸭子在游泳');
    }
}

class Penguin extends SwimmingBird {
    swim() {
        console.log('企鹅在游泳');
    }
}

// 现在测试函数更加明确
function makeFlyingBirdFly(bird) {
    if (!(bird instanceof FlyingBird)) {
        throw new Error('需要会飞的鸟');
    }
    bird.fly();
}

function makeSwimmingBirdSwim(bird) {
    if (!(bird instanceof SwimmingBird)) {
        throw new Error('需要会游泳的鸟');
    }
    bird.swim();
}

const duck = new Duck();
const penguin = new Penguin();

makeFlyingBirdFly(duck);      // 正常工作
makeSwimmingBirdSwim(penguin); // 正常工作
```

### 例子2：文件处理问题

#### ❌ 违反LSP的设计

```javascript
class File {
    read() {
        return '文件内容';
    }
    
    write(content) {
        console.log(`写入内容: ${content}`);
    }
}

class ReadOnlyFile extends File {
    write(content) {
        throw new Error('只读文件不能写入！'); // 违反LSP
    }
}

// 客户端代码期望所有File都能读写
function processFile(file) {
    const content = file.read();
    file.write(content + ' - 已处理'); // 对ReadOnlyFile会失败
}

const normalFile = new File();
const readOnlyFile = new ReadOnlyFile();

processFile(normalFile);   // 正常工作
processFile(readOnlyFile); // 抛出异常！
```

#### ✅ 遵循LSP的设计

```javascript
// 基础文件接口
class FileBase {
    read() {
        throw new Error('子类必须实现read方法');
    }
}

// 可读文件
class ReadableFile extends FileBase {
    read() {
        return '文件内容';
    }
}

// 可写文件
class WritableFile extends ReadableFile {
    write(content) {
        console.log(`写入内容: ${content}`);
    }
}

// 只读文件
class ReadOnlyFile extends ReadableFile {
    // 只实现read方法，不提供write方法
}

// 读写文件
class ReadWriteFile extends WritableFile {
    // 继承了read和write方法
}

// 现在客户端代码更明确
function readFile(file) {
    if (!(file instanceof ReadableFile)) {
        throw new Error('需要可读文件');
    }
    return file.read();
}

function writeToFile(file, content) {
    if (!(file instanceof WritableFile)) {
        throw new Error('需要可写文件');
    }
    file.write(content);
}

function processWritableFile(file) {
    if (!(file instanceof WritableFile)) {
        throw new Error('需要可写文件');
    }
    const content = file.read();
    file.write(content + ' - 已处理');
}
```

---

## 🔍 如何检查LSP违反

### 检查清单

#### 1. 异常检查
```javascript
// 子类是否抛出父类不会抛出的异常？
class Parent {
    method() {
        // 正常执行，不抛异常
    }
}

class Child extends Parent {
    method() {
        throw new Error('不支持此操作'); // ❌ 违反LSP
    }
}
```

#### 2. 前置条件检查
```javascript
// 子类是否加强了前置条件？
class Calculator {
    divide(a, b) {
        // 父类只要求b不为0
        if (b === 0) throw new Error('除数不能为0');
        return a / b;
    }
}

class StrictCalculator extends Calculator {
    divide(a, b) {
        // 子类要求更严格的条件 ❌ 违反LSP
        if (b === 0) throw new Error('除数不能为0');
        if (b < 0) throw new Error('除数不能为负数'); // 加强了前置条件
        return a / b;
    }
}
```

#### 3. 后置条件检查
```javascript
// 子类是否削弱了后置条件？
class DataProcessor {
    process(data) {
        // 父类保证返回非空结果
        return data.map(item => item.toUpperCase());
    }
}

class LazyProcessor extends DataProcessor {
    process(data) {
        // 子类可能返回空结果 ❌ 违反LSP
        if (data.length > 1000) {
            return null; // 削弱了后置条件
        }
        return super.process(data);
    }
}
```

#### 4. 不变量检查
```javascript
// 子类是否维护了父类的不变量？
class BankAccount {
    constructor(balance) {
        this.balance = balance;
    }
    
    withdraw(amount) {
        if (amount <= this.balance) {
            this.balance -= amount;
            return true;
        }
        return false;
    }
    
    getBalance() {
        return this.balance; // 不变量：余额不能为负
    }
}

class OverdraftAccount extends BankAccount {
    withdraw(amount) {
        // 允许透支 ❌ 可能违反父类的不变量
        this.balance -= amount;
        return true;
    }
}
```

---

## 🛠️ LSP的实践指南

### 1. 设计继承时的原则

#### 使用"IS-A"关系测试
```javascript
// 问自己：子类真的"是一个"父类吗？
// ✅ 正确：汽车 IS-A 交通工具
// ✅ 正确：狗 IS-A 动物
// ❌ 错误：正方形 IS-A 长方形（行为上不一致）
```

#### 契约式设计
```javascript
class Service {
    /**
     * 处理请求
     * @param {Object} request - 请求对象
     * @returns {Object} 响应对象
     * 
     * 前置条件：request不能为null
     * 后置条件：返回值不能为null
     * 不变量：处理过程中不修改request
     */
    process(request) {
        // 实现
    }
}

class CachedService extends Service {
    process(request) {
        // 必须遵守父类的契约
        if (!request) throw new Error('request不能为null');
        
        // 实现缓存逻辑
        const result = this.getFromCache(request) || super.process(request);
        
        // 确保返回值不为null
        return result || {};
    }
}
```

### 2. 重构违反LSP的代码

#### 步骤1：识别问题
```javascript
// 寻找这些模式：
// - 子类抛出新异常
// - 子类空实现方法
// - 子类改变方法语义
// - 需要类型检查的代码
```

#### 步骤2：重新设计
```javascript
// 选择合适的解决方案：
// - 重新设计继承结构
// - 使用组合替代继承
// - 引入接口分离
// - 使用策略模式
```

#### 步骤3：验证设计
```javascript
// 确保：
// - 子类可以完全替换父类
// - 不需要类型检查
// - 客户端代码不需要修改
// - 所有测试都能通过
```

---

## 🎯 总结

### LSP的核心思想
**子类必须能够替换父类，且不改变程序的正确性**

### 关键要点
1. **行为兼容**：子类的行为必须与父类兼容
2. **契约遵守**：子类必须遵守父类的契约
3. **可替换性**：任何使用父类的地方都能用子类替换

### 违反LSP的信号
- 子类抛出父类不会抛出的异常
- 子类有空实现或抛异常的方法
- 需要用instanceof检查类型
- 子类改变了方法的预期行为

### 解决方案
- 重新设计继承结构
- 使用组合替代继承
- 应用接口隔离原则
- 使用多态和策略模式

### 记忆口诀
**"子承父业，行为如一，随时替换，程序无忧"**

LSP确保了继承的正确性，是面向对象设计中非常重要的原则！
