# Git 数据恢复详解

## 🔄 数据恢复概览

### 📋 Git 数据丢失的常见场景
Git 虽然是一个强大的版本控制系统，但在某些操作中仍可能导致数据丢失。了解恢复方法可以帮你挽救重要的工作成果。

```bash
# 常见数据丢失场景：
├── 误删除分支 - 删除了包含重要工作的分支
├── 错误的 reset - 使用 --hard 重置丢失提交
├── 误删除提交 - rebase 或 filter-branch 删除提交
├── 工作区文件丢失 - 意外删除或覆盖文件
├── 暂存区数据丢失 - 错误的 reset 操作
└── 仓库损坏 - 文件系统错误或硬件故障
```

### 🛡️ Git 的数据保护机制
```bash
# Git 内置的数据保护：
├── 引用日志 (reflog) - 记录引用变更历史
├── 对象存储 - 所有数据以对象形式存储
├── 垃圾回收延迟 - 删除的对象有保留期
├── 备份机制 - 远程仓库作为天然备份
└── 完整性检查 - SHA-1 校验确保数据完整性
```

## 🔍 引用日志 (Reflog) 恢复

### 📊 理解 Reflog

#### 什么是 Reflog
```bash
# Reflog 记录了引用（分支、HEAD）的变更历史
git reflog
# 输出示例：
# a1b2c3d (HEAD -> main) HEAD@{0}: commit: Add new feature
# e4f5g6h HEAD@{1}: checkout: moving from feature to main
# i7j8k9l HEAD@{2}: commit: Fix bug in authentication
# m0n1o2p HEAD@{3}: checkout: moving from main to feature
```

#### 查看 Reflog
```bash
# 查看 HEAD 的 reflog
git reflog

# 查看指定分支的 reflog
git reflog show main
git reflog show feature-branch

# 查看详细信息
git reflog --pretty=fuller

# 查看指定时间范围
git reflog --since="2 hours ago"
git reflog --until="yesterday"

# 查看所有引用的 reflog
git reflog --all
```

### 🔄 使用 Reflog 恢复数据

#### 恢复误删除的分支
```bash
# 场景：误删除了 feature-branch
git branch -D feature-branch  # 误删除

# 1. 查看 reflog 找到分支的最后位置
git reflog
# 找到类似这样的记录：
# a1b2c3d HEAD@{5}: checkout: moving from feature-branch to main

# 2. 恢复分支
git branch feature-branch a1b2c3d
# 或者
git checkout -b feature-branch a1b2c3d

# 3. 验证恢复结果
git log --oneline feature-branch
```

#### 恢复错误的 reset 操作
```bash
# 场景：错误执行了 git reset --hard HEAD~3
git reset --hard HEAD~3  # 误操作，丢失了 3 个提交

# 1. 查看 reflog
git reflog
# 找到 reset 操作前的状态：
# e4f5g6h HEAD@{1}: reset: moving to HEAD~3
# a1b2c3d HEAD@{0}: commit: Latest work  # 这是我们要恢复的

# 2. 恢复到 reset 前的状态
git reset --hard HEAD@{0}
# 或者使用具体的提交哈希
git reset --hard a1b2c3d

# 3. 验证恢复
git log --oneline
```

#### 恢复误删除的提交
```bash
# 场景：在 rebase 过程中意外删除了提交

# 1. 查看 reflog 找到丢失的提交
git reflog
# 找到被删除的提交：
# x9y8z7w HEAD@{3}: commit: Important feature

# 2. 创建新分支保存恢复的提交
git branch recovery-branch x9y8z7w

# 3. 或者直接 cherry-pick 到当前分支
git cherry-pick x9y8z7w

# 4. 验证恢复结果
git log --oneline recovery-branch
```

## 🔧 对象级别恢复

### 📦 Git 对象系统

#### 理解 Git 对象
```bash
# Git 中的四种对象类型：
├── blob - 文件内容
├── tree - 目录结构
├── commit - 提交对象
└── tag - 标签对象

# 查看对象信息
git cat-file -t a1b2c3d  # 查看对象类型
git cat-file -p a1b2c3d  # 查看对象内容
git cat-file -s a1b2c3d  # 查看对象大小
```

#### 查找丢失的对象
```bash
# 使用 fsck 查找悬空对象
git fsck --lost-found
# 输出示例：
# dangling commit a1b2c3d4e5f6789...
# dangling blob b2c3d4e5f6789a1...

# 查看悬空提交
git show a1b2c3d4e5f6789

# 查看悬空 blob
git show b2c3d4e5f6789a1

# 查找所有不可达的对象
git fsck --unreachable
```

### 🔍 高级对象恢复

#### 恢复特定文件的历史版本
```bash
# 场景：需要恢复某个文件的特定版本

# 1. 查找文件的历史版本
git log --oneline --follow -- path/to/file.txt

# 2. 查看特定版本的文件内容
git show commit-hash:path/to/file.txt

# 3. 恢复文件到特定版本
git checkout commit-hash -- path/to/file.txt

# 4. 或者将内容保存到新文件
git show commit-hash:path/to/file.txt > recovered-file.txt
```

#### 从损坏的仓库恢复
```bash
# 场景：仓库文件损坏

# 1. 检查仓库完整性
git fsck --full

# 2. 尝试修复
git gc --aggressive --prune=now

# 3. 如果有备份，从备份恢复
git clone --mirror backup-repo.git
cd backup-repo.git
git push --mirror original-repo.git

# 4. 重建索引
rm .git/index
git reset
```

## 💾 工作区和暂存区恢复

### 📁 工作区文件恢复

#### 恢复误删除的文件
```bash
# 场景：意外删除了工作区的文件

# 1. 如果文件已被 Git 跟踪
git checkout HEAD -- deleted-file.txt

# 2. 如果文件在暂存区
git checkout -- deleted-file.txt

# 3. 从特定提交恢复
git checkout commit-hash -- deleted-file.txt

# 4. 恢复整个目录
git checkout HEAD -- deleted-directory/
```

#### 恢复修改的文件
```bash
# 场景：文件被意外修改，需要恢复到原始状态

# 1. 恢复到最后一次提交的状态
git checkout -- modified-file.txt

# 2. 恢复到暂存区的状态
git checkout --staged modified-file.txt

# 3. 恢复到特定提交的状态
git checkout commit-hash -- modified-file.txt
```

### 📋 暂存区恢复

#### 恢复暂存区内容
```bash
# 场景：错误地清空了暂存区

# 1. 如果只是执行了 git reset
# 文件仍在工作区，重新添加即可
git add .

# 2. 如果执行了 git reset --hard
# 需要从 reflog 恢复
git reflog
git reset --soft HEAD@{1}

# 3. 恢复特定文件到暂存区
git reset HEAD@{1} -- specific-file.txt
```

## 🗂️ 高级恢复技术

### 🔍 使用 git-filter-repo 恢复

#### 从历史中恢复删除的文件
```bash
# 安装 git-filter-repo
pip install git-filter-repo

# 场景：文件在某次提交中被删除，需要恢复

# 1. 找到删除文件的提交
git log --diff-filter=D --summary | grep filename

# 2. 从删除前的提交恢复
git show commit-before-deletion:path/to/filename > recovered-filename

# 3. 或者恢复整个历史分支
git checkout -b recovery-branch commit-before-deletion
```

#### 恢复被 filter-branch 删除的内容
```bash
# 场景：使用 filter-branch 后想恢复某些内容

# 1. 查看 filter-branch 的备份
ls .git/refs/original/
git log refs/original/refs/heads/main

# 2. 恢复到 filter-branch 前的状态
git reset --hard refs/original/refs/heads/main

# 3. 或者选择性恢复内容
git cherry-pick refs/original/refs/heads/main~5..refs/original/refs/heads/main
```

### 🔄 使用第三方工具恢复

#### Git 恢复工具
```bash
# 1. git-recover
# 专门用于恢复丢失的 Git 数据
git clone https://github.com/mikegerwitz/git-recover.git
cd git-recover
./git-recover /path/to/repository

# 2. git-rescue
# 另一个恢复工具
pip install git-rescue
git-rescue /path/to/repository

# 3. 使用 IDE 的本地历史
# VS Code: Ctrl+Shift+P -> "Local History"
# IntelliJ IDEA: VCS -> Local History
```

## 🛡️ 预防数据丢失

### ✅ 最佳实践

#### 定期备份
```bash
# 1. 远程仓库备份
git remote add backup https://backup-server.com/repo.git
git push backup --all
git push backup --tags

# 2. 本地备份
git bundle create backup-$(date +%Y%m%d).bundle --all

# 3. 自动备份脚本
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/git-repos"
REPO_NAME=$(basename $(git rev-parse --show-toplevel))
DATE=$(date +%Y%m%d-%H%M%S)

git bundle create "$BACKUP_DIR/$REPO_NAME-$DATE.bundle" --all
echo "Backup created: $BACKUP_DIR/$REPO_NAME-$DATE.bundle"
```

#### 安全操作习惯
```bash
# 1. 危险操作前创建分支
git branch backup-$(date +%Y%m%d-%H%M%S)

# 2. 使用 --dry-run 预览操作
git clean -fd --dry-run

# 3. 使用 stash 保存临时工作
git stash push -m "Work in progress before risky operation"

# 4. 定期检查 reflog 过期设置
git config gc.reflogExpire  # 默认 90 天
git config gc.reflogExpireUnreachable  # 默认 30 天
```

### 🔧 配置数据保护

#### 延长 reflog 保留期
```bash
# 延长 reflog 保留时间
git config gc.reflogExpire "1 year"
git config gc.reflogExpireUnreachable "6 months"

# 全局配置
git config --global gc.reflogExpire "1 year"
git config --global gc.reflogExpireUnreachable "6 months"
```

#### 配置自动备份钩子
```bash
# .git/hooks/post-commit
#!/bin/bash
# 每次提交后自动备份

BACKUP_DIR="$HOME/.git-backups"
REPO_NAME=$(basename $(git rev-parse --show-toplevel))
DATE=$(date +%Y%m%d)

mkdir -p "$BACKUP_DIR"

# 创建增量备份
if [ ! -f "$BACKUP_DIR/$REPO_NAME-$DATE.bundle" ]; then
    git bundle create "$BACKUP_DIR/$REPO_NAME-$DATE.bundle" --all
fi
```

## 🚨 紧急恢复指南

### 🆘 快速恢复检查清单

#### 立即行动
```bash
# 1. 停止所有 Git 操作
# 避免进一步的数据丢失

# 2. 检查 reflog
git reflog --all

# 3. 检查工作区状态
git status
ls -la  # 检查是否有文件残留

# 4. 检查暂存区
git ls-files --stage

# 5. 查找悬空对象
git fsck --lost-found
```

#### 系统性恢复
```bash
# 1. 创建恢复工作分支
git checkout -b emergency-recovery

# 2. 收集所有可能的恢复点
git reflog > reflog-backup.txt
git fsck --lost-found > fsck-output.txt

# 3. 逐一尝试恢复
# 从最近的 reflog 条目开始
git reset --hard HEAD@{1}
git reset --hard HEAD@{2}
# ... 继续尝试

# 4. 验证恢复结果
git log --oneline
git status
```

## 💡 恢复成功率提升技巧

### 🎯 提高恢复成功率
1. **快速行动** - 数据丢失后立即停止操作
2. **多种方法** - 尝试不同的恢复方法
3. **保留现场** - 不要急于清理，保留所有信息
4. **寻求帮助** - 复杂情况下寻求专业帮助
5. **学习预防** - 从恢复经历中学习预防措施

### 🔍 恢复工具推荐
```bash
# 命令行工具
git reflog          # 最重要的恢复工具
git fsck           # 检查和恢复对象
git show           # 查看对象内容
git cat-file       # 底层对象操作

# 图形化工具
gitk --all         # 可视化历史查看
git gui            # 图形化操作界面
GitKraken          # 专业 Git 客户端
Sourcetree         # 免费图形化工具
```

---

**记住**: Git 的设计让数据丢失变得困难，大多数情况下都可以恢复。保持冷静，系统性地尝试恢复方法，并从中学习如何预防类似问题！ 🔄
