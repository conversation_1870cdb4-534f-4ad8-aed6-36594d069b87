# 设备像素比（DPR）媒体查询指南

## 概述

设备像素比（Device Pixel Ratio, DPR）是物理像素与CSS像素的比值，是现代响应式设计中的重要概念。通过媒体查询检测DPR，我们可以为不同分辨率的设备提供最适合的资源和样式。

## 基础概念

### 什么是设备像素比？

```
DPR = 物理像素 / CSS像素
```

- **物理像素**：设备屏幕的实际像素点
- **CSS像素**：浏览器中的逻辑像素单位
- **设备像素比**：两者的比值

### 常见设备的DPR值

| 设备类型 | DPR | 示例设备 |
|----------|-----|----------|
| 标准屏幕 | 1.0 | 普通PC显示器 |
| 高清屏幕 | 1.5 | 部分Android设备 |
| Retina屏幕 | 2.0 | iPhone 6/7/8, MacBook |
| 超高清屏幕 | 3.0 | iPhone X/11/12 Pro |
| 4K屏幕 | 4.0 | 部分高端设备 |

## 媒体查询语法

### 1. Webkit前缀语法（推荐）

```css
/* DPR = 1 (标准屏幕) */
@media (-webkit-device-pixel-ratio: 1) {
    /* 标准分辨率样式 */
}

/* DPR ≥ 1.5 */
@media (-webkit-min-device-pixel-ratio: 1.5) {
    /* 高分辨率样式 */
}

/* DPR ≥ 2 (Retina屏幕) */
@media (-webkit-min-device-pixel-ratio: 2) {
    /* Retina屏幕样式 */
}

/* DPR ≥ 3 */
@media (-webkit-min-device-pixel-ratio: 3) {
    /* 超高分辨率样式 */
}
```

### 2. 标准resolution语法

```css
/* 使用dpi (dots per inch) */
@media (min-resolution: 192dpi) {
    /* 相当于 DPR ≥ 2 */
}

/* 使用dppx (dots per pixel) */
@media (min-resolution: 2dppx) {
    /* 相当于 DPR ≥ 2 */
}

/* 使用dpcm (dots per centimeter) */
@media (min-resolution: 76dpcm) {
    /* 相当于 DPR ≥ 2 */
}
```

### 3. 兼容性最佳写法

```css
@media (-webkit-min-device-pixel-ratio: 2),
       (min-resolution: 192dpi),
       (min-resolution: 2dppx) {
    /* 高分辨率样式 */
}
```

## 实际应用场景

### 1. 图片优化

```css
/* 标准分辨率图片 */
.hero-image {
    background-image: url('hero-1x.jpg');
    width: 300px;
    height: 200px;
}

/* 高分辨率图片 */
@media (-webkit-min-device-pixel-ratio: 2),
       (min-resolution: 192dpi) {
    .hero-image {
        background-image: url('hero-2x.jpg');
        background-size: 300px 200px;
    }
}

/* 超高分辨率图片 */
@media (-webkit-min-device-pixel-ratio: 3),
       (min-resolution: 288dpi) {
    .hero-image {
        background-image: url('hero-3x.jpg');
        background-size: 300px 200px;
    }
}
```

### 2. 图标和小图片

```css
.icon {
    width: 24px;
    height: 24px;
    background-image: url('icon-24.png');
}

@media (-webkit-min-device-pixel-ratio: 2) {
    .icon {
        background-image: url('icon-48.png');
        background-size: 24px 24px;
    }
}

@media (-webkit-min-device-pixel-ratio: 3) {
    .icon {
        background-image: url('icon-72.png');
        background-size: 24px 24px;
    }
}
```

### 3. 边框和线条优化

```css
.thin-border {
    border-bottom: 1px solid #ddd;
}

/* 高DPR设备使用更细的边框 */
@media (-webkit-min-device-pixel-ratio: 2) {
    .thin-border {
        border-bottom: 0.5px solid #ddd;
    }
}

/* 或者使用transform实现1px边框 */
@media (-webkit-min-device-pixel-ratio: 2) {
    .hairline-border {
        position: relative;
        border: none;
    }
    
    .hairline-border::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: #ddd;
        transform: scaleY(0.5);
        transform-origin: bottom;
    }
}
```

### 4. 字体渲染优化

```css
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 高DPR设备的字体优化 */
@media (-webkit-min-device-pixel-ratio: 2) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}
```

## 结合其他媒体特性

### 1. DPR + 屏幕尺寸

```css
/* 小屏幕高分辨率设备（如iPhone） */
@media (max-width: 480px) and (-webkit-min-device-pixel-ratio: 2) {
    .mobile-retina-specific {
        /* 移动端Retina屏幕专用样式 */
    }
}

/* 大屏幕高分辨率设备（如MacBook Pro） */
@media (min-width: 1200px) and (-webkit-min-device-pixel-ratio: 2) {
    .desktop-retina-specific {
        /* 桌面端Retina屏幕专用样式 */
    }
}
```

### 2. DPR + 设备方向

```css
/* 横屏高分辨率设备 */
@media (orientation: landscape) and (-webkit-min-device-pixel-ratio: 2) {
    .landscape-retina {
        /* 横屏Retina设备样式 */
    }
}

/* 竖屏高分辨率设备 */
@media (orientation: portrait) and (-webkit-min-device-pixel-ratio: 3) {
    .portrait-super-retina {
        /* 竖屏超高分辨率设备样式 */
    }
}
```

### 3. 复杂组合查询

```css
/* iPhone X/11/12 Pro 系列特定样式 */
@media (device-width: 375px) and 
       (device-height: 812px) and 
       (-webkit-device-pixel-ratio: 3) and 
       (orientation: portrait) {
    .iphone-x-portrait {
        /* iPhone X系列竖屏样式 */
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
    }
}
```

## HTML中的响应式图片

### 1. srcset属性

```html
<img src="image-1x.jpg"
     srcset="image-1x.jpg 1x,
             image-2x.jpg 2x,
             image-3x.jpg 3x"
     alt="响应式图片">
```

### 2. picture元素

```html
<picture>
    <source media="(-webkit-min-device-pixel-ratio: 3)"
            srcset="hero-3x.jpg">
    <source media="(-webkit-min-device-pixel-ratio: 2)"
            srcset="hero-2x.jpg">
    <img src="hero-1x.jpg" alt="Hero图片">
</picture>
```

### 3. 结合媒体查询

```html
<picture>
    <source media="(max-width: 768px) and (-webkit-min-device-pixel-ratio: 2)"
            srcset="mobile-hero-2x.jpg">
    <source media="(max-width: 768px)"
            srcset="mobile-hero-1x.jpg">
    <source media="(-webkit-min-device-pixel-ratio: 2)"
            srcset="desktop-hero-2x.jpg">
    <img src="desktop-hero-1x.jpg" alt="响应式Hero图片">
</picture>
```

## JavaScript检测DPR

```javascript
// 获取设备像素比
const dpr = window.devicePixelRatio || 1;
console.log('当前设备像素比:', dpr);

// 根据DPR动态加载图片
function loadImageByDPR(baseName, extension = 'jpg') {
    const dpr = window.devicePixelRatio || 1;
    let suffix = '';
    
    if (dpr >= 3) {
        suffix = '@3x';
    } else if (dpr >= 2) {
        suffix = '@2x';
    }
    
    return `${baseName}${suffix}.${extension}`;
}

// 使用示例
const imageSrc = loadImageByDPR('hero', 'jpg');
// 结果: <EMAIL> (在2x设备上)

// 监听DPR变化（如外接显示器）
window.matchMedia('(-webkit-device-pixel-ratio: 2)').addListener((mq) => {
    if (mq.matches) {
        console.log('切换到高分辨率显示');
        // 重新加载高分辨率资源
    }
});
```

## 性能优化建议

### 1. 图片资源管理

```css
/* 渐进式加载策略 */
.progressive-image {
    background-image: url('placeholder-1x.jpg');
    background-size: cover;
}

@media (-webkit-min-device-pixel-ratio: 2) {
    .progressive-image.loaded {
        background-image: url('full-image-2x.jpg');
    }
}
```

### 2. 条件加载

```javascript
// 只在高DPR设备加载高分辨率资源
if (window.devicePixelRatio >= 2) {
    // 加载高分辨率CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'retina-styles.css';
    document.head.appendChild(link);
}
```

### 3. 图片压缩策略

- **1x图片**: 标准压缩，文件大小优先
- **2x图片**: 适度压缩，平衡质量和大小
- **3x图片**: 轻度压缩，质量优先

## 最佳实践

1. **优先使用标准语法**：`min-resolution` 比 `-webkit-device-pixel-ratio` 更标准
2. **提供兼容性写法**：同时使用多种语法确保兼容性
3. **合理的图片策略**：不是所有图片都需要多倍图
4. **性能考虑**：高分辨率资源会增加加载时间
5. **测试充分**：在真实的高DPR设备上测试效果
6. **渐进增强**：确保低DPR设备也有良好体验

## 常见问题

### Q: 为什么我的2x图片看起来模糊？
A: 确保设置了正确的 `background-size` 或图片的显示尺寸。

### Q: 如何检测当前设备的DPR？
A: 使用 `window.devicePixelRatio` 或媒体查询。

### Q: 是否需要为所有图片提供多倍图？
A: 不需要，主要为重要的内容图片和图标提供即可。
