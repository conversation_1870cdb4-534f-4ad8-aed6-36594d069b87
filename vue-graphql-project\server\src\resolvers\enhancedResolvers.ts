import { Context, User, Task, Project } from '../models';

/**
 * 增强版 Resolvers - 展示如何使用注入的上下文数据
 */
export const enhancedResolvers = {
  Query: {
    // 🎯 使用 DataLoader 获取用户信息
    user: async (parent: any, { id }: { id: string }, context: Context) => {
      // 更新统计
      context.stats!.resolverCalls++;
      
      // 检查权限
      if (!context.permissions?.includes('read:users')) {
        throw new Error('Insufficient permissions to read user data');
      }
      
      // 使用请求级别缓存
      const cacheKey = `user_${id}`;
      if (context.cache!.has(cacheKey)) {
        context.stats!.cacheHits++;
        console.log(`💾 Cache hit for user ${id}`);
        return context.cache!.get(cacheKey);
      }
      
      // 使用 DataLoader 加载用户
      context.stats!.dataLoaderCalls++;
      const user = await context.loaders!.userLoader.load(id);
      
      if (user) {
        context.cache!.set(cacheKey, user);
        context.stats!.cacheMisses++;
        console.log(`[${context.requestId}] Loaded user: ${user.username}`);
      }
      
      return user;
    },

    // 🎯 使用批量加载获取多个用户
    users: async (parent: any, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      
      if (!context.permissions?.includes('read:users')) {
        throw new Error('Insufficient permissions to read users');
      }
      
      console.log(`[${context.requestId}] Fetching all users`);
      
      // 如果启用了高级搜索功能
      if (context.featureFlags?.enableAdvancedSearch && args.search) {
        // 实现高级搜索逻辑
        console.log(`🔍 Advanced search enabled for: ${args.search}`);
      }
      
      // 这里可以使用 DataLoader 批量加载
      const userIds = ['1', '2', '3']; // 实际应该从数据库查询
      return await context.loaders!.userLoader.loadMany(userIds);
    },

    // 🎯 获取任务列表，使用权限检查和缓存
    tasks: async (parent: any, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      
      if (!context.permissions?.includes('read:tasks')) {
        throw new Error('Insufficient permissions to read tasks');
      }
      
      const cacheKey = `tasks_user_${context.user?.id}`;
      if (context.cache!.has(cacheKey)) {
        context.stats!.cacheHits++;
        return context.cache!.get(cacheKey);
      }
      
      // 使用 DataLoader 获取用户的任务
      if (context.user) {
        context.stats!.dataLoaderCalls++;
        const tasks = await context.loaders!.userTasksLoader.load(context.user.id);
        context.cache!.set(cacheKey, tasks);
        context.stats!.cacheMisses++;
        
        console.log(`[${context.requestId}] Loaded ${tasks.length} tasks for user ${context.user.username}`);
        return tasks;
      }
      
      return [];
    }
  },

  Mutation: {
    // 🎯 创建任务，使用事务和权限检查
    createTask: async (parent: any, { input }: { input: any }, context: Context) => {
      context.stats!.resolverCalls++;
      
      // 检查权限
      if (!context.permissions?.includes('write:tasks')) {
        throw new Error('Insufficient permissions to create tasks');
      }
      
      // 检查是否为变更操作
      if (context.operationType !== 'mutation') {
        throw new Error('Invalid operation type');
      }
      
      console.log(`[${context.requestId}] Creating task by user: ${context.user?.username}`);
      
      // 这里可以使用数据库事务
      // if (context.transaction) {
      //   // 在事务中执行创建操作
      // }
      
      // 模拟创建任务
      const newTask: Task = {
        id: `task_${Date.now()}`,
        title: input.title,
        description: input.description,
        status: 'TODO',
        priority: input.priority || 'MEDIUM',
        creatorId: context.user!.id,
        assigneeId: input.assigneeId,
        projectId: input.projectId,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // 清除相关缓存
      context.cache!.delete(`tasks_user_${context.user?.id}`);
      if (input.assigneeId) {
        context.cache!.delete(`tasks_user_${input.assigneeId}`);
      }
      
      // 清除 DataLoader 缓存
      context.loaders!.userTasksLoader.clear(context.user!.id);
      if (input.assigneeId) {
        context.loaders!.userTasksLoader.clear(input.assigneeId);
      }
      
      console.log(`✅ Task created: ${newTask.id}`);
      return newTask;
    }
  },

  // 🎯 嵌套解析器，展示 DataLoader 的威力
  User: {
    tasks: async (parent: User, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      context.stats!.dataLoaderCalls++;
      
      // 使用 DataLoader 批量加载用户任务，避免 N+1 问题
      return await context.loaders!.userTasksLoader.load(parent.id);
    },

    assignedTasks: async (parent: User, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      
      // 使用缓存
      const cacheKey = `assigned_tasks_${parent.id}`;
      if (context.cache!.has(cacheKey)) {
        context.stats!.cacheHits++;
        return context.cache!.get(cacheKey);
      }
      
      // 从所有任务中筛选分配给该用户的任务
      const allTasks = await context.loaders!.userTasksLoader.load(parent.id);
      const assignedTasks = allTasks.filter(task => task.assigneeId === parent.id);
      
      context.cache!.set(cacheKey, assignedTasks);
      context.stats!.cacheMisses++;
      
      return assignedTasks;
    }
  },

  Project: {
    members: async (parent: Project, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      context.stats!.dataLoaderCalls++;
      
      // 使用 DataLoader 批量加载项目成员
      return await context.loaders!.projectMembersLoader.load(parent.id);
    },

    tasks: async (parent: Project, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      
      const cacheKey = `project_tasks_${parent.id}`;
      if (context.cache!.has(cacheKey)) {
        context.stats!.cacheHits++;
        return context.cache!.get(cacheKey);
      }
      
      // 获取项目的所有任务
      const taskIds = parent.taskIds || [];
      const tasks = await context.loaders!.taskLoader.loadMany(taskIds);
      
      // 过滤掉 null 值
      const validTasks = tasks.filter((task): task is Task => task !== null);
      
      context.cache!.set(cacheKey, validTasks);
      context.stats!.cacheMisses++;
      
      return validTasks;
    }
  },

  Task: {
    creator: async (parent: Task, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      context.stats!.dataLoaderCalls++;
      
      return await context.loaders!.userLoader.load(parent.creatorId);
    },

    assignee: async (parent: Task, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      
      if (!parent.assigneeId) return null;
      
      context.stats!.dataLoaderCalls++;
      return await context.loaders!.userLoader.load(parent.assigneeId);
    },

    project: async (parent: Task, args: any, context: Context) => {
      context.stats!.resolverCalls++;
      
      if (!parent.projectId) return null;
      
      context.stats!.dataLoaderCalls++;
      return await context.loaders!.projectLoader.load(parent.projectId);
    }
  }
};

/**
 * 辅助函数：检查用户是否有特定权限
 */
export function hasPermission(context: Context, permission: string): boolean {
  return context.permissions?.includes(permission) || false;
}

/**
 * 辅助函数：记录操作日志
 */
export function logOperation(context: Context, operation: string, details?: any): void {
  console.log(`[${context.requestId}] ${operation}`, {
    user: context.user?.username,
    timestamp: new Date().toISOString(),
    ...details
  });
}
