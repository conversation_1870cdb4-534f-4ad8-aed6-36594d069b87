/**
 * 用户管理API模拟数据
 */

// 用户数据
export const users = [
    {
        id: 1,
        name: "张三",
        email: "z<PERSON><PERSON>@example.com",
        role: "admin",
        status: "active",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=zhang",
        created_at: "2023-10-01T08:00:00Z",
        updated_at: "2023-10-21T10:30:00Z"
    },
    {
        id: 2,
        name: "李四",
        email: "<EMAIL>",
        role: "user",
        status: "active",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=li",
        created_at: "2023-10-02T09:15:00Z",
        updated_at: "2023-10-20T14:20:00Z"
    },
    {
        id: 3,
        name: "王五",
        email: "<EMAIL>",
        role: "moderator",
        status: "active",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=wang",
        created_at: "2023-10-03T10:30:00Z",
        updated_at: "2023-10-19T16:45:00Z"
    },
    {
        id: 4,
        name: "赵六",
        email: "<EMAIL>",
        role: "user",
        status: "inactive",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=zhao",
        created_at: "2023-10-04T11:45:00Z",
        updated_at: "2023-10-18T13:30:00Z"
    }
];

// API端点配置
export const apiEndpoints = {
    base: "/api/v1",
    users: {
        list: "GET /api/v1/users",
        get: "GET /api/v1/users/{id}",
        create: "POST /api/v1/users",
        update: "PUT /api/v1/users/{id}",
        delete: "DELETE /api/v1/users/{id}"
    }
};

// API响应模板
export const responseTemplates = {
    success: {
        status: "success",
        code: 200,
        message: "操作成功"
    },
    error: {
        status: "error",
        code: 400,
        message: "请求失败"
    },
    notFound: {
        status: "error",
        code: 404,
        message: "资源不存在"
    },
    validation: {
        status: "error",
        code: 422,
        message: "数据验证失败"
    }
};

// 状态码说明
export const statusCodes = {
    200: { name: "OK", description: "请求成功" },
    201: { name: "Created", description: "资源创建成功" },
    400: { name: "Bad Request", description: "请求参数错误" },
    401: { name: "Unauthorized", description: "未授权访问" },
    403: { name: "Forbidden", description: "禁止访问" },
    404: { name: "Not Found", description: "资源不存在" },
    422: { name: "Unprocessable Entity", description: "数据验证失败" },
    500: { name: "Internal Server Error", description: "服务器内部错误" }
};

// HTTP方法说明
export const httpMethods = {
    GET: {
        name: "GET",
        description: "获取资源",
        color: "#27ae60",
        usage: "用于获取数据，不应有副作用"
    },
    POST: {
        name: "POST",
        description: "创建资源",
        color: "#f39c12",
        usage: "用于创建新资源或执行操作"
    },
    PUT: {
        name: "PUT",
        description: "更新资源",
        color: "#2196f3",
        usage: "用于完整更新资源"
    },
    PATCH: {
        name: "PATCH",
        description: "部分更新",
        color: "#9c27b0",
        usage: "用于部分更新资源"
    },
    DELETE: {
        name: "DELETE",
        description: "删除资源",
        color: "#e74c3c",
        usage: "用于删除资源"
    }
};

// API文档数据
export const apiDocumentation = [
    {
        title: "获取用户列表",
        method: "GET",
        endpoint: "/api/v1/users",
        description: "获取系统中的所有用户列表，支持分页和过滤",
        parameters: [
            { name: "page", type: "integer", required: false, description: "页码，默认为1" },
            { name: "limit", type: "integer", required: false, description: "每页数量，默认为10" },
            { name: "status", type: "string", required: false, description: "用户状态过滤" }
        ],
        response: {
            "data": [
                {
                    "id": 1,
                    "name": "张三",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "status": "active"
                }
            ],
            "pagination": {
                "page": 1,
                "limit": 10,
                "total": 100
            }
        }
    },
    {
        title: "获取单个用户",
        method: "GET",
        endpoint: "/api/v1/users/{id}",
        description: "根据用户ID获取用户详细信息",
        parameters: [
            { name: "id", type: "integer", required: true, description: "用户ID" }
        ],
        response: {
            "id": 1,
            "name": "张三",
            "email": "<EMAIL>",
            "role": "admin",
            "status": "active",
            "created_at": "2023-10-01T08:00:00Z"
        }
    },
    {
        title: "创建用户",
        method: "POST",
        endpoint: "/api/v1/users",
        description: "创建新的用户账户",
        parameters: [
            { name: "name", type: "string", required: true, description: "用户姓名" },
            { name: "email", type: "string", required: true, description: "邮箱地址" },
            { name: "role", type: "string", required: true, description: "用户角色" }
        ],
        requestBody: {
            "name": "新用户",
            "email": "<EMAIL>",
            "role": "user"
        },
        response: {
            "id": 5,
            "name": "新用户",
            "email": "<EMAIL>",
            "role": "user",
            "status": "active",
            "created_at": "2023-10-21T12:00:00Z"
        }
    },
    {
        title: "更新用户",
        method: "PUT",
        endpoint: "/api/v1/users/{id}",
        description: "更新用户信息",
        parameters: [
            { name: "id", type: "integer", required: true, description: "用户ID" },
            { name: "name", type: "string", required: false, description: "用户姓名" },
            { name: "email", type: "string", required: false, description: "邮箱地址" },
            { name: "role", type: "string", required: false, description: "用户角色" }
        ],
        requestBody: {
            "name": "更新的姓名",
            "email": "<EMAIL>"
        },
        response: {
            "id": 1,
            "name": "更新的姓名",
            "email": "<EMAIL>",
            "role": "admin",
            "status": "active",
            "updated_at": "2023-10-21T12:00:00Z"
        }
    },
    {
        title: "删除用户",
        method: "DELETE",
        endpoint: "/api/v1/users/{id}",
        description: "删除指定的用户",
        parameters: [
            { name: "id", type: "integer", required: true, description: "用户ID" }
        ],
        response: {
            "message": "用户删除成功"
        }
    }
];

// 示例请求数据
export const sampleRequests = {
    createUser: {
        name: "测试用户",
        email: "<EMAIL>",
        role: "user"
    },
    updateUser: {
        name: "更新用户",
        email: "<EMAIL>",
        role: "moderator"
    }
};

export default {
    users,
    apiEndpoints,
    responseTemplates,
    statusCodes,
    httpMethods,
    apiDocumentation,
    sampleRequests
};
