# 第一次考核需加强学习的点

## 1.适配

-   [x] 媒体查询 关于 纵向横向  打印print 知识点 (orientation:landscape || orientation:portrait) print 
    -   [ ] orientation: portrait/landscape 设备方向检测
    -   [ ] @media print 打印样式优化
    -   [ ] 屏幕尺寸断点设计
    -   [ ] 移动端横竖屏切换适配
    -   [ ] 设备像素比 DPR 媒体查询适配
    -   [ ] 高分辨率屏幕图片优化
-   [x] flex 布局 例子一行排三个元素，依次左中右排列
    -   justify-content: space-between;
    -   左margin-right:auto , 右margin-left:auto
    -   文案 三flex:1; 左 text-align: left;  中 text-align: center;  右 text-align: right;
    -   左右固定宽  中间flex:1;

-   [x] rem 动态设置根节点的 font-size （核心思想： viewportMeta 动态设置好dpr: 初始化、最大最小的缩放比例为1/dpr user-scalable:no  然后 fontSize 为当前屏幕宽度/ 设计稿的宽度 再*100  【移动端和pc 的设计稿宽度不同】   ）
-   [x] IOS 安全区在纵向和横向上的出现位置  （竖屏 主要是 上下  横屏 左右 下看情况考虑）
-   [x] Viewport meta标签中是否允许用户缩放的属性  （user-scalable）

## 2.编码原则

-   [x] LSP 里氏替换原则  
    - 概念：子类能够替换父类，并且程序不会出现错误。
    - 替换的过程，
      -  不能够去加强前置条件：比如 父类add方法 允许小数相加，但是子类 函数体内，添加了，不允许小数相加的判断,然后程序运行的时候输入小数就报错了，这种情况就是违反了LSP。
      -  不能削弱后置条件： 比如，父类add方法,小数相加后 取整返回，但是子类 在相加完后，直接返回了结果，然后程序运行时也得到不同的结果，也违反了LSP。
    - 可替换 
    - 行为兼容
    - 契约兼容

-   [x] 接口隔离原则 （一个类不应该实现它们不需要的方法，应该把大接口拆分小接口，例如包含所有类型权限的接口，应该拆分成普通用户，管理员等权限接口。）
-   [x] 开闭原则 （对扩展开放，对修改封闭；在原有的代码上进行拓展，而不是修改原有的代码满足新的需求。）

## 3.RESTful 和 GraphQL

-   [x] BFF 是什么 → [详细学习资料](03-RESTful-GraphQL/07-BFF架构模式/README.md)
-   [x] 状态码301 302 区分
   -  301 永久重定向 302 临时重定向 304 资源未修改
-   [ ] 实操 node.js +express +vue 写一套 Restful 接口的项目
-   [ ] 实操 node.js + express + graphql 写一套 GraphQL 接口的项目
-   [ ] 一套适配移动端和 pc 端的代码。通过 DPR 实现
