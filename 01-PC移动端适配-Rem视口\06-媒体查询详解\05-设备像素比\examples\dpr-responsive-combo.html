<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DPR + 响应式设计综合示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .status-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 0.5rem;
            font-size: 0.8rem;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .grid {
            display: grid;
            gap: 1rem;
            margin: 2rem 0;
        }

        .card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        /* 基础响应式网格 - 移动优先 */
        .grid {
            grid-template-columns: 1fr;
        }

        /* 小屏幕 (≥576px) */
        @media (min-width: 576px) {
            .grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 中等屏幕 (≥768px) */
        @media (min-width: 768px) {
            .grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* 大屏幕 (≥992px) */
        @media (min-width: 992px) {
            .grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* DPR 1x 设备优化 */
        @media (-webkit-device-pixel-ratio: 1) {
            .card {
                border: 1px solid #e1e5e9;
            }
            
            .dpr-indicator::before {
                content: "标准分辨率设备";
                background: #6c757d;
            }
        }

        /* DPR 2x 设备优化 */
        @media (-webkit-min-device-pixel-ratio: 2) {
            .card {
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }
            
            .dpr-indicator::before {
                content: "高分辨率设备 (Retina)";
                background: #28a745;
            }
            
            /* 字体平滑 */
            body {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        /* DPR 3x 设备优化 */
        @media (-webkit-min-device-pixel-ratio: 3) {
            .dpr-indicator::before {
                content: "超高分辨率设备";
                background: #dc3545;
            }
        }

        /* 移动设备 + 高DPR */
        @media (max-width: 767px) and (-webkit-min-device-pixel-ratio: 2) {
            .header {
                padding: 1.5rem 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .card {
                padding: 1rem;
            }
            
            .mobile-retina::before {
                content: "移动端 Retina 设备";
                display: block;
                background: #17a2b8;
                color: white;
                padding: 0.5rem;
                border-radius: 4px;
                margin-bottom: 1rem;
                text-align: center;
                font-weight: bold;
            }
        }

        /* 平板设备 + 高DPR */
        @media (min-width: 768px) and (max-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
            .tablet-retina::before {
                content: "平板 Retina 设备";
                display: block;
                background: #6f42c1;
                color: white;
                padding: 0.5rem;
                border-radius: 4px;
                margin-bottom: 1rem;
                text-align: center;
                font-weight: bold;
            }
        }

        /* 桌面设备 + 高DPR */
        @media (min-width: 1025px) and (-webkit-min-device-pixel-ratio: 2) {
            .desktop-retina::before {
                content: "桌面 Retina 设备";
                display: block;
                background: #fd7e14;
                color: white;
                padding: 0.5rem;
                border-radius: 4px;
                margin-bottom: 1rem;
                text-align: center;
                font-weight: bold;
            }
        }

        /* 横屏 + 高DPR */
        @media (orientation: landscape) and (-webkit-min-device-pixel-ratio: 2) {
            .landscape-retina {
                border-left: 4px solid #20c997;
            }
            
            .landscape-retina::after {
                content: "横屏 + 高DPR";
                display: block;
                color: #20c997;
                font-weight: bold;
                margin-top: 0.5rem;
            }
        }

        /* 竖屏 + 高DPR */
        @media (orientation: portrait) and (-webkit-min-device-pixel-ratio: 2) {
            .portrait-retina {
                border-left: 4px solid #e83e8c;
            }
            
            .portrait-retina::after {
                content: "竖屏 + 高DPR";
                display: block;
                color: #e83e8c;
                font-weight: bold;
                margin-top: 0.5rem;
            }
        }

        /* 小屏幕横屏 + 高DPR */
        @media (max-width: 768px) and (orientation: landscape) and (-webkit-min-device-pixel-ratio: 2) {
            .status-bar {
                padding: 0.25rem 0.5rem;
                font-size: 0.7rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 1.25rem;
            }
            
            .grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.5rem;
            }
            
            .card {
                padding: 0.75rem;
            }
        }

        /* 图片容器 */
        .image-container {
            width: 100%;
            height: 120px;
            background: #e9ecef;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: #6c757d;
            font-weight: bold;
        }

        /* 1x 图片 */
        .image-1x {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }

        .image-1x::before {
            content: "1x 图片";
        }

        /* 2x 图片 */
        @media (-webkit-min-device-pixel-ratio: 2) {
            .image-2x {
                background: linear-gradient(45deg, #28a745, #1e7e34);
                color: white;
            }
            
            .image-2x::before {
                content: "2x 图片";
            }
        }

        /* 3x 图片 */
        @media (-webkit-min-device-pixel-ratio: 3) {
            .image-3x {
                background: linear-gradient(45deg, #dc3545, #c82333);
                color: white;
            }
            
            .image-3x::before {
                content: "3x 图片";
            }
        }

        /* DPR指示器 */
        .dpr-indicator {
            position: relative;
            padding-top: 2rem;
        }

        .dpr-indicator::before {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            padding: 0.5rem;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            color: white;
        }

        /* 打印样式 */
        @media print {
            .status-bar {
                display: none !important;
            }
            
            .header {
                background: white !important;
                color: black !important;
                border: 1px solid black !important;
            }
            
            .grid {
                grid-template-columns: 1fr !important;
            }
            
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="status-bar">
        <div>DPR: <span id="dprValue">-</span></div>
        <div>尺寸: <span id="sizeInfo">-</span></div>
        <div>方向: <span id="orientationInfo">-</span></div>
        <div>断点: <span id="breakpointInfo">-</span></div>
    </div>

    <div class="container" style="margin-top: 3rem;">
        <header class="header">
            <h1>DPR + 响应式设计综合示例</h1>
            <p>展示设备像素比与屏幕尺寸、设备方向的组合应用</p>
        </header>

        <div class="grid">
            <div class="card dpr-indicator">
                <h3>DPR 检测</h3>
                <p>这个卡片会根据设备的像素比显示不同的指示器。</p>
            </div>

            <div class="card mobile-retina">
                <h3>移动端适配</h3>
                <div class="image-container image-1x"></div>
                <p>在移动端高DPR设备上会显示特殊样式。</p>
            </div>

            <div class="card tablet-retina">
                <h3>平板适配</h3>
                <div class="image-container image-2x"></div>
                <p>平板设备的高DPR适配效果。</p>
            </div>

            <div class="card desktop-retina">
                <h3>桌面适配</h3>
                <div class="image-container image-3x"></div>
                <p>桌面设备的高DPR优化。</p>
            </div>

            <div class="card landscape-retina">
                <h3>横屏检测</h3>
                <p>横屏模式下的高DPR设备会显示特殊边框。</p>
            </div>

            <div class="card portrait-retina">
                <h3>竖屏检测</h3>
                <p>竖屏模式下的高DPR设备会显示不同颜色的边框。</p>
            </div>

            <div class="card">
                <h3>组合查询</h3>
                <p>这个示例展示了如何将DPR与其他媒体特性结合使用。</p>
            </div>

            <div class="card">
                <h3>性能优化</h3>
                <p>高DPR设备会加载更高质量的资源，同时优化字体渲染。</p>
            </div>
        </div>
    </div>

    <script>
        function updateStatusBar() {
            const dpr = window.devicePixelRatio || 1;
            const width = window.innerWidth;
            const height = window.innerHeight;
            const orientation = height > width ? '竖屏' : '横屏';
            
            let breakpoint = 'xs';
            if (width >= 992) breakpoint = 'lg';
            else if (width >= 768) breakpoint = 'md';
            else if (width >= 576) breakpoint = 'sm';
            
            document.getElementById('dprValue').textContent = dpr;
            document.getElementById('sizeInfo').textContent = `${width}×${height}`;
            document.getElementById('orientationInfo').textContent = orientation;
            document.getElementById('breakpointInfo').textContent = breakpoint;
        }

        // 检测当前匹配的组合查询
        function checkCombinedQueries() {
            const queries = [
                { name: '移动端+高DPR', query: '(max-width: 767px) and (-webkit-min-device-pixel-ratio: 2)' },
                { name: '平板+高DPR', query: '(min-width: 768px) and (max-width: 1024px) and (-webkit-min-device-pixel-ratio: 2)' },
                { name: '桌面+高DPR', query: '(min-width: 1025px) and (-webkit-min-device-pixel-ratio: 2)' },
                { name: '横屏+高DPR', query: '(orientation: landscape) and (-webkit-min-device-pixel-ratio: 2)' },
                { name: '竖屏+高DPR', query: '(orientation: portrait) and (-webkit-min-device-pixel-ratio: 2)' },
                { name: '小屏横屏+高DPR', query: '(max-width: 768px) and (orientation: landscape) and (-webkit-min-device-pixel-ratio: 2)' }
            ];

            const matched = queries.filter(q => window.matchMedia(q.query).matches);
            console.log('匹配的组合查询:', matched.map(q => q.name));
        }

        // 初始化
        updateStatusBar();
        checkCombinedQueries();

        // 监听变化
        window.addEventListener('resize', () => {
            updateStatusBar();
            checkCombinedQueries();
        });

        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                updateStatusBar();
                checkCombinedQueries();
            }, 100);
        });

        // 输出设备信息
        console.log('=== 设备信息 ===');
        console.log('DPR:', window.devicePixelRatio);
        console.log('User Agent:', navigator.userAgent);
        console.log('屏幕:', screen.width, '×', screen.height);
        console.log('视口:', window.innerWidth, '×', window.innerHeight);
    </script>
</body>
</html>
