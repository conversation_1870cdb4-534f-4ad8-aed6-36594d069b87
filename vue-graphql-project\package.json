{"name": "vue-graphql-project", "version": "1.0.0", "description": "A modern full-stack project with Vue 3 + GraphQL + Apollo", "scripts": {"dev": "concurrently \"yarn dev:server\" \"yarn dev:client\"", "dev:server": "cd server && yarn dev", "dev:client": "cd client && yarn dev", "build": "yarn build:server && yarn build:client", "build:server": "cd server && yarn build", "build:client": "cd client && yarn build", "start": "cd server && yarn start", "install:all": "yarn install && cd server && yarn install && cd ../client && yarn install", "clean": "rimraf server/dist client/dist server/node_modules client/node_modules node_modules", "type-check": "cd server && yarn type-check && cd ../client && yarn type-check"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "keywords": ["vue", "vue3", "graphql", "apollo", "typescript", "fullstack", "project-management"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/vue-graphql-project.git"}, "dependencies": {"@types/dotenv": "^6.1.1", "dotenv": "^16.5.0"}}