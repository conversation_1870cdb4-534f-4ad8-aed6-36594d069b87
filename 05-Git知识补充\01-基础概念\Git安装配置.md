# Git 安装与配置详解

## 📥 Git 安装

### 🖥️ Windows 系统安装

#### 方法一：官方安装包
1. **下载**: 访问 [https://git-scm.com/download/win](https://git-scm.com/download/win)
2. **安装**: 下载 `.exe` 文件并运行
3. **配置选项**:
   - 选择组件：推荐全部勾选
   - 调整 PATH 环境：选择 "Git from the command line and also from 3rd-party software"
   - 选择 HTTPS 传输后端：选择 "Use the OpenSSL library"
   - 配置行尾转换：选择 "Checkout Windows-style, commit Unix-style line endings"
   - 配置终端模拟器：选择 "Use MinTTY"

#### 方法二：包管理器安装
```powershell
# 使用 Chocolatey
choco install git

# 使用 Scoop
scoop install git

# 使用 Winget
winget install Git.Git
```

### 🍎 macOS 系统安装

#### 方法一：Homebrew (推荐)
```bash
# 安装 Homebrew (如果没有)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装 Git
brew install git
```

#### 方法二：官方安装包
1. 下载：[https://git-scm.com/download/mac](https://git-scm.com/download/mac)
2. 运行 `.dmg` 文件并安装

#### 方法三：Xcode Command Line Tools
```bash
xcode-select --install
```

### 🐧 Linux 系统安装

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装 Git
sudo apt install git
```

#### CentOS/RHEL/Fedora
```bash
# CentOS/RHEL
sudo yum install git

# Fedora
sudo dnf install git
```

#### Arch Linux
```bash
sudo pacman -S git
```

## ⚙️ Git 基础配置

### 🔧 必需配置

安装完成后，首先需要配置用户信息：

```bash
# 配置用户名
git config --global user.name "Your Name"

# 配置邮箱
git config --global user.email "<EMAIL>"
```

### 📝 配置级别

Git 有三个配置级别：

#### 1. 系统级配置 (--system)
```bash
# 影响系统上所有用户
git config --system user.name "System User"

# 配置文件位置：
# Windows: C:\Program Files\Git\etc\gitconfig
# Linux/macOS: /etc/gitconfig
```

#### 2. 全局配置 (--global)
```bash
# 影响当前用户的所有仓库
git config --global user.name "Global User"

# 配置文件位置：
# Windows: C:\Users\<USER>\.gitconfig
# Linux/macOS: ~/.gitconfig
```

#### 3. 仓库配置 (--local，默认)
```bash
# 只影响当前仓库
git config --local user.name "Local User"

# 配置文件位置：.git/config
```

### 🎨 常用配置选项

#### 编辑器配置
```bash
# 设置默认编辑器
git config --global core.editor "code --wait"  # VS Code
git config --global core.editor "vim"          # Vim
git config --global core.editor "nano"         # Nano
```

#### 差异工具配置
```bash
# 设置差异查看工具
git config --global diff.tool vscode
git config --global difftool.vscode.cmd 'code --wait --diff $LOCAL $REMOTE'

# 设置合并工具
git config --global merge.tool vscode
git config --global mergetool.vscode.cmd 'code --wait $MERGED'
```

#### 行尾处理
```bash
# Windows 用户推荐
git config --global core.autocrlf true

# Linux/macOS 用户推荐
git config --global core.autocrlf input

# 不转换（不推荐）
git config --global core.autocrlf false
```

#### 颜色配置
```bash
# 启用颜色输出
git config --global color.ui auto

# 具体配置
git config --global color.branch auto
git config --global color.diff auto
git config --global color.status auto
```

#### 别名配置
```bash
# 常用别名
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'

# 高级别名
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"
```

### 🔐 SSH 密钥配置

#### 生成 SSH 密钥
```bash
# 生成新的 SSH 密钥
ssh-keygen -t ed25519 -C "<EMAIL>"

# 如果系统不支持 ed25519，使用 RSA
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

#### 添加密钥到 SSH 代理
```bash
# 启动 SSH 代理
eval "$(ssh-agent -s)"

# 添加私钥到代理
ssh-add ~/.ssh/id_ed25519
```

#### 添加公钥到 GitHub/GitLab
```bash
# 复制公钥到剪贴板
# macOS
pbcopy < ~/.ssh/id_ed25519.pub

# Linux
xclip -sel clip < ~/.ssh/id_ed25519.pub

# Windows (Git Bash)
clip < ~/.ssh/id_ed25519.pub
```

然后在 GitHub/GitLab 的设置中添加 SSH 密钥。

### 📋 查看配置

#### 查看所有配置
```bash
# 查看所有配置
git config --list

# 查看特定配置
git config user.name
git config user.email

# 查看配置来源
git config --list --show-origin
```

#### 编辑配置文件
```bash
# 编辑全局配置
git config --global --edit

# 编辑当前仓库配置
git config --edit
```

## 🔧 高级配置

### 🚀 性能优化
```bash
# 启用文件系统监控
git config --global core.fsmonitor true

# 启用并行处理
git config --global checkout.workers 0

# 设置压缩级别
git config --global core.compression 9
```

### 🛡️ 安全配置
```bash
# 禁用自动 CRLF 转换（避免安全问题）
git config --global core.autocrlf false

# 设置安全目录
git config --global safe.directory '*'
```

### 📁 忽略文件配置
```bash
# 设置全局忽略文件
git config --global core.excludesfile ~/.gitignore_global
```

创建全局忽略文件：
```bash
# 创建全局 .gitignore
cat > ~/.gitignore_global << EOF
# 操作系统文件
.DS_Store
Thumbs.db

# 编辑器文件
.vscode/
.idea/
*.swp
*.swo

# 临时文件
*.tmp
*.temp
EOF
```

## 🔍 验证安装

### 检查版本
```bash
# 检查 Git 版本
git --version

# 检查配置
git config --list
```

### 测试 SSH 连接
```bash
# 测试 GitHub 连接
ssh -T **************

# 测试 GitLab 连接
ssh -T **************
```

## 🛠️ 常见问题解决

### 问题1：中文文件名显示乱码
```bash
git config --global core.quotepath false
```

### 问题2：HTTPS 认证问题
```bash
# 存储认证信息
git config --global credential.helper store

# Windows 使用 Windows Credential Manager
git config --global credential.helper manager-core
```

### 问题3：代理配置
```bash
# HTTP 代理
git config --global http.proxy http://proxy.company.com:8080

# HTTPS 代理
git config --global https.proxy https://proxy.company.com:8080

# 取消代理
git config --global --unset http.proxy
git config --global --unset https.proxy
```

## 📚 配置文件示例

### 完整的 .gitconfig 示例
```ini
[user]
    name = Your Name
    email = <EMAIL>

[core]
    editor = code --wait
    autocrlf = true
    quotepath = false
    excludesfile = ~/.gitignore_global

[color]
    ui = auto

[alias]
    st = status
    co = checkout
    br = branch
    ci = commit
    lg = log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit

[push]
    default = simple

[pull]
    rebase = false

[init]
    defaultBranch = main
```

## 🚀 下一步

配置完成后，您可以：
1. 学习 Git 工作流程
2. 开始使用基本操作
3. 创建第一个仓库

---

**提示**: 配置是一次性的工作，但可以随时调整。建议先使用基本配置，随着使用经验的增加再添加高级配置。
