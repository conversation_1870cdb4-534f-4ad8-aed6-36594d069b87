# 移动端适配基础概念详解

## 1. 像素相关概念

### 1.1 物理像素 (Physical Pixel)

-   **定义**: 设备屏幕上真实存在的最小显示单元
-   **特点**:
    -   固定不变，由设备硬件决定
    -   也称为设备像素(Device Pixel)
-   **举例**: iPhone 12 Pro 的物理像素为 2532 × 1170

### 1.2 逻辑像素 (Logical Pixel)

-   **定义**: CSS 中使用的抽象像素单位，也称为 CSS 像素
-   **特点**:
    -   可以缩放，相对单位
    -   开发者在 CSS 中直接使用的 px 单位
-   **举例**: CSS 中写的 `width: 375px` 就是逻辑像素提升计划

### 1.3 设备像素比 (Device Pixel Ratio, DPR)

-   **定义**: 物理像素与逻辑像素的比值
-   **计算公式**: `DPR = 物理像素 / 逻辑像素`
-   **常见 DPR 值**:
    -   普通屏幕: DPR = 1
    -   高清屏幕: DPR = 2
    -   超高清屏幕: DPR = 3

```javascript
// 获取设备像素比
console.log('设备像素比:', window.devicePixelRatio)
```

## 2. 视口 (Viewport) 概念

### 2.1 布局视口 (Layout Viewport)

-   **定义**: 网页布局的基准视口
-   **特点**: 通常比设备屏幕宽，避免页面被压缩
-   **默认宽度**: 移动端通常为 980px 或 1024px

### 2.2 视觉视口 (Visual Viewport)

-   **定义**: 用户当前看到的网页区域
-   **特点**: 会随用户缩放而改变
-   **影响因素**: 用户缩放、软键盘弹出等

### 2.3 理想视口 (Ideal Viewport)

-   **定义**: 最适合移动设备浏览的视口大小
-   **特点**: 宽度等于设备宽度，无需缩放
-   **实现方式**: 通过 viewport meta 标签设置

## 3. Rem 单位详解

### 3.1 Rem 的定义

-   **全称**: Root em
-   **定义**: 相对于根元素(html)字体大小的单位
-   **计算**: `1rem = html元素的font-size值`

### 3.2 Rem vs Em 对比

| 特性       | Rem              | Em               |
| ---------- | ---------------- | ---------------- |
| 参考基准   | 根元素 font-size | 父元素 font-size |
| 嵌套影响   | 无               | 有(会累积)       |
| 计算复杂度 | 简单             | 复杂             |
| 适用场景   | 整体布局         | 组件内部         |

### 3.3 Rem 的优势

1. **统一基准**: 所有 rem 都基于根元素，便于管理
2. **整体缩放**: 修改根字体大小可整体缩放页面
3. **避免嵌套**: 不会像 em 那样产生嵌套累积问题
4. **响应式友好**: 配合媒体查询实现响应式设计

## 4. 移动端适配核心原理

### 4.1 适配目标

-   在不同尺寸设备上保持页面比例一致
-   确保内容可读性和操作便利性
-   提供良好的用户体验

### 4.2 适配策略

1. **等比缩放**: 页面整体按比例缩放
2. **弹性布局**: 使用 flex、grid 等布局方式
3. **响应式设计**: 不同断点使用不同样式
4. **混合方案**: 结合多种技术的综合方案

### 4.3 常见问题

-   **1px 边框问题**: 高 DPR 设备上 1px 显示过粗
-   **图片模糊**: 普通图片在高 DPR 设备上显示模糊
-   **字体过小**: 某些设备上字体显示过小
-   **布局错乱**: 不同设备上布局表现不一致

## 5. 实际应用场景

### 5.1 设计稿适配

```javascript
// 假设设计稿宽度为750px
const designWidth = 750
const deviceWidth = document.documentElement.clientWidth
const fontSize = (deviceWidth / designWidth) * 100
document.documentElement.style.fontSize = fontSize + 'px'
```

### 5.2 响应式断点

```css
/* 移动端 */
@media screen and (max-width: 768px) {
	html {
		font-size: 14px;
	}
}

/* 平板 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
	html {
		font-size: 16px;
	}
}

/* 桌面端 */
@media screen and (min-width: 1025px) {
	html {
		font-size: 18px;
	}
}
```

## 6. 最佳实践建议

### 6.1 Rem 使用建议

-   布局尺寸使用 rem
-   字体大小可以使用 rem 或 px
-   边框通常使用 px
-   小于 1px 的线条需要特殊处理

### 6.2 性能考虑

-   避免频繁修改根字体大小
-   使用 CSS 变量优化 rem 计算
-   合理使用媒体查询，避免过多断点

### 6.3 兼容性处理

-   提供 px 降级方案
-   考虑老版本浏览器兼容
-   测试各种设备和浏览器

## 7. 学习检验

### 理论检验题

1. 解释物理像素、逻辑像素、DPR 的关系
2. 说明 rem 和 em 的区别及适用场景
3. 描述三种视口的概念和作用

### 实践检验题

1. 编写代码获取当前设备的 DPR
2. 实现一个 rem 计算函数
3. 配置 viewport 实现理想视口

---

**学习提示**:

-   理解概念比记忆更重要
-   多在不同设备上测试效果
-   结合实际项目加深理解
