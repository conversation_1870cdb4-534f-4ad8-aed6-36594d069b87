# CSS Grid 布局速查表

## 🏗️ Grid容器属性

### 基础设置
```css
.container {
    display: grid;                    /* 创建Grid容器 */
    display: inline-grid;             /* 内联Grid容器 */
}
```

### 定义网格结构
```css
/* 列定义 */
grid-template-columns: 100px 200px 100px;           /* 固定宽度 */
grid-template-columns: 1fr 2fr 1fr;                 /* 弹性比例 */
grid-template-columns: repeat(3, 1fr);              /* 重复模式 */
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* 响应式 */

/* 行定义 */
grid-template-rows: 100px 200px;                    /* 固定高度 */
grid-template-rows: repeat(3, 100px);               /* 重复行 */

/* 间距 */
gap: 10px;                          /* 行列间距 */
gap: 10px 20px;                     /* 行间距 列间距 */
row-gap: 10px;                      /* 行间距 */
column-gap: 20px;                   /* 列间距 */
```

### 命名网格线
```css
grid-template-columns: [start] 1fr [middle] 2fr [end];
grid-template-rows: [header-start] 100px [header-end content-start] 1fr [content-end];
```

### Grid区域
```css
grid-template-areas: 
    "header header header"
    "sidebar main main"
    "footer footer footer";
```

### 自动网格
```css
grid-auto-columns: 100px;           /* 隐式列宽度 */
grid-auto-rows: 100px;              /* 隐式行高度 */
grid-auto-flow: row;                /* 自动放置方向: row | column | dense */
```

### 对齐方式
```css
/* 整个网格对齐 */
justify-content: start | end | center | stretch | space-around | space-between | space-evenly;
align-content: start | end | center | stretch | space-around | space-between | space-evenly;

/* 所有项目默认对齐 */
justify-items: start | end | center | stretch;
align-items: start | end | center | stretch;

/* 简写 */
place-content: center;              /* align-content justify-content */
place-items: center;                /* align-items justify-items */
```

## 📦 Grid项目属性

### 基于线号定位
```css
.item {
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 2;
    grid-row-end: 4;
    
    /* 简写形式 */
    grid-column: 1 / 3;             /* start / end */
    grid-row: 2 / 4;                /* start / end */
    
    /* 使用span */
    grid-column: span 2;            /* 跨越2列 */
    grid-row: span 3;               /* 跨越3行 */
    
    /* 完整简写 */
    grid-area: 2 / 1 / 4 / 3;       /* row-start / col-start / row-end / col-end */
}
```

### 基于命名线定位
```css
.item {
    grid-column: start / middle;
    grid-row: header-start / content-end;
}
```

### 基于区域定位
```css
.item {
    grid-area: header;              /* 使用命名区域 */
}
```

### 项目对齐
```css
.item {
    justify-self: start | end | center | stretch;
    align-self: start | end | center | stretch;
    place-self: center;             /* align-self justify-self */
}
```

## 🎯 常用模式

### 等宽列
```css
.grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}
```

### 响应式卡片网格
```css
.cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}
```

### 圣杯布局
```css
.layout {
    display: grid;
    grid-template-areas:
        "header header header"
        "sidebar main aside"
        "footer footer footer";
    grid-template-columns: 200px 1fr 200px;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
}
```

### 12列网格系统
```css
.grid-12 {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 20px;
}

.col-6 { grid-column: span 6; }    /* 占6列 */
.col-4 { grid-column: span 4; }    /* 占4列 */
.col-3 { grid-column: span 3; }    /* 占3列 */
```

### 居中布局
```css
.center {
    display: grid;
    place-items: center;
    min-height: 100vh;
}
```

### 重叠元素
```css
.overlap {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
}

.overlap > * {
    grid-column: 1;
    grid-row: 1;
}
```

## 📱 响应式技巧

### auto-fit vs auto-fill
```css
/* auto-fit: 拉伸项目填充容器 */
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

/* auto-fill: 保持项目尺寸，可能留空 */
grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
```

### 媒体查询中的Grid
```css
.responsive-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

@media (min-width: 768px) {
    .responsive-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .responsive-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

### 容器查询中的Grid
```css
.card-container {
    container-type: inline-size;
}

@container (min-width: 400px) {
    .card-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@container (min-width: 600px) {
    .card-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

## 🔧 调试技巧

### CSS调试
```css
/* 显示网格结构 */
.grid-debug {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid red;
}

.grid-debug > * {
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid green;
}
```

### 浏览器开发者工具
- **Chrome/Edge**: Elements面板 → Grid标签
- **Firefox**: Inspector → Grid overlay
- **Safari**: Elements面板 → 启用Grid显示

## 🌐 浏览器支持

| 浏览器 | 版本 | 支持程度 |
|--------|------|----------|
| Chrome | 57+ | ✅ 完全支持 |
| Firefox | 52+ | ✅ 完全支持 |
| Safari | 10.1+ | ✅ 完全支持 |
| Edge | 16+ | ✅ 完全支持 |
| IE | 10-11 | ⚠️ 部分支持（需要前缀） |

## 💡 最佳实践

### 何时使用Grid
- ✅ 二维布局（同时控制行和列）
- ✅ 复杂的页面布局
- ✅ 需要精确控制项目位置
- ✅ 响应式网格系统

### 何时使用Flexbox
- ✅ 一维布局（只需要行或列）
- ✅ 组件内部布局
- ✅ 需要项目自动分配空间
- ✅ 简单的对齐需求

### 组合使用
```css
/* Grid用于页面布局 */
.page-layout {
    display: grid;
    grid-template-areas: "header" "main" "footer";
}

/* Flexbox用于组件内部 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
```

## 🚀 高级技巧

### 子网格 (Subgrid) - 实验性
```css
.parent {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}

.child {
    display: grid;
    grid-column: 1 / 4;
    grid-template-columns: subgrid;  /* 继承父网格的列 */
}
```

### 动画效果
```css
.animated-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    transition: grid-template-columns 0.3s ease;
}

.animated-grid:hover {
    grid-template-columns: 2fr 1fr 1fr;
}
```

### 条件布局
```css
/* 根据项目数量调整布局 */
.grid:has(.item:nth-child(4)) {
    grid-template-columns: repeat(2, 1fr);
}

.grid:has(.item:nth-child(7)) {
    grid-template-columns: repeat(3, 1fr);
}
```
