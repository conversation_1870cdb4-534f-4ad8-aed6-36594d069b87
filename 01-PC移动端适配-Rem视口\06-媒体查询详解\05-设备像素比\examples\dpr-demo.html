<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备像素比（DPR）演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .dpr-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            z-index: 1000;
            min-width: 200px;
        }

        .content {
            padding: 2rem;
        }

        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #fafbfc;
        }

        .demo-section h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        /* 1. 图片演示 */
        .image-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .image-item {
            text-align: center;
        }

        .demo-image {
            width: 150px;
            height: 150px;
            margin: 0 auto 0.5rem;
            border-radius: 8px;
            border: 2px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            background: linear-gradient(45deg, #3498db, #2ecc71);
        }

        /* 标准分辨率图片 */
        .retina-image-1x {
            background: linear-gradient(45deg, #e74c3c, #f39c12);
        }

        .retina-image-1x::before {
            content: "1x 图片";
        }

        /* 高分辨率图片 */
        @media (-webkit-min-device-pixel-ratio: 2),
               (min-resolution: 192dpi),
               (min-resolution: 2dppx) {
            .retina-image-2x {
                background: linear-gradient(45deg, #9b59b6, #e91e63);
            }

            .retina-image-2x::before {
                content: "2x 图片";
            }
        }

        /* 超高分辨率图片 */
        @media (-webkit-min-device-pixel-ratio: 3),
               (min-resolution: 288dpi),
               (min-resolution: 3dppx) {
            .retina-image-3x {
                background: linear-gradient(45deg, #1abc9c, #16a085);
            }

            .retina-image-3x::before {
                content: "3x 图片";
            }
        }

        /* 2. 边框演示 */
        .border-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .border-item {
            padding: 1rem;
            text-align: center;
            background: white;
            border-radius: 4px;
        }

        .normal-border {
            border-bottom: 1px solid #ddd;
        }

        .hairline-border {
            position: relative;
        }

        /* 高DPR设备的1px边框解决方案 */
        @media (-webkit-min-device-pixel-ratio: 2) {
            .hairline-border::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: #ddd;
                transform: scaleY(0.5);
                transform-origin: bottom;
            }
        }

        @media (-webkit-min-device-pixel-ratio: 3) {
            .hairline-border::after {
                transform: scaleY(0.33);
            }
        }

        /* 3. 图标演示 */
        .icon-demo {
            display: flex;
            gap: 2rem;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
        }

        .icon {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .icon-1x::before { content: "1x"; }

        @media (-webkit-min-device-pixel-ratio: 2) {
            .icon-2x {
                background: #e74c3c;
            }
            .icon-2x::before { content: "2x"; }
        }

        @media (-webkit-min-device-pixel-ratio: 3) {
            .icon-3x {
                background: #9b59b6;
            }
            .icon-3x::before { content: "3x"; }
        }

        /* 4. 字体渲染优化 */
        .font-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .font-item {
            padding: 1rem;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .font-normal {
            font-size: 16px;
        }

        .font-optimized {
            font-size: 16px;
        }

        @media (-webkit-min-device-pixel-ratio: 2) {
            .font-optimized {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
            }
        }

        /* 5. 响应式背景图片 */
        .bg-demo {
            height: 200px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjM2Y1MWI1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj4xeCDlm77niYc8L3RleHQ+Cjwvc3ZnPg==') center/cover;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 1rem 0;
        }

        @media (-webkit-min-device-pixel-ratio: 2) {
            .bg-demo {
                background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZTc0YzNjIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj4yeCDlm77niYc8L3RleHQ+Cjwvc3ZnPg==');
            }
        }

        @media (-webkit-min-device-pixel-ratio: 3) {
            .bg-demo {
                background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjOWI1OWI2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIzMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj4zeCDlm77niYc8L3RleHQ+Cjwvc3ZnPg==');
            }
        }

        /* 设备特定样式 */
        .device-specific {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            background: #ecf0f1;
            text-align: center;
        }

        /* iPhone 样式 */
        @media (max-width: 414px) and (-webkit-min-device-pixel-ratio: 2) {
            .device-specific {
                background: #e8f5e8;
                border: 2px solid #27ae60;
            }
            .device-specific::before {
                content: "检测到 iPhone 设备";
                display: block;
                font-weight: bold;
                color: #27ae60;
            }
        }

        /* iPad 样式 */
        @media (min-width: 768px) and (max-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
            .device-specific {
                background: #e8f4fd;
                border: 2px solid #3498db;
            }
            .device-specific::before {
                content: "检测到 iPad 设备";
                display: block;
                font-weight: bold;
                color: #3498db;
            }
        }

        /* MacBook 样式 */
        @media (min-width: 1200px) and (-webkit-min-device-pixel-ratio: 2) {
            .device-specific {
                background: #fdf2e9;
                border: 2px solid #e67e22;
            }
            .device-specific::before {
                content: "检测到 MacBook 设备";
                display: block;
                font-weight: bold;
                color: #e67e22;
            }
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .info-table th,
        .info-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .info-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .code {
            background: #f1f3f4;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="dpr-indicator" id="dprInfo">
        <div><strong>设备信息</strong></div>
        <div>DPR: <span id="dprValue"></span></div>
        <div>屏幕: <span id="screenRes"></span></div>
        <div>视口: <span id="viewportRes"></span></div>
        <div>匹配: <span id="mediaMatch"></span></div>
    </div>

    <div class="container">
        <header class="header">
            <h1>设备像素比（DPR）演示</h1>
            <p>在不同DPR的设备上查看样式和资源的变化</p>
        </header>

        <div class="content">
            <div class="demo-section">
                <h3>当前设备信息</h3>
                <table class="info-table">
                    <tr>
                        <th>属性</th>
                        <th>值</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>设备像素比</td>
                        <td><span id="currentDPR"></span></td>
                        <td>window.devicePixelRatio</td>
                    </tr>
                    <tr>
                        <td>屏幕分辨率</td>
                        <td><span id="screenResolution"></span></td>
                        <td>screen.width × screen.height</td>
                    </tr>
                    <tr>
                        <td>视口尺寸</td>
                        <td><span id="viewportSize"></span></td>
                        <td>window.innerWidth × window.innerHeight</td>
                    </tr>
                    <tr>
                        <td>物理像素</td>
                        <td><span id="physicalPixels"></span></td>
                        <td>视口尺寸 × DPR</td>
                    </tr>
                </table>
            </div>

            <div class="demo-section">
                <h3>1. 响应式图片演示</h3>
                <p>根据设备像素比显示不同的图片资源：</p>
                <div class="image-demo">
                    <div class="image-item">
                        <div class="demo-image retina-image-1x"></div>
                        <p>标准分辨率<br><span class="code">DPR = 1</span></p>
                    </div>
                    <div class="image-item">
                        <div class="demo-image retina-image-2x"></div>
                        <p>高分辨率<br><span class="code">DPR ≥ 2</span></p>
                    </div>
                    <div class="image-item">
                        <div class="demo-image retina-image-3x"></div>
                        <p>超高分辨率<br><span class="code">DPR ≥ 3</span></p>
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h3>2. 1px边框问题解决</h3>
                <p>高DPR设备上的1px边框优化：</p>
                <div class="border-demo">
                    <div class="border-item normal-border">
                        <strong>普通边框</strong><br>
                        <span class="code">border: 1px solid</span><br>
                        在高DPR设备上可能显得较粗
                    </div>
                    <div class="border-item hairline-border">
                        <strong>优化边框</strong><br>
                        <span class="code">transform: scaleY(0.5)</span><br>
                        使用transform实现真正的1px
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h3>3. 图标适配演示</h3>
                <p>不同DPR下的图标显示：</p>
                <div class="icon-demo">
                    <div class="icon icon-1x" title="标准分辨率图标"></div>
                    <div class="icon icon-2x" title="2x分辨率图标"></div>
                    <div class="icon icon-3x" title="3x分辨率图标"></div>
                </div>
            </div>

            <div class="demo-section">
                <h3>4. 字体渲染优化</h3>
                <div class="font-demo">
                    <div class="font-item">
                        <h4>普通字体渲染</h4>
                        <p class="font-normal">这是普通的字体渲染效果。在高DPR设备上可能会显得不够清晰。</p>
                    </div>
                    <div class="font-item">
                        <h4>优化字体渲染</h4>
                        <p class="font-optimized">这是优化后的字体渲染效果。在高DPR设备上启用了抗锯齿和字体平滑。</p>
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h3>5. 背景图片适配</h3>
                <p>根据DPR自动切换背景图片：</p>
                <div class="bg-demo">
                    背景图片会根据设备DPR自动切换
                </div>
            </div>

            <div class="demo-section">
                <h3>6. 设备特定样式</h3>
                <div class="device-specific">
                    根据设备类型和DPR显示特定样式
                </div>
            </div>

            <div class="demo-section">
                <h3>7. 媒体查询匹配状态</h3>
                <div id="mediaQueryStatus">
                    <p><strong>当前匹配的媒体查询：</strong></p>
                    <ul id="matchedQueries"></ul>
                </div>
            </div>

            <div class="demo-section">
                <h3>测试建议</h3>
                <ul>
                    <li><strong>移动设备：</strong>在iPhone、iPad等Retina设备上测试</li>
                    <li><strong>桌面设备：</strong>在MacBook Pro等高DPR显示器上测试</li>
                    <li><strong>浏览器工具：</strong>使用Chrome DevTools的设备模拟器</li>
                    <li><strong>外接显示器：</strong>连接不同DPR的显示器测试</li>
                    <li><strong>缩放测试：</strong>调整浏览器缩放级别观察变化</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function updateDeviceInfo() {
            const dpr = window.devicePixelRatio || 1;
            const screenWidth = screen.width;
            const screenHeight = screen.height;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const physicalWidth = Math.round(viewportWidth * dpr);
            const physicalHeight = Math.round(viewportHeight * dpr);

            // 更新右上角信息
            document.getElementById('dprValue').textContent = dpr;
            document.getElementById('screenRes').textContent = `${screenWidth}×${screenHeight}`;
            document.getElementById('viewportRes').textContent = `${viewportWidth}×${viewportHeight}`;

            // 更新表格信息
            document.getElementById('currentDPR').textContent = dpr;
            document.getElementById('screenResolution').textContent = `${screenWidth} × ${screenHeight}`;
            document.getElementById('viewportSize').textContent = `${viewportWidth} × ${viewportHeight}`;
            document.getElementById('physicalPixels').textContent = `${physicalWidth} × ${physicalHeight}`;

            // 检查媒体查询匹配状态
            checkMediaQueries();
        }

        function checkMediaQueries() {
            const queries = [
                { name: 'DPR = 1', query: '(-webkit-device-pixel-ratio: 1)' },
                { name: 'DPR ≥ 1.5', query: '(-webkit-min-device-pixel-ratio: 1.5)' },
                { name: 'DPR ≥ 2', query: '(-webkit-min-device-pixel-ratio: 2)' },
                { name: 'DPR ≥ 3', query: '(-webkit-min-device-pixel-ratio: 3)' },
                { name: '192dpi+', query: '(min-resolution: 192dpi)' },
                { name: '288dpi+', query: '(min-resolution: 288dpi)' },
                { name: '2dppx+', query: '(min-resolution: 2dppx)' },
                { name: '3dppx+', query: '(min-resolution: 3dppx)' }
            ];

            const matchedQueries = queries.filter(q => window.matchMedia(q.query).matches);
            const matchedNames = matchedQueries.map(q => q.name);

            document.getElementById('mediaMatch').textContent = matchedNames.join(', ') || '无';

            // 更新匹配列表
            const listElement = document.getElementById('matchedQueries');
            listElement.innerHTML = '';
            matchedQueries.forEach(q => {
                const li = document.createElement('li');
                li.innerHTML = `<span class="code">${q.query}</span> - ${q.name}`;
                listElement.appendChild(li);
            });
        }

        // 监听DPR变化（如连接外接显示器）
        function setupDPRListener() {
            const mediaQuery = window.matchMedia('(-webkit-device-pixel-ratio: 2)');
            mediaQuery.addListener(function(mq) {
                console.log('DPR媒体查询状态变化:', mq.matches);
                setTimeout(updateDeviceInfo, 100);
            });
        }

        // 初始化
        updateDeviceInfo();
        setupDPRListener();

        // 监听窗口变化
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 100);
        });

        // 输出详细信息到控制台
        console.log('=== 设备像素比信息 ===');
        console.log('DPR:', window.devicePixelRatio);
        console.log('屏幕分辨率:', screen.width, '×', screen.height);
        console.log('视口尺寸:', window.innerWidth, '×', window.innerHeight);
        console.log('物理像素:', Math.round(window.innerWidth * window.devicePixelRatio), '×', Math.round(window.innerHeight * window.devicePixelRatio));

        // 测试不同DPR的图片加载
        function getImageByDPR(baseName, extension = 'jpg') {
            const dpr = window.devicePixelRatio || 1;
            let suffix = '';

            if (dpr >= 3) {
                suffix = '@3x';
            } else if (dpr >= 2) {
                suffix = '@2x';
            }

            return `${baseName}${suffix}.${extension}`;
        }

        console.log('推荐图片:', getImageByDPR('hero'));
    </script>
</body>
</html>