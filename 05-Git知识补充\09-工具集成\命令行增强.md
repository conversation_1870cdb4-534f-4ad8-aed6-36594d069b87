# Git 命令行增强工具

## 🚀 命令行增强概览

### 📋 为什么需要命令行增强？
虽然 Git 命令行功能强大，但原生界面相对简陋。通过增强工具可以提供更好的用户体验、更丰富的信息显示和更高的操作效率。

```bash
# 命令行增强的优势：
├── 美化显示 - 彩色输出和图标
├── 智能提示 - 自动补全和建议
├── 状态增强 - 丰富的仓库状态信息
├── 快捷操作 - 简化常用命令
└── 集成工具 - 与其他开发工具集成
```

## 🎨 终端美化工具

### 🌟 Oh My Zsh

#### 安装配置
```bash
# 安装 Oh My Zsh
sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"

# 或者使用 wget
sh -c "$(wget https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh -O -)"

# 配置文件位置
~/.zshrc
```

#### Git 相关插件
```bash
# 编辑 ~/.zshrc
plugins=(
    git
    git-flow
    gitignore
    github
    git-auto-fetch
    git-escape-magic
    git-extras
    git-prompt
)

# 重新加载配置
source ~/.zshrc
```

#### 常用 Git 别名
```bash
# Oh My Zsh 内置的 Git 别名
g='git'
ga='git add'
gaa='git add --all'
gb='git branch'
gba='git branch -a'
gbd='git branch -d'
gc='git commit -v'
gc!='git commit -v --amend'
gca='git commit -v -a'
gca!='git commit -v -a --amend'
gcb='git checkout -b'
gcd='git checkout develop'
gcm='git checkout main'
gco='git checkout'
gcp='git cherry-pick'
gd='git diff'
gds='git diff --staged'
gf='git fetch'
gfa='git fetch --all --prune'
gl='git pull'
glog='git log --oneline --decorate --graph'
gp='git push'
gpd='git push --dry-run'
gr='git remote'
grb='git rebase'
grbi='git rebase -i'
gst='git status'
gsta='git stash push'
gstp='git stash pop'
```

### 🎯 Powerlevel10k 主题

#### 安装配置
```bash
# 克隆仓库
git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k

# 设置主题
# 在 ~/.zshrc 中设置
ZSH_THEME="powerlevel10k/powerlevel10k"

# 配置主题
p10k configure
```

#### Git 状态显示
```bash
# Powerlevel10k Git 状态元素：
├── 分支名称 - 当前分支
├── 提交状态 - 领先/落后提交数
├── 工作区状态 - 修改、新增、删除文件数
├── 暂存区状态 - 暂存的文件数
├── 冲突状态 - 合并冲突指示
└── 标签信息 - 当前标签
```

### 💻 Windows Terminal + PowerShell

#### Posh-Git 模块
```powershell
# 安装 Posh-Git
Install-Module posh-git -Scope CurrentUser

# 导入模块
Import-Module posh-git

# 添加到 PowerShell 配置文件
Add-PoshGitToProfile

# 自定义提示符
$GitPromptSettings.DefaultPromptAbbreviateHomeDirectory = $true
$GitPromptSettings.DefaultPromptPath.ForegroundColor = 'Orange'
```

#### Oh My Posh
```powershell
# 安装 Oh My Posh
winget install JanDeDobbeleer.OhMyPosh

# 或使用 Scoop
scoop install oh-my-posh

# 设置主题
oh-my-posh init pwsh --config "$env:POSH_THEMES_PATH\jandedobbeleer.omp.json" | Invoke-Expression

# 添加到 PowerShell 配置文件
notepad $PROFILE
```

## 🔧 Git 增强工具

### ⚡ Git Extras

#### 安装
```bash
# macOS
brew install git-extras

# Ubuntu/Debian
sudo apt-get install git-extras

# 手动安装
git clone https://github.com/tj/git-extras.git
cd git-extras
sudo make install
```

#### 实用命令
```bash
# 查看仓库摘要
git summary

# 查看贡献者统计
git contrib

# 查看文件修改统计
git effort

# 创建功能分支
git feature feature-name

# 完成功能分支
git feature finish feature-name

# 查看忽略的文件
git ignore-io

# 删除已合并的分支
git delete-merged-branches

# 查看最近的标签
git recent

# 撤销最后一次提交
git undo

# 查看文件的行数统计
git line-summary
```

### 🎨 Delta - 更好的 diff 工具

#### 安装配置
```bash
# macOS
brew install git-delta

# Ubuntu/Debian
wget https://github.com/dandavison/delta/releases/download/0.16.5/git-delta_0.16.5_amd64.deb
sudo dpkg -i git-delta_0.16.5_amd64.deb

# 配置 Git 使用 Delta
git config --global core.pager delta
git config --global interactive.diffFilter 'delta --color-only'
git config --global delta.navigate true
git config --global delta.light false
git config --global merge.conflictstyle diff3
git config --global diff.colorMoved default
```

#### Delta 配置选项
```bash
# ~/.gitconfig
[delta]
    features = side-by-side line-numbers decorations
    syntax-theme = Dracula
    plus-style = syntax "#003800"
    minus-style = syntax "#3f0001"

[delta "decorations"]
    commit-decoration-style = bold yellow box ul
    file-style = bold yellow ul
    file-decoration-style = none
    hunk-header-decoration-style = cyan box ul

[delta "line-numbers"]
    line-numbers-left-style = cyan
    line-numbers-right-style = cyan
    line-numbers-minus-style = 124
    line-numbers-plus-style = 28
```

### 🔍 Bat - 更好的文件查看器

#### 安装使用
```bash
# macOS
brew install bat

# Ubuntu/Debian
sudo apt install bat

# 配置 Git 使用 bat
git config --global core.pager "bat --style=plain"

# 查看文件
bat filename.js

# 显示行号
bat -n filename.js

# 指定语言
bat -l javascript filename.js
```

### 📊 Lazygit - TUI Git 客户端

#### 安装
```bash
# macOS
brew install lazygit

# Ubuntu/Debian
sudo add-apt-repository ppa:lazygit-team/release
sudo apt-get update
sudo apt-get install lazygit

# 手动安装
go install github.com/jesseduffield/lazygit@latest
```

#### 基本使用
```bash
# 启动 Lazygit
lazygit

# 快捷键
j/k     # 上下移动
h/l     # 左右切换面板
space   # 暂存/取消暂存
c       # 提交
P       # 推送
p       # 拉取
R       # 刷新
q       # 退出
```

#### 配置文件
```yaml
# ~/.config/lazygit/config.yml
gui:
  theme:
    lightTheme: false
    activeBorderColor:
      - green
      - bold
    inactiveBorderColor:
      - white
    optionsTextColor:
      - blue
  commitLength: 50
  sidePanelWidth: 0.3333
git:
  paging:
    colorArg: always
    pager: delta --dark --paging=never
  merging:
    manualCommit: false
  skipHookPrefix: WIP
  autoFetch: true
```

## 🔧 自动补全工具

### 📝 Git 自动补全

#### Bash 补全
```bash
# 下载补全脚本
curl https://raw.githubusercontent.com/git/git/master/contrib/completion/git-completion.bash -o ~/.git-completion.bash

# 添加到 ~/.bashrc
if [ -f ~/.git-completion.bash ]; then
    source ~/.git-completion.bash
fi
```

#### Zsh 补全
```bash
# Oh My Zsh 已内置 Git 补全
# 手动安装
mkdir -p ~/.zsh/completion
curl https://raw.githubusercontent.com/git/git/master/contrib/completion/git-completion.zsh -o ~/.zsh/completion/_git

# 添加到 ~/.zshrc
fpath=(~/.zsh/completion $fpath)
autoload -Uz compinit && compinit
```

### 🎯 高级补全工具

#### Zsh-autosuggestions
```bash
# 安装
git clone https://github.com/zsh-users/zsh-autosuggestions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions

# 添加到插件列表
plugins=(zsh-autosuggestions)
```

#### Zsh-syntax-highlighting
```bash
# 安装
git clone https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting

# 添加到插件列表
plugins=(zsh-syntax-highlighting)
```

## 🎨 提示符增强

### 🌟 Starship

#### 安装配置
```bash
# 安装 Starship
curl -sS https://starship.rs/install.sh | sh

# 添加到 shell 配置
# Bash
echo 'eval "$(starship init bash)"' >> ~/.bashrc

# Zsh
echo 'eval "$(starship init zsh)"' >> ~/.zshrc

# PowerShell
echo 'Invoke-Expression (&starship init powershell)' >> $PROFILE
```

#### Git 配置
```toml
# ~/.config/starship.toml
[git_branch]
symbol = "🌱 "
truncation_length = 4
truncation_symbol = ""

[git_commit]
commit_hash_length = 4
tag_symbol = "🔖 "

[git_state]
format = '[\($state( $progress_current of $progress_total)\)]($style) '
cherry_pick = "[🍒 PICKING](bold red)"

[git_status]
conflicted = "🏳"
ahead = "🏎💨"
behind = "😰"
diverged = "😵"
up_to_date = "✓"
untracked = "🤷‍"
stashed = "📦"
modified = "📝"
staged = '[++\($count\)](green)'
renamed = "👅"
deleted = "🗑"
```

### 📊 自定义提示符

#### Bash 提示符
```bash
# 添加到 ~/.bashrc
parse_git_branch() {
    git branch 2> /dev/null | sed -e '/^[^*]/d' -e 's/* \(.*\)/(\1)/'
}

PS1="\[\033[32m\]\w\[\033[33m\]\$(parse_git_branch)\[\033[00m\] $ "
```

#### Zsh 提示符
```bash
# 添加到 ~/.zshrc
autoload -Uz vcs_info
precmd() { vcs_info }

zstyle ':vcs_info:git:*' formats ' (%b)'

setopt PROMPT_SUBST
PROMPT='%F{green}%~%f%F{yellow}${vcs_info_msg_0_}%f $ '
```

## 🔧 集成开发环境

### 💻 终端集成

#### iTerm2 (macOS)
```bash
# 安装 iTerm2
brew install --cask iterm2

# 配置 Git 集成
# Preferences -> Profiles -> Advanced -> Semantic History
# 设置为 "Open with editor..." 并选择编辑器
```

#### Windows Terminal
```json
// settings.json
{
    "profiles": {
        "defaults": {
            "colorScheme": "One Half Dark"
        },
        "list": [
            {
                "name": "PowerShell",
                "source": "Windows.Terminal.PowershellCore",
                "startingDirectory": "C:\\Projects"
            }
        ]
    },
    "schemes": [
        {
            "name": "Git",
            "background": "#0C0C0C",
            "foreground": "#CCCCCC"
        }
    ]
}
```

### 🔗 编辑器集成

#### Vim/Neovim
```vim
" ~/.vimrc 或 ~/.config/nvim/init.vim
" Git 插件
Plug 'tpope/vim-fugitive'
Plug 'airblade/vim-gitgutter'
Plug 'junegunn/gv.vim'

" Git 状态行
set statusline+=%{FugitiveStatusline()}

" 快捷键
nnoremap <leader>gs :Git<CR>
nnoremap <leader>gd :Gdiff<CR>
nnoremap <leader>gb :Git blame<CR>
```

#### Emacs
```elisp
;; ~/.emacs.d/init.el
(use-package magit
  :ensure t
  :bind ("C-x g" . magit-status))

(use-package git-gutter
  :ensure t
  :config
  (global-git-gutter-mode +1))
```

## 💡 最佳实践

### ✅ 命令行增强建议
1. **选择合适的 Shell** - Zsh 提供更好的 Git 支持
2. **使用主题** - 选择支持 Git 状态显示的主题
3. **配置别名** - 设置常用 Git 命令的别名
4. **启用补全** - 配置 Git 命令自动补全
5. **定制提示符** - 显示有用的 Git 状态信息

### 🔧 性能优化
```bash
# 大仓库优化
git config --global core.preloadindex true
git config --global core.fscache true
git config --global gc.auto 256

# 状态检查优化
git config --global status.submoduleSummary false
git config --global diff.mnemonicPrefix true
```

### 🚨 常见问题
1. **字体问题** - 确保终端支持 Unicode 字符
2. **颜色显示** - 检查终端颜色支持
3. **性能问题** - 在大仓库中可能影响性能
4. **兼容性** - 确保工具与 Git 版本兼容
5. **配置冲突** - 避免多个工具的配置冲突

---

**记住**: 命令行增强工具可以大大提高 Git 使用体验，但不要过度依赖。选择适合你工作流程的工具组合，并保持配置的简洁和高效！ 🚀
