<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- viewport-fit=cover 让内容延伸到整个屏幕 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>viewport-fit 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }

        /* 安全区域变量（iOS 11+支持） */
        :root {
            --safe-area-inset-top: env(safe-area-inset-top);
            --safe-area-inset-right: env(safe-area-inset-right);
            --safe-area-inset-bottom: env(safe-area-inset-bottom);
            --safe-area-inset-left: env(safe-area-inset-left);
        }

        /* 头部区域 - 延伸到刘海区域 */
        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: var(--safe-area-inset-top, 20px) 20px 20px 20px;
            padding-left: max(20px, var(--safe-area-inset-left, 20px));
            padding-right: max(20px, var(--safe-area-inset-right, 20px));
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 1.2rem;
            text-align: center;
            margin-top: 10px;
        }

        /* 主要内容区域 */
        .main-content {
            padding-top: calc(80px + var(--safe-area-inset-top, 0px));
            padding-bottom: calc(80px + var(--safe-area-inset-bottom, 0px));
            padding-left: max(20px, var(--safe-area-inset-left, 20px));
            padding-right: max(20px, var(--safe-area-inset-right, 20px));
            min-height: 100vh;
        }

        /* 底部导航 - 延伸到底部安全区域 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px 20px;
            padding-bottom: max(15px, var(--safe-area-inset-bottom, 15px));
            padding-left: max(20px, var(--safe-area-inset-left, 20px));
            padding-right: max(20px, var(--safe-area-inset-right, 20px));
            backdrop-filter: blur(10px);
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            text-align: center;
            color: white;
            text-decoration: none;
            padding: 8px;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 内容卡片 */
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card h2 {
            margin-bottom: 15px;
            color: #fff;
        }

        .card p {
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* 安全区域指示器 */
        .safe-area-indicator {
            position: fixed;
            background: rgba(255, 0, 0, 0.3);
            pointer-events: none;
            z-index: 1000;
        }

        .safe-area-top {
            top: 0;
            left: 0;
            right: 0;
            height: var(--safe-area-inset-top, 0px);
        }

        .safe-area-bottom {
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--safe-area-inset-bottom, 0px);
        }

        .safe-area-left {
            top: 0;
            bottom: 0;
            left: 0;
            width: var(--safe-area-inset-left, 0px);
        }

        .safe-area-right {
            top: 0;
            bottom: 0;
            right: 0;
            width: var(--safe-area-inset-right, 0px);
        }

        /* 调试信息 */
        .debug-info {
            position: fixed;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            font-family: monospace;
            z-index: 1001;
            max-width: 200px;
        }

        .debug-info div {
            margin-bottom: 5px;
        }

        /* 不同viewport-fit值的演示样式 */
        .viewport-demo {
            margin: 20px 0;
        }

        .demo-option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .demo-option:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .demo-option.active {
            background: rgba(255, 255, 255, 0.4);
            border: 2px solid white;
        }

        /* 响应式设计 */
        @media (orientation: landscape) {
            .header h1 {
                font-size: 1rem;
            }
            
            .main-content {
                padding-top: calc(60px + var(--safe-area-inset-top, 0px));
            }
        }
    </style>
</head>
<body>
    <!-- 安全区域指示器（红色半透明区域） -->
    <div class="safe-area-indicator safe-area-top"></div>
    <div class="safe-area-indicator safe-area-bottom"></div>
    <div class="safe-area-indicator safe-area-left"></div>
    <div class="safe-area-indicator safe-area-right"></div>

    <!-- 头部导航 -->
    <header class="header">
        <h1>viewport-fit 演示</h1>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="card">
            <h2>什么是 viewport-fit？</h2>
            <p>viewport-fit 是为了适配iPhone X等带有"刘海"或"药丸"屏幕的设备而设计的CSS属性。</p>
            <p>当前页面使用了 <code>viewport-fit=cover</code>，内容会延伸到整个屏幕。</p>
        </div>

        <div class="card">
            <h2>三种取值对比</h2>
            <div class="viewport-demo">
                <div class="demo-option" data-value="auto">
                    <strong>auto (默认)</strong><br>
                    内容显示在安全区域内，不会被刘海遮挡
                </div>
                <div class="demo-option active" data-value="cover">
                    <strong>cover (当前)</strong><br>
                    内容延伸到整个屏幕，需要处理安全区域
                </div>
                <div class="demo-option" data-value="contain">
                    <strong>contain</strong><br>
                    确保内容包含在安全区域内
                </div>
            </div>
        </div>

        <div class="card">
            <h2>CSS 安全区域变量</h2>
            <p>使用 <code>env()</code> 函数获取安全区域信息：</p>
            <ul style="margin-left: 20px; margin-top: 10px;">
                <li><code>env(safe-area-inset-top)</code> - 顶部安全距离</li>
                <li><code>env(safe-area-inset-bottom)</code> - 底部安全距离</li>
                <li><code>env(safe-area-inset-left)</code> - 左侧安全距离</li>
                <li><code>env(safe-area-inset-right)</code> - 右侧安全距离</li>
            </ul>
        </div>

        <div class="card">
            <h2>实际应用场景</h2>
            <p><strong>适合使用 cover：</strong></p>
            <ul style="margin-left: 20px;">
                <li>沉浸式应用（游戏、视频播放器）</li>
                <li>需要全屏背景的页面</li>
                <li>想要充分利用屏幕空间的应用</li>
            </ul>
            <p style="margin-top: 15px;"><strong>适合使用 auto/contain：</strong></p>
            <ul style="margin-left: 20px;">
                <li>内容型网站</li>
                <li>不需要特殊适配的页面</li>
                <li>简单的移动端页面</li>
            </ul>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="nav-items">
            <a href="#" class="nav-item">首页</a>
            <a href="#" class="nav-item">分类</a>
            <a href="#" class="nav-item">购物车</a>
            <a href="#" class="nav-item">我的</a>
        </div>
    </nav>

    <!-- 调试信息 -->
    <div class="debug-info" id="debugInfo">
        <div>加载中...</div>
    </div>

    <script>
        // 更新调试信息
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            
            // 获取安全区域信息
            const safeAreaTop = computedStyle.getPropertyValue('--safe-area-inset-top') || '0px';
            const safeAreaBottom = computedStyle.getPropertyValue('--safe-area-inset-bottom') || '0px';
            const safeAreaLeft = computedStyle.getPropertyValue('--safe-area-inset-left') || '0px';
            const safeAreaRight = computedStyle.getPropertyValue('--safe-area-inset-right') || '0px';
            
            debugInfo.innerHTML = `
                <div><strong>安全区域信息</strong></div>
                <div>Top: ${safeAreaTop}</div>
                <div>Bottom: ${safeAreaBottom}</div>
                <div>Left: ${safeAreaLeft}</div>
                <div>Right: ${safeAreaRight}</div>
                <div>---</div>
                <div>屏幕: ${screen.width}×${screen.height}</div>
                <div>视口: ${window.innerWidth}×${window.innerHeight}</div>
                <div>DPR: ${window.devicePixelRatio}</div>
            `;
        }

        // 页面加载完成后更新信息
        document.addEventListener('DOMContentLoaded', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDebugInfo, 100);
        });

        // 演示不同viewport-fit值的效果（仅用于说明）
        document.querySelectorAll('.demo-option').forEach(option => {
            option.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.demo-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                
                // 添加active类到当前选项
                this.classList.add('active');
                
                const value = this.dataset.value;
                console.log(`选择了 viewport-fit: ${value}`);
                console.log('注意：实际切换需要修改meta标签并刷新页面');
                
                // 显示提示
                alert(`选择了 viewport-fit: ${value}\n\n注意：实际效果需要修改HTML中的meta标签并刷新页面才能看到。`);
            });
        });

        // 输出当前viewport配置
        console.log('=== Viewport配置信息 ===');
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        if (viewportMeta) {
            console.log('当前viewport配置:', viewportMeta.content);
        }
        
        // 检测是否支持安全区域
        if (CSS.supports('padding: env(safe-area-inset-top)')) {
            console.log('✅ 当前浏览器支持安全区域 env() 函数');
        } else {
            console.log('❌ 当前浏览器不支持安全区域 env() 函数');
        }
    </script>
</body>
</html>
