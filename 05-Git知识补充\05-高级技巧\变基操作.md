# Git 变基操作详解

## 🔄 变基概念

### 📋 什么是变基（Rebase）？
变基是将一系列提交重新应用到另一个基础提交上的操作，它可以创建更线性、更清晰的提交历史。

```bash
# 变基前：
#     A---B---C feature
#    /
# D---E---F---G main

# 变基后：
# D---E---F---G---A'---B'---C' main
#                 feature
```

### 🎯 变基 vs 合并
```bash
# 合并（Merge）：
# - 保留原始历史
# - 创建合并提交
# - 分支结构清晰
# - 操作安全

# 变基（Rebase）：
# - 线性历史
# - 无合并提交
# - 历史更清晰
# - 需要谨慎使用
```

## 🔧 基本变基操作

### 📋 git rebase 基本用法

#### 简单变基
```bash
# 将当前分支变基到 main
git checkout feature-branch
git rebase main

# 或者一步完成
git rebase main feature-branch

# 变基到指定提交
git rebase commit-hash

# 变基到标签
git rebase v1.0.0
```

#### 变基过程
```bash
# 1. 开始变基
git rebase main
# First, rewinding head to replay your work on top of it...
# Applying: commit message 1
# Applying: commit message 2

# 2. 如果有冲突
# CONFLICT (content): Merge conflict in file.txt
# error: could not apply abc123... commit message

# 3. 解决冲突
# 编辑冲突文件...
git add file.txt

# 4. 继续变基
git rebase --continue

# 5. 重复直到完成
```

#### 变基选项
```bash
# 中止变基
git rebase --abort

# 跳过当前提交
git rebase --skip

# 编辑提交信息
git rebase --edit-todo

# 显示变基状态
git status
```

### 🎯 onto 变基
```bash
# 将 feature 分支从 old-base 变基到 new-base
git rebase --onto new-base old-base feature

# 示例：将 feature 的部分提交变基到 main
#     A---B---C---D feature
#    /
# E---F---G main
#     H---I old-feature

# 只将 C---D 变基到 main
git rebase --onto main B feature
# 结果：
# E---F---G---C'---D' main
#                   feature
```

## 🎨 交互式变基

### 📝 git rebase -i 交互式操作

#### 启动交互式变基
```bash
# 变基最近 3 个提交
git rebase -i HEAD~3

# 变基到指定提交
git rebase -i commit-hash

# 变基到分支
git rebase -i main
```

#### 交互式编辑器
```bash
# 编辑器中显示的内容：
pick abc123 First commit
pick def456 Second commit
pick ghi789 Third commit

# Rebase instructions:
# p, pick = use commit
# r, reword = use commit, but edit the commit message
# e, edit = use commit, but stop for amending
# s, squash = use commit, but meld into previous commit
# f, fixup = like "squash", but discard this commit's log message
# x, exec = run command (the rest of the line) using shell
# d, drop = remove commit
```

#### 常用操作示例
```bash
# 1. 压缩提交（squash）
pick abc123 First commit
squash def456 Second commit  # 合并到上一个提交
squash ghi789 Third commit   # 合并到上一个提交

# 2. 修改提交信息（reword）
pick abc123 First commit
reword def456 Second commit  # 修改提交信息
pick ghi789 Third commit

# 3. 编辑提交（edit）
pick abc123 First commit
edit def456 Second commit    # 暂停以修改提交
pick ghi789 Third commit

# 4. 删除提交（drop）
pick abc123 First commit
drop def456 Second commit    # 删除这个提交
pick ghi789 Third commit

# 5. 重新排序
pick ghi789 Third commit     # 调整提交顺序
pick abc123 First commit
pick def456 Second commit
```

### 🔧 高级交互式操作

#### 拆分提交
```bash
# 1. 标记要拆分的提交为 edit
edit abc123 Commit to split

# 2. 重置到上一个提交
git reset HEAD~1

# 3. 分别提交不同的修改
git add file1.txt
git commit -m "First part of changes"

git add file2.txt
git commit -m "Second part of changes"

# 4. 继续变基
git rebase --continue
```

#### 执行命令
```bash
# 在变基过程中执行命令
pick abc123 First commit
exec npm test               # 运行测试
pick def456 Second commit
exec npm run lint          # 运行代码检查
pick ghi789 Third commit
```

#### fixup 和 squash 的区别
```bash
# squash：合并提交并编辑提交信息
pick abc123 Main feature
squash def456 Fix typo      # 会提示编辑合并后的提交信息

# fixup：合并提交但丢弃提交信息
pick abc123 Main feature
fixup def456 Fix typo       # 直接使用 abc123 的提交信息
```

## 🚨 变基安全规则

### ⚠️ 黄金法则
```bash
# 🚨 永远不要对已经推送到公共仓库的提交进行变基！

# 安全的变基：
✅ 本地分支的提交
✅ 还未推送的提交
✅ 只有你在使用的分支

# 危险的变基：
❌ 已推送到远程的提交
❌ 其他人正在使用的分支
❌ 主分支或共享分支
```

### 🔒 安全实践
```bash
# 1. 变基前备份分支
git branch backup-feature feature-branch

# 2. 检查分支状态
git log --oneline --graph

# 3. 确认没有其他人使用这个分支
git branch -r --contains HEAD

# 4. 变基后强制推送时使用 --force-with-lease
git push --force-with-lease origin feature-branch
```

### 🛡️ 恢复变基错误
```bash
# 查看引用日志
git reflog

# 恢复到变基前的状态
git reset --hard HEAD@{1}

# 或者恢复到备份分支
git reset --hard backup-feature
```

## 🎯 变基应用场景

### 🧹 清理提交历史

#### 场景1：压缩多个小提交
```bash
# 开发过程中的多个小提交：
abc123 Add feature
def456 Fix typo
ghi789 Update documentation
jkl012 Fix another typo

# 使用交互式变基压缩：
git rebase -i HEAD~4

# 编辑器中：
pick abc123 Add feature
fixup def456 Fix typo
fixup ghi789 Update documentation
fixup jkl012 Fix another typo

# 结果：一个干净的提交
abc123 Add feature
```

#### 场景2：重新组织提交
```bash
# 混乱的提交顺序：
abc123 Add tests
def456 Implement feature
ghi789 Update docs
jkl012 Fix feature bug

# 重新排序：
pick def456 Implement feature
pick jkl012 Fix feature bug
pick abc123 Add tests
pick ghi789 Update docs
```

### 🔄 同步主分支

#### 保持功能分支最新
```bash
# 定期将主分支的更新变基到功能分支
git checkout feature-branch
git fetch origin
git rebase origin/main

# 解决冲突后推送
git push --force-with-lease origin feature-branch
```

#### 准备合并前的清理
```bash
# 合并前清理功能分支
git checkout feature-branch
git rebase -i main

# 压缩、重排、清理提交
# 然后合并到主分支
git checkout main
git merge feature-branch  # 这将是快进合并
```

## 🔧 高级变基技巧

### 🎯 部分变基
```bash
# 只变基特定范围的提交
git rebase --onto target-branch start-commit end-commit

# 示例：将 C 到 E 的提交变基到 main
#     A---B---C---D---E feature
#    /
# F---G---H main

git rebase --onto main B E
# 结果：
# F---G---H---C'---D'---E' main
```

### 🔄 变基时保留合并提交
```bash
# 默认变基会线性化历史，丢失合并提交
# 使用 --rebase-merges 保留合并结构
git rebase --rebase-merges main

# 或者使用 --preserve-merges（已弃用）
git rebase --preserve-merges main
```

### 📝 自动化变基
```bash
# 设置自动变基
git config --global pull.rebase true

# 为特定分支设置自动变基
git config branch.feature.rebase true

# 拉取时自动变基
git pull --rebase origin main
```

## 🛠️ 变基工具和配置

### ⚙️ 配置变基编辑器
```bash
# 设置变基编辑器
git config --global core.editor "code --wait"  # VS Code
git config --global core.editor "vim"          # Vim
git config --global core.editor "nano"         # Nano

# 设置序列编辑器（用于交互式变基）
git config --global sequence.editor "code --wait"
```

### 🎨 变基钩子
```bash
# pre-rebase 钩子（.git/hooks/pre-rebase）
#!/bin/sh
# 防止变基受保护的分支
if [ "$1" = "main" ]; then
    echo "Cannot rebase main branch"
    exit 1
fi

# post-rewrite 钩子（.git/hooks/post-rewrite）
#!/bin/sh
# 变基后执行的操作
if [ "$1" = "rebase" ]; then
    echo "Rebase completed, running tests..."
    npm test
fi
```

### 📊 变基统计
```bash
# 查看变基统计信息
git log --oneline --graph

# 比较变基前后
git log --oneline backup-branch..feature-branch

# 查看变基的提交
git reflog --grep="rebase"
```

## 💡 最佳实践

### ✅ 变基使用建议
1. **小而频繁** - 经常进行小的变基操作
2. **本地优先** - 主要在本地分支使用变基
3. **清理历史** - 合并前清理提交历史
4. **保持线性** - 使用变基保持线性历史
5. **谨慎推送** - 变基后使用 --force-with-lease 推送

### 🚨 避免的陷阱
1. **变基公共分支** - 不要变基已推送的共享分支
2. **复杂冲突** - 避免在有复杂冲突时变基
3. **频繁强制推送** - 减少强制推送的频率
4. **忽略备份** - 变基前总是创建备份
5. **盲目变基** - 理解变基的影响再操作

### 🔧 团队协作中的变基
```bash
# 团队变基规范：
1. 只变基自己的功能分支
2. 变基前与团队沟通
3. 使用 --force-with-lease 推送
4. 定期同步主分支
5. 保持提交历史清晰
```

---

**记住**: 变基是一个强大但需要谨慎使用的工具。正确使用变基可以创建清晰的提交历史，但错误使用可能会造成问题。始终遵循黄金法则：不要变基已推送的公共提交！ 🔄
