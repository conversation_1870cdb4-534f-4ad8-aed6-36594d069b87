{"name": "vue-graphql-client", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@apollo/client": "^3.8.7", "@element-plus/icons-vue": "^2.1.0", "@vue/apollo-composable": "^4.0.0-beta.11", "dayjs": "^1.11.10", "element-plus": "^2.4.2", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.0.1", "@graphql-codegen/typescript-vue-apollo": "^3.3.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.8.10", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^4.4.1", "sass": "^1.89.2", "typescript": "^5.2.2", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.0", "vue-tsc": "^1.8.22"}}