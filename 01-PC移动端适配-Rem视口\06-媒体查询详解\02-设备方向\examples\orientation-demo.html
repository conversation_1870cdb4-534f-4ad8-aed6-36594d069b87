<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备方向适配演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        .header {
            background: #4a90e2;
            color: white;
            padding: 1rem;
            text-align: center;
            margin-bottom: 1rem;
            border-radius: 8px;
        }

        .orientation-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            z-index: 1000;
        }

        /* 演示区域 */
        .demo-section {
            background: white;
            margin: 1rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .demo-section h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        /* 1. 导航栏演示 */
        .nav-demo {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .nav-logo {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 0.5rem;
        }

        /* 2. 布局演示 */
        .layout-demo {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 1rem;
            min-height: 200px;
        }

        .sidebar-demo {
            background: #ecf0f1;
            padding: 1rem;
            border-radius: 4px;
        }

        .content-demo {
            background: #3498db;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 3. 卡片网格演示 */
        .card-grid-demo {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .card-demo {
            background: #e8f4fd;
            padding: 1rem;
            border-radius: 4px;
            text-align: center;
            border: 2px solid #3498db;
        }

        /* 4. 表单演示 */
        .form-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .form-group input,
        .form-group textarea {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        /* 竖屏样式 */
        @media (orientation: portrait) {
            .orientation-indicator {
                background: #e74c3c;
            }

            .orientation-indicator::before {
                content: "📱 竖屏模式";
            }

            /* 导航栏竖屏优化 */
            .nav-demo {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .nav-menu {
                justify-content: center;
            }

            /* 布局竖屏优化 */
            .layout-demo {
                grid-template-columns: 1fr;
            }

            .sidebar-demo {
                order: -1; /* 侧边栏移到顶部 */
            }

            /* 卡片竖屏优化 */
            .card-grid-demo {
                grid-template-columns: 1fr;
            }

            /* 表单竖屏优化 */
            .form-demo {
                grid-template-columns: 1fr;
            }
        }

        /* 横屏样式 */
        @media (orientation: landscape) {
            .orientation-indicator {
                background: #27ae60;
            }

            .orientation-indicator::before {
                content: "💻 横屏模式";
            }

            /* 导航栏横屏优化 */
            .nav-demo {
                padding: 0.75rem 1rem;
            }

            /* 卡片横屏优化 */
            .card-grid-demo {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* 移动设备横屏特殊处理 */
        @media (max-width: 768px) and (orientation: landscape) {
            .header {
                padding: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .header h1 {
                font-size: 1.2rem;
            }

            .demo-section {
                padding: 1rem;
                margin: 0.5rem 0;
            }

            .nav-demo {
                padding: 0.5rem;
            }

            .nav-logo {
                font-size: 1rem;
            }

            .nav-menu {
                gap: 0.5rem;
            }

            .layout-demo {
                min-height: 150px;
            }

            .card-grid-demo {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }

            .card-demo {
                padding: 0.75rem;
            }
        }

        /* 小屏幕横屏极限优化 */
        @media (max-height: 500px) and (orientation: landscape) {
            .container {
                padding: 0.5rem;
            }

            .header {
                padding: 0.25rem;
                margin-bottom: 0.25rem;
            }

            .header h1 {
                font-size: 1rem;
                margin: 0;
            }

            .demo-section {
                padding: 0.5rem;
                margin: 0.25rem 0;
            }

            .demo-section h3 {
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            .layout-demo {
                min-height: 100px;
            }

            .sidebar-demo {
                display: none; /* 极小高度时隐藏侧边栏 */
            }

            .layout-demo {
                grid-template-columns: 1fr;
            }
        }

        /* 平板竖屏 */
        @media (min-width: 768px) and (orientation: portrait) {
            .card-grid-demo {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-demo {
                grid-template-columns: 1fr 1fr;
            }
        }

        /* 大屏幕横屏 */
        @media (min-width: 1024px) and (orientation: landscape) {
            .card-grid-demo {
                grid-template-columns: repeat(5, 1fr);
            }
        }

        /* 动画效果 */
        .demo-section,
        .nav-demo,
        .card-demo,
        .sidebar-demo,
        .content-demo {
            transition: all 0.3s ease;
        }

        /* 提示信息 */
        .tip {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .tip strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="orientation-indicator"></div>

    <div class="container">
        <header class="header">
            <h1>设备方向适配演示</h1>
            <p>旋转设备或调整浏览器窗口来查看不同方向下的布局变化</p>
        </header>

        <div class="tip">
            <strong>测试方法：</strong>
            <ul>
                <li>移动设备：旋转设备查看横竖屏效果</li>
                <li>桌面浏览器：调整窗口宽高比例模拟不同方向</li>
                <li>开发者工具：使用设备模拟器并旋转</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>1. 导航栏适配</h3>
            <nav class="nav-demo">
                <div class="nav-logo">Logo</div>
                <ul class="nav-menu">
                    <li><a href="#">首页</a></li>
                    <li><a href="#">产品</a></li>
                    <li><a href="#">服务</a></li>
                    <li><a href="#">联系</a></li>
                </ul>
            </nav>
            <p><strong>效果：</strong>竖屏时导航变为垂直布局，横屏时保持水平布局</p>
        </div>

        <div class="demo-section">
            <h3>2. 侧边栏布局</h3>
            <div class="layout-demo">
                <aside class="sidebar-demo">
                    <h4>侧边栏</h4>
                    <ul>
                        <li>菜单项 1</li>
                        <li>菜单项 2</li>
                        <li>菜单项 3</li>
                    </ul>
                </aside>
                <main class="content-demo">
                    <div>主要内容区域</div>
                </main>
            </div>
            <p><strong>效果：</strong>竖屏时侧边栏移到顶部，横屏时保持左侧布局</p>
        </div>

        <div class="demo-section">
            <h3>3. 卡片网格</h3>
            <div class="card-grid-demo">
                <div class="card-demo">卡片 1</div>
                <div class="card-demo">卡片 2</div>
                <div class="card-demo">卡片 3</div>
                <div class="card-demo">卡片 4</div>
                <div class="card-demo">卡片 5</div>
                <div class="card-demo">卡片 6</div>
            </div>
            <p><strong>效果：</strong>根据方向和屏幕大小自动调整列数</p>
        </div>

        <div class="demo-section">
            <h3>4. 表单布局</h3>
            <form class="form-demo">
                <div class="form-group">
                    <label for="name">姓名</label>
                    <input type="text" id="name" placeholder="请输入姓名">
                </div>
                <div class="form-group">
                    <label for="email">邮箱</label>
                    <input type="email" id="email" placeholder="请输入邮箱">
                </div>
                <div class="form-group">
                    <label for="phone">电话</label>
                    <input type="tel" id="phone" placeholder="请输入电话">
                </div>
                <div class="form-group">
                    <label for="message">留言</label>
                    <textarea id="message" rows="3" placeholder="请输入留言"></textarea>
                </div>
            </form>
            <p><strong>效果：</strong>竖屏时表单变为单列，横屏时保持双列布局</p>
        </div>

        <div class="demo-section">
            <h3>当前设备信息</h3>
            <div id="deviceInfo">
                <p>屏幕尺寸: <span id="screenSize"></span></p>
                <p>视口尺寸: <span id="viewportSize"></span></p>
                <p>设备方向: <span id="deviceOrientation"></span></p>
                <p>像素比: <span id="pixelRatio"></span></p>
            </div>
        </div>
    </div>

    <script>
        function updateDeviceInfo() {
            const screenSize = `${screen.width}×${screen.height}`;
            const viewportSize = `${window.innerWidth}×${window.innerHeight}`;
            const orientation = window.innerHeight > window.innerWidth ? '竖屏 (Portrait)' : '横屏 (Landscape)';
            const pixelRatio = window.devicePixelRatio || 1;

            document.getElementById('screenSize').textContent = screenSize;
            document.getElementById('viewportSize').textContent = viewportSize;
            document.getElementById('deviceOrientation').textContent = orientation;
            document.getElementById('pixelRatio').textContent = pixelRatio;
        }

        // 初始化
        updateDeviceInfo();

        // 监听窗口变化
        window.addEventListener('resize', updateDeviceInfo);

        // 监听方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 100);
        });

        // 输出当前匹配的媒体查询
        function logCurrentQueries() {
            const queries = [
                { name: '竖屏', query: '(orientation: portrait)' },
                { name: '横屏', query: '(orientation: landscape)' },
                { name: '移动设备横屏', query: '(max-width: 768px) and (orientation: landscape)' },
                { name: '小屏幕横屏', query: '(max-height: 500px) and (orientation: landscape)' },
                { name: '平板竖屏', query: '(min-width: 768px) and (orientation: portrait)' },
                { name: '大屏幕横屏', query: '(min-width: 1024px) and (orientation: landscape)' }
            ];

            console.log('当前匹配的方向查询：');
            queries.forEach(q => {
                if (window.matchMedia(q.query).matches) {
                    console.log(`✓ ${q.name}`);
                }
            });
        }

        // 每次变化时输出
        window.addEventListener('resize', () => {
            setTimeout(logCurrentQueries, 100);
        });

        window.addEventListener('orientationchange', () => {
            setTimeout(logCurrentQueries, 200);
        });

        // 初始输出
        logCurrentQueries();
    </script>
</body>
</html>
