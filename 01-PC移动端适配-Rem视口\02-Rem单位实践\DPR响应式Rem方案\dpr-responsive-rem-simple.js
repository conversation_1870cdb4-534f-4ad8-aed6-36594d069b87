/**
 * 简化的 DPR + 响应式 rem 计算方案
 * 
 * 特性：
 * - 轻量级实现
 * - 基础 DPR 支持
 * - PC 和移动端适配
 * - 性能优化
 * 
 * 使用方法：
 * 直接引入即可，无需额外配置
 * 
 * 全局方法：
 * - window.px2rem(px) - px 转 rem
 * - window.getDPR() - 获取当前 DPR
 * - window.isMobile() - 判断是否移动端
 */

(function() {
    'use strict';
    
    // 配置参数
    const config = {
        mobileDesignWidth: 750,    // 移动端设计稿宽度
        mobileBaseSize: 75,        // 移动端基准字体大小
        pcBaseSize: 16,            // PC端基准字体大小
        breakpoint: 768,           // 移动端/PC端断点
        maxWidth: 2560,            // 最大宽度限制
        minWidth: 320              // 最小宽度限制
    };
    
    // 获取设备像素比
    const dpr = Math.min(window.devicePixelRatio || 1, 3);
    
    // 判断是否移动端
    const isMobile = () => window.innerWidth <= config.breakpoint;
    
    // 设置 viewport（仅移动端）
    function setViewport() {
        if (!isMobile()) return;
        
        const scale = 1 / dpr;
        let meta = document.querySelector('meta[name="viewport"]');
        
        if (!meta) {
            meta = document.createElement('meta');
            meta.name = 'viewport';
            document.head.appendChild(meta);
        }
        
        meta.content = [
            'width=device-width',
            `initial-scale=${scale}`,
            `maximum-scale=${scale}`,
            `minimum-scale=${scale}`,
            'user-scalable=no'
        ].join(', ');
    }
    
    // 设置 rem 基准值
    function setRem() {
        let width = document.documentElement.clientWidth || window.innerWidth;
        const mobile = isMobile();
        
        // 限制宽度范围
        width = Math.max(config.minWidth, Math.min(width, config.maxWidth));
        
        let fontSize;
        if (mobile) {
            // 移动端：考虑 DPR
            const actualWidth = width * dpr;
            fontSize = (actualWidth / config.mobileDesignWidth) * config.mobileBaseSize;
            
            // 设置 viewport
            setViewport();
        } else {
            // PC端：不考虑 DPR，使用标准计算
            fontSize = config.pcBaseSize * Math.min(width / 1920, 1.2);
        }
        
        // 设置根字体大小
        document.documentElement.style.fontSize = fontSize + 'px';
        
        // 设置相关属性
        document.documentElement.setAttribute('data-dpr', dpr);
        document.documentElement.setAttribute('data-device', mobile ? 'mobile' : 'desktop');
        
        // 设置 CSS 变量
        document.documentElement.style.setProperty('--dpr', dpr);
        document.documentElement.style.setProperty('--hairline', (1 / dpr) + 'px');
        document.documentElement.style.setProperty('--rem-base', fontSize + 'px');
        
        // 设置 body 类名
        document.body.className = document.body.className.replace(/device-\w+|dpr-\d+/g, '');
        document.body.classList.add(mobile ? 'device-mobile' : 'device-desktop', `dpr-${dpr}`);
        
        // 设置 body 字体大小
        document.body.style.fontSize = mobile ? (14 * dpr) + 'px' : '16px';
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 初始化
    function init() {
        setRem();
        
        // 事件监听
        const debouncedSetRem = debounce(setRem, 100);
        
        window.addEventListener('resize', debouncedSetRem);
        window.addEventListener('orientationchange', () => {
            setTimeout(setRem, 300);
        });
        
        // 监听 DPR 变化（用户缩放）
        if (window.matchMedia) {
            try {
                const mediaQuery = window.matchMedia(`(resolution: ${dpr}dppx)`);
                if (mediaQuery.addListener) {
                    mediaQuery.addListener(setRem);
                }
            } catch (e) {
                // 某些浏览器可能不支持 resolution 媒体查询
                console.warn('DPR media query not supported');
            }
        }
    }
    
    // DOM 就绪后初始化
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        init();
    } else {
        document.addEventListener('DOMContentLoaded', init);
    }
    
    // 全局工具函数
    window.px2rem = function(px) {
        const mobile = isMobile();
        const baseSize = mobile ? config.mobileBaseSize : config.pcBaseSize;
        return px / baseSize;
    };
    
    window.getDPR = function() {
        return dpr;
    };
    
    window.isMobile = isMobile;
    
    window.getRemBase = function() {
        return parseFloat(document.documentElement.style.fontSize) || 16;
    };
    
    // 获取实际像素值（考虑 DPR）
    window.getActualPx = function(designPx) {
        return isMobile() ? designPx * dpr : designPx;
    };
    
    // 获取当前配置信息
    window.getRemInfo = function() {
        return {
            dpr: dpr,
            isMobile: isMobile(),
            remBase: parseFloat(document.documentElement.style.fontSize),
            deviceWidth: document.documentElement.clientWidth,
            config: config
        };
    };
    
})();

/**
 * 使用示例：
 * 
 * // 获取当前信息
 * console.log(window.getRemInfo());
 * 
 * // px 转 rem
 * const remValue = window.px2rem(100); // 设计稿 100px 转为 rem
 * 
 * // 获取实际像素值
 * const actualPx = window.getActualPx(100); // 考虑 DPR 的实际像素
 * 
 * // 判断设备类型
 * if (window.isMobile()) {
 *     console.log('当前是移动端');
 * }
 * 
 * // 获取 DPR
 * console.log('当前 DPR:', window.getDPR());
 */
