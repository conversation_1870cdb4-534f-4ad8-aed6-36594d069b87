<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>媒体查询基础语法示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .example {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .example h3 {
            margin-top: 0;
            color: #333;
        }

        .demo-box {
            width: 100%;
            height: 100px;
            background: #3498db;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 10px 0;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        /* 1. 基础媒体查询 */
        @media screen {
            .screen-only {
                background: #2ecc71;
            }
        }

        @media print {
            .print-only {
                background: #e74c3c !important;
            }
        }

        /* 2. 宽度断点 */
        @media (max-width: 600px) {
            .responsive-width {
                background: #e67e22;
            }
        }

        @media (min-width: 601px) and (max-width: 900px) {
            .responsive-width {
                background: #9b59b6;
            }
        }

        @media (min-width: 901px) {
            .responsive-width {
                background: #1abc9c;
            }
        }

        /* 3. 高度查询 */
        @media (max-height: 500px) {
            .responsive-height {
                background: #f39c12;
                height: 50px;
            }
        }

        /* 4. 方向查询 */
        @media (orientation: portrait) {
            .orientation-demo {
                background: #e91e63;
            }
        }

        @media (orientation: landscape) {
            .orientation-demo {
                background: #673ab7;
            }
        }

        /* 5. 组合查询 */
        @media screen and (min-width: 768px) and (orientation: landscape) {
            .combined-demo {
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            }
        }

        /* 6. 逻辑操作符 */
        @media (max-width: 600px), (orientation: portrait) {
            .or-demo {
                background: #ff9ff3;
            }
        }

        @media not screen {
            .not-demo {
                background: #333;
            }
        }

        /* 7. 分辨率查询 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .high-dpi {
                background: #34495e;
                background-image: linear-gradient(45deg, #34495e 25%, transparent 25%), 
                                  linear-gradient(-45deg, #34495e 25%, transparent 25%), 
                                  linear-gradient(45deg, transparent 75%, #34495e 75%), 
                                  linear-gradient(-45deg, transparent 75%, #34495e 75%);
                background-size: 20px 20px;
                background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            }
        }

        /* 信息显示 */
        .info {
            background: #ecf0f1;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin: 10px 0;
            font-size: 14px;
        }

        .current-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }
            
            .current-info {
                display: none;
            }
            
            .example {
                box-shadow: none;
                border: 1px solid #ddd;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="current-info" id="currentInfo">
        <div>宽度: <span id="width"></span>px</div>
        <div>高度: <span id="height"></span>px</div>
        <div>方向: <span id="orientation"></span></div>
        <div>DPR: <span id="dpr"></span></div>
    </div>

    <h1>媒体查询基础语法示例</h1>
    <p>调整浏览器窗口大小或旋转设备来查看不同媒体查询的效果。</p>

    <div class="example">
        <h3>1. 媒体类型</h3>
        <div class="demo-box screen-only">屏幕设备 - 绿色</div>
        <div class="demo-box print-only">打印设备 - 红色（打印时可见）</div>
        <div class="info">
            <strong>说明：</strong>第一个框在屏幕上显示为绿色，第二个框在打印时显示为红色。
        </div>
    </div>

    <div class="example">
        <h3>2. 宽度断点</h3>
        <div class="demo-box responsive-width">响应式宽度演示</div>
        <div class="info">
            <strong>说明：</strong>
            <ul>
                <li>≤600px: 橙色</li>
                <li>601px-900px: 紫色</li>
                <li>≥901px: 青色</li>
            </ul>
        </div>
    </div>

    <div class="example">
        <h3>3. 高度查询</h3>
        <div class="demo-box responsive-height">高度响应演示</div>
        <div class="info">
            <strong>说明：</strong>当浏览器高度 ≤500px 时，背景变为橙色，高度减半。
        </div>
    </div>

    <div class="example">
        <h3>4. 设备方向</h3>
        <div class="demo-box orientation-demo">方向演示</div>
        <div class="info">
            <strong>说明：</strong>竖屏时显示粉色，横屏时显示紫色。
        </div>
    </div>

    <div class="example">
        <h3>5. 组合查询 (AND)</h3>
        <div class="demo-box combined-demo">组合条件演示</div>
        <div class="info">
            <strong>说明：</strong>同时满足"屏幕设备 AND 宽度≥768px AND 横屏"时显示渐变背景。
        </div>
    </div>

    <div class="example">
        <h3>6. 逻辑操作符 (OR)</h3>
        <div class="demo-box or-demo">OR 操作符演示</div>
        <div class="info">
            <strong>说明：</strong>满足"宽度≤600px OR 竖屏"任一条件时显示粉色。
        </div>
    </div>

    <div class="example">
        <h3>7. 高分辨率屏幕</h3>
        <div class="demo-box high-dpi">高DPI屏幕演示</div>
        <div class="info">
            <strong>说明：</strong>在高分辨率屏幕（Retina等）上显示特殊的棋盘图案。
        </div>
    </div>

    <div class="example">
        <h3>测试建议</h3>
        <div class="info">
            <ol>
                <li><strong>调整窗口宽度：</strong>拖拽浏览器边缘改变宽度</li>
                <li><strong>调整窗口高度：</strong>观察高度查询效果</li>
                <li><strong>移动设备：</strong>旋转设备测试方向变化</li>
                <li><strong>打印预览：</strong>按Ctrl+P查看打印样式</li>
                <li><strong>开发者工具：</strong>使用设备模拟器测试</li>
            </ol>
        </div>
    </div>

    <script>
        function updateInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const orientation = height > width ? '竖屏' : '横屏';
            const dpr = window.devicePixelRatio || 1;

            document.getElementById('width').textContent = width;
            document.getElementById('height').textContent = height;
            document.getElementById('orientation').textContent = orientation;
            document.getElementById('dpr').textContent = dpr;
        }

        // 初始化
        updateInfo();

        // 监听窗口变化
        window.addEventListener('resize', updateInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateInfo, 100);
        });

        // 控制台输出当前媒体查询匹配情况
        function logMediaQueries() {
            const queries = [
                { name: '小屏幕', query: '(max-width: 600px)' },
                { name: '中等屏幕', query: '(min-width: 601px) and (max-width: 900px)' },
                { name: '大屏幕', query: '(min-width: 901px)' },
                { name: '竖屏', query: '(orientation: portrait)' },
                { name: '横屏', query: '(orientation: landscape)' },
                { name: '高分辨率', query: '(-webkit-min-device-pixel-ratio: 2)' }
            ];

            console.log('当前匹配的媒体查询：');
            queries.forEach(q => {
                if (window.matchMedia(q.query).matches) {
                    console.log(`✓ ${q.name}: ${q.query}`);
                }
            });
        }

        // 每次窗口变化时输出匹配的查询
        window.addEventListener('resize', () => {
            setTimeout(logMediaQueries, 100);
        });

        // 初始输出
        logMediaQueries();
    </script>
</body>
</html>
