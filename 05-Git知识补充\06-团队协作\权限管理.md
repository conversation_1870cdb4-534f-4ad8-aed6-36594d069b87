# Git 权限管理

## 📋 概述

Git 权限管理是团队协作中的重要环节，涉及仓库访问控制、分支保护、操作权限等方面。本文档详细介绍了不同平台的权限管理策略和最佳实践。

## 🔐 权限管理基础

### 1. 权限层级

**仓库级权限**:
- **Owner**: 完全控制权限，包括删除仓库
- **Admin**: 管理权限，可以修改设置和管理成员
- **Write**: 读写权限，可以推送代码
- **Read**: 只读权限，可以克隆和查看代码
- **None**: 无权限，无法访问仓库

**组织级权限**:
- **Organization Owner**: 组织完全控制权
- **Organization Member**: 组织成员基础权限
- **Outside Collaborator**: 外部协作者权限

### 2. 权限继承关系

```
Organization Owner
    ↓
Organization Admin
    ↓
Repository Admin
    ↓
Repository Write
    ↓
Repository Read
```

## 🏢 GitHub 权限管理

### 1. 仓库权限设置

**基础权限配置**:
```bash
# 通过 GitHub CLI 管理权限
gh repo edit owner/repo --visibility private
gh repo edit owner/repo --enable-issues=true
gh repo edit owner/repo --enable-wiki=false
```

**协作者管理**:
```bash
# 添加协作者
gh api repos/owner/repo/collaborators/username \
  --method PUT \
  --field permission=write

# 查看协作者
gh api repos/owner/repo/collaborators

# 移除协作者
gh api repos/owner/repo/collaborators/username \
  --method DELETE
```

### 2. 分支保护规则

**保护主分支**:
```yaml
# .github/branch-protection.yml
protection_rules:
  main:
    required_status_checks:
      strict: true
      contexts:
        - "ci/tests"
        - "ci/lint"
    enforce_admins: true
    required_pull_request_reviews:
      required_approving_review_count: 2
      dismiss_stale_reviews: true
      require_code_owner_reviews: true
    restrictions:
      users: []
      teams: ["core-team"]
```

**通过 API 设置分支保护**:
```bash
# 设置分支保护规则
curl -X PUT \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/owner/repo/branches/main/protection \
  -d '{
    "required_status_checks": {
      "strict": true,
      "contexts": ["ci/tests"]
    },
    "enforce_admins": true,
    "required_pull_request_reviews": {
      "required_approving_review_count": 2,
      "dismiss_stale_reviews": true
    },
    "restrictions": null
  }'
```

### 3. CODEOWNERS 文件

**配置代码所有者**:
```bash
# .github/CODEOWNERS

# 全局所有者
* @team-leads

# 特定目录所有者
/src/core/ @core-team @senior-dev
/docs/ @doc-team
/tests/ @qa-team

# 特定文件所有者
package.json @maintainers
*.md @doc-team
.github/ @devops-team

# 多个所有者（任一批准即可）
/src/api/ @backend-team
/src/ui/ @frontend-team

# 嵌套规则（更具体的规则优先）
/src/core/security/ @security-team @core-team
```

### 4. 团队权限管理

**创建和管理团队**:
```bash
# 创建团队
gh api orgs/orgname/teams \
  --method POST \
  --field name="frontend-team" \
  --field description="Frontend development team" \
  --field privacy="closed"

# 添加团队成员
gh api orgs/orgname/teams/frontend-team/memberships/username \
  --method PUT \
  --field role="member"

# 设置团队仓库权限
gh api orgs/orgname/teams/frontend-team/repos/owner/repo \
  --method PUT \
  --field permission="write"
```

## 🦊 GitLab 权限管理

### 1. 项目权限级别

**GitLab 权限级别**:
- **Guest (10)**: 查看项目，创建 Issue
- **Reporter (20)**: 拉取代码，下载项目
- **Developer (30)**: 推送到非保护分支，创建 MR
- **Maintainer (40)**: 推送到保护分支，管理项目设置
- **Owner (50)**: 完全控制权限

**权限配置示例**:
```bash
# 通过 GitLab CLI 管理权限
glab api projects/:id/members \
  --method POST \
  --field user_id=123 \
  --field access_level=30

# 设置分支保护
glab api projects/:id/protected_branches \
  --method POST \
  --field name="main" \
  --field push_access_level=40 \
  --field merge_access_level=30
```

### 2. 分支保护策略

**保护分支配置**:
```yaml
# .gitlab-ci.yml 中的保护策略
variables:
  PROTECTED_BRANCHES: "main,develop,release/*"

protect_branches:
  script:
    - |
      for branch in $PROTECTED_BRANCHES; do
        curl --request POST \
          --header "PRIVATE-TOKEN: $CI_JOB_TOKEN" \
          --data "name=$branch&push_access_level=40&merge_access_level=30" \
          "$CI_API_V4_URL/projects/$CI_PROJECT_ID/protected_branches"
      done
  only:
    - main
```

## 🔧 企业级权限管理

### 1. LDAP/AD 集成

**GitHub Enterprise 配置**:
```yaml
# github-enterprise.yml
ldap:
  host: ldap.company.com
  port: 636
  encryption: ssl
  base_dn: "dc=company,dc=com"
  uid_attribute: sAMAccountName
  bind_dn: "cn=github,ou=service,dc=company,dc=com"
  
  # 用户映射
  user_groups:
    - cn=developers,ou=groups,dc=company,dc=com
    - cn=admins,ou=groups,dc=company,dc=com
    
  # 权限映射
  team_mapping:
    developers: developer
    admins: admin
```

**GitLab LDAP 配置**:
```yaml
# gitlab.yml
ldap:
  enabled: true
  servers:
    main:
      label: 'Company LDAP'
      host: 'ldap.company.com'
      port: 636
      uid: 'sAMAccountName'
      bind_dn: 'cn=gitlab,ou=service,dc=company,dc=com'
      password: 'password'
      encryption: 'simple_tls'
      base: 'dc=company,dc=com'
      
      # 用户过滤
      user_filter: '(&(objectClass=user)(!(userAccountControl:1.2.840.113556.1.4.803:=2)))'
      
      # 组同步
      group_base: 'ou=groups,dc=company,dc=com'
      admin_group: 'cn=gitlab-admins,ou=groups,dc=company,dc=com'
```

### 2. SSO 集成

**SAML 配置示例**:
```yaml
# GitHub Enterprise SAML
saml:
  sso_url: https://company.okta.com/app/github/sso/saml
  certificate: |
    -----BEGIN CERTIFICATE-----
    MIICertificateData...
    -----END CERTIFICATE-----
  issuer: http://www.okta.com/exampleIssuer
  
  # 属性映射
  attribute_mapping:
    username: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name
    email: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress
    full_name: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname
```

## 🛡️ 安全最佳实践

### 1. 访问控制原则

**最小权限原则**:
```markdown
## 权限分配指南

### 开发人员
- 对自己负责的仓库: Write 权限
- 对其他仓库: Read 权限
- 对核心仓库: 通过 PR 贡献

### 团队负责人
- 对团队仓库: Admin 权限
- 对其他仓库: Write 权限
- 分支保护规则管理权限

### 项目经理
- 对所有项目仓库: Read 权限
- Issue 和 Project 管理权限
- 不需要代码推送权限

### 外部协作者
- 特定仓库的 Read 权限
- 通过 Fork 和 PR 贡献
- 限时访问权限
```

### 2. 定期权限审查

**权限审查清单**:
```bash
#!/bin/bash
# 权限审查脚本

echo "=== GitHub 权限审查报告 ==="
echo "生成时间: $(date)"
echo

# 获取所有仓库
repos=$(gh repo list --limit 100 --json name,visibility)

for repo in $(echo $repos | jq -r '.[].name'); do
    echo "仓库: $repo"
    
    # 获取协作者
    collaborators=$(gh api repos/owner/$repo/collaborators)
    echo "协作者数量: $(echo $collaborators | jq length)"
    
    # 检查分支保护
    protection=$(gh api repos/owner/$repo/branches/main/protection 2>/dev/null || echo "null")
    if [ "$protection" = "null" ]; then
        echo "⚠️  主分支未受保护"
    else
        echo "✅ 主分支已受保护"
    fi
    
    echo "---"
done
```

### 3. 敏感操作监控

**审计日志监控**:
```bash
# GitHub 审计日志查询
gh api orgs/orgname/audit-log \
  --field phrase="action:repo.create OR action:repo.delete" \
  --field include="all" \
  | jq '.[] | {action: .action, actor: .actor, repo: .repo, created_at: .created_at}'

# GitLab 审计事件
curl --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
  "https://gitlab.com/api/v4/audit_events?target_type=Project&after=2023-01-01"
```

## 📊 权限管理工具

### 1. 自动化权限管理

**Terraform 配置**:
```hcl
# github-permissions.tf
resource "github_repository" "main" {
  name        = "my-project"
  description = "My awesome project"
  visibility  = "private"
  
  # 分支保护
  has_issues   = true
  has_wiki     = false
  has_projects = true
}

resource "github_branch_protection" "main" {
  repository_id = github_repository.main.node_id
  pattern       = "main"
  
  required_status_checks {
    strict = true
    contexts = ["ci/tests", "ci/lint"]
  }
  
  required_pull_request_reviews {
    required_approving_review_count = 2
    dismiss_stale_reviews          = true
    require_code_owner_reviews     = true
  }
  
  enforce_admins = true
}

resource "github_team" "developers" {
  name        = "developers"
  description = "Development team"
  privacy     = "closed"
}

resource "github_team_repository" "developers" {
  team_id    = github_team.developers.id
  repository = github_repository.main.name
  permission = "push"
}
```

### 2. 权限监控脚本

**Python 权限监控**:
```python
#!/usr/bin/env python3
import requests
import json
from datetime import datetime

class GitHubPermissionMonitor:
    def __init__(self, token, org):
        self.token = token
        self.org = org
        self.headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
    
    def get_repositories(self):
        """获取组织所有仓库"""
        url = f'https://api.github.com/orgs/{self.org}/repos'
        response = requests.get(url, headers=self.headers)
        return response.json()
    
    def check_branch_protection(self, repo_name):
        """检查分支保护状态"""
        url = f'https://api.github.com/repos/{self.org}/{repo_name}/branches/main/protection'
        response = requests.get(url, headers=self.headers)
        return response.status_code == 200
    
    def get_collaborators(self, repo_name):
        """获取仓库协作者"""
        url = f'https://api.github.com/repos/{self.org}/{repo_name}/collaborators'
        response = requests.get(url, headers=self.headers)
        return response.json()
    
    def generate_report(self):
        """生成权限报告"""
        repos = self.get_repositories()
        report = {
            'generated_at': datetime.now().isoformat(),
            'organization': self.org,
            'repositories': []
        }
        
        for repo in repos:
            repo_info = {
                'name': repo['name'],
                'visibility': repo['visibility'],
                'protected': self.check_branch_protection(repo['name']),
                'collaborators': len(self.get_collaborators(repo['name']))
            }
            report['repositories'].append(repo_info)
        
        return report

# 使用示例
monitor = GitHubPermissionMonitor('your-token', 'your-org')
report = monitor.generate_report()
print(json.dumps(report, indent=2))
```

## 🎯 权限管理最佳实践总结

### 1. 核心原则
- **最小权限**: 只给予必要的最小权限
- **定期审查**: 定期检查和清理权限
- **职责分离**: 不同角色分配不同权限
- **审计跟踪**: 记录所有权限变更

### 2. 实施建议
- 建立清晰的权限分级制度
- 使用自动化工具管理权限
- 实施强制代码审查
- 保护关键分支和操作

### 3. 监控和维护
- 定期生成权限报告
- 监控异常访问行为
- 及时响应安全事件
- 持续优化权限策略

---

通过建立完善的权限管理体系，可以确保代码安全，提高团队协作效率，降低安全风险。
