# PC+移动端适配，Rem+视口 学习指南

## 📚 学习目标
- 掌握移动端适配的核心概念和技术
- 理解Rem单位的工作原理和使用场景
- 学会配置视口(Viewport)实现响应式设计
- 能够独立完成PC和移动端的适配方案

## 📖 知识点概览

### 1. 移动端适配基础概念
- **物理像素(Physical Pixel)**: 设备屏幕的实际像素点
- **逻辑像素(Logical Pixel)**: CSS中使用的像素单位
- **设备像素比(DPR)**: 物理像素与逻辑像素的比值
- **视口(Viewport)**: 浏览器显示网页的区域

### 2. Rem单位详解
- **定义**: 相对于根元素(html)字体大小的单位
- **优势**: 
  - 相对单位，便于整体缩放
  - 避免em的嵌套问题
  - 适合响应式设计
- **计算公式**: `1rem = html元素的font-size值`

### 3. 视口配置
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
```

### 4. 常见适配方案
1. **Rem + 动态设置根字体大小**
2. **Vw/Vh 视口单位**
3. **媒体查询 + 弹性布局**
4. **Rem + 媒体查询组合**

## 🗂️ 文件结构说明

```
01-PC移动端适配-Rem视口/
├── README.md                    # 学习指南(当前文件)
├── 01-基础概念/
│   ├── 概念解析.md             # 详细概念说明
│   └── 设备像素比测试.html      # DPR测试页面
├── 02-Rem单位实践/
│   ├── rem基础使用.html        # Rem基本用法
│   ├── rem响应式布局.html      # Rem响应式实例
│   └── rem计算工具.js          # Rem计算辅助函数
├── 03-视口配置/
│   ├── 视口配置详解.md         # Viewport详细说明
│   ├── 不同视口配置对比.html    # 视口配置效果对比
│   └── 视口适配方案.html       # 完整视口适配
├── 04-实战项目/
│   ├── 移动端商城首页/         # 完整移动端项目
│   ├── 响应式新闻网站/         # PC+移动端响应式
│   └── 多端适配组件库/         # 可复用组件
└── 05-最佳实践/
    ├── 适配方案对比.md         # 各种方案优缺点
    ├── 常见问题解决.md         # FAQ和解决方案
    └── 性能优化建议.md         # 性能相关建议
```

## 🎯 学习路径建议

### 第一阶段：理论基础 (1-2天)
1. 阅读 `01-基础概念/概念解析.md`
2. 运行 `01-基础概念/设备像素比测试.html` 理解DPR
3. 学习 `03-视口配置/视口配置详解.md`

### 第二阶段：实践操作 (2-3天)
1. 完成 `02-Rem单位实践/` 下的所有示例
2. 测试 `03-视口配置/` 下的配置方案
3. 理解不同配置的效果差异

### 第三阶段：项目实战 (3-5天)
1. 完成 `04-实战项目/移动端商城首页/`
2. 完成 `04-实战项目/响应式新闻网站/`
3. 尝试构建 `04-实战项目/多端适配组件库/`

### 第四阶段：总结提升 (1天)
1. 阅读 `05-最佳实践/` 下的所有文档
2. 总结学习心得
3. 准备考核展示

## ✅ 学习检查清单

- [ ] 理解物理像素、逻辑像素、DPR的概念和关系
- [ ] 掌握Rem单位的计算和使用方法
- [ ] 能够正确配置viewport元标签
- [ ] 会使用JavaScript动态设置根字体大小
- [ ] 能够实现基本的移动端页面适配
- [ ] 掌握响应式设计的核心技巧
- [ ] 了解各种适配方案的优缺点
- [ ] 能够解决常见的适配问题
- [ ] 完成至少一个完整的实战项目

## 📝 考核要点

1. **理论知识**: 能够清晰解释移动端适配的核心概念
2. **实践能力**: 独立完成一个PC+移动端适配的页面
3. **方案选择**: 能够根据项目需求选择合适的适配方案
4. **问题解决**: 能够分析和解决常见的适配问题
5. **代码质量**: 代码结构清晰，注释完整，符合最佳实践

## 🔗 推荐资源

- [MDN - Viewport meta tag](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Viewport_meta_tag)
- [CSS单位详解](https://developer.mozilla.org/zh-CN/docs/Learn/CSS/Building_blocks/Values_and_units)
- [移动端适配方案](https://github.com/amfe/article/issues/17)

---

**开始学习时间**: ___________  
**预计完成时间**: ___________  
**实际完成时间**: ___________  
**学习评分**: ___/100
