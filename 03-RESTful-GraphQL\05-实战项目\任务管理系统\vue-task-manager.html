<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 任务管理系统 - RESTful vs GraphQL</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .api-toggle {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 10px;
        }

        .toggle-btn {
            padding: 12px 24px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 600;
        }

        .toggle-btn.active {
            background: #667eea;
            color: white;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .main-content {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-group {
            margin: 15px 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .task-list {
            margin-top: 20px;
        }

        .task-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s;
            position: relative;
        }

        .task-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .task-item.completed {
            opacity: 0.7;
            background: #e8f5e8;
        }

        .task-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .task-title.completed {
            text-decoration: line-through;
        }

        .task-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
        }

        .task-priority {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-high {
            background: #fee;
            color: #e74c3c;
        }

        .priority-medium {
            background: #fff3cd;
            color: #f39c12;
        }

        .priority-low {
            background: #e8f5e8;
            color: #27ae60;
        }

        .task-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-todo {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-progress {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-completed {
            background: #e8f5e8;
            color: #388e3c;
        }

        .task-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .filters {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
        }

        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
            margin: 15px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }

        .api-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .api-info h3 {
            margin-bottom: 10px;
            color: #1976d2;
        }

        .api-info code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 10px;
            }

            .filters {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 头部 -->
            <div class="header">
                <h1>📋 Vue.js 任务管理系统</h1>
                <p>对比RESTful API和GraphQL在实际项目中的应用</p>
            </div>

            <!-- API切换 -->
            <div class="api-toggle">
                <button
                    class="toggle-btn"
                    :class="{ active: apiType === 'rest' }"
                    @click="switchApi('rest')"
                >
                    RESTful API
                </button>
                <button
                    class="toggle-btn"
                    :class="{ active: apiType === 'graphql' }"
                    @click="switchApi('graphql')"
                >
                    GraphQL
                </button>
            </div>

            <!-- API信息 -->
            <div class="api-info">
                <h3>当前使用: {{ apiType === 'rest' ? 'RESTful API' : 'GraphQL' }}</h3>
                <div v-if="apiType === 'rest'">
                    <p><strong>特点:</strong> 多个端点，标准HTTP方法，简单直观</p>
                    <p><strong>示例:</strong> <code>GET /api/tasks</code>, <code>POST /api/tasks</code>, <code>PUT /api/tasks/:id</code></p>
                </div>
                <div v-else>
                    <p><strong>特点:</strong> 单一端点，按需获取数据，强类型系统</p>
                    <p><strong>示例:</strong> <code>query { tasks { id title status } }</code></p>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{{ totalTasks }}</div>
                    <div class="stat-label">总任务数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ completedTasks }}</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ inProgressTasks }}</div>
                    <div class="stat-label">进行中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ todoTasks }}</div>
                    <div class="stat-label">待办</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ apiRequests }}</div>
                    <div class="stat-label">API请求</div>
                </div>
            </div>

            <!-- 错误和成功消息 -->
            <div v-if="error" class="error">
                {{ error }}
                <button @click="error = ''" style="float: right; background: none; border: none; color: inherit; cursor: pointer;">✕</button>
            </div>

            <div v-if="success" class="success">
                {{ success }}
                <button @click="success = ''" style="float: right; background: none; border: none; color: inherit; cursor: pointer;">✕</button>
            </div>

            <!-- 主要布局 -->
            <div class="main-layout">
                <!-- 侧边栏 - 任务表单 -->
                <div class="sidebar">
                    <h2>{{ editingTask ? '编辑任务' : '创建新任务' }}</h2>

                    <form @submit.prevent="editingTask ? updateTask() : createTask()">
                        <div class="form-group">
                            <label for="title">任务标题:</label>
                            <input
                                type="text"
                                id="title"
                                v-model="form.title"
                                required
                                placeholder="请输入任务标题"
                            >
                        </div>

                        <div class="form-group">
                            <label for="description">任务描述:</label>
                            <textarea
                                id="description"
                                v-model="form.description"
                                rows="3"
                                placeholder="请输入任务描述（可选）"
                            ></textarea>
                        </div>

                        <div class="form-group">
                            <label for="priority">优先级:</label>
                            <select id="priority" v-model="form.priority" required>
                                <option value="">请选择优先级</option>
                                <option value="high">高</option>
                                <option value="medium">中</option>
                                <option value="low">低</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="dueDate">截止日期:</label>
                            <input
                                type="date"
                                id="dueDate"
                                v-model="form.dueDate"
                            >
                        </div>

                        <button type="submit" class="btn" :disabled="loading">
                            {{ loading ? '处理中...' : (editingTask ? '更新任务' : '创建任务') }}
                        </button>

                        <button
                            v-if="editingTask"
                            type="button"
                            class="btn btn-danger"
                            @click="cancelEdit()"
                        >
                            取消编辑
                        </button>
                    </form>
                </div>

                <!-- 主内容区 - 任务列表 -->
                <div class="main-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>任务列表</h2>
                        <button class="btn" @click="refreshTasks" :disabled="loading">
                            {{ loading ? '刷新中...' : '🔄 刷新' }}
                        </button>
                    </div>

                    <!-- 过滤器 -->
                    <div class="filters">
                        <button
                            class="filter-btn"
                            :class="{ active: statusFilter === '' }"
                            @click="statusFilter = ''"
                        >
                            全部
                        </button>
                        <button
                            class="filter-btn"
                            :class="{ active: statusFilter === 'todo' }"
                            @click="statusFilter = 'todo'"
                        >
                            待办
                        </button>
                        <button
                            class="filter-btn"
                            :class="{ active: statusFilter === 'progress' }"
                            @click="statusFilter = 'progress'"
                        >
                            进行中
                        </button>
                        <button
                            class="filter-btn"
                            :class="{ active: statusFilter === 'completed' }"
                            @click="statusFilter = 'completed'"
                        >
                            已完成
                        </button>
                        <button
                            class="filter-btn"
                            :class="{ active: priorityFilter === 'high' }"
                            @click="priorityFilter = priorityFilter === 'high' ? '' : 'high'"
                        >
                            高优先级
                        </button>
                    </div>

                    <!-- 加载状态 -->
                    <div v-if="loading" class="loading">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>

                    <!-- 任务列表 -->
                    <div v-else-if="filteredTasks.length > 0" class="task-list">
                        <div
                            v-for="task in filteredTasks"
                            :key="task.id"
                            class="task-item"
                            :class="{ completed: task.status === 'completed' }"
                        >
                            <div class="task-title" :class="{ completed: task.status === 'completed' }">
                                {{ task.title }}
                            </div>

                            <div v-if="task.description" class="task-description">
                                {{ task.description }}
                            </div>

                            <div class="task-meta">
                                <div>
                                    <span class="task-priority" :class="`priority-${task.priority}`">
                                        {{ getPriorityLabel(task.priority) }}
                                    </span>
                                    <span class="task-status" :class="`status-${task.status}`">
                                        {{ getStatusLabel(task.status) }}
                                    </span>
                                </div>
                                <div>
                                    <span v-if="task.dueDate">截止: {{ formatDate(task.dueDate) }}</span>
                                    <span>创建: {{ formatDate(task.createdAt) }}</span>
                                </div>
                            </div>

                            <div class="task-actions">
                                <button
                                    v-if="task.status !== 'completed'"
                                    class="btn btn-success"
                                    @click="markCompleted(task.id)"
                                    :disabled="loading"
                                >
                                    ✓ 完成
                                </button>
                                <button
                                    v-if="task.status === 'todo'"
                                    class="btn"
                                    @click="markInProgress(task.id)"
                                    :disabled="loading"
                                >
                                    ▶ 开始
                                </button>
                                <button
                                    class="btn"
                                    @click="editTask(task)"
                                >
                                    ✏ 编辑
                                </button>
                                <button
                                    class="btn btn-danger"
                                    @click="deleteTask(task.id)"
                                    :disabled="loading"
                                >
                                    🗑 删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div v-else style="text-align: center; padding: 40px; color: #666;">
                        <p>{{ statusFilter || priorityFilter ? '没有找到匹配的任务' : '暂无任务，创建第一个任务吧！' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed, onMounted, reactive } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const apiType = ref('rest'); // 'rest' 或 'graphql'
                const tasks = ref([]);
                const form = reactive({
                    title: '',
                    description: '',
                    priority: '',
                    dueDate: ''
                });
                const editingTask = ref(null);
                const loading = ref(false);
                const error = ref('');
                const success = ref('');
                const statusFilter = ref('');
                const priorityFilter = ref('');
                const apiRequests = ref(0);

                // 模拟任务数据
                const mockTasks = [
                    {
                        id: 1,
                        title: '学习Vue.js基础',
                        description: '掌握Vue.js的基本概念和语法',
                        priority: 'high',
                        status: 'completed',
                        dueDate: '2023-10-25',
                        createdAt: '2023-10-20T08:00:00Z'
                    },
                    {
                        id: 2,
                        title: '实现RESTful API',
                        description: '设计和实现用户管理的RESTful API接口',
                        priority: 'medium',
                        status: 'progress',
                        dueDate: '2023-10-28',
                        createdAt: '2023-10-21T09:15:00Z'
                    },
                    {
                        id: 3,
                        title: '学习GraphQL',
                        description: '了解GraphQL的查询语法和实际应用',
                        priority: 'high',
                        status: 'todo',
                        dueDate: '2023-10-30',
                        createdAt: '2023-10-22T10:30:00Z'
                    }
                ];

                // 计算属性
                const totalTasks = computed(() => tasks.value.length);
                const completedTasks = computed(() => tasks.value.filter(t => t.status === 'completed').length);
                const inProgressTasks = computed(() => tasks.value.filter(t => t.status === 'progress').length);
                const todoTasks = computed(() => tasks.value.filter(t => t.status === 'todo').length);

                const filteredTasks = computed(() => {
                    let filtered = tasks.value;

                    if (statusFilter.value) {
                        filtered = filtered.filter(task => task.status === statusFilter.value);
                    }

                    if (priorityFilter.value) {
                        filtered = filtered.filter(task => task.priority === priorityFilter.value);
                    }

                    return filtered.sort((a, b) => {
                        // 按优先级和创建时间排序
                        const priorityOrder = { high: 3, medium: 2, low: 1 };
                        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                            return priorityOrder[b.priority] - priorityOrder[a.priority];
                        }
                        return new Date(b.createdAt) - new Date(a.createdAt);
                    });
                });

                // API模拟函数
                const simulateApiCall = async (operation, data = null) => {
                    loading.value = true;
                    error.value = '';
                    apiRequests.value++;

                    try {
                        // 模拟网络延迟
                        await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 700));

                        if (apiType.value === 'rest') {
                            return await simulateRestApi(operation, data);
                        } else {
                            return await simulateGraphQLApi(operation, data);
                        }
                    } catch (err) {
                        throw err;
                    } finally {
                        loading.value = false;
                    }
                };

                // RESTful API模拟
                const simulateRestApi = async (operation, data) => {
                    console.log(`🔄 RESTful API: ${operation}`, data);

                    switch (operation) {
                        case 'GET_TASKS':
                            return { data: [...mockTasks] };

                        case 'CREATE_TASK':
                            const newTask = {
                                id: Date.now(),
                                ...data,
                                status: 'todo',
                                createdAt: new Date().toISOString()
                            };
                            mockTasks.push(newTask);
                            return { data: newTask };

                        case 'UPDATE_TASK':
                            const index = mockTasks.findIndex(t => t.id === data.id);
                            if (index !== -1) {
                                mockTasks[index] = { ...mockTasks[index], ...data };
                                return { data: mockTasks[index] };
                            }
                            throw new Error('任务不存在');

                        case 'DELETE_TASK':
                            const deleteIndex = mockTasks.findIndex(t => t.id === data);
                            if (deleteIndex !== -1) {
                                mockTasks.splice(deleteIndex, 1);
                                return { data: { success: true } };
                            }
                            throw new Error('任务不存在');

                        default:
                            throw new Error('不支持的操作');
                    }
                };

                // GraphQL API模拟
                const simulateGraphQLApi = async (operation, data) => {
                    console.log(`🔄 GraphQL: ${operation}`, data);

                    switch (operation) {
                        case 'GET_TASKS':
                            return {
                                data: {
                                    tasks: [...mockTasks]
                                }
                            };

                        case 'CREATE_TASK':
                            const newTask = {
                                id: Date.now(),
                                ...data,
                                status: 'todo',
                                createdAt: new Date().toISOString()
                            };
                            mockTasks.push(newTask);
                            return {
                                data: {
                                    createTask: newTask
                                }
                            };

                        case 'UPDATE_TASK':
                            const index = mockTasks.findIndex(t => t.id === data.id);
                            if (index !== -1) {
                                mockTasks[index] = { ...mockTasks[index], ...data };
                                return {
                                    data: {
                                        updateTask: mockTasks[index]
                                    }
                                };
                            }
                            throw new Error('任务不存在');

                        case 'DELETE_TASK':
                            const deleteIndex = mockTasks.findIndex(t => t.id === data);
                            if (deleteIndex !== -1) {
                                mockTasks.splice(deleteIndex, 1);
                                return {
                                    data: {
                                        deleteTask: true
                                    }
                                };
                            }
                            throw new Error('任务不存在');

                        default:
                            throw new Error('不支持的操作');
                    }
                };

                // 方法
                const switchApi = (type) => {
                    apiType.value = type;
                    showSuccess(`已切换到 ${type === 'rest' ? 'RESTful API' : 'GraphQL'}`);
                    refreshTasks();
                };

                const showSuccess = (message) => {
                    success.value = message;
                    setTimeout(() => {
                        success.value = '';
                    }, 3000);
                };

                const fetchTasks = async () => {
                    try {
                        const response = await simulateApiCall('GET_TASKS');
                        tasks.value = apiType.value === 'rest' ? response.data : response.data.tasks;
                    } catch (err) {
                        error.value = '获取任务列表失败: ' + err.message;
                    }
                };

                const createTask = async () => {
                    try {
                        const response = await simulateApiCall('CREATE_TASK', form);
                        const newTask = apiType.value === 'rest' ? response.data : response.data.createTask;
                        tasks.value.push(newTask);
                        resetForm();
                        showSuccess('任务创建成功！');
                    } catch (err) {
                        error.value = '创建任务失败: ' + err.message;
                    }
                };

                const updateTask = async () => {
                    try {
                        const taskData = { ...form, id: editingTask.value.id };
                        const response = await simulateApiCall('UPDATE_TASK', taskData);
                        const updatedTask = apiType.value === 'rest' ? response.data : response.data.updateTask;

                        const index = tasks.value.findIndex(t => t.id === editingTask.value.id);
                        if (index !== -1) {
                            tasks.value[index] = updatedTask;
                        }

                        resetForm();
                        showSuccess('任务更新成功！');
                    } catch (err) {
                        error.value = '更新任务失败: ' + err.message;
                    }
                };

                const deleteTask = async (id) => {
                    if (!confirm('确定要删除这个任务吗？')) return;

                    try {
                        await simulateApiCall('DELETE_TASK', id);
                        tasks.value = tasks.value.filter(t => t.id !== id);
                        showSuccess('任务删除成功！');
                    } catch (err) {
                        error.value = '删除任务失败: ' + err.message;
                    }
                };

                const markCompleted = async (id) => {
                    const task = tasks.value.find(t => t.id === id);
                    if (task) {
                        try {
                            const taskData = { ...task, status: 'completed' };
                            await simulateApiCall('UPDATE_TASK', taskData);
                            task.status = 'completed';
                            showSuccess('任务已标记为完成！');
                        } catch (err) {
                            error.value = '更新任务状态失败: ' + err.message;
                        }
                    }
                };

                const markInProgress = async (id) => {
                    const task = tasks.value.find(t => t.id === id);
                    if (task) {
                        try {
                            const taskData = { ...task, status: 'progress' };
                            await simulateApiCall('UPDATE_TASK', taskData);
                            task.status = 'progress';
                            showSuccess('任务已开始进行！');
                        } catch (err) {
                            error.value = '更新任务状态失败: ' + err.message;
                        }
                    }
                };

                const editTask = (task) => {
                    editingTask.value = task;
                    Object.assign(form, {
                        title: task.title,
                        description: task.description || '',
                        priority: task.priority,
                        dueDate: task.dueDate || ''
                    });
                };

                const cancelEdit = () => {
                    editingTask.value = null;
                    resetForm();
                };

                const resetForm = () => {
                    Object.assign(form, {
                        title: '',
                        description: '',
                        priority: '',
                        dueDate: ''
                    });
                    editingTask.value = null;
                };

                const refreshTasks = () => {
                    fetchTasks();
                };

                const getPriorityLabel = (priority) => {
                    const labels = { high: '高', medium: '中', low: '低' };
                    return labels[priority] || priority;
                };

                const getStatusLabel = (status) => {
                    const labels = {
                        todo: '待办',
                        progress: '进行中',
                        completed: '已完成'
                    };
                    return labels[status] || status;
                };

                const formatDate = (dateString) => {
                    return new Date(dateString).toLocaleDateString('zh-CN');
                };

                // 生命周期
                onMounted(() => {
                    fetchTasks();
                    console.log('📋 Vue.js 任务管理系统已加载');
                    console.log('💡 功能包括：');
                    console.log('- RESTful API vs GraphQL 对比');
                    console.log('- 完整的CRUD操作');
                    console.log('- 任务状态管理');
                    console.log('- 过滤和搜索');
                    console.log('- 实时统计');
                });

                return {
                    apiType,
                    tasks,
                    form,
                    editingTask,
                    loading,
                    error,
                    success,
                    statusFilter,
                    priorityFilter,
                    apiRequests,
                    totalTasks,
                    completedTasks,
                    inProgressTasks,
                    todoTasks,
                    filteredTasks,
                    switchApi,
                    createTask,
                    updateTask,
                    deleteTask,
                    markCompleted,
                    markInProgress,
                    editTask,
                    cancelEdit,
                    resetForm,
                    refreshTasks,
                    getPriorityLabel,
                    getStatusLabel,
                    formatDate
                };
            }
        }).mount('#app');
    </script>
</body>
</html>