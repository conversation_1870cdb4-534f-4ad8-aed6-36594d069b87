# Git 历史查看详解

## 📚 git log - 查看提交历史

### 🔍 基本用法
```bash
# 查看完整提交历史
git log

# 简洁单行显示
git log --oneline

# 显示最近 n 次提交
git log -n 5
git log -5

# 显示指定时间范围的提交
git log --since="2023-01-01"
git log --until="2023-12-31"
git log --since="2 weeks ago"
```

### 🎨 格式化输出
```bash
# 图形化显示分支结构
git log --graph --oneline --all

# 自定义格式
git log --pretty=format:"%h - %an, %ar : %s"
git log --pretty=format:"%C(yellow)%h%C(reset) - %C(green)%an%C(reset), %C(blue)%ar%C(reset) : %s"

# 显示统计信息
git log --stat

# 显示详细差异
git log -p

# 显示简短统计
git log --shortstat
```

### 🔍 过滤和搜索
```bash
# 按作者过滤
git log --author="John Doe"
git log --author="john"  # 部分匹配

# 按提交信息搜索
git log --grep="fix"
git log --grep="bug" --grep="feature" --all-match  # 同时包含

# 按文件过滤
git log -- filename.txt
git log --follow -- filename.txt  # 跟踪重命名

# 按目录过滤
git log -- src/

# 搜索代码变更
git log -S "function_name"  # 搜索添加或删除的代码
git log -G "regex_pattern"  # 正则表达式搜索
```

### 📊 高级选项
```bash
# 显示合并提交
git log --merges

# 不显示合并提交
git log --no-merges

# 显示第一父提交
git log --first-parent

# 显示所有分支
git log --all

# 反向显示（最老的在前）
git log --reverse

# 显示引用信息
git log --decorate
```

## 🔍 git show - 查看提交详情

### 📋 基本用法
```bash
# 查看最新提交
git show

# 查看指定提交
git show commit-hash
git show HEAD~1
git show v1.0.0

# 查看指定文件在某次提交的内容
git show commit-hash:path/to/file.txt

# 查看分支指向的提交
git show branch-name
```

### 🎯 显示选项
```bash
# 只显示统计信息
git show --stat

# 只显示文件名
git show --name-only

# 显示文件名和状态
git show --name-status

# 不显示差异内容
git show --no-patch

# 显示格式化信息
git show --pretty=format:"%h %s" --no-patch
```

## 📊 git diff - 查看差异

### 🔄 基本差异比较
```bash
# 工作目录 vs 暂存区
git diff

# 暂存区 vs 最后提交
git diff --staged
git diff --cached

# 工作目录 vs 最后提交
git diff HEAD

# 指定文件的差异
git diff filename.txt
git diff --staged filename.txt
```

### 🔍 提交间差异
```bash
# 两个提交之间的差异
git diff commit1 commit2

# 与上一个提交的差异
git diff HEAD~1

# 与指定提交的差异
git diff abc123

# 分支间差异
git diff main feature-branch

# 查看分支分叉点后的差异
git diff main...feature-branch
```

### 🎨 差异显示选项
```bash
# 只显示文件名
git diff --name-only

# 显示文件名和状态
git diff --name-status

# 显示统计信息
git diff --stat

# 单词级别差异
git diff --word-diff

# 字符级别差异
git diff --word-diff=color

# 忽略空白字符
git diff -w
git diff --ignore-all-space

# 忽略行尾空白
git diff --ignore-space-at-eol
```

## 🕵️ git blame - 查看文件注释

### 📝 基本用法
```bash
# 查看文件每行的最后修改信息
git blame filename.txt

# 显示指定行范围
git blame -L 10,20 filename.txt

# 显示邮箱而不是用户名
git blame -e filename.txt

# 显示原始提交（忽略空白修改）
git blame -w filename.txt
```

### 🔍 高级选项
```bash
# 显示更多上下文
git blame -C filename.txt

# 跟踪文件移动
git blame -M filename.txt

# 显示提交哈希的完整形式
git blame --show-name filename.txt

# 显示行号
git blame -n filename.txt

# 从指定提交开始查看
git blame commit-hash -- filename.txt
```

## 🔍 git grep - 在仓库中搜索

### 🔎 基本搜索
```bash
# 在工作目录中搜索
git grep "search-term"

# 在指定提交中搜索
git grep "search-term" HEAD~1

# 在所有分支中搜索
git grep "search-term" --all

# 搜索多个模式
git grep -e "pattern1" -e "pattern2"
```

### 🎯 搜索选项
```bash
# 显示行号
git grep -n "search-term"

# 显示匹配的行数
git grep -c "search-term"

# 只显示文件名
git grep -l "search-term"

# 不显示文件名
git grep -h "search-term"

# 忽略大小写
git grep -i "search-term"

# 使用正则表达式
git grep -E "regex-pattern"
```

## 📈 git shortlog - 提交统计

### 📊 基本统计
```bash
# 按作者统计提交数
git shortlog -sn

# 显示详细提交信息
git shortlog

# 按邮箱统计
git shortlog -se

# 指定时间范围
git shortlog --since="2023-01-01"
```

### 🎯 统计选项
```bash
# 只统计数量
git shortlog -s

# 按数量排序
git shortlog -n

# 显示邮箱
git shortlog -e

# 按作者分组
git shortlog --group=author
```

## 🔄 git reflog - 引用日志

### 📝 基本用法
```bash
# 查看引用日志
git reflog

# 查看指定分支的引用日志
git reflog branch-name

# 查看指定数量的记录
git reflog -10

# 查看指定时间范围
git reflog --since="1 hour ago"
```

### 🔍 引用日志选项
```bash
# 显示详细信息
git reflog --pretty=fuller

# 显示所有引用的日志
git reflog --all

# 查看特定引用
git reflog show HEAD@{2.hours.ago}

# 清理引用日志
git reflog expire --expire=now --all
```

## 🎯 实用查看技巧

### 📊 项目统计
```bash
# 查看代码行数统计
git log --author="Author Name" --pretty=tformat: --numstat | \
awk '{ add += $1; subs += $2; loc += $1 - $2 } END { printf "added lines: %s, removed lines: %s, total lines: %s\n", add, subs, loc }'

# 查看文件修改频率
git log --name-only --pretty=format: | sort | uniq -c | sort -rg

# 查看最活跃的文件
git log --pretty=format: --name-only | sort | uniq -c | sort -rg | head -10

# 查看提交活跃度
git log --format='%ad' --date=short | sort | uniq -c | sort -rg
```

### 🔍 问题排查
```bash
# 查找引入 bug 的提交
git log --grep="bug" --oneline

# 查找特定功能的实现
git log -S "function_name" --oneline

# 查看文件的完整变更历史
git log -p --follow filename.txt

# 查找删除的代码
git log -S "deleted_code" --all --source --
```

### 📈 可视化历史
```bash
# 美化的分支图
git log --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit --all

# 简洁的分支图
git log --oneline --graph --all

# 查看分支合并历史
git log --graph --oneline --merges

# 查看标签历史
git log --oneline --decorate --tags --no-walk
```

## 💡 最佳实践

### ✅ 查看历史的建议
1. **使用别名** - 为常用的复杂命令设置别名
2. **组合使用** - 结合多个选项获得最佳效果
3. **理解输出** - 学会解读 Git 的输出信息
4. **保存有用的命令** - 将复杂的查询命令保存为脚本

### 🔧 推荐别名
```bash
# 设置有用的别名
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"
git config --global alias.lga "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit --all"
git config --global alias.ls "log --pretty=format:'%C(yellow)%h %C(blue)%ad%C(red)%d %C(reset)%s%C(green) [%cn]' --decorate --date=short"
git config --global alias.ll "log --pretty=format:'%C(yellow)%h%C(red)%d %C(reset)%s%C(green) [%cn]' --decorate --numstat"
```

---

**记住**: 熟练掌握历史查看命令将帮助你更好地理解项目的演进过程，快速定位问题和了解代码变更！ 🔍
