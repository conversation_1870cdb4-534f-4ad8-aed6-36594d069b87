# Git 团队协作最佳实践

## 📋 概述

本文档总结了 Git 团队协作中的最佳实践，包括提交规范、分支命名、工作流程等方面的建议，帮助团队建立高效的协作模式。

## 📝 提交信息规范

### 1. 提交信息格式

**标准格式**:
```
<type>(<scope>): <subject>

<body>

<footer>
```

**示例**:
```
feat(auth): 添加用户登录功能

实现了基于 JWT 的用户认证系统，包括：
- 登录接口开发
- Token 验证中间件
- 用户状态管理

Closes #123
Breaking Change: 修改了用户 API 的响应格式
```

### 2. 提交类型 (Type)

**主要类型**:
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整（不影响功能）
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**详细说明**:
```bash
# 新功能
git commit -m "feat(user): 添加用户注册功能"

# Bug 修复
git commit -m "fix(login): 修复登录页面验证码显示问题"

# 文档更新
git commit -m "docs(readme): 更新安装说明"

# 代码重构
git commit -m "refactor(utils): 重构日期处理工具函数"

# 测试
git commit -m "test(auth): 添加用户认证单元测试"

# 构建相关
git commit -m "chore(deps): 升级 React 到 18.0"
```

### 3. 作用域 (Scope)

**常见作用域**:
```
auth        # 认证相关
user        # 用户功能
api         # API 接口
ui          # 用户界面
db          # 数据库
config      # 配置文件
build       # 构建系统
ci          # 持续集成
```

### 4. 提交信息最佳实践

**好的提交信息**:
```bash
✅ feat(cart): 实现购物车商品数量修改功能
✅ fix(payment): 修复支付页面金额计算错误
✅ docs(api): 更新用户 API 文档
✅ refactor(components): 提取公共按钮组件
```

**避免的提交信息**:
```bash
❌ 修改了一些文件
❌ bug fix
❌ 更新
❌ wip
❌ 临时提交
```

## 🌿 分支命名规范

### 1. 分支类型和命名

**功能分支**:
```bash
feature/JIRA-123-user-login
feature/add-shopping-cart
feature/user-profile-page
```

**修复分支**:
```bash
bugfix/JIRA-456-login-error
bugfix/fix-payment-calculation
hotfix/critical-security-patch
```

**发布分支**:
```bash
release/v1.2.0
release/2023-12-sprint
```

**实验分支**:
```bash
experiment/new-ui-framework
experiment/performance-optimization
```

### 2. 分支命名原则

**命名规则**:
- 使用小写字母和连字符
- 包含 Issue/Ticket 编号（如果有）
- 描述性强，一目了然
- 避免使用特殊字符

**示例对比**:
```bash
✅ feature/PROJ-123-user-authentication
✅ bugfix/fix-header-responsive-issue
✅ hotfix/security-vulnerability-patch

❌ feature/123
❌ fix_bug
❌ my-branch
❌ temp
```

## 🔄 工作流程最佳实践

### 1. 日常开发流程

**标准工作流程**:
```bash
# 1. 同步主分支
git checkout main
git pull origin main

# 2. 创建功能分支
git checkout -b feature/PROJ-123-user-dashboard

# 3. 开发过程中定期提交
git add .
git commit -m "feat(dashboard): 实现用户信息卡片组件"

# 4. 定期推送和同步
git push origin feature/PROJ-123-user-dashboard
git checkout main
git pull origin main
git checkout feature/PROJ-123-user-dashboard
git rebase main

# 5. 完成开发后创建 PR
# 在 GitHub/GitLab 上创建 Pull Request

# 6. 合并后清理分支
git checkout main
git pull origin main
git branch -d feature/PROJ-123-user-dashboard
git push origin --delete feature/PROJ-123-user-dashboard
```

### 2. 提交频率和粒度

**提交原则**:
- **小步快跑**: 频繁提交，每次提交一个逻辑单元
- **原子性**: 每次提交都是完整的功能点
- **可回滚**: 每次提交都应该是可以安全回滚的

**提交粒度示例**:
```bash
# 好的提交粒度
git commit -m "feat(auth): 添加登录表单组件"
git commit -m "feat(auth): 实现表单验证逻辑"
git commit -m "feat(auth): 集成登录 API 调用"
git commit -m "test(auth): 添加登录组件单元测试"

# 避免的提交方式
git commit -m "完成整个用户认证模块"  # 太大
git commit -m "修改了一个字符"        # 太小
```

## 📁 项目结构规范

### 1. Git 配置文件

**.gitignore 模板**:
```gitignore
# 依赖文件
node_modules/
vendor/
*.egg-info/

# 构建产物
dist/
build/
*.min.js
*.min.css

# 环境配置
.env
.env.local
.env.production

# IDE 文件
.vscode/
.idea/
*.swp
*.swo

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 测试覆盖率
coverage/
.nyc_output/
```

**.gitattributes 配置**:
```gitattributes
# 文本文件统一使用 LF 换行符
* text=auto eol=lf

# 二进制文件
*.png binary
*.jpg binary
*.gif binary
*.ico binary
*.pdf binary

# 语言特定配置
*.js text eol=lf
*.css text eol=lf
*.html text eol=lf
*.md text eol=lf
*.json text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
```

### 2. 项目文档结构

**标准文档结构**:
```
docs/
├── README.md              # 项目概述
├── CONTRIBUTING.md        # 贡献指南
├── CHANGELOG.md           # 变更日志
├── CODE_OF_CONDUCT.md     # 行为准则
├── SECURITY.md            # 安全政策
├── api/                   # API 文档
├── guides/                # 使用指南
└── development/           # 开发文档
    ├── setup.md           # 环境搭建
    ├── coding-standards.md # 编码规范
    └── deployment.md      # 部署指南
```

## 🤝 团队协作规范

### 1. 代码审查规范

**审查要求**:
- 所有代码必须经过至少一人审查
- 核心模块需要两人以上审查
- 审查者应在 24 小时内响应
- 修复建议后需要重新审查

**审查检查清单**:
```markdown
## 代码审查检查清单

### 功能性
- [ ] 功能实现符合需求
- [ ] 边界条件处理完善
- [ ] 错误处理充分

### 代码质量
- [ ] 代码结构清晰
- [ ] 命名有意义
- [ ] 无重复代码
- [ ] 遵循 SOLID 原则

### 测试
- [ ] 单元测试覆盖充分
- [ ] 集成测试通过
- [ ] 手动测试完成

### 文档
- [ ] 代码注释充分
- [ ] API 文档更新
- [ ] 用户文档更新
```

### 2. 冲突解决规范

**冲突预防**:
```bash
# 定期同步主分支
git checkout main
git pull origin main
git checkout feature-branch
git rebase main

# 小步提交，减少冲突范围
git add -p  # 部分提交
git commit -m "feat: 实现基础功能"
```

**冲突解决流程**:
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 查看冲突文件
git status

# 3. 解决冲突
# 编辑冲突文件，选择正确的代码

# 4. 标记解决
git add resolved-file.js

# 5. 完成合并
git commit -m "resolve: 解决与主分支的合并冲突"

# 6. 推送结果
git push origin feature-branch
```

## 🔧 工具和自动化

### 1. Git Hooks 配置

**pre-commit hook**:
```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "运行 pre-commit 检查..."

# 代码格式检查
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ 代码格式检查失败"
  exit 1
fi

# 运行测试
npm test
if [ $? -ne 0 ]; then
  echo "❌ 测试失败"
  exit 1
fi

echo "✅ pre-commit 检查通过"
```

**commit-msg hook**:
```bash
#!/bin/sh
# .git/hooks/commit-msg

# 检查提交信息格式
commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ 提交信息格式不正确"
    echo "格式: type(scope): description"
    echo "示例: feat(auth): 添加用户登录功能"
    exit 1
fi

echo "✅ 提交信息格式正确"
```

### 2. CI/CD 集成

**GitHub Actions 配置**:
```yaml
# .github/workflows/ci.yml
name: CI
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linter
        run: npm run lint
        
      - name: Run tests
        run: npm test
        
      - name: Build project
        run: npm run build
```

## 📊 团队效率指标

### 1. 关键指标

**开发效率**:
- 平均功能开发周期
- 代码审查时间
- 分支合并频率
- 冲突解决时间

**代码质量**:
- 测试覆盖率
- 代码重复率
- 技术债务指标
- 生产环境 Bug 率

### 2. 持续改进

**定期回顾**:
```markdown
## 团队 Git 实践回顾

### 📈 本月数据
- 总提交数: 234
- 平均 PR 大小: 156 行
- 代码审查时间: 平均 6 小时
- 冲突解决: 12 次

### 🎯 改进点
1. 减少大型 PR 数量
2. 提高提交信息质量
3. 加强自动化检查

### 📝 行动计划
- 制定 PR 大小指导原则
- 组织提交规范培训
- 引入更多自动化工具
```

---

通过遵循这些最佳实践，团队可以建立高效、规范的 Git 协作流程，提高开发效率和代码质量。
