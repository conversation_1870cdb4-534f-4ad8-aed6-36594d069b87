// 枚举类型
export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  DEVELOPER = 'DEVELOPER',
  DESIGNER = 'DESIGNER',
  TESTER = 'TESTER'
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

export enum ProjectStatus {
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  ON_HOLD = 'ON_HOLD',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  IN_REVIEW = 'IN_REVIEW',
  TESTING = 'TESTING',
  DONE = 'DONE',
  CANCELLED = 'CANCELLED'
}

export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 基础类型
export interface User {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  avatar?: string
  role: UserRole
  status: UserStatus
  createdAt: string
  updatedAt: string
  tasks?: Task[]
  assignedTasks?: Task[]
  projects?: Project[]
  comments?: Comment[]
}

export interface Project {
  id: string
  name: string
  description?: string
  status: ProjectStatus
  priority: Priority
  startDate?: string
  endDate?: string
  createdAt: string
  updatedAt: string
  owner: User
  members: User[]
  tasks: Task[]
}

export interface Task {
  id: string
  title: string
  description?: string
  status: TaskStatus
  priority: Priority
  dueDate?: string
  estimatedHours?: number
  actualHours?: number
  createdAt: string
  updatedAt: string
  project: Project
  creator: User
  assignee?: User
  comments: Comment[]
  tags: string[]
}

export interface Comment {
  id: string
  content: string
  createdAt: string
  updatedAt: string
  author: User
  task: Task
}

// 输入类型
export interface CreateUserInput {
  username: string
  email: string
  password: string
  firstName: string
  lastName: string
  role?: UserRole
}

export interface UpdateUserInput {
  username?: string
  email?: string
  firstName?: string
  lastName?: string
  avatar?: string
  role?: UserRole
  status?: UserStatus
}

export interface CreateProjectInput {
  name: string
  description?: string
  priority?: Priority
  startDate?: string
  endDate?: string
  memberIds?: string[]
}

export interface UpdateProjectInput {
  name?: string
  description?: string
  status?: ProjectStatus
  priority?: Priority
  startDate?: string
  endDate?: string
  memberIds?: string[]
}

export interface CreateTaskInput {
  title: string
  description?: string
  projectId: string
  assigneeId?: string
  priority?: Priority
  dueDate?: string
  estimatedHours?: number
  tags?: string[]
}

export interface UpdateTaskInput {
  title?: string
  description?: string
  status?: TaskStatus
  priority?: Priority
  assigneeId?: string
  dueDate?: string
  estimatedHours?: number
  actualHours?: number
  tags?: string[]
}

export interface CreateCommentInput {
  content: string
  taskId: string
}

export interface LoginInput {
  email: string
  password: string
}

// 过滤器类型
export interface TaskFilter {
  status?: TaskStatus
  priority?: Priority
  assigneeId?: string
  projectId?: string
  search?: string
}

export interface ProjectFilter {
  status?: ProjectStatus
  priority?: Priority
  ownerId?: string
  search?: string
}

// 分页类型
export interface PaginationInput {
  page?: number
  limit?: number
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
  hasNext: boolean
  hasPrev: boolean
}

// 连接类型
export interface TaskConnection {
  tasks: Task[]
  pagination: PaginationInfo
}

export interface ProjectConnection {
  projects: Project[]
  pagination: PaginationInfo
}

export interface UserConnection {
  users: User[]
  pagination: PaginationInfo
}

// 统计类型
export interface TaskStats {
  total: number
  todo: number
  inProgress: number
  inReview: number
  testing: number
  done: number
  cancelled: number
}

export interface ProjectStats {
  total: number
  planning: number
  active: number
  onHold: number
  completed: number
  cancelled: number
}

export interface DashboardStats {
  taskStats: TaskStats
  projectStats: ProjectStats
  userCount: number
  recentTasks: Task[]
  recentProjects: Project[]
}

// 认证类型
export interface AuthPayload {
  token: string
  user: User
}

// 表单类型
export interface LoginForm {
  email: string
  password: string
  remember: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  role: UserRole
}

export interface TaskForm {
  title: string
  description: string
  projectId: string
  assigneeId: string
  priority: Priority
  dueDate: string
  estimatedHours: number
  tags: string[]
}

export interface ProjectForm {
  name: string
  description: string
  priority: Priority
  startDate: string
  endDate: string
  memberIds: string[]
}

// 路由类型
export interface RouteMenuItem {
  path: string
  name: string
  icon: string
  title: string
  roles?: UserRole[]
  children?: RouteMenuItem[]
}

// 通知类型
export interface Notification {
  id: string
  type: 'success' | 'warning' | 'info' | 'error'
  title: string
  message: string
  duration?: number
  timestamp: string
}

// API响应类型
export interface ApiResponse<T = any> {
  data?: T
  errors?: Array<{
    message: string
    locations?: Array<{
      line: number
      column: number
    }>
    path?: string[]
  }>
}

// 主题类型
export interface ThemeConfig {
  primaryColor: string
  isDark: boolean
  sidebarCollapsed: boolean
  language: 'zh-CN' | 'en-US'
}
