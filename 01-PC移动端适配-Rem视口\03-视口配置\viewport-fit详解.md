# viewport-fit 详解

## 1. 背景介绍

### 1.1 问题的由来
随着iPhone X的发布，苹果引入了"刘海屏"设计，这给网页适配带来了新的挑战：

```
传统屏幕：  ┌─────────────────┐
           │                 │
           │    网页内容      │
           │                 │
           └─────────────────┘

刘海屏：    ┌───┐         ┌───┐
           │   │  刘海区域  │   │
           ├───┼─────────┼───┤
           │               │
           │    网页内容    │
           │               │
           └─────────────────┘
           │    底部指示器    │
           └─────────────────┘
```

### 1.2 安全区域概念
- **安全区域**：不会被设备UI（刘海、底部指示器等）遮挡的屏幕区域
- **非安全区域**：可能被设备UI遮挡的区域

## 2. viewport-fit 属性详解

### 2.1 语法
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=VALUE">
```

### 2.2 三个取值

#### `viewport-fit=auto` (默认值)
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=auto">
```

**特点**：
- ✅ 内容自动限制在安全区域内
- ✅ 不会被刘海或底部指示器遮挡
- ❌ 无法充分利用屏幕空间
- ❌ 刘海区域显示为黑色或白色背景

**适用场景**：
- 普通网站
- 不需要特殊适配的页面
- 内容型应用

#### `viewport-fit=cover` 
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
```

**特点**：
- ✅ 内容延伸到整个屏幕
- ✅ 可以实现沉浸式体验
- ✅ 充分利用屏幕空间
- ❌ 内容可能被刘海遮挡
- ❌ 需要额外处理安全区域

**适用场景**：
- 游戏应用
- 视频播放器
- 需要全屏背景的页面
- 沉浸式应用

#### `viewport-fit=contain`
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=contain">
```

**特点**：
- ✅ 明确确保内容在安全区域内
- ✅ 与auto类似但语义更明确
- ❌ 同样无法充分利用屏幕空间

**适用场景**：
- 需要明确语义的项目
- 对安全性要求较高的应用

## 3. CSS 安全区域变量

### 3.1 env() 函数
iOS 11+ 和现代浏览器支持通过 `env()` 函数获取安全区域信息：

```css
:root {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
}
```

### 3.2 实际应用示例

#### 头部适配
```css
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    /* 顶部padding包含安全区域 */
    padding-top: calc(20px + env(safe-area-inset-top));
    /* 左右padding考虑安全区域 */
    padding-left: max(20px, env(safe-area-inset-left));
    padding-right: max(20px, env(safe-area-inset-right));
    background: #000;
    z-index: 100;
}
```

#### 底部导航适配
```css
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    /* 底部padding包含安全区域 */
    padding-bottom: calc(15px + env(safe-area-inset-bottom));
    padding-left: max(20px, env(safe-area-inset-left));
    padding-right: max(20px, env(safe-area-inset-right));
    background: #000;
}
```

#### 主内容区域适配
```css
.main-content {
    /* 顶部留出头部空间 + 安全区域 */
    padding-top: calc(60px + env(safe-area-inset-top));
    /* 底部留出导航空间 + 安全区域 */
    padding-bottom: calc(60px + env(safe-area-inset-bottom));
    /* 左右考虑安全区域 */
    padding-left: max(20px, env(safe-area-inset-left));
    padding-right: max(20px, env(safe-area-inset-right));
}
```

## 4. 兼容性处理

### 4.1 渐进增强
```css
/* 基础样式（不支持安全区域的设备） */
.header {
    padding: 20px;
}

/* 支持安全区域的设备 */
@supports (padding: env(safe-area-inset-top)) {
    .header {
        padding-top: calc(20px + env(safe-area-inset-top));
        padding-left: max(20px, env(safe-area-inset-left));
        padding-right: max(20px, env(safe-area-inset-right));
    }
}
```

### 4.2 JavaScript检测
```javascript
// 检测是否支持安全区域
function supportsSafeArea() {
    return CSS.supports('padding: env(safe-area-inset-top)');
}

// 获取安全区域值
function getSafeAreaInsets() {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    
    return {
        top: computedStyle.getPropertyValue('--safe-area-inset-top') || '0px',
        right: computedStyle.getPropertyValue('--safe-area-inset-right') || '0px',
        bottom: computedStyle.getPropertyValue('--safe-area-inset-bottom') || '0px',
        left: computedStyle.getPropertyValue('--safe-area-inset-left') || '0px'
    };
}
```

## 5. 最佳实践

### 5.1 选择合适的值
```html
<!-- 普通网站：使用默认值 -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<!-- 沉浸式应用：使用cover -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

<!-- 明确语义：使用contain -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=contain">
```

### 5.2 CSS设计模式
```css
/* 1. 定义CSS变量 */
:root {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
}

/* 2. 使用max()确保最小值 */
.container {
    padding-left: max(20px, var(--safe-area-inset-left));
    padding-right: max(20px, var(--safe-area-inset-right));
}

/* 3. 使用calc()组合值 */
.fixed-header {
    padding-top: calc(20px + var(--safe-area-inset-top));
}
```

### 5.3 测试建议
1. **真机测试**：在iPhone X及以上设备测试
2. **模拟器测试**：使用Safari开发者工具模拟
3. **横竖屏测试**：确保两种方向都正常
4. **不同机型测试**：测试不同的刘海屏设备

## 6. 常见问题

### 6.1 安全区域值为0
**原因**：
- 设备不支持安全区域
- 没有使用 `viewport-fit=cover`
- 浏览器不支持 `env()` 函数

**解决方案**：
```css
/* 提供降级方案 */
.header {
    padding-top: 20px; /* 降级值 */
    padding-top: calc(20px + env(safe-area-inset-top)); /* 现代浏览器 */
}
```

### 6.2 内容被遮挡
**原因**：使用了 `viewport-fit=cover` 但没有处理安全区域

**解决方案**：
```css
/* 确保重要内容在安全区域内 */
.important-content {
    margin-top: env(safe-area-inset-top);
    margin-bottom: env(safe-area-inset-bottom);
}
```

## 7. 总结

- **viewport-fit** 是为适配刘海屏等特殊设备而设计的属性
- **cover** 值可以实现沉浸式体验，但需要处理安全区域
- **env()** 函数提供了获取安全区域信息的标准方法
- **渐进增强** 是处理兼容性的最佳策略
- **真机测试** 是确保效果的唯一可靠方法
