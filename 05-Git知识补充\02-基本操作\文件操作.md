# Git 文件操作详解

## 📁 文件添加操作

### ➕ git add - 添加文件到暂存区

#### 基本用法
```bash
# 添加单个文件
git add filename.txt

# 添加多个文件
git add file1.txt file2.txt file3.txt

# 添加当前目录所有文件
git add .

# 添加所有文件（包括删除的文件）
git add -A
git add --all
```

#### 高级添加选项
```bash
# 交互式添加
git add -i

# 分块添加（部分添加文件内容）
git add -p filename.txt
git add --patch filename.txt

# 添加所有已跟踪文件的修改
git add -u
git add --update

# 强制添加被忽略的文件
git add -f ignored-file.txt
git add --force ignored-file.txt
```

#### 交互式添加详解
```bash
# git add -i 交互菜单
staged     unstaged path
1:    unchanged        +0/-1 README.md
2:    unchanged        +1/-0 app.js

*** Commands ***
1: status       2: update       3: revert       4: add untracked
5: patch        6: diff         7: quit         8: help

# 选择操作：
# 1: status - 查看状态
# 2: update - 更新已跟踪文件
# 3: revert - 撤销暂存
# 4: add untracked - 添加未跟踪文件
# 5: patch - 分块添加
# 6: diff - 查看差异
# 7: quit - 退出
# 8: help - 帮助
```

#### 分块添加详解
```bash
# git add -p 分块选项
Stage this hunk [y,n,q,a,d,/,j,J,g,e,?]?

# 选项说明：
# y - 暂存这个块
# n - 不暂存这个块
# q - 退出，不暂存剩余的块
# a - 暂存这个文件的所有块
# d - 不暂存这个文件的任何块
# / - 搜索匹配的块
# j - 跳到下一个未决定的块
# J - 跳到下一个块
# g - 选择要跳转的块
# e - 手动编辑当前块
# ? - 显示帮助
```

### 💾 git commit - 提交文件

#### 基本提交
```bash
# 提交暂存区的文件
git commit -m "Add new feature"

# 提交并添加所有已跟踪文件的修改
git commit -am "Update existing files"

# 空提交（用于触发 CI/CD）
git commit --allow-empty -m "Trigger CI build"
```

#### 高级提交选项
```bash
# 修改最后一次提交
git commit --amend -m "Corrected commit message"

# 修改最后一次提交但不改变提交信息
git commit --amend --no-edit

# 交互式提交
git commit -v

# 使用指定作者提交
git commit --author="John Doe <<EMAIL>>" -m "Fix bug"

# 指定提交日期
git commit --date="2023-01-01 12:00:00" -m "Backdated commit"
```

#### 提交信息规范
```bash
# 常用提交类型
feat: 新功能
fix: 修复 bug
docs: 文档更新
style: 代码格式化
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 提交信息格式示例
git commit -m "feat(auth): add user login functionality

- Add login form component
- Implement authentication service
- Add login route and validation

Closes #123"
```

## 🗑️ 文件删除操作

### ❌ git rm - 删除文件

#### 基本删除
```bash
# 删除文件（从工作目录和暂存区）
git rm filename.txt

# 删除多个文件
git rm file1.txt file2.txt

# 删除目录
git rm -r directory/

# 使用通配符删除
git rm *.log
git rm "*.txt"  # 引号防止 shell 展开
```

#### 高级删除选项
```bash
# 只从暂存区删除，保留工作目录文件
git rm --cached filename.txt

# 强制删除（即使文件已修改）
git rm -f filename.txt

# 递归删除目录
git rm -r --cached directory/

# 删除已忽略的文件
git rm --cached -r .
git add .
```

#### 删除后的操作
```bash
# 删除文件后需要提交
git rm old-file.txt
git commit -m "Remove old-file.txt"

# 批量删除已删除的文件
git add -u
git commit -m "Remove deleted files"
```

## 📝 文件重命名和移动

### 🔄 git mv - 移动/重命名文件

#### 基本用法
```bash
# 重命名文件
git mv old-name.txt new-name.txt

# 移动文件到目录
git mv filename.txt directory/

# 移动并重命名
git mv old-name.txt directory/new-name.txt

# 移动目录
git mv old-directory/ new-directory/
```

#### 等效操作
```bash
# git mv 等效于以下三个命令
mv old-name.txt new-name.txt
git rm old-name.txt
git add new-name.txt

# 但 git mv 更安全，能保持文件历史
```

#### 重命名大小写（Windows/macOS）
```bash
# 在不区分大小写的文件系统中重命名
git mv filename.txt temp-name.txt
git mv temp-name.txt FileName.txt

# 或者使用
git mv filename.txt FileName.txt
```

## 🔍 文件差异查看

### 📊 git diff - 查看文件差异

#### 基本用法
```bash
# 查看工作目录与暂存区的差异
git diff

# 查看暂存区与最后提交的差异
git diff --staged
git diff --cached

# 查看工作目录与最后提交的差异
git diff HEAD
```

#### 指定文件差异
```bash
# 查看特定文件的差异
git diff filename.txt

# 查看特定目录的差异
git diff directory/

# 查看多个文件的差异
git diff file1.txt file2.txt
```

#### 提交间差异
```bash
# 查看两个提交之间的差异
git diff commit1 commit2

# 查看与上一个提交的差异
git diff HEAD~1

# 查看与指定提交的差异
git diff abc123

# 查看分支间差异
git diff main feature-branch
```

#### 差异显示选项
```bash
# 只显示文件名
git diff --name-only

# 显示文件名和状态
git diff --name-status

# 显示统计信息
git diff --stat

# 单词级别的差异
git diff --word-diff

# 忽略空白字符
git diff -w
git diff --ignore-all-space
```

## 📋 文件状态管理

### 🔄 文件状态转换

#### 状态转换图
```
未跟踪 (Untracked) → [git add] → 已暂存 (Staged)
已暂存 (Staged) → [git commit] → 已提交 (Committed)
已提交 (Committed) → [修改文件] → 已修改 (Modified)
已修改 (Modified) → [git add] → 已暂存 (Staged)
```

#### 查看文件状态
```bash
# 查看所有文件状态
git status

# 查看特定文件状态
git status filename.txt

# 简洁状态显示
git status -s
```

### 🔄 暂存区管理

#### 暂存区操作
```bash
# 查看暂存区内容
git ls-files --stage

# 查看暂存区的文件内容
git show :filename.txt

# 比较暂存区和工作目录
git diff filename.txt

# 比较暂存区和最后提交
git diff --staged filename.txt
```

#### 部分暂存
```bash
# 交互式选择要暂存的内容
git add -p filename.txt

# 编辑要暂存的内容
git add -e filename.txt

# 暂存文件的特定行
git add --patch filename.txt
```

## 🔧 文件操作技巧

### 📁 批量操作
```bash
# 添加所有 .js 文件
git add "*.js"

# 删除所有 .log 文件
git rm "*.log"

# 移动所有 .txt 文件到目录
find . -name "*.txt" -exec git mv {} docs/ \;
```

### 🔍 文件搜索
```bash
# 在 Git 历史中搜索文件
git log --all --full-history -- filename.txt

# 搜索删除的文件
git log --diff-filter=D --summary

# 搜索包含特定内容的文件
git grep "search-term"
git grep -n "search-term"  # 显示行号
```

### 📊 文件统计
```bash
# 统计文件修改次数
git log --oneline filename.txt | wc -l

# 查看文件的贡献者
git shortlog -sn -- filename.txt

# 查看文件的详细历史
git log -p filename.txt
```

### 🔄 文件恢复
```bash
# 恢复工作目录的文件
git checkout -- filename.txt

# 恢复到特定提交的版本
git checkout commit-hash -- filename.txt

# 恢复删除的文件
git checkout HEAD~1 -- deleted-file.txt
```

## 💡 最佳实践

### ✅ 文件操作建议

#### 1. 提交前检查
```bash
# 提交前的检查流程
git status                    # 查看状态
git diff                      # 查看修改
git diff --staged            # 查看暂存的修改
git add .                     # 添加文件
git commit -m "descriptive message"  # 提交
```

#### 2. 使用 .gitignore
```bash
# 创建合适的 .gitignore
echo "node_modules/" >> .gitignore
echo "*.log" >> .gitignore
echo ".env" >> .gitignore
git add .gitignore
git commit -m "Add .gitignore"
```

#### 3. 原子性提交
```bash
# 每次提交只包含相关的修改
git add feature-file.js
git commit -m "feat: add new feature"

git add test-file.js
git commit -m "test: add tests for new feature"
```

### 🚨 常见错误避免

#### 1. 避免提交敏感信息
```bash
# 检查是否包含敏感信息
git diff --staged | grep -i "password\|secret\|key"

# 如果误提交，立即修复
git reset --soft HEAD~1
git reset HEAD sensitive-file.txt
# 编辑文件移除敏感信息
git add sensitive-file.txt
git commit -m "Remove sensitive information"
```

#### 2. 避免大文件提交
```bash
# 检查大文件
find . -size +10M -not -path "./.git/*"

# 使用 Git LFS 处理大文件
git lfs track "*.zip"
git add .gitattributes
```

## 🚀 下一步学习

掌握文件操作后，建议学习：
1. **历史查看** - log、show、blame 等
2. **撤销操作** - reset、revert、checkout 等
3. **分支管理** - 分支创建、合并、删除

---

**记住**: 文件操作是 Git 日常使用的核心，熟练掌握这些操作将让您的版本控制更加高效！
