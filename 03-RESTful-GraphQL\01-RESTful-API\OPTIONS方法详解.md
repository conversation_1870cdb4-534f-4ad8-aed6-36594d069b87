# OPTIONS 方法详解

## 1. OPTIONS 方法概述

OPTIONS 方法是 HTTP 协议中用于获取目标资源所支持的通信选项的方法。它有两个主要用途：

1. **API 能力发现** - 查询服务器支持的 HTTP 方法
2. **CORS 预检请求** - 浏览器自动发送的跨域检查

### 1.1 基本特性

| 特性 | 描述 |
|------|------|
| **响应体** | 通常无响应体（204 No Content） |
| **幂等性** | ✅ 幂等 |
| **安全性** | ✅ 安全（不修改服务器状态） |
| **缓存** | ✅ 可缓存 |
| **主要用途** | 能力发现、CORS 预检 |

## 2. OPTIONS 的触发机制

### 2.1 手动调用

```javascript
// 开发者主动发送 OPTIONS 请求
fetch('/api/users/123', { method: 'OPTIONS' })
  .then(response => {
    console.log('支持的方法:', response.headers.get('Allow'));
    console.log('CORS 配置:', {
      origin: response.headers.get('Access-Control-Allow-Origin'),
      methods: response.headers.get('Access-Control-Allow-Methods'),
      headers: response.headers.get('Access-Control-Allow-Headers')
    });
  });
```

### 2.2 浏览器自动触发（CORS 预检）

浏览器在以下情况会自动发送 OPTIONS 预检请求：

#### **触发条件**

**1. 非简单请求方法**
```javascript
// 这些方法会触发预检
fetch('/api/users/123', { method: 'PUT' });     // ✓ 触发
fetch('/api/users/123', { method: 'DELETE' });  // ✓ 触发
fetch('/api/users/123', { method: 'PATCH' });   // ✓ 触发

// 简单方法不触发预检
fetch('/api/users/123', { method: 'GET' });     // ✗ 不触发
fetch('/api/users/123', { method: 'POST' });    // ✗ 不触发（满足其他条件时）
```

**2. 自定义请求头**
```javascript
// 这些头部会触发预检
fetch('/api/users', {
  headers: {
    'Authorization': 'Bearer token123',    // ✓ 触发
    'X-API-Key': 'abc123',                // ✓ 触发
    'X-Requested-With': 'XMLHttpRequest'  // ✓ 触发
  }
});

// 简单头部不触发预检
fetch('/api/users', {
  headers: {
    'Accept': 'application/json',         // ✗ 不触发
    'Accept-Language': 'zh-CN',          // ✗ 不触发
    'Content-Language': 'zh-CN'          // ✗ 不触发
  }
});
```

**3. 特殊 Content-Type**
```javascript
// 这些 Content-Type 会触发预检
fetch('/api/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'     // ✓ 触发
  }
});

fetch('/api/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/xml'      // ✓ 触发
  }
});

// 简单 Content-Type 不触发预检
fetch('/api/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'text/plain'                           // ✗ 不触发
    'Content-Type': 'application/x-www-form-urlencoded'    // ✗ 不触发
    'Content-Type': 'multipart/form-data'                  // ✗ 不触发
  }
});
```

### 2.3 预检请求流程

```javascript
// 1. 用户代码发起复杂请求
fetch('https://api.example.com/users', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token123'
  },
  body: JSON.stringify({ name: '张三' })
});

// 2. 浏览器自动发送预检请求
// OPTIONS /users HTTP/1.1
// Host: api.example.com
// Origin: https://frontend.com
// Access-Control-Request-Method: PUT
// Access-Control-Request-Headers: Content-Type, Authorization

// 3. 服务器响应预检
// HTTP/1.1 204 No Content
// Access-Control-Allow-Origin: https://frontend.com
// Access-Control-Allow-Methods: GET, POST, PUT, DELETE
// Access-Control-Allow-Headers: Content-Type, Authorization
// Access-Control-Max-Age: 86400

// 4. 预检通过后，浏览器发送实际请求
// PUT /users HTTP/1.1
// Host: api.example.com
// Origin: https://frontend.com
// Content-Type: application/json
// Authorization: Bearer token123
```

## 3. OPTIONS 响应内容详解

### 3.1 状态码

```javascript
// 成功响应
HTTP/1.1 204 No Content    // 推荐：无响应体
HTTP/1.1 200 OK           // 可选：有响应体

// 错误响应
HTTP/1.1 405 Method Not Allowed  // 不支持 OPTIONS
HTTP/1.1 403 Forbidden          // 权限不足
HTTP/1.1 404 Not Found          // 资源不存在
```

### 3.2 响应头部

#### **基本头部**
```javascript
Allow: GET, POST, PUT, PATCH, DELETE, OPTIONS  // 支持的 HTTP 方法
Content-Length: 0                              // 响应体长度
Cache-Control: max-age=86400                   // 缓存控制
```

#### **CORS 头部**
```javascript
// 必需的 CORS 头部
Access-Control-Allow-Origin: https://frontend.com     // 允许的源
Access-Control-Allow-Methods: GET, POST, PUT, DELETE  // 允许的方法
Access-Control-Allow-Headers: Content-Type, Authorization  // 允许的头部

// 可选的 CORS 头部
Access-Control-Allow-Credentials: true         // 是否允许凭据
Access-Control-Max-Age: 86400                 // 预检缓存时间（秒）
Access-Control-Expose-Headers: X-Total-Count  // 暴露给客户端的响应头
```

#### **其他头部**
```javascript
Server: nginx/1.18.0                          // 服务器信息
Vary: Origin, Access-Control-Request-Headers  // 缓存变化依据
Accept-Ranges: bytes                          // 是否支持范围请求
```

### 3.3 响应体

#### **无响应体（推荐）**
```javascript
HTTP/1.1 204 No Content
Allow: GET, POST, PUT, DELETE
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE

// 无响应体内容
```

#### **有响应体（可选）**
```javascript
HTTP/1.1 200 OK
Content-Type: application/json
Allow: GET, POST, PUT, DELETE

{
  "methods": ["GET", "POST", "PUT", "DELETE"],
  "cors": {
    "origins": ["https://app.com", "https://admin.com"],
    "credentials": true,
    "maxAge": 86400
  },
  "features": {
    "pagination": true,
    "filtering": true,
    "sorting": true
  }
}
```

## 4. 服务端实现示例

### 4.1 Node.js + Express

```javascript
const express = require('express');
const app = express();

// 方法1：全局 OPTIONS 处理
app.options('*', (req, res) => {
  res.set({
    'Allow': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Origin': req.headers.origin || '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': req.headers['access-control-request-headers'] || 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400'
  });
  
  res.status(204).end();
});

// 方法2：特定路由的 OPTIONS 处理
app.options('/api/users/:id?', (req, res) => {
  const userId = req.params.id;
  
  // 根据资源存在性返回不同的方法
  if (userId) {
    // 单个用户资源
    res.set('Allow', 'GET, PUT, PATCH, DELETE, OPTIONS');
  } else {
    // 用户集合资源
    res.set('Allow', 'GET, POST, OPTIONS');
  }
  
  // CORS 头部
  res.set({
    'Access-Control-Allow-Origin': req.headers.origin,
    'Access-Control-Allow-Methods': res.get('Allow'),
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '3600'
  });
  
  res.status(204).end();
});

// 方法3：中间件方式
app.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    res.set({
      'Access-Control-Allow-Origin': req.headers.origin,
      'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': req.headers['access-control-request-headers'],
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400'
    });
    return res.status(204).end();
  }
  next();
});
```

### 4.2 动态方法检测

```javascript
// 根据用户权限动态返回允许的方法
app.options('/api/users/:id', async (req, res) => {
  const userId = req.params.id;
  const userRole = req.headers['x-user-role'];
  
  try {
    // 检查资源是否存在
    const userExists = await User.exists({ id: userId });
    if (!userExists) {
      return res.status(404).end();
    }
    
    // 根据角色确定允许的方法
    let allowedMethods = ['GET', 'OPTIONS'];
    
    if (userRole === 'admin') {
      allowedMethods.push('PUT', 'PATCH', 'DELETE');
    } else if (userRole === 'user') {
      allowedMethods.push('PATCH'); // 只能更新自己的信息
    }
    
    res.set({
      'Allow': allowedMethods.join(', '),
      'Access-Control-Allow-Origin': req.headers.origin,
      'Access-Control-Allow-Methods': allowedMethods.join(', '),
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-User-Role'
    });
    
    res.status(204).end();
    
  } catch (error) {
    console.error('OPTIONS 处理失败:', error);
    res.status(500).end();
  }
});
```

## 5. 前端应用示例

### 5.1 API 能力发现

```javascript
// API 能力检查器
class APICapabilityChecker {
  constructor() {
    this.cache = new Map();
  }
  
  async checkCapabilities(endpoint) {
    if (this.cache.has(endpoint)) {
      return this.cache.get(endpoint);
    }
    
    try {
      const response = await fetch(endpoint, { method: 'OPTIONS' });
      
      if (!response.ok) {
        throw new Error(`OPTIONS 请求失败: ${response.status}`);
      }
      
      const capabilities = {
        methods: this.parseMethods(response.headers.get('Allow')),
        cors: this.parseCORS(response.headers),
        cacheMaxAge: parseInt(response.headers.get('Access-Control-Max-Age')) || 0
      };
      
      // 缓存结果
      this.cache.set(endpoint, capabilities);
      
      return capabilities;
    } catch (error) {
      console.error('检查 API 能力失败:', error);
      return null;
    }
  }
  
  parseMethods(allowHeader) {
    if (!allowHeader) return [];
    return allowHeader.split(',').map(method => method.trim());
  }
  
  parseCORS(headers) {
    return {
      origin: headers.get('Access-Control-Allow-Origin'),
      methods: headers.get('Access-Control-Allow-Methods'),
      headers: headers.get('Access-Control-Allow-Headers'),
      credentials: headers.get('Access-Control-Allow-Credentials') === 'true'
    };
  }
}

// 使用示例
const checker = new APICapabilityChecker();

async function initializeUserInterface() {
  const capabilities = await checker.checkCapabilities('/api/users/123');
  
  if (capabilities) {
    // 根据 API 能力动态显示界面元素
    document.getElementById('editBtn').style.display = 
      capabilities.methods.includes('PUT') || capabilities.methods.includes('PATCH') 
        ? 'block' : 'none';
    
    document.getElementById('deleteBtn').style.display = 
      capabilities.methods.includes('DELETE') ? 'block' : 'none';
  }
}
```

### 5.2 CORS 预检优化

```javascript
// 预检请求优化策略
class OptimizedFetch {
  constructor() {
    this.preflightCache = new Map();
  }
  
  async fetch(url, options = {}) {
    const cacheKey = this.getPreflightCacheKey(url, options);
    
    // 检查是否需要预检
    if (this.needsPreflight(options)) {
      const cached = this.preflightCache.get(cacheKey);
      
      if (cached && !this.isPreflightExpired(cached)) {
        console.log('使用缓存的预检结果');
      } else {
        console.log('发送新的预检请求');
        await this.performPreflight(url, options);
      }
    }
    
    // 发送实际请求
    return fetch(url, options);
  }
  
  needsPreflight(options) {
    const method = options.method || 'GET';
    const headers = options.headers || {};
    
    // 检查是否为简单请求
    const isSimpleMethod = ['GET', 'HEAD', 'POST'].includes(method.toUpperCase());
    const hasCustomHeaders = Object.keys(headers).some(header => 
      !['Accept', 'Accept-Language', 'Content-Language', 'Content-Type'].includes(header)
    );
    const hasComplexContentType = headers['Content-Type'] && 
      !['text/plain', 'multipart/form-data', 'application/x-www-form-urlencoded']
        .includes(headers['Content-Type']);
    
    return !isSimpleMethod || hasCustomHeaders || hasComplexContentType;
  }
  
  async performPreflight(url, options) {
    const preflightResponse = await fetch(url, { method: 'OPTIONS' });
    const maxAge = parseInt(preflightResponse.headers.get('Access-Control-Max-Age')) || 0;
    
    this.preflightCache.set(this.getPreflightCacheKey(url, options), {
      timestamp: Date.now(),
      maxAge: maxAge * 1000 // 转换为毫秒
    });
  }
  
  getPreflightCacheKey(url, options) {
    const method = options.method || 'GET';
    const headers = Object.keys(options.headers || {}).sort().join(',');
    return `${url}:${method}:${headers}`;
  }
  
  isPreflightExpired(cached) {
    return Date.now() - cached.timestamp > cached.maxAge;
  }
}

// 使用示例
const optimizedFetch = new OptimizedFetch();

// 这个请求会被优化
await optimizedFetch.fetch('/api/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token123'
  },
  body: JSON.stringify(userData)
});
```

## 6. 调试和监控

### 6.1 预检请求调试

```javascript
// CORS 预检调试工具
function debugCORSPreflight(url, options = {}) {
  const method = options.method || 'GET';
  const headers = options.headers || {};
  
  console.group('🔍 CORS 预检分析');
  console.log('目标 URL:', url);
  console.log('请求方法:', method);
  console.log('请求头部:', headers);
  
  // 分析是否会触发预检
  const analysis = {
    isSimpleMethod: ['GET', 'HEAD', 'POST'].includes(method.toUpperCase()),
    hasCustomHeaders: Object.keys(headers).some(header => 
      !['Accept', 'Accept-Language', 'Content-Language', 'Content-Type'].includes(header)
    ),
    hasComplexContentType: headers['Content-Type'] && 
      !['text/plain', 'multipart/form-data', 'application/x-www-form-urlencoded']
        .includes(headers['Content-Type'])
  };
  
  const willPreflight = !analysis.isSimpleMethod || 
                       analysis.hasCustomHeaders || 
                       analysis.hasComplexContentType;
  
  console.log('预检分析:', analysis);
  console.log('是否触发预检:', willPreflight ? '✅ 是' : '❌ 否');
  
  if (willPreflight) {
    console.log('预检原因:', {
      '非简单方法': !analysis.isSimpleMethod,
      '自定义头部': analysis.hasCustomHeaders,
      '复杂Content-Type': analysis.hasComplexContentType
    });
  }
  
  console.groupEnd();
  
  return willPreflight;
}

// 使用示例
debugCORSPreflight('https://api.example.com/users', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token123'
  }
});
```

## 7. 最佳实践

### 7.1 服务端最佳实践

```javascript
// ✅ 推荐的 OPTIONS 实现
app.options('*', (req, res) => {
  // 1. 设置合适的缓存时间
  const maxAge = 86400; // 24小时
  
  // 2. 动态处理 Origin
  const origin = req.headers.origin;
  const allowedOrigins = ['https://app.com', 'https://admin.com'];
  
  res.set({
    'Allow': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Origin': allowedOrigins.includes(origin) ? origin : 'null',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': req.headers['access-control-request-headers'] || 'Content-Type',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': maxAge.toString(),
    'Vary': 'Origin'
  });
  
  // 3. 返回 204 状态码
  res.status(204).end();
});

// ❌ 避免的做法
app.options('*', (req, res) => {
  // 不要返回不必要的响应体
  res.json({ message: 'OPTIONS request handled' });
  
  // 不要设置过短的缓存时间
  res.set('Access-Control-Max-Age', '60');
  
  // 不要忽略 Vary 头部
  // 缺少 Vary: Origin 可能导致缓存问题
});
```

### 7.2 前端最佳实践

```javascript
// ✅ 合理使用 OPTIONS 检查
async function smartAPICall(endpoint, options) {
  // 只在必要时检查 API 能力
  if (options.checkCapabilities) {
    const capabilities = await checkAPICapabilities(endpoint);
    if (!capabilities.methods.includes(options.method)) {
      throw new Error(`方法 ${options.method} 不被支持`);
    }
  }
  
  return fetch(endpoint, options);
}

// ❌ 避免过度使用
async function inefficientAPICall(endpoint, options) {
  // 不要每次请求都检查能力
  await fetch(endpoint, { method: 'OPTIONS' });
  return fetch(endpoint, options);
}
```

## 8. 总结

OPTIONS 方法是现代 Web 开发中的重要工具：

**主要用途**：
- **API 能力发现**：查询服务器支持的操作
- **CORS 预检**：浏览器自动的跨域安全检查

**触发机制**：
- **手动调用**：开发者主动发送
- **自动触发**：浏览器在复杂跨域请求前自动发送

**返回内容**：
- **状态码**：通常 204 No Content
- **响应头**：Allow、CORS 相关头部
- **响应体**：通常为空

**最佳实践**：
- 服务端正确配置 CORS 头部
- 设置合理的预检缓存时间
- 前端合理使用能力检查
- 做好调试和监控

正确理解和使用 OPTIONS 方法对于构建安全、高效的现代 Web 应用至关重要。
