<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 基础Meta标签 -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- 页面标题（最重要的SEO因素） -->
    <title>页面标题 - 网站名称</title>
    
    <!-- ⭐⭐⭐⭐⭐ 最重要：页面描述 -->
    <meta name="description" content="这里写页面的简洁描述，120-160个字符，会显示在搜索结果中，影响用户点击率">
    
    <!-- ⭐⭐⭐⭐ 搜索引擎指令 -->
    <meta name="robots" content="index, follow">
    
    <!-- ⭐⭐ 作者信息 -->
    <meta name="author" content="作者姓名">
    
    <!-- 页面关键词（已过时，不建议使用） -->
    <!-- <meta name="keywords" content="关键词1, 关键词2"> -->
    
    <!-- ⭐⭐⭐⭐ Open Graph (Facebook, LinkedIn等) -->
    <meta property="og:title" content="页面标题">
    <meta property="og:description" content="页面描述，可以与meta description相同或略有不同">
    <meta property="og:image" content="https://example.com/images/og-image.jpg">
    <meta property="og:url" content="https://example.com/current-page">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="网站名称">
    <meta property="og:locale" content="zh_CN">
    
    <!-- ⭐⭐⭐ Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="页面标题">
    <meta name="twitter:description" content="页面描述">
    <meta name="twitter:image" content="https://example.com/images/twitter-image.jpg">
    <meta name="twitter:site" content="@网站Twitter账号">
    <meta name="twitter:creator" content="@作者Twitter账号">
    
    <!-- 移动端优化 -->
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- 网站验证（根据需要添加） -->
    <!-- <meta name="google-site-verification" content="Google验证码"> -->
    <!-- <meta name="baidu-site-verification" content="百度验证码"> -->
    <!-- <meta name="msvalidate.01" content="Bing验证码"> -->
    
    <!-- 防止重复内容 -->
    <link rel="canonical" href="https://example.com/current-page">
    
    <!-- 结构化数据（JSON-LD格式） -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "页面标题",
        "description": "页面描述",
        "url": "https://example.com/current-page",
        "author": {
            "@type": "Person",
            "name": "作者姓名"
        },
        "publisher": {
            "@type": "Organization",
            "name": "网站名称",
            "logo": {
                "@type": "ImageObject",
                "url": "https://example.com/logo.png"
            }
        }
    }
    </script>
    
    <!-- 其他重要的link标签 -->
    <link rel="icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/fonts/main.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    
    <!-- CSS文件 -->
    <style>
        /* 关键CSS内联，提升首屏渲染速度 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .meta-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .importance {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .high { background: #e74c3c; color: white; }
        .medium { background: #f39c12; color: white; }
        .low { background: #95a5a6; color: white; }
        
        code {
            background: #f1f2f6;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        .checklist {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 20px 0;
        }
        
        .checklist ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SEO Meta标签完整指南</h1>
        
        <div class="meta-info">
            <h2>重要性排序</h2>
            <p><span class="importance high">高</span> <code>&lt;title&gt;</code> 页面标题</p>
            <p><span class="importance high">高</span> <code>meta name="description"</code> 页面描述</p>
            <p><span class="importance medium">中</span> <code>meta name="robots"</code> 搜索引擎指令</p>
            <p><span class="importance medium">中</span> Open Graph 标签（社交媒体）</p>
            <p><span class="importance low">低</span> <code>meta name="author"</code> 作者信息</p>
            <p><span class="importance low">废弃</span> <code>meta name="keywords"</code> 关键词（不建议使用）</p>
        </div>
        
        <div class="checklist">
            <h3>✅ SEO检查清单</h3>
            <ul>
                <li>每个页面都有独特的 <code>&lt;title&gt;</code> 标签</li>
                <li>每个页面都有独特的 <code>meta description</code></li>
                <li>描述长度控制在120-160个字符</li>
                <li>设置了合适的 <code>robots</code> 指令</li>
                <li>配置了Open Graph标签用于社交媒体分享</li>
                <li>添加了结构化数据（JSON-LD）</li>
                <li>设置了canonical链接防止重复内容</li>
                <li>移动端适配完善</li>
            </ul>
        </div>
        
        <h2>实际应用示例</h2>
        <p>这个页面本身就是一个完整的SEO优化示例，你可以查看源代码学习具体实现。</p>
        
        <h3>注意事项</h3>
        <ul>
            <li><strong>内容质量最重要</strong>：Meta标签只是辅助，优质内容才是SEO的核心</li>
            <li><strong>避免关键词堆砌</strong>：自然地描述页面内容</li>
            <li><strong>保持更新</strong>：定期检查和更新Meta标签</li>
            <li><strong>测试效果</strong>：使用Google Search Console等工具监控效果</li>
        </ul>
    </div>
    
    <!-- 页面加载完成后的JavaScript -->
    <script>
        // 显示当前页面的Meta信息
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 当前页面SEO信息 ===');
            console.log('标题:', document.title);
            console.log('描述:', document.querySelector('meta[name="description"]')?.content);
            console.log('作者:', document.querySelector('meta[name="author"]')?.content);
            console.log('Robots:', document.querySelector('meta[name="robots"]')?.content);
        });
    </script>
</body>
</html>
