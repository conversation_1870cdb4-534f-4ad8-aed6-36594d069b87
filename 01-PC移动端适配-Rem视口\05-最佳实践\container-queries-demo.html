<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>容器查询演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .section {
            margin-bottom: 40px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section h2 {
            margin-bottom: 20px;
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        /* 1. 定义容器查询上下文 */
        .card-container {
            container-type: inline-size; /* 监听容器的内联尺寸变化 */
            container-name: card-container; /* 给容器命名 */
            
            /* 或者简写形式 */
            /* container: card-container / inline-size; */
            
            background: #ecf0f1;
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            resize: horizontal; /* 允许水平调整大小 */
            overflow: auto;
            min-width: 200px;
            max-width: 100%;
        }

        .card-container::before {
            content: "📏 可拖拽调整容器宽度 →";
            display: block;
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
            text-align: center;
        }

        /* 2. 基础卡片样式 */
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .card-content {
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .card-meta {
            display: flex;
            gap: 10px;
            font-size: 0.9rem;
            color: #95a5a6;
        }

        .card-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 6px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* 3. 容器查询样式 */
        
        /* 当容器宽度 >= 300px 时 */
        @container card-container (min-width: 300px) {
            .card {
                border-left: 4px solid #3498db;
            }
            
            .card-title {
                font-size: 1.4rem;
            }
            
            .card::after {
                content: "📱 容器宽度 ≥ 300px";
                display: block;
                background: #3498db;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
                margin-top: 10px;
                text-align: center;
            }
        }

        /* 当容器宽度 >= 500px 时 */
        @container card-container (min-width: 500px) {
            .card {
                display: grid;
                grid-template-columns: 150px 1fr;
                gap: 20px;
                align-items: start;
            }
            
            .card-image {
                height: 100px;
                margin-bottom: 0;
            }
            
            .card::after {
                content: "💻 容器宽度 ≥ 500px - 网格布局";
                background: #27ae60;
                grid-column: 1 / -1;
            }
        }

        /* 当容器宽度 >= 700px 时 */
        @container card-container (min-width: 700px) {
            .card {
                grid-template-columns: 200px 1fr 150px;
            }
            
            .card-meta {
                flex-direction: column;
                justify-self: end;
                align-self: center;
                text-align: right;
            }
            
            .card::after {
                content: "🖥️ 容器宽度 ≥ 700px - 三列布局";
                background: #e74c3c;
            }
        }

        /* 多个容器演示 */
        .multi-container-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }

        .sidebar-container {
            container-type: inline-size;
            container-name: sidebar;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        .main-container {
            container-type: inline-size;
            container-name: main;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        .widget {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* 侧边栏容器查询 */
        @container sidebar (min-width: 200px) {
            .widget {
                border-left: 3px solid #f39c12;
            }
            
            .widget::after {
                content: "侧边栏模式";
                display: block;
                font-size: 12px;
                color: #f39c12;
                margin-top: 5px;
            }
        }

        /* 主内容容器查询 */
        @container main (min-width: 300px) {
            .widget {
                display: flex;
                align-items: center;
                gap: 15px;
            }
            
            .widget::before {
                content: "📊";
                font-size: 24px;
            }
            
            .widget::after {
                content: "主内容模式";
                display: block;
                font-size: 12px;
                color: #27ae60;
                margin-left: auto;
            }
        }

        /* 容器查询支持检测 */
        .support-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .support-info.supported {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .support-info.not-supported {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        /* 传统媒体查询对比 */
        .media-query-demo {
            background: #e9ecef;
            border: 2px solid #adb5bd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .media-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* 传统媒体查询 - 基于视口宽度 */
        @media (min-width: 768px) {
            .media-card::after {
                content: "📺 媒体查询：视口宽度 ≥ 768px";
                display: block;
                background: #6c757d;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
                margin-top: 10px;
                text-align: center;
            }
        }

        /* 调整手柄样式 */
        .card-container::-webkit-resizer {
            background: #3498db;
            border-radius: 4px;
        }

        /* 说明文字 */
        .instruction {
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }

        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }

        .highlight {
            background: #f1c40f;
            color: #2c3e50;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🔍 容器查询（Container Queries）演示</h1>
        
        <!-- 浏览器支持检测 -->
        <div class="support-info" id="supportInfo">
            <strong>检测浏览器支持情况...</strong>
        </div>

        <!-- 基础概念 -->
        <div class="section">
            <h2>📖 基础概念</h2>
            <p><strong>容器查询</strong>允许你基于<span class="highlight">特定容器的尺寸</span>来应用样式，而不是整个视口的尺寸。</p>
            
            <div class="instruction">
                <strong>💡 关键区别：</strong><br>
                • <strong>媒体查询</strong>：基于整个浏览器视口的尺寸<br>
                • <strong>容器查询</strong>：基于特定元素（容器）的尺寸
            </div>

            <div class="code-example">
/* 1. 定义容器 */
.card-container {
    container-type: inline-size;  /* 监听内联尺寸变化 */
    container-name: card-container;  /* 给容器命名 */
}

/* 2. 基于容器尺寸应用样式 */
@container card-container (min-width: 400px) {
    .card {
        display: grid;
        grid-template-columns: 1fr 1fr;
    }
}</div>
        </div>

        <!-- 实际演示 -->
        <div class="section">
            <h2>🎮 交互演示</h2>
            <p>拖拽下面容器的右下角来调整宽度，观察卡片样式的变化：</p>
            
            <div class="card-container">
                <div class="card">
                    <div class="card-image">🖼️ 图片区域</div>
                    <div class="card-content-wrapper">
                        <div class="card-title">响应式卡片</div>
                        <div class="card-content">
                            这个卡片会根据其<strong>容器的宽度</strong>而不是浏览器视口的宽度来改变布局。
                            尝试拖拽容器边缘来看看效果！
                        </div>
                        <div class="card-meta">
                            <span>📅 2025-06-25</span>
                            <span>👤 作者</span>
                            <span>🏷️ 演示</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="instruction">
                <strong>🎯 观察要点：</strong><br>
                • 容器宽度 < 300px：基础样式<br>
                • 容器宽度 ≥ 300px：添加左边框和状态提示<br>
                • 容器宽度 ≥ 500px：切换为网格布局（图片+内容）<br>
                • 容器宽度 ≥ 700px：三列布局（图片+内容+元数据）
            </div>
        </div>

        <!-- 多容器演示 -->
        <div class="section">
            <h2>🔄 多容器独立响应</h2>
            <p>不同的容器可以有不同的查询规则，彼此独立响应：</p>
            
            <div class="multi-container-demo">
                <div class="sidebar-container">
                    <h3>侧边栏容器</h3>
                    <div class="widget">
                        <div>小部件 1</div>
                        <div>基于侧边栏宽度响应</div>
                    </div>
                    <div class="widget">
                        <div>小部件 2</div>
                        <div>独立的容器查询规则</div>
                    </div>
                </div>
                
                <div class="main-container">
                    <h3>主内容容器</h3>
                    <div class="widget">
                        <div>主要内容 1</div>
                        <div>基于主容器宽度响应</div>
                    </div>
                    <div class="widget">
                        <div>主要内容 2</div>
                        <div>不同的布局规则</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 与媒体查询对比 -->
        <div class="section">
            <h2>⚖️ 与媒体查询对比</h2>
            
            <div class="media-query-demo">
                <h3>传统媒体查询</h3>
                <div class="media-card">
                    <div>这个卡片使用传统的媒体查询</div>
                    <div>只会根据<strong>整个浏览器视口</strong>的宽度变化</div>
                    <div>调整浏览器窗口大小试试看</div>
                </div>
            </div>

            <div class="instruction">
                <strong>🔍 对比总结：</strong><br>
                • <strong>媒体查询</strong>：适合整体布局响应（导航栏、侧边栏显隐等）<br>
                • <strong>容器查询</strong>：适合组件级响应（卡片、小部件等）<br>
                • <strong>组合使用</strong>：两者结合可以实现更精细的响应式设计
            </div>
        </div>

        <!-- 实际应用场景 -->
        <div class="section">
            <h2>🎯 实际应用场景</h2>
            <ul style="margin-left: 20px; line-height: 2;">
                <li><strong>组件库</strong>：让组件根据使用环境自适应</li>
                <li><strong>卡片布局</strong>：根据容器空间调整内容排列</li>
                <li><strong>侧边栏小部件</strong>：根据侧边栏宽度调整显示方式</li>
                <li><strong>网格系统</strong>：让网格项目根据可用空间自适应</li>
                <li><strong>模块化设计</strong>：每个模块独立响应其容器环境</li>
            </ul>
        </div>
    </div>

    <script>
        // 检测浏览器对容器查询的支持
        function checkContainerQuerySupport() {
            const supportInfo = document.getElementById('supportInfo');
            
            if (CSS.supports('container-type: inline-size')) {
                supportInfo.className = 'support-info supported';
                supportInfo.innerHTML = '<strong>✅ 太棒了！</strong> 你的浏览器支持容器查询，可以看到完整的演示效果。';
            } else {
                supportInfo.className = 'support-info not-supported';
                supportInfo.innerHTML = '<strong>❌ 抱歉！</strong> 你的浏览器暂不支持容器查询。建议使用 Chrome 105+、Firefox 110+ 或 Safari 16+ 查看演示。';
            }
        }

        // 页面加载完成后检测支持情况
        document.addEventListener('DOMContentLoaded', checkContainerQuerySupport);

        // 输出一些调试信息
        console.log('=== 容器查询演示 ===');
        console.log('支持容器查询:', CSS.supports('container-type: inline-size'));
        console.log('当前浏览器:', navigator.userAgent);
        
        // 监听容器大小变化（仅用于演示）
        if (window.ResizeObserver) {
            const containers = document.querySelectorAll('.card-container');
            const observer = new ResizeObserver(entries => {
                entries.forEach(entry => {
                    const width = Math.round(entry.contentRect.width);
                    console.log(`容器宽度变化: ${width}px`);
                });
            });
            
            containers.forEach(container => observer.observe(container));
        }
    </script>
</body>
</html>
