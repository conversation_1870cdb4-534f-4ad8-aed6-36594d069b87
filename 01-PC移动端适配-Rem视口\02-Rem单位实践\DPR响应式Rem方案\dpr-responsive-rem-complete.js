/**
 * 基于 DPR 的 PC + 移动端兼容 rem 计算方案（完整版）
 * 
 * 特性：
 * - 支持 DPR 高清屏适配
 * - PC 和移动端响应式
 * - 多种 DPR 策略
 * - 完整的事件监听
 * - CSS 变量支持
 * 
 * 使用方法：
 * const remCalc = new DPRResponsiveRemCalculator({
 *     mobileDesignWidth: 750,
 *     pcDesignWidth: 1920,
 *     enableDPR: true,
 *     dprStrategy: 'viewport'
 * });
 */

class DPRResponsiveRemCalculator {
    constructor(options = {}) {
        this.config = {
            // 设计稿配置
            mobileDesignWidth: options.mobileDesignWidth || 750,
            pcDesignWidth: options.pcDesignWidth || 1920,
            
            // 基准字体大小
            mobileBaseSize: options.mobileBaseSize || 75,
            pcBaseSize: options.pcBaseSize || 16,
            
            // 断点设置
            breakpoints: {
                mobile: 768,
                tablet: 1024,
                desktop: 1920
            },
            
            // DPR 相关配置
            enableDPR: options.enableDPR !== false, // 默认启用 DPR
            maxDPR: options.maxDPR || 3,            // 最大 DPR 支持
            dprStrategy: options.dprStrategy || 'viewport', // 'scale' | 'viewport' | 'both'
            
            // 限制
            maxWidth: options.maxWidth || 2560,
            minWidth: options.minWidth || 320
        };
        
        this.dpr = this.getDPR();
        this.init();
    }
    
    // 获取设备像素比
    getDPR() {
        let dpr = window.devicePixelRatio || 1;
        
        // 限制 DPR 范围，避免过大的值
        dpr = Math.min(dpr, this.config.maxDPR);
        
        // 在某些 Android 设备上，DPR 可能是小数，需要处理
        if (dpr > 3) dpr = 3;
        else if (dpr > 2) dpr = 2;
        else if (dpr > 1) dpr = 2;
        else dpr = 1;
        
        return dpr;
    }
    
    // 获取设备类型
    getDeviceType(width) {
        const { breakpoints } = this.config;
        
        if (width <= breakpoints.mobile) {
            return 'mobile';
        } else if (width <= breakpoints.tablet) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }
    
    // 设置 viewport（移动端）
    setViewport() {
        if (!this.config.enableDPR) return;
        
        const deviceType = this.getDeviceType(document.documentElement.clientWidth);
        
        // 只在移动端设置 viewport
        if (deviceType === 'mobile') {
            const scale = 1 / this.dpr;
            let metaEl = document.querySelector('meta[name="viewport"]');
            
            if (!metaEl) {
                metaEl = document.createElement('meta');
                metaEl.setAttribute('name', 'viewport');
                document.head.appendChild(metaEl);
            }
            
            const content = [
                `width=device-width`,
                `initial-scale=${scale}`,
                `maximum-scale=${scale}`,
                `minimum-scale=${scale}`,
                `user-scalable=no`
            ].join(', ');
            
            metaEl.setAttribute('content', content);
        }
    }
    
    // 计算字体大小
    calculateFontSize(deviceWidth, deviceType) {
        let fontSize;
        
        switch (deviceType) {
            case 'mobile':
                fontSize = this.calculateMobileFontSize(deviceWidth);
                break;
            case 'tablet':
                fontSize = this.calculateTabletFontSize(deviceWidth);
                break;
            case 'desktop':
                fontSize = this.calculateDesktopFontSize(deviceWidth);
                break;
        }
        
        // 根据 DPR 策略调整字体大小
        if (this.config.enableDPR && deviceType === 'mobile') {
            fontSize = this.adjustFontSizeByDPR(fontSize, deviceType);
        }
        
        return fontSize;
    }
    
    // 移动端字体计算
    calculateMobileFontSize(width) {
        const actualWidth = this.config.enableDPR ? width * this.dpr : width;
        return (actualWidth / this.config.mobileDesignWidth) * this.config.mobileBaseSize;
    }
    
    // 平板端字体计算
    calculateTabletFontSize(width) {
        const { breakpoints, mobileBaseSize, pcBaseSize } = this.config;
        const mobileMaxSize = (breakpoints.mobile / this.config.mobileDesignWidth) * mobileBaseSize;
        
        // 渐进式过渡
        const progress = (width - breakpoints.mobile) / (breakpoints.tablet - breakpoints.mobile);
        return mobileMaxSize + (pcBaseSize - mobileMaxSize) * progress;
    }
    
    // 桌面端字体计算
    calculateDesktopFontSize(width) {
        // PC端通常不需要考虑 DPR，使用标准计算
        const scale = Math.min(width / this.config.pcDesignWidth, 1.5);
        return this.config.pcBaseSize * scale;
    }
    
    // 根据 DPR 调整字体大小
    adjustFontSizeByDPR(fontSize, deviceType) {
        if (deviceType !== 'mobile' || !this.config.enableDPR) {
            return fontSize;
        }
        
        switch (this.config.dprStrategy) {
            case 'scale':
                // 字体大小根据 DPR 缩放
                return fontSize;
                
            case 'viewport':
                // 通过 viewport 缩放，字体保持原大小
                return fontSize;
                
            case 'both':
                // 综合策略：轻微调整字体大小
                return fontSize * (1 + (this.dpr - 1) * 0.1);
                
            default:
                return fontSize;
        }
    }
    
    // 设置 rem 基准值
    setRem() {
        let deviceWidth = document.documentElement.clientWidth || window.innerWidth;
        const deviceType = this.getDeviceType(deviceWidth);
        
        // 限制宽度范围
        deviceWidth = Math.max(this.config.minWidth, Math.min(deviceWidth, this.config.maxWidth));
        
        // 计算字体大小
        const fontSize = this.calculateFontSize(deviceWidth, deviceType);
        
        // 设置根字体大小
        document.documentElement.style.fontSize = fontSize + 'px';
        
        // 设置相关属性
        this.setAttributes(deviceType, fontSize, deviceWidth);
        
        // 设置 CSS 变量
        this.setCSSVariables(fontSize, deviceWidth, deviceType);
    }
    
    // 设置元素属性
    setAttributes(deviceType, fontSize, deviceWidth) {
        const html = document.documentElement;
        
        html.setAttribute('data-device', deviceType);
        html.setAttribute('data-dpr', this.dpr);
        html.setAttribute('data-rem-base', fontSize);
        html.setAttribute('data-device-width', deviceWidth);
        
        // 设置 body 类名
        document.body.className = document.body.className.replace(/device-\w+|dpr-\d+/g, '');
        document.body.classList.add(`device-${deviceType}`, `dpr-${this.dpr}`);
    }
    
    // 设置 CSS 变量
    setCSSVariables(fontSize, deviceWidth, deviceType) {
        const html = document.documentElement;
        
        html.style.setProperty('--rem-base', fontSize + 'px');
        html.style.setProperty('--device-width', deviceWidth + 'px');
        html.style.setProperty('--device-type', deviceType);
        html.style.setProperty('--dpr', this.dpr);
        html.style.setProperty('--scale', 1 / this.dpr);
        html.style.setProperty('--hairline', (1 / this.dpr) + 'px');
    }
    
    // 初始化
    init() {
        // 设置 viewport（移动端）
        this.setViewport();
        
        // 设置 rem
        this.setRem();
        
        // 绑定事件
        this.bindEvents();
        
        // 设置 body 基础字体
        this.setBodyFont();
    }
    
    // 设置 body 字体
    setBodyFont() {
        const deviceType = this.getDeviceType(document.documentElement.clientWidth);
        let bodyFontSize;
        
        switch (deviceType) {
            case 'mobile':
                bodyFontSize = 14 * this.dpr; // 高清屏下字体更清晰
                break;
            case 'tablet':
                bodyFontSize = 16;
                break;
            case 'desktop':
                bodyFontSize = 16;
                break;
        }
        
        document.body.style.fontSize = bodyFontSize + 'px';
    }
    
    // 绑定事件
    bindEvents() {
        let resizeTimer = null;
        let orientationTimer = null;
        
        const handleResize = () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.setRem();
                this.setBodyFont();
            }, 100);
        };
        
        const handleOrientationChange = () => {
            clearTimeout(orientationTimer);
            orientationTimer = setTimeout(() => {
                // 重新获取 DPR（某些设备旋转后可能变化）
                this.dpr = this.getDPR();
                this.setViewport();
                this.setRem();
                this.setBodyFont();
            }, 300);
        };
        
        window.addEventListener('resize', handleResize);
        window.addEventListener('orientationchange', handleOrientationChange);
        
        // 监听 DPR 变化（如用户缩放）
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia(`(resolution: ${this.dpr}dppx)`);
            if (mediaQuery.addListener) {
                mediaQuery.addListener(() => {
                    this.dpr = this.getDPR();
                    this.setRem();
                });
            }
        }
    }
    
    // 工具方法：px 转 rem
    px2rem(px, deviceType = null) {
        if (!deviceType) {
            const width = document.documentElement.clientWidth;
            deviceType = this.getDeviceType(width);
        }
        
        let baseSize;
        switch (deviceType) {
            case 'mobile':
                baseSize = this.config.mobileBaseSize;
                break;
            case 'tablet':
                const width = document.documentElement.clientWidth;
                const fontSize = this.calculateTabletFontSize(width);
                baseSize = fontSize;
                break;
            case 'desktop':
                baseSize = this.config.pcBaseSize;
                break;
            default:
                baseSize = 16;
        }
        
        return px / baseSize;
    }
    
    // 工具方法：获取实际像素值
    getActualPx(designPx, deviceType = null) {
        if (!deviceType) {
            const width = document.documentElement.clientWidth;
            deviceType = this.getDeviceType(width);
        }
        
        if (deviceType === 'mobile' && this.config.enableDPR) {
            return designPx * this.dpr;
        }
        
        return designPx;
    }
    
    // 获取当前配置信息
    getInfo() {
        return {
            dpr: this.dpr,
            deviceType: this.getDeviceType(document.documentElement.clientWidth),
            remBase: parseFloat(document.documentElement.style.fontSize),
            deviceWidth: document.documentElement.clientWidth,
            config: this.config
        };
    }
}

// 使用示例
if (typeof window !== 'undefined') {
    // 初始化
    const dprResponsiveRem = new DPRResponsiveRemCalculator({
        mobileDesignWidth: 750,
        pcDesignWidth: 1920,
        mobileBaseSize: 75,
        pcBaseSize: 16,
        enableDPR: true,
        dprStrategy: 'viewport' // 'scale' | 'viewport' | 'both'
    });
    
    // 全局工具函数
    window.px2rem = dprResponsiveRem.px2rem.bind(dprResponsiveRem);
    window.getActualPx = dprResponsiveRem.getActualPx.bind(dprResponsiveRem);
    window.dprRemCalculator = dprResponsiveRem;
}

// 模块导出（支持 CommonJS 和 ES6）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DPRResponsiveRemCalculator;
}

if (typeof exports !== 'undefined') {
    exports.DPRResponsiveRemCalculator = DPRResponsiveRemCalculator;
}
