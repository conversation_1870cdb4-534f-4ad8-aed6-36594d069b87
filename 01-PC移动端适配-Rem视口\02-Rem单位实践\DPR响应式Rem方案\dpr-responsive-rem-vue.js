/**
 * Vue 3 组合式 API 版本的 DPR + 响应式 rem 计算方案
 * 
 * 特性：
 * - Vue 3 Composition API
 * - TypeScript 支持
 * - 响应式数据
 * - 插件形式安装
 * 
 * 使用方法：
 * // main.js
 * import { createApp } from 'vue';
 * import { DPRResponsiveRemPlugin } from './dpr-responsive-rem-vue';
 * 
 * const app = createApp(App);
 * app.use(DPRResponsiveRemPlugin, {
 *     mobileDesignWidth: 750,
 *     enableDPR: true
 * });
 * 
 * // 组件中使用
 * import { useResponsiveRem, useDPR } from './dpr-responsive-rem-vue';
 * 
 * export default {
 *     setup() {
 *         const { deviceType, px2rem } = useResponsiveRem();
 *         const dpr = useDPR();
 *         
 *         return { deviceType, px2rem, dpr };
 *     }
 * };
 */

import { ref, reactive, computed, onMounted, onUnmounted, inject, provide } from 'vue';

// 配置符号
const DPR_CONFIG_KEY = Symbol('dpr-config');

// 默认配置
const DEFAULT_CONFIG = {
    mobileDesignWidth: 750,
    pcDesignWidth: 1920,
    mobileBaseSize: 75,
    pcBaseSize: 16,
    breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1920
    },
    enableDPR: true,
    maxWidth: 2560,
    minWidth: 320
};

/**
 * DPR 响应式数据
 */
const dprState = reactive({
    value: 1,
    isHighDPR: false
});

/**
 * 设备信息响应式数据
 */
const deviceState = reactive({
    type: 'desktop',
    width: 375,
    height: 667
});

/**
 * Rem 基准值响应式数据
 */
const remState = reactive({
    base: 16,
    scale: 1
});

/**
 * 获取设备像素比
 */
function getDPR() {
    if (typeof window === 'undefined') return 1;
    let dpr = window.devicePixelRatio || 1;
    
    // 限制 DPR 范围
    if (dpr > 3) dpr = 3;
    else if (dpr > 2) dpr = 2;
    else if (dpr > 1) dpr = 2;
    else dpr = 1;
    
    return dpr;
}

/**
 * 获取设备类型
 */
function getDeviceType(width, breakpoints) {
    if (width <= breakpoints.mobile) return 'mobile';
    if (width <= breakpoints.tablet) return 'tablet';
    return 'desktop';
}

/**
 * DPR Hook
 */
export function useDPR() {
    const updateDPR = () => {
        const newDPR = getDPR();
        dprState.value = newDPR;
        dprState.isHighDPR = newDPR > 1;
    };
    
    onMounted(() => {
        updateDPR();
        
        // 监听 DPR 变化
        if (window.matchMedia) {
            try {
                const mediaQuery = window.matchMedia(`(resolution: ${dprState.value}dppx)`);
                const handleDPRChange = () => updateDPR();
                
                if (mediaQuery.addListener) {
                    mediaQuery.addListener(handleDPRChange);
                    onUnmounted(() => mediaQuery.removeListener(handleDPRChange));
                }
            } catch (e) {
                console.warn('DPR media query not supported');
            }
        }
    });
    
    return {
        dpr: computed(() => dprState.value),
        isHighDPR: computed(() => dprState.isHighDPR)
    };
}

/**
 * 设备类型 Hook
 */
export function useDeviceType() {
    const config = inject(DPR_CONFIG_KEY, DEFAULT_CONFIG);
    
    const updateDeviceInfo = () => {
        if (typeof window === 'undefined') return;
        
        const width = window.innerWidth;
        const height = window.innerHeight;
        const type = getDeviceType(width, config.breakpoints);
        
        deviceState.width = width;
        deviceState.height = height;
        deviceState.type = type;
    };
    
    onMounted(() => {
        updateDeviceInfo();
        
        let resizeTimer = null;
        const handleResize = () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(updateDeviceInfo, 100);
        };
        
        const handleOrientationChange = () => {
            setTimeout(updateDeviceInfo, 300);
        };
        
        window.addEventListener('resize', handleResize);
        window.addEventListener('orientationchange', handleOrientationChange);
        
        onUnmounted(() => {
            window.removeEventListener('resize', handleResize);
            window.removeEventListener('orientationchange', handleOrientationChange);
            clearTimeout(resizeTimer);
        });
    });
    
    return {
        deviceType: computed(() => deviceState.type),
        deviceWidth: computed(() => deviceState.width),
        deviceHeight: computed(() => deviceState.height),
        isMobile: computed(() => deviceState.type === 'mobile'),
        isTablet: computed(() => deviceState.type === 'tablet'),
        isDesktop: computed(() => deviceState.type === 'desktop')
    };
}

/**
 * 响应式 Rem Hook
 */
export function useResponsiveRem(options = {}) {
    const config = inject(DPR_CONFIG_KEY, { ...DEFAULT_CONFIG, ...options });
    const { dpr } = useDPR();
    const { deviceType, deviceWidth } = useDeviceType();
    
    // 计算字体大小
    const calculateFontSize = (width, type, dprValue) => {
        let fontSize;
        
        switch (type) {
            case 'mobile':
                const actualWidth = config.enableDPR ? width * dprValue : width;
                fontSize = (actualWidth / config.mobileDesignWidth) * config.mobileBaseSize;
                break;
            case 'tablet':
                const mobileMaxSize = (config.breakpoints.mobile / config.mobileDesignWidth) * config.mobileBaseSize;
                const progress = (width - config.breakpoints.mobile) / (config.breakpoints.tablet - config.breakpoints.mobile);
                fontSize = mobileMaxSize + (config.pcBaseSize - mobileMaxSize) * progress;
                break;
            case 'desktop':
                const scale = Math.min(width / config.pcDesignWidth, 1.5);
                fontSize = config.pcBaseSize * scale;
                break;
            default:
                fontSize = 16;
        }
        
        return fontSize;
    };
    
    // 设置 viewport（仅移动端）
    const setViewport = (dprValue, type) => {
        if (typeof window === 'undefined' || !config.enableDPR || type !== 'mobile') return;
        
        const scale = 1 / dprValue;
        let meta = document.querySelector('meta[name="viewport"]');
        
        if (!meta) {
            meta = document.createElement('meta');
            meta.name = 'viewport';
            document.head.appendChild(meta);
        }
        
        meta.content = [
            'width=device-width',
            `initial-scale=${scale}`,
            `maximum-scale=${scale}`,
            `minimum-scale=${scale}`,
            'user-scalable=no'
        ].join(', ');
    };
    
    // 设置 rem
    const setRem = () => {
        if (typeof window === 'undefined') return;
        
        const width = Math.max(config.minWidth, Math.min(deviceWidth.value, config.maxWidth));
        const type = deviceType.value;
        const dprValue = dpr.value;
        
        const fontSize = calculateFontSize(width, type, dprValue);
        
        // 设置根字体大小
        document.documentElement.style.fontSize = fontSize + 'px';
        
        // 设置属性和变量
        document.documentElement.setAttribute('data-device', type);
        document.documentElement.setAttribute('data-dpr', dprValue);
        document.documentElement.style.setProperty('--rem-base', fontSize + 'px');
        document.documentElement.style.setProperty('--dpr', dprValue);
        document.documentElement.style.setProperty('--hairline', (1 / dprValue) + 'px');
        document.documentElement.style.setProperty('--device-width', width + 'px');
        
        // 设置 body 类名
        document.body.className = document.body.className.replace(/device-\w+|dpr-\d+/g, '');
        document.body.classList.add(`device-${type}`, `dpr-${dprValue}`);
        
        // 更新状态
        remState.base = fontSize;
        remState.scale = fontSize / 16;
        
        // 设置 viewport
        setViewport(dprValue, type);
    };
    
    // 监听变化
    onMounted(() => {
        setRem();
    });
    
    // 监听响应式数据变化
    const stopWatcher = computed(() => {
        // 当 dpr、deviceType、deviceWidth 变化时重新计算
        setRem();
        return `${dpr.value}-${deviceType.value}-${deviceWidth.value}`;
    });
    
    onUnmounted(() => {
        stopWatcher.effect.stop();
    });
    
    // px 转 rem 工具函数
    const px2rem = (px, targetDeviceType = null) => {
        const type = targetDeviceType || deviceType.value;
        let baseSize;
        
        switch (type) {
            case 'mobile':
                baseSize = config.mobileBaseSize;
                break;
            case 'tablet':
                baseSize = remState.base;
                break;
            case 'desktop':
                baseSize = config.pcBaseSize;
                break;
            default:
                baseSize = 16;
        }
        
        return px / baseSize;
    };
    
    // 获取实际像素值
    const getActualPx = (designPx) => {
        if (deviceType.value === 'mobile' && config.enableDPR) {
            return designPx * dpr.value;
        }
        return designPx;
    };
    
    // 样式计算
    const styles = computed(() => {
        const hairline = 1 / dpr.value;
        const type = deviceType.value;
        
        return {
            container: {
                padding: type === 'mobile' ? `${px2rem(32)}rem` : '2rem',
                maxWidth: type === 'mobile' ? 'none' : '1200px',
                margin: '0 auto'
            },
            
            hairlineBorder: {
                border: `${hairline}px solid #e0e0e0`
            },
            
            text: {
                fontSize: type === 'mobile' ? `${px2rem(28)}rem` : '1rem',
                lineHeight: type === 'mobile' ? '1.4' : '1.6'
            },
            
            button: {
                padding: type === 'mobile' ? `${px2rem(24)}rem ${px2rem(48)}rem` : '0.75rem 1.5rem',
                fontSize: type === 'mobile' ? `${px2rem(28)}rem` : '1rem',
                border: `${hairline}px solid #007bff`,
                borderRadius: type === 'mobile' ? `${px2rem(8)}rem` : '0.25rem'
            }
        };
    });
    
    return {
        deviceType,
        deviceWidth,
        dpr,
        remBase: computed(() => remState.base),
        remScale: computed(() => remState.scale),
        px2rem,
        getActualPx,
        styles,
        config
    };
}

/**
 * 响应式样式 Hook
 */
export function useResponsiveStyles(styleConfig = {}) {
    const { deviceType, px2rem, dpr } = useResponsiveRem();
    
    return computed(() => {
        const hairline = 1 / dpr.value;
        const type = deviceType.value;
        
        const baseStyles = {
            mobile: styleConfig.mobile || {},
            tablet: styleConfig.tablet || {},
            desktop: styleConfig.desktop || {}
        };
        
        const currentStyles = baseStyles[type] || {};
        
        // 自动转换 px 值为 rem
        const convertedStyles = {};
        Object.keys(currentStyles).forEach(key => {
            const value = currentStyles[key];
            if (typeof value === 'string' && value.endsWith('px')) {
                const pxValue = parseFloat(value);
                convertedStyles[key] = `${px2rem(pxValue)}rem`;
            } else {
                convertedStyles[key] = value;
            }
        });
        
        return {
            ...convertedStyles,
            '--hairline': `${hairline}px`,
            '--dpr': dpr.value
        };
    });
}

/**
 * 媒体查询 Hook
 */
export function useMediaQuery(query) {
    const matches = ref(false);
    
    onMounted(() => {
        if (typeof window === 'undefined') return;
        
        const mediaQuery = window.matchMedia(query);
        matches.value = mediaQuery.matches;
        
        const handleChange = (e) => {
            matches.value = e.matches;
        };
        
        if (mediaQuery.addListener) {
            mediaQuery.addListener(handleChange);
            onUnmounted(() => mediaQuery.removeListener(handleChange));
        } else if (mediaQuery.addEventListener) {
            mediaQuery.addEventListener('change', handleChange);
            onUnmounted(() => mediaQuery.removeEventListener('change', handleChange));
        }
    });
    
    return matches;
}

/**
 * Vue 插件
 */
export const DPRResponsiveRemPlugin = {
    install(app, options = {}) {
        const config = { ...DEFAULT_CONFIG, ...options };
        
        // 提供配置
        app.provide(DPR_CONFIG_KEY, config);
        
        // 全局属性
        app.config.globalProperties.$px2rem = (px, deviceType) => {
            const { px2rem } = useResponsiveRem();
            return px2rem(px, deviceType);
        };
        
        app.config.globalProperties.$dpr = computed(() => dprState.value);
        app.config.globalProperties.$deviceType = computed(() => deviceState.type);
        
        // 初始化
        if (typeof window !== 'undefined') {
            dprState.value = getDPR();
            deviceState.width = window.innerWidth;
            deviceState.height = window.innerHeight;
            deviceState.type = getDeviceType(window.innerWidth, config.breakpoints);
        }
    }
};

// 导出默认配置
export { DEFAULT_CONFIG };

/**
 * 使用示例：
 * 
 * // main.js
 * import { createApp } from 'vue';
 * import { DPRResponsiveRemPlugin } from './dpr-responsive-rem-vue';
 * import App from './App.vue';
 * 
 * const app = createApp(App);
 * app.use(DPRResponsiveRemPlugin, {
 *     mobileDesignWidth: 750,
 *     enableDPR: true
 * });
 * app.mount('#app');
 * 
 * // 组件中使用
 * <template>
 *   <div :style="styles.container">
 *     <h1 :style="customStyles">
 *       当前设备: {{ deviceType }}, DPR: {{ dpr }}
 *     </h1>
 *     <button :style="styles.button">
 *       响应式按钮
 *     </button>
 *   </div>
 * </template>
 * 
 * <script>
 * import { useResponsiveRem, useResponsiveStyles, useDPR } from './dpr-responsive-rem-vue';
 * 
 * export default {
 *   setup() {
 *     const { deviceType, px2rem, styles } = useResponsiveRem();
 *     const { dpr } = useDPR();
 *     
 *     const customStyles = useResponsiveStyles({
 *       mobile: {
 *         fontSize: '28px', // 自动转换为 rem
 *         padding: '32px'
 *       },
 *       desktop: {
 *         fontSize: '16px',
 *         padding: '24px'
 *       }
 *     });
 *     
 *     return {
 *       deviceType,
 *       dpr,
 *       styles,
 *       customStyles
 *     };
 *   }
 * };
 * </script>
 */
