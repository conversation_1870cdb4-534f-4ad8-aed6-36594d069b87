import { defineStore } from 'pinia'
import { apolloClient } from '@/apollo'
import { LOGIN_MUTATION, REGISTER_MUTATION, ME_QUERY, GET_USER_QUERY, UPDATE_USER_MUTATION } from '@/graphql/auth'
import type { User, LoginInput, CreateUserInput } from '@/types'

interface AuthState {
    user: User | null
    token: string | null
    isAuthenticated: boolean
    loading: boolean
}

export const useAuthStore = defineStore('auth', {
    state: (): AuthState => ({
        user: null,
        token: localStorage.getItem('token'),
        isAuthenticated: false,
        loading: false
    }),

    getters: {
        isAdmin: (state) => state.user?.role === 'ADMIN',
        isManager: (state) => state.user?.role === 'MANAGER' || state.user?.role === 'ADMIN',
        fullName: (state) => state.user ? `${state.user.firstName} ${state.user.lastName}` : '',
        initials: (state) => {
            if (!state.user) return ''
            return `${state.user.firstName[0]}${state.user.lastName[0]}`.toUpperCase()
        }
    },

    actions: {
        // 登录
        async login(input: LoginInput) {
            this.loading = true
            try {
                const { data } = await apolloClient.mutate({
                    mutation: LOGIN_MUTATION,
                    variables: { input }
                })

                if (data?.login) {
                    this.setAuth(data.login.token, data.login.user)
                    return { success: true, user: data.login.user }
                }

                return { success: false, message: '登录失败' }
            } catch (error: any) {
                console.error('Login error:', error)
                return {
                    success: false,
                    message: error.message || '登录失败，请检查用户名和密码'
                }
            } finally {
                this.loading = false
            }
        },

        // 注册
        async register(input: CreateUserInput) {
            this.loading = true
            try {
                const { data } = await apolloClient.mutate({
                    mutation: REGISTER_MUTATION,
                    variables: { input }
                })

                if (data?.register) {
                    this.setAuth(data.register.token, data.register.user)
                    return { success: true, user: data.register.user }
                }

                return { success: false, message: '注册失败' }
            } catch (error: any) {
                console.error('Register error:', error)
                return {
                    success: false,
                    message: error.message || '注册失败，请检查输入信息'
                }
            } finally {
                this.loading = false
            }
        },

        // 获取当前用户信息
        async fetchMe() {
            if (!this.token) return false

            this.loading = true
            try {
                const { data } = await apolloClient.query({
                    query: ME_QUERY,
                    fetchPolicy: 'network-only'
                })

                await apolloClient.query({
                    query: GET_USER_QUERY,
                    variables: { userId: "3" }
                })

                await apolloClient.mutate({
                    mutation: UPDATE_USER_MUTATION,
                    variables: {
                        id: "3",
                        input: {
                            username: 'Rex',
                            firstName: 'Rex',
                            lastName: 'Chan'
                        }
                    }
                })

                if (data?.me) {
                    this.user = data.me
                    this.isAuthenticated = true
                    return true
                } else {
                    this.logout()
                    return false
                }
            } catch (error) {
                console.error('Fetch me error:', error)
                this.logout()
                return false
            } finally {
                this.loading = false
            }
        },

        // 设置认证信息
        setAuth(token: string, user: User) {
            this.token = token
            this.user = user
            this.isAuthenticated = true
            localStorage.setItem('token', token)

            // 重置Apollo缓存
            apolloClient.resetStore()
        },

        // 登出
        logout() {
            this.user = null
            this.token = null
            this.isAuthenticated = false
            localStorage.removeItem('token')

            // 清除Apollo缓存
            apolloClient.clearStore()
        },

        // 更新用户信息
        updateUser(user: User) {
            this.user = user
        },

        // 初始化认证状态
        async initAuth() {
            if (this.token) {
                await this.fetchMe()
            }
        },

        // 检查权限
        hasRole(roles: string | string[]): boolean {
            if (!this.user) return false

            const userRole = this.user.role
            if (typeof roles === 'string') {
                return userRole === roles
            }

            return roles.includes(userRole)
        },

        // 检查是否可以访问资源
        canAccess(resource: string, action: string = 'read'): boolean {
            if (!this.user) return false

            const role = this.user.role

            // 管理员可以访问所有资源
            if (role === 'ADMIN') return true

            // 根据资源和操作检查权限
            switch (resource) {
                case 'users':
                    return role === 'ADMIN' || (role === 'MANAGER' && action === 'read')
                case 'projects':
                    return ['ADMIN', 'MANAGER'].includes(role) || action === 'read'
                case 'tasks':
                    return true // 所有用户都可以访问任务
                default:
                    return false
            }
        }
    }
})
