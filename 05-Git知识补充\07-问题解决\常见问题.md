# Git 常见问题解决

## 🚨 提交相关问题

### 问题1：提交信息写错了
```bash
# 场景：刚提交但发现提交信息有误

# 解决方案1：修改最后一次提交信息
git commit --amend -m "正确的提交信息"

# 解决方案2：如果已经推送，需要强制推送
git commit --amend -m "正确的提交信息"
git push --force-with-lease origin branch-name

# 解决方案3：修改更早的提交信息
git rebase -i HEAD~3  # 修改最近3个提交
# 将要修改的提交标记为 reword
```

### 问题2：提交了错误的文件
```bash
# 场景：不小心提交了不应该提交的文件

# 解决方案1：从最后一次提交中移除文件
git reset --soft HEAD~1
git reset HEAD unwanted-file.txt
git commit -m "原来的提交信息"

# 解决方案2：使用 git rm 移除文件
git rm --cached unwanted-file.txt
git commit --amend --no-edit

# 解决方案3：如果文件包含敏感信息
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch sensitive-file.txt' \
--prune-empty --tag-name-filter cat -- --all
```

### 问题3：忘记添加文件到提交
```bash
# 场景：提交后发现遗漏了文件

# 解决方案：添加文件并修改提交
git add forgotten-file.txt
git commit --amend --no-edit

# 如果已经推送
git add forgotten-file.txt
git commit --amend --no-edit
git push --force-with-lease origin branch-name
```

## 🌿 分支相关问题

### 问题4：在错误的分支上开发
```bash
# 场景：在 main 分支上开发了功能，应该在功能分支

# 解决方案1：创建新分支并重置 main
git branch feature-branch  # 创建分支保存当前工作
git reset --hard origin/main  # 重置 main 到远程状态
git checkout feature-branch  # 切换到功能分支

# 解决方案2：使用 stash
git stash  # 暂存当前修改
git checkout -b feature-branch  # 创建并切换到新分支
git stash pop  # 恢复修改
```

### 问题5：分支切换失败
```bash
# 错误信息：Your local changes would be overwritten by checkout

# 解决方案1：提交当前修改
git add .
git commit -m "WIP: 临时保存工作"
git checkout target-branch

# 解决方案2：使用 stash
git stash
git checkout target-branch
git stash pop  # 在新分支恢复修改

# 解决方案3：强制切换（丢失修改）
git checkout --force target-branch
```

### 问题6：删除了重要分支
```bash
# 场景：误删除了包含重要工作的分支

# 解决方案：使用 reflog 恢复
git reflog  # 查找被删除分支的最后提交
git checkout -b recovered-branch commit-hash

# 或者直接恢复分支
git branch recovered-branch commit-hash
```

## 🔄 合并和变基问题

### 问题7：合并冲突太复杂
```bash
# 场景：合并时出现大量复杂冲突

# 解决方案1：中止合并重新规划
git merge --abort
# 重新分析冲突原因，可能需要：
# - 先同步主分支
# - 分步骤合并
# - 寻求团队帮助

# 解决方案2：使用工具辅助
git mergetool  # 启动合并工具
# 或使用 IDE 的合并功能

# 解决方案3：选择策略性合并
git merge -X ours feature-branch    # 优先当前分支
git merge -X theirs feature-branch  # 优先功能分支
```

### 问题8：变基过程中出错
```bash
# 场景：变基过程中遇到问题想要停止

# 解决方案1：中止变基
git rebase --abort

# 解决方案2：跳过有问题的提交
git rebase --skip

# 解决方案3：解决冲突后继续
# 编辑冲突文件...
git add .
git rebase --continue
```

### 问题9：变基后历史混乱
```bash
# 场景：变基操作导致历史变得混乱

# 解决方案：使用 reflog 恢复
git reflog  # 查找变基前的状态
git reset --hard HEAD@{n}  # 恢复到变基前

# 预防措施：变基前创建备份分支
git branch backup-branch
git rebase main
# 如果出问题：git reset --hard backup-branch
```

## 📡 远程仓库问题

### 问题10：推送被拒绝
```bash
# 错误信息：Updates were rejected because the remote contains work

# 解决方案1：先拉取再推送
git pull origin main
git push origin main

# 解决方案2：使用变基拉取
git pull --rebase origin main
git push origin main

# 解决方案3：强制推送（谨慎使用）
git push --force-with-lease origin main
```

### 问题11：远程分支不存在
```bash
# 错误信息：fatal: couldn't find remote ref branch-name

# 解决方案1：更新远程引用
git fetch origin
git remote prune origin

# 解决方案2：检查分支名是否正确
git branch -r  # 查看远程分支
git ls-remote origin  # 查看远程引用
```

### 问题12：认证失败
```bash
# 错误信息：Authentication failed

# 解决方案1：更新凭据
git config --global credential.helper manager
git push origin main  # 会提示输入新凭据

# 解决方案2：使用 SSH 密钥
git remote set-<NAME_EMAIL>:user/repo.git

# 解决方案3：检查权限
# 确认有仓库的推送权限
```

## 🗂️ 文件和工作区问题

### 问题13：文件权限问题
```bash
# 场景：Git 跟踪文件权限变化导致不必要的修改

# 解决方案：忽略文件权限
git config core.filemode false

# 全局设置
git config --global core.filemode false
```

### 问题14：换行符问题
```bash
# 场景：不同操作系统的换行符导致文件显示为修改

# 解决方案：配置换行符处理
# Windows
git config --global core.autocrlf true

# macOS/Linux
git config --global core.autocrlf input

# 统一设置
git config --global core.autocrlf false
```

### 问题15：大文件问题
```bash
# 场景：误提交了大文件导致仓库臃肿

# 解决方案1：从历史中移除大文件
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch large-file.zip' \
--prune-empty --tag-name-filter cat -- --all

# 解决方案2：使用 BFG Repo-Cleaner
java -jar bfg.jar --delete-files large-file.zip
git reflog expire --expire=now --all && git gc --prune=now --aggressive

# 解决方案3：使用 Git LFS
git lfs track "*.zip"
git add .gitattributes
```

## 🔧 配置和环境问题

### 问题16：用户信息配置错误
```bash
# 场景：提交显示错误的作者信息

# 解决方案1：修改全局配置
git config --global user.name "正确的姓名"
git config --global user.email "<EMAIL>"

# 解决方案2：修改最后一次提交的作者
git commit --amend --author="正确姓名 <<EMAIL>>"

# 解决方案3：批量修改历史提交的作者
git filter-branch --env-filter '
if [ "$GIT_COMMITTER_EMAIL" = "<EMAIL>" ]
then
    export GIT_COMMITTER_NAME="正确姓名"
    export GIT_COMMITTER_EMAIL="<EMAIL>"
fi
if [ "$GIT_AUTHOR_EMAIL" = "<EMAIL>" ]
then
    export GIT_AUTHOR_NAME="正确姓名"
    export GIT_AUTHOR_EMAIL="<EMAIL>"
fi
' --tag-name-filter cat -- --branches --tags
```

### 问题17：编辑器配置问题
```bash
# 场景：Git 打开了不熟悉的编辑器

# 解决方案：配置熟悉的编辑器
git config --global core.editor "code --wait"  # VS Code
git config --global core.editor "vim"          # Vim
git config --global core.editor "nano"         # Nano

# 临时使用不同编辑器
GIT_EDITOR="nano" git commit
```

### 问题18：代理配置问题
```bash
# 场景：公司网络需要代理才能访问 Git 仓库

# 解决方案：配置代理
git config --global http.proxy http://proxy.company.com:8080
git config --global https.proxy https://proxy.company.com:8080

# 取消代理
git config --global --unset http.proxy
git config --global --unset https.proxy

# 为特定域名设置代理
git config --global http.https://github.com.proxy http://proxy:8080
```

## 🔍 诊断和调试

### 问题19：Git 命令执行缓慢
```bash
# 诊断步骤：
# 1. 检查仓库大小
du -sh .git

# 2. 检查对象数量
git count-objects -v

# 3. 运行垃圾回收
git gc --aggressive

# 4. 检查网络连接
git config --global http.postBuffer *********
```

### 问题20：找不到丢失的提交
```bash
# 解决方案：使用 reflog 和 fsck
# 1. 查看引用日志
git reflog

# 2. 查找悬空提交
git fsck --lost-found

# 3. 查看悬空提交
git show commit-hash

# 4. 恢复提交
git branch recovered-work commit-hash
```

## 💡 预防措施

### ✅ 最佳实践
```bash
# 1. 定期备份重要分支
git branch backup-$(date +%Y%m%d) important-branch

# 2. 使用 --force-with-lease 而不是 --force
git push --force-with-lease origin branch-name

# 3. 提交前检查状态
git status
git diff --staged

# 4. 使用 .gitignore 防止误提交
echo "*.log" >> .gitignore
echo "node_modules/" >> .gitignore

# 5. 配置有用的别名
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'
```

### 🛡️ 安全检查清单
```bash
# 提交前检查：
□ 检查要提交的文件：git status
□ 检查文件内容：git diff --staged
□ 确认提交信息：git commit -m "clear message"
□ 检查分支：git branch

# 推送前检查：
□ 确认目标分支：git branch -vv
□ 检查远程状态：git fetch && git status
□ 确认推送内容：git log origin/main..HEAD
□ 使用安全推送：git push --force-with-lease
```

---

**记住**: 大多数 Git 问题都有解决方案，关键是保持冷静，仔细分析问题，并选择合适的解决方法。当遇到复杂问题时，不要害怕寻求帮助或查阅文档！ 🚨
