/**
 * RESTful API 模拟服务
 * 提供完整的RESTful API模拟功能
 */

class RESTfulAPI {
    constructor() {
        this.baseURL = '/api';
        this.requestCount = 0;
        this.responseTimeSum = 0;
        this.successCount = 0;

        // 获取模拟数据
        this.mockUsers = window.mockData?.users || [];
        this.mockTasks = window.mockData?.tasks || [];
    }

    // 模拟网络延迟
    async simulateDelay(min = 300, max = 1000) {
        const delay = Math.random() * (max - min) + min;
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    // 模拟HTTP请求
    async request(method, url, data = null) {
        const startTime = Date.now();
        this.requestCount++;

        console.log(`🔄 RESTful API Request: ${method.toUpperCase()} ${url}`);

        try {
            await this.simulateDelay();

            let response;
            if (method === 'GET') {
                response = await this.handleGet(url);
            } else if (method === 'POST') {
                response = await this.handlePost(url, data);
            } else if (method === 'PUT') {
                response = await this.handlePut(url, data);
            } else if (method === 'DELETE') {
                response = await this.handleDelete(url);
            } else {
                throw new Error(`Unsupported method: ${method}`);
            }

            const duration = Date.now() - startTime;
            this.responseTimeSum += duration;
            this.successCount++;

            console.log(`✅ RESTful API Response: 200 (${duration}ms)`);
            return response;

        } catch (error) {
            const duration = Date.now() - startTime;
            this.responseTimeSum += duration;

            console.error(`❌ RESTful API Error: ${error.message} (${duration}ms)`);
            throw error;
        }
    }

    // GET请求处理
    async handleGet(url) {
        if (url === '/tasks') {
            return { data: this.mockTasks };
        } else if (url.startsWith('/tasks/')) {
            const id = parseInt(url.split('/')[2]);
            const task = this.mockTasks.find(t => t.id === id);
            if (!task) throw new Error('Task not found');
            return { data: task };
        } else if (url === '/users') {
            return { data: this.mockUsers };
        } else if (url.startsWith('/users/')) {
            const id = parseInt(url.split('/')[2]);
            const user = this.mockUsers.find(u => u.id === id);
            if (!user) throw new Error('User not found');
            return { data: user };
        }
        throw new Error('Endpoint not found');
    }

    // POST请求处理
    async handlePost(url, data) {
        if (url === '/tasks') {
            const newTask = {
                id: Math.max(...this.mockTasks.map(t => t.id), 0) + 1,
                title: data.title,
                description: data.description || '',
                priority: data.priority || 'medium',
                status: 'todo',
                dueDate: data.dueDate || null,
                assignee: data.assignee || '',
                tags: data.tags || [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                completedAt: null
            };
            this.mockTasks.push(newTask);
            return { data: newTask };
        } else if (url === '/users') {
            const newUser = {
                id: Math.max(...this.mockUsers.map(u => u.id), 0) + 1,
                name: data.name,
                email: data.email,
                role: data.role || 'user',
                bio: data.bio || '',
                avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`,
                status: 'active',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            this.mockUsers.push(newUser);
            return { data: newUser };
        }
        throw new Error('Endpoint not found');
    }

    // PUT请求处理
    async handlePut(url, data) {
        if (url.startsWith('/tasks/')) {
            const id = parseInt(url.split('/')[2]);
            const taskIndex = this.mockTasks.findIndex(t => t.id === id);
            if (taskIndex === -1) throw new Error('Task not found');

            const updatedTask = {
                ...this.mockTasks[taskIndex],
                ...data,
                updatedAt: new Date().toISOString()
            };

            if (data.status === 'completed' && this.mockTasks[taskIndex].status !== 'completed') {
                updatedTask.completedAt = new Date().toISOString();
            }

            this.mockTasks[taskIndex] = updatedTask;
            return { data: updatedTask };
        } else if (url.startsWith('/users/')) {
            const id = parseInt(url.split('/')[2]);
            const userIndex = this.mockUsers.findIndex(u => u.id === id);
            if (userIndex === -1) throw new Error('User not found');

            const updatedUser = {
                ...this.mockUsers[userIndex],
                ...data,
                updated_at: new Date().toISOString()
            };

            this.mockUsers[userIndex] = updatedUser;
            return { data: updatedUser };
        }
        throw new Error('Endpoint not found');
    }

    // DELETE请求处理
    async handleDelete(url) {
        if (url.startsWith('/tasks/')) {
            const id = parseInt(url.split('/')[2]);
            const taskIndex = this.mockTasks.findIndex(t => t.id === id);
            if (taskIndex === -1) throw new Error('Task not found');

            this.mockTasks.splice(taskIndex, 1);
            return { data: { success: true } };
        } else if (url.startsWith('/users/')) {
            const id = parseInt(url.split('/')[2]);
            const userIndex = this.mockUsers.findIndex(u => u.id === id);
            if (userIndex === -1) throw new Error('User not found');

            this.mockUsers.splice(userIndex, 1);
            return { data: { success: true } };
        }
        throw new Error('Endpoint not found');
    }

    // 便捷方法
    async get(url) {
        return this.request('GET', url);
    }

    async post(url, data) {
        return this.request('POST', url, data);
    }

    async put(url, data) {
        return this.request('PUT', url, data);
    }

    async delete(url) {
        return this.request('DELETE', url);
    }

    // 获取统计信息
    getStats() {
        return {
            totalRequests: this.requestCount,
            successCount: this.successCount,
            averageResponseTime: this.requestCount > 0 ? Math.round(this.responseTimeSum / this.requestCount) : 0,
            successRate: this.requestCount > 0 ? Math.round((this.successCount / this.requestCount) * 100) : 100
        };
    }


}

// 创建全局实例
window.RESTfulAPI = RESTfulAPI;
