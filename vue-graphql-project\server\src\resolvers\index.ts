import { PubSub } from 'graphql-subscriptions';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { GraphQLScalarType } from 'graphql';
import { Kind } from 'graphql/language';
import { db } from '../data/mockData';
import {
  User,
  Project,
  Task,
  Comment,
  Context,
  CreateUserInput,
  UpdateUserInput,
  CreateProjectInput,
  UpdateProjectInput,
  CreateTaskInput,
  UpdateTaskInput,
  CreateCommentInput,
  LoginInput,
  TaskFilter,
  ProjectFilter,
  PaginationInput,
  TaskStatus,
  ProjectStatus,
  UserRole
} from '../models';

const pubsub = new PubSub();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// DateTime标量类型
const DateTimeScalar = new GraphQLScalarType({
  name: 'DateTime',
  description: 'Date custom scalar type',
  serialize(value: any) {
    return value instanceof Date ? value.toISOString() : null;
  },
  parseValue(value: any) {
    return new Date(value);
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      return new Date(ast.value);
    }
    return null;
  },
});

// 工具函数
const generateToken = (userId: string): string => {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
};

const hashPassword = (password: string): string => {
  return bcrypt.hashSync(password, 10);
};

const comparePassword = (password: string, hash: string): boolean => {
  return bcrypt.compareSync(password, hash);
};

// 分页工具函数
const paginate = <T>(
  items: T[],
  pagination: PaginationInput = {}
): { items: T[]; paginationInfo: any } => {
  const page = pagination.page || 1;
  const limit = pagination.limit || 10;
  const offset = (page - 1) * limit;
  const total = items.length;
  const pages = Math.ceil(total / limit);
  
  const paginatedItems = items.slice(offset, offset + limit);
  
  return {
    items: paginatedItems,
    paginationInfo: {
      page,
      limit,
      total,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1
    }
  };
};

// 过滤工具函数
const filterTasks = (tasks: Task[], filter: TaskFilter = {}): Task[] => {
  return tasks.filter(task => {
    if (filter.status && task.status !== filter.status) return false;
    if (filter.priority && task.priority !== filter.priority) return false;
    if (filter.assigneeId && task.assigneeId !== filter.assigneeId) return false;
    if (filter.projectId && task.projectId !== filter.projectId) return false;
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      if (!task.title.toLowerCase().includes(searchLower) &&
          !task.description?.toLowerCase().includes(searchLower)) {
        return false;
      }
    }
    return true;
  });
};

const filterProjects = (projects: Project[], filter: ProjectFilter = {}): Project[] => {
  return projects.filter(project => {
    if (filter.status && project.status !== filter.status) return false;
    if (filter.priority && project.priority !== filter.priority) return false;
    if (filter.ownerId && project.ownerId !== filter.ownerId) return false;
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      if (!project.name.toLowerCase().includes(searchLower) &&
          !project.description?.toLowerCase().includes(searchLower)) {
        return false;
      }
    }
    return true;
  });
};

// 认证中间件
const requireAuth = (context: Context): User => {
  if (!context.user) {
    throw new Error('Authentication required');
  }
  return context.user;
};

const requireRole = (context: Context, roles: UserRole[]): User => {
  const user = requireAuth(context);
  if (!roles.includes(user.role)) {
    throw new Error('Insufficient permissions');
  }
  return user;
};

export const resolvers = {
  DateTime: DateTimeScalar,

  // 类型解析器
  User: {
    tasks: (parent: User): Task[] => {
      return db.getTasks().filter(task => task.creatorId === parent.id);
    },
    assignedTasks: (parent: User): Task[] => {
      return db.getTasks().filter(task => task.assigneeId === parent.id);
    },
    projects: (parent: User): Project[] => {
      return db.getProjects().filter(project => 
        project.ownerId === parent.id || project.memberIds.includes(parent.id)
      );
    },
    comments: (parent: User): Comment[] => {
      return db.getComments().filter(comment => comment.authorId === parent.id);
    }
  },

  Project: {
    owner: (parent: Project): User | null => {
      return db.getUserById(parent.ownerId) || null;
    },
    members: (parent: Project): User[] => {
      return parent.memberIds
        .map(id => db.getUserById(id))
        .filter((user): user is User => user !== undefined);
    },
    tasks: (parent: Project): Task[] => {
      return db.getTasks().filter(task => task.projectId === parent.id);
    }
  },

  Task: {
    project: (parent: Task): Project | null => {
      return db.getProjectById(parent.projectId) || null;
    },
    creator: (parent: Task): User | null => {
      return db.getUserById(parent.creatorId) || null;
    },
    assignee: (parent: Task): User | null => {
      return parent.assigneeId ? db.getUserById(parent.assigneeId) || null : null;
    },
    comments: (parent: Task): Comment[] => {
      return db.getComments().filter(comment => comment.taskId === parent.id);
    }
  },

  Comment: {
    author: (parent: Comment): User | null => {
      return db.getUserById(parent.authorId) || null;
    },
    task: (parent: Comment): Task | null => {
      return db.getTaskById(parent.taskId) || null;
    }
  },

  // 查询解析器
  Query: {
    me: (_: any, __: any, context: Context): User | null => {
      return context.user || null;
    },

    users: (_: any, args: { pagination?: PaginationInput; search?: string }) => {
      let users = db.getUsers();
      
      if (args.search) {
        const searchLower = args.search.toLowerCase();
        users = users.filter(user => 
          user.username.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          user.firstName.toLowerCase().includes(searchLower) ||
          user.lastName.toLowerCase().includes(searchLower)
        );
      }
      
      const { items, paginationInfo } = paginate(users, args.pagination);
      return {
        users: items,
        pagination: paginationInfo
      };
    },

    user: (_: any, args: { id: string }): User | null => {
      return db.getUserById(args.id) || null;
    },

    projects: (_: any, args: { filter?: ProjectFilter; pagination?: PaginationInput }) => {
      const filteredProjects = filterProjects(db.getProjects(), args.filter);
      const { items, paginationInfo } = paginate(filteredProjects, args.pagination);
      return {
        projects: items,
        pagination: paginationInfo
      };
    },

    project: (_: any, args: { id: string }): Project | null => {
      return db.getProjectById(args.id) || null;
    },

    myProjects: (_: any, __: any, context: Context): Project[] => {
      const user = requireAuth(context);
      return db.getProjects().filter(project => 
        project.ownerId === user.id || project.memberIds.includes(user.id)
      );
    },

    tasks: (_: any, args: { filter?: TaskFilter; pagination?: PaginationInput }) => {
      const filteredTasks = filterTasks(db.getTasks(), args.filter);
      const { items, paginationInfo } = paginate(filteredTasks, args.pagination);
      return {
        tasks: items,
        pagination: paginationInfo
      };
    },

    task: (_: any, args: { id: string }): Task | null => {
      return db.getTaskById(args.id) || null;
    },

    myTasks: (_: any, __: any, context: Context): Task[] => {
      const user = requireAuth(context);
      return db.getTasks().filter(task => 
        task.creatorId === user.id || task.assigneeId === user.id
      );
    },

    dashboardStats: () => {
      const tasks = db.getTasks();
      const projects = db.getProjects();
      const users = db.getUsers();

      const taskStats = {
        total: tasks.length,
        todo: tasks.filter(t => t.status === TaskStatus.TODO).length,
        inProgress: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length,
        inReview: tasks.filter(t => t.status === TaskStatus.IN_REVIEW).length,
        testing: tasks.filter(t => t.status === TaskStatus.TESTING).length,
        done: tasks.filter(t => t.status === TaskStatus.DONE).length,
        cancelled: tasks.filter(t => t.status === TaskStatus.CANCELLED).length
      };

      const projectStats = {
        total: projects.length,
        planning: projects.filter(p => p.status === ProjectStatus.PLANNING).length,
        active: projects.filter(p => p.status === ProjectStatus.ACTIVE).length,
        onHold: projects.filter(p => p.status === ProjectStatus.ON_HOLD).length,
        completed: projects.filter(p => p.status === ProjectStatus.COMPLETED).length,
        cancelled: projects.filter(p => p.status === ProjectStatus.CANCELLED).length
      };

      const recentTasks = tasks
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 5);

      const recentProjects = projects
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 3);

      return {
        taskStats,
        projectStats,
        userCount: users.length,
        recentTasks,
        recentProjects
      };
    },

    taskStats: (_: any, args: { projectId?: string }) => {
      let tasks = db.getTasks();
      if (args.projectId) {
        tasks = tasks.filter(task => task.projectId === args.projectId);
      }

      return {
        total: tasks.length,
        todo: tasks.filter(t => t.status === TaskStatus.TODO).length,
        inProgress: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length,
        inReview: tasks.filter(t => t.status === TaskStatus.IN_REVIEW).length,
        testing: tasks.filter(t => t.status === TaskStatus.TESTING).length,
        done: tasks.filter(t => t.status === TaskStatus.DONE).length,
        cancelled: tasks.filter(t => t.status === TaskStatus.CANCELLED).length
      };
    },

    projectStats: () => {
      const projects = db.getProjects();
      return {
        total: projects.length,
        planning: projects.filter(p => p.status === ProjectStatus.PLANNING).length,
        active: projects.filter(p => p.status === ProjectStatus.ACTIVE).length,
        onHold: projects.filter(p => p.status === ProjectStatus.ON_HOLD).length,
        completed: projects.filter(p => p.status === ProjectStatus.COMPLETED).length,
        cancelled: projects.filter(p => p.status === ProjectStatus.CANCELLED).length
      };
    }
  },

  // 变更解析器
  Mutation: {
    // 认证
    register: async (_: any, args: { input: CreateUserInput }) => {
      const { input } = args;

      // 检查邮箱是否已存在
      const existingUser = db.getUserByEmail(input.email);
      if (existingUser) {
        throw new Error('Email already exists');
      }

      // 创建用户
      const hashedPassword = hashPassword(input.password);
      const user = db.createUser({
        ...input,
        password: hashedPassword,
        role: input.role || UserRole.DEVELOPER,
        status: 'ACTIVE' as any
      });

      const token = generateToken(user.id);

      return { token, user };
    },

    login: async (_: any, args: { input: LoginInput }) => {
      const { email, password } = args.input;

      const user = db.getUserByEmail(email);
      if (!user) {
        throw new Error('Invalid email or password');
      }

      const isValidPassword = comparePassword(password, user.password);
      if (!isValidPassword) {
        throw new Error('Invalid email or password');
      }

      const token = generateToken(user.id);

      return { token, user };
    },

    // 用户管理
    createUser: (_: any, args: { input: CreateUserInput }, context: Context) => {
      requireRole(context, [UserRole.ADMIN]);

      const existingUser = db.getUserByEmail(args.input.email);
      if (existingUser) {
        throw new Error('Email already exists');
      }

      const hashedPassword = hashPassword(args.input.password);
      const user = db.createUser({
        ...args.input,
        password: hashedPassword,
        role: args.input.role || UserRole.DEVELOPER,
        status: 'ACTIVE' as any
      });

      return user;
    },

    updateUser: (_: any, args: { id: string; input: UpdateUserInput }, context: Context) => {
      const currentUser = requireAuth(context);

      // 只有管理员或用户本人可以更新
      if (currentUser.role !== UserRole.ADMIN && currentUser.id !== args.id) {
        throw new Error('Insufficient permissions');
      }

      const user = db.updateUser(args.id, args.input);
      if (!user) {
        throw new Error('User not found');
      }

      return user;
    },

    deleteUser: (_: any, args: { id: string }, context: Context) => {
      requireRole(context, [UserRole.ADMIN]);

      const success = db.deleteUser(args.id);
      if (!success) {
        throw new Error('User not found');
      }

      return true;
    },

    // 项目管理
    createProject: (_: any, args: { input: CreateProjectInput }, context: Context) => {
      const user = requireAuth(context);

      const project = db.createProject({
        ...args.input,
        status: ProjectStatus.PLANNING,
        ownerId: user.id,
        memberIds: args.input.memberIds || []
      });

      pubsub.publish('PROJECT_CREATED', { projectCreated: project });

      return project;
    },

    updateProject: (_: any, args: { id: string; input: UpdateProjectInput }, context: Context) => {
      const user = requireAuth(context);

      const existingProject = db.getProjectById(args.id);
      if (!existingProject) {
        throw new Error('Project not found');
      }

      // 只有项目所有者或管理员可以更新
      if (existingProject.ownerId !== user.id && user.role !== UserRole.ADMIN) {
        throw new Error('Insufficient permissions');
      }

      const project = db.updateProject(args.id, args.input);
      if (!project) {
        throw new Error('Project not found');
      }

      pubsub.publish('PROJECT_UPDATED', { projectUpdated: project });

      return project;
    },

    deleteProject: (_: any, args: { id: string }, context: Context) => {
      const user = requireAuth(context);

      const existingProject = db.getProjectById(args.id);
      if (!existingProject) {
        throw new Error('Project not found');
      }

      // 只有项目所有者或管理员可以删除
      if (existingProject.ownerId !== user.id && user.role !== UserRole.ADMIN) {
        throw new Error('Insufficient permissions');
      }

      const success = db.deleteProject(args.id);
      if (!success) {
        throw new Error('Project not found');
      }

      return true;
    },

    addProjectMember: (_: any, args: { projectId: string; userId: string }, context: Context) => {
      const user = requireAuth(context);

      const project = db.getProjectById(args.projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // 只有项目所有者或管理员可以添加成员
      if (project.ownerId !== user.id && user.role !== UserRole.ADMIN) {
        throw new Error('Insufficient permissions');
      }

      if (!project.memberIds.includes(args.userId)) {
        project.memberIds.push(args.userId);
        const updatedProject = db.updateProject(args.projectId, { memberIds: project.memberIds });
        return updatedProject!;
      }

      return project;
    },

    removeProjectMember: (_: any, args: { projectId: string; userId: string }, context: Context) => {
      const user = requireAuth(context);

      const project = db.getProjectById(args.projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // 只有项目所有者或管理员可以移除成员
      if (project.ownerId !== user.id && user.role !== UserRole.ADMIN) {
        throw new Error('Insufficient permissions');
      }

      const memberIndex = project.memberIds.indexOf(args.userId);
      if (memberIndex > -1) {
        project.memberIds.splice(memberIndex, 1);
        const updatedProject = db.updateProject(args.projectId, { memberIds: project.memberIds });
        return updatedProject!;
      }

      return project;
    },

    // 任务管理
    createTask: (_: any, args: { input: CreateTaskInput }, context: Context) => {
      const user = requireAuth(context);

      const project = db.getProjectById(args.input.projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // 检查用户是否是项目成员
      if (project.ownerId !== user.id && !project.memberIds.includes(user.id) && user.role !== UserRole.ADMIN) {
        throw new Error('You are not a member of this project');
      }

      const task = db.createTask({
        ...args.input,
        status: TaskStatus.TODO,
        creatorId: user.id,
        tags: args.input.tags || []
      });

      pubsub.publish('TASK_CREATED', { taskCreated: task });

      return task;
    },

    updateTask: (_: any, args: { id: string; input: UpdateTaskInput }, context: Context) => {
      const user = requireAuth(context);

      const existingTask = db.getTaskById(args.id);
      if (!existingTask) {
        throw new Error('Task not found');
      }

      const project = db.getProjectById(existingTask.projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // 检查权限
      const canUpdate = existingTask.creatorId === user.id ||
                       existingTask.assigneeId === user.id ||
                       project.ownerId === user.id ||
                       project.memberIds.includes(user.id) ||
                       user.role === UserRole.ADMIN;

      if (!canUpdate) {
        throw new Error('Insufficient permissions');
      }

      const task = db.updateTask(args.id, args.input);
      if (!task) {
        throw new Error('Task not found');
      }

      pubsub.publish('TASK_UPDATED', { taskUpdated: task });

      return task;
    },

    deleteTask: (_: any, args: { id: string }, context: Context) => {
      const user = requireAuth(context);

      const existingTask = db.getTaskById(args.id);
      if (!existingTask) {
        throw new Error('Task not found');
      }

      const project = db.getProjectById(existingTask.projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // 只有任务创建者、项目所有者或管理员可以删除
      if (existingTask.creatorId !== user.id &&
          project.ownerId !== user.id &&
          user.role !== UserRole.ADMIN) {
        throw new Error('Insufficient permissions');
      }

      const success = db.deleteTask(args.id);
      if (!success) {
        throw new Error('Task not found');
      }

      pubsub.publish('TASK_DELETED', { taskDeleted: args.id });

      return true;
    },

    // 评论管理
    createComment: (_: any, args: { input: CreateCommentInput }, context: Context) => {
      const user = requireAuth(context);

      const task = db.getTaskById(args.input.taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      const project = db.getProjectById(task.projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // 检查用户是否是项目成员
      if (project.ownerId !== user.id && !project.memberIds.includes(user.id) && user.role !== UserRole.ADMIN) {
        throw new Error('You are not a member of this project');
      }

      const comment = db.createComment({
        ...args.input,
        authorId: user.id
      });

      pubsub.publish('COMMENT_ADDED', {
        commentAdded: comment,
        taskId: args.input.taskId
      });

      return comment;
    },

    deleteComment: (_: any, args: { id: string }, context: Context) => {
      const user = requireAuth(context);

      const existingComment = db.getCommentById(args.id);
      if (!existingComment) {
        throw new Error('Comment not found');
      }

      // 只有评论作者或管理员可以删除
      if (existingComment.authorId !== user.id && user.role !== UserRole.ADMIN) {
        throw new Error('Insufficient permissions');
      }

      const success = db.deleteComment(args.id);
      if (!success) {
        throw new Error('Comment not found');
      }

      return true;
    }
  },

  // 订阅解析器
  Subscription: {
    taskCreated: {
      subscribe: () => pubsub.asyncIterator(['TASK_CREATED'])
    },
    taskUpdated: {
      subscribe: () => pubsub.asyncIterator(['TASK_UPDATED'])
    },
    taskDeleted: {
      subscribe: () => pubsub.asyncIterator(['TASK_DELETED'])
    },
    projectCreated: {
      subscribe: () => pubsub.asyncIterator(['PROJECT_CREATED'])
    },
    projectUpdated: {
      subscribe: () => pubsub.asyncIterator(['PROJECT_UPDATED'])
    },
    commentAdded: {
      subscribe: (_: any, args: { taskId: string }) => {
        return pubsub.asyncIterator(['COMMENT_ADDED']);
      },
      resolve: (payload: any, args: { taskId: string }) => {
        if (payload.taskId === args.taskId) {
          return payload.commentAdded;
        }
        return null;
      }
    },
    userStatusChanged: {
      subscribe: () => pubsub.asyncIterator(['USER_STATUS_CHANGED'])
    }
  }
};
