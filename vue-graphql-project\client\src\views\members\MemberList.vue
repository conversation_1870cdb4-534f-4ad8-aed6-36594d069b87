<template>
	<div class="member-list">
		<!-- 页面头部 -->
		<div class="page-header">
			<div class="header-content">
				<h1 class="page-title">
					<el-icon><UserFilled /></el-icon>
					成员信息
				</h1>
				<p class="page-description">查看团队成员的详细信息</p>
			</div>

			<!-- 搜索和筛选 -->
			<div class="header-actions">
				<el-input
					v-model="searchQuery"
					placeholder="搜索成员..."
					prefix-icon="Search"
					clearable
					style="width: 300px"
					@input="handleSearch"
				/>
				<el-select
					v-model="selectedRole"
					placeholder="筛选角色"
					clearable
					style="width: 150px; margin-left: 12px"
					@change="handleRoleFilter"
				>
					<el-option label="全部" value="" />
					<el-option label="管理员" value="ADMIN" />
					<el-option label="项目经理" value="MANAGER" />
					<el-option label="开发者" value="DEVELOPER" />
					<el-option label="设计师" value="DESIGNER" />
					<el-option label="测试员" value="TESTER" />
				</el-select>
			</div>
		</div>

		<!-- 成员卡片网格 -->
		<div class="member-grid" v-loading="loading">
			<div
				v-for="member in filteredMembers"
				:key="member.id"
				class="member-card"
			>
				<el-card shadow="hover" class="member-card-content">
					<!-- 成员头像和基本信息 -->
					<div class="member-header">
						<el-avatar
							:size="80"
							:src="member.avatar"
							class="member-avatar"
						>
							{{ member.firstName[0] + member.lastName[0] }}
						</el-avatar>

						<div class="member-info">
							<h3 class="member-name">
								{{ member.firstName }} {{ member.lastName }}
							</h3>
							<p class="member-username">
								@{{ member.username }}
							</p>
							<el-tag
								:type="getRoleTagType(member.role)"
								size="small"
								class="member-role"
							>
								{{ getRoleText(member.role) }}
							</el-tag>
						</div>

						<!-- 状态指示器 -->
						<div class="member-status">
							<el-tag
								:type="
									member.status === 'ACTIVE'
										? 'success'
										: 'danger'
								"
								size="small"
								effect="plain"
							>
								{{
									member.status === 'ACTIVE' ? '在线' : '离线'
								}}
							</el-tag>
						</div>
					</div>

					<!-- 联系信息 -->
					<div class="member-contact">
						<div class="contact-item">
							<el-icon><Message /></el-icon>
							<span>{{ member.email }}</span>
						</div>
					</div>

					<!-- 统计信息 -->
					<div class="member-stats">
						<div class="stat-item">
							<span class="stat-number">{{
								member.tasks?.length || 0
							}}</span>
							<span class="stat-label">创建任务</span>
						</div>
						<div class="stat-item">
							<span class="stat-number">{{
								member.assignedTasks?.length || 0
							}}</span>
							<span class="stat-label">分配任务</span>
						</div>
						<div class="stat-item">
							<span class="stat-number">{{
								member.projects?.length || 0
							}}</span>
							<span class="stat-label">参与项目</span>
						</div>
					</div>

					<!-- 操作按钮 -->
					<div class="member-actions">
						<el-button
							type="primary"
							size="small"
							@click="viewMemberDetail(member)"
						>
							查看详情
						</el-button>
						<el-button
							type="default"
							size="small"
							@click="sendMessage(member)"
						>
							发送消息
						</el-button>
					</div>

					<!-- 加入时间 -->
					<div class="member-footer">
						<span class="join-date">
							加入时间：{{ formatDate(member.createdAt) }}
						</span>
					</div>
				</el-card>
			</div>
		</div>

		<!-- 空状态 -->
		<el-empty
			v-if="!loading && filteredMembers.length === 0"
			description="没有找到匹配的成员"
			:image-size="200"
		/>

		<!-- 分页 -->
		<div class="pagination-wrapper" v-if="pagination.total > 0">
			<el-pagination
				v-model:current-page="pagination.page"
				v-model:page-size="pagination.limit"
				:total="pagination.total"
				:page-sizes="[12, 24, 48, 96]"
				layout="total, sizes, prev, pager, next, jumper"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 成员详情对话框 -->
		<el-dialog
			v-model="showMemberDetail"
			:title="`${selectedMember?.firstName} ${selectedMember?.lastName} 的详细信息`"
			width="800px"
			:before-close="closeMemberDetail"
		>
			<div v-if="selectedMember" class="member-detail">
				<!-- 基本信息 -->
				<div class="detail-section">
					<h3>基本信息</h3>
					<el-descriptions :column="2" border>
						<el-descriptions-item label="姓名">
							{{ selectedMember.firstName }}
							{{ selectedMember.lastName }}
						</el-descriptions-item>
						<el-descriptions-item label="用户名">
							@{{ selectedMember.username }}
						</el-descriptions-item>
						<el-descriptions-item label="邮箱">
							{{ selectedMember.email }}
						</el-descriptions-item>
						<el-descriptions-item label="角色">
							<el-tag :type="getRoleTagType(selectedMember.role)">
								{{ getRoleText(selectedMember.role) }}
							</el-tag>
						</el-descriptions-item>
						<el-descriptions-item label="状态">
							<el-tag
								:type="
									selectedMember.status === 'ACTIVE'
										? 'success'
										: 'danger'
								"
							>
								{{
									selectedMember.status === 'ACTIVE'
										? '活跃'
										: '非活跃'
								}}
							</el-tag>
						</el-descriptions-item>
						<el-descriptions-item label="加入时间">
							{{ formatDate(selectedMember.createdAt) }}
						</el-descriptions-item>
					</el-descriptions>
				</div>

				<!-- 工作统计 -->
				<div class="detail-section">
					<h3>工作统计</h3>
					<div class="stats-grid">
						<div class="stat-card">
							<div class="stat-icon">📝</div>
							<div class="stat-content">
								<div class="stat-number">
									{{ selectedMember.tasks?.length || 0 }}
								</div>
								<div class="stat-label">创建的任务</div>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">📋</div>
							<div class="stat-content">
								<div class="stat-number">
									{{
										selectedMember.assignedTasks?.length ||
										0
									}}
								</div>
								<div class="stat-label">分配的任务</div>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">📁</div>
							<div class="stat-content">
								<div class="stat-number">
									{{ selectedMember.projects?.length || 0 }}
								</div>
								<div class="stat-label">参与的项目</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 最近任务 -->
				<div
					class="detail-section"
					v-if="
						selectedMember.tasks && selectedMember.tasks.length > 0
					"
				>
					<h3>最近创建的任务</h3>
					<div class="task-list">
						<div
							v-for="task in selectedMember.tasks.slice(0, 5)"
							:key="task.id"
							class="task-item"
						>
							<div class="task-info">
								<div class="task-title">{{ task.title }}</div>
								<div class="task-meta">
									<el-tag
										size="small"
										:type="getTaskStatusType(task.status)"
									>
										{{ getTaskStatusText(task.status) }}
									</el-tag>
									<el-tag
										size="small"
										:type="getPriorityType(task.priority)"
									>
										{{ getPriorityText(task.priority) }}
									</el-tag>
									<span class="task-project">{{
										task.project?.name
									}}</span>
								</div>
							</div>
							<div class="task-date">
								{{
									task.dueDate
										? formatDate(task.dueDate)
										: '无截止日期'
								}}
							</div>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<el-button @click="closeMemberDetail">关闭</el-button>
				<el-button type="primary" @click="sendMessage(selectedMember)">
					发送消息
				</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuery } from '@vue/apollo-composable'
import { ElMessage, ElMessageBox } from 'element-plus'
import { GET_USERS_QUERY } from '@/graphql/users'
import type { User } from '@/types'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const selectedRole = ref('')
const showMemberDetail = ref(false)
const selectedMember = ref<User | null>(null)
const pagination = ref({
	page: 1,
	limit: 12,
	total: 0,
})

// GraphQL 查询
const { result, loading, refetch } = useQuery(GET_USERS_QUERY, () => ({
	pagination: {
		page: pagination.value.page,
		limit: pagination.value.limit,
	},
	search: searchQuery.value || undefined,
}))

// 计算属性
const members = computed(() => result.value?.users?.users || [])
const filteredMembers = computed(() => {
	let filtered = members.value

	if (selectedRole.value) {
		filtered = filtered.filter(
			(member: User) => member.role === selectedRole.value
		)
	}

	return filtered
})

// 监听查询结果变化
const updatePagination = () => {
	if (result.value?.users?.pagination) {
		pagination.value.total = result.value.users.pagination.total
	}
}

// 方法
const handleSearch = () => {
	pagination.value.page = 1
	refetch()
}

const handleRoleFilter = () => {
	// 角色筛选在前端进行，不需要重新查询
}

const handleSizeChange = (size: number) => {
	pagination.value.limit = size
	pagination.value.page = 1
	refetch()
}

const handleCurrentChange = (page: number) => {
	pagination.value.page = page
	refetch()
}

const getRoleTagType = (role: string) => {
	const roleTypes: Record<string, string> = {
		ADMIN: 'danger',
		MANAGER: 'warning',
		DEVELOPER: 'primary',
		DESIGNER: 'success',
		TESTER: 'info',
	}
	return roleTypes[role] || 'default'
}

const getRoleText = (role: string) => {
	const roleTexts: Record<string, string> = {
		ADMIN: '管理员',
		MANAGER: '项目经理',
		DEVELOPER: '开发者',
		DESIGNER: '设计师',
		TESTER: '测试员',
	}
	return roleTexts[role] || role
}

const formatDate = (dateString: string) => {
	return new Date(dateString).toLocaleDateString('zh-CN')
}

const viewMemberDetail = (member: User) => {
	selectedMember.value = member
	showMemberDetail.value = true
}

const closeMemberDetail = () => {
	showMemberDetail.value = false
	selectedMember.value = null
}

const sendMessage = (member: User) => {
	ElMessage.info(`向 ${member.firstName} ${member.lastName} 发送消息`)
}

// 任务状态相关方法
const getTaskStatusType = (status: string) => {
	const statusTypes: Record<string, string> = {
		TODO: 'info',
		IN_PROGRESS: 'warning',
		DONE: 'success',
		CANCELLED: 'danger',
	}
	return statusTypes[status] || 'default'
}

const getTaskStatusText = (status: string) => {
	const statusTexts: Record<string, string> = {
		TODO: '待办',
		IN_PROGRESS: '进行中',
		DONE: '已完成',
		CANCELLED: '已取消',
	}
	return statusTexts[status] || status
}

const getPriorityType = (priority: string) => {
	const priorityTypes: Record<string, string> = {
		LOW: 'success',
		MEDIUM: 'warning',
		HIGH: 'danger',
		URGENT: 'danger',
	}
	return priorityTypes[priority] || 'default'
}

const getPriorityText = (priority: string) => {
	const priorityTexts: Record<string, string> = {
		LOW: '低',
		MEDIUM: '中',
		HIGH: '高',
		URGENT: '紧急',
	}
	return priorityTexts[priority] || priority
}

// 生命周期
onMounted(() => {
	updatePagination()
})
</script>

<style lang="scss" scoped>
.member-list {
	padding: 24px;
	background: var(--el-bg-color-page);
	min-height: calc(100vh - 60px);
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24px;
	padding: 24px;
	background: white;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

	.header-content {
		.page-title {
			display: flex;
			align-items: center;
			gap: 8px;
			font-size: 24px;
			font-weight: 600;
			color: var(--el-text-color-primary);
			margin: 0 0 8px 0;
		}

		.page-description {
			color: var(--el-text-color-regular);
			margin: 0;
		}
	}

	.header-actions {
		display: flex;
		align-items: center;
	}
}

.member-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
	gap: 24px;
	margin-bottom: 24px;
}

.member-card {
	.member-card-content {
		height: 100%;
		transition: transform 0.2s;

		&:hover {
			transform: translateY(-2px);
		}
	}
}

.member-header {
	display: flex;
	align-items: flex-start;
	gap: 16px;
	margin-bottom: 16px;
	position: relative;

	.member-avatar {
		flex-shrink: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		font-weight: 600;
	}

	.member-info {
		flex: 1;

		.member-name {
			font-size: 18px;
			font-weight: 600;
			color: var(--el-text-color-primary);
			margin: 0 0 4px 0;
		}

		.member-username {
			color: var(--el-text-color-regular);
			font-size: 14px;
			margin: 0 0 8px 0;
		}

		.member-role {
			font-size: 12px;
		}
	}

	.member-status {
		position: absolute;
		top: 0;
		right: 0;
	}
}

.member-contact {
	margin-bottom: 16px;

	.contact-item {
		display: flex;
		align-items: center;
		gap: 8px;
		color: var(--el-text-color-regular);
		font-size: 14px;

		.el-icon {
			color: var(--el-color-primary);
		}
	}
}

.member-stats {
	display: flex;
	justify-content: space-around;
	margin-bottom: 16px;
	padding: 16px 0;
	border-top: 1px solid var(--el-border-color-light);
	border-bottom: 1px solid var(--el-border-color-light);

	.stat-item {
		text-align: center;

		.stat-number {
			display: block;
			font-size: 20px;
			font-weight: 600;
			color: var(--el-color-primary);
		}

		.stat-label {
			font-size: 12px;
			color: var(--el-text-color-regular);
		}
	}
}

.member-actions {
	display: flex;
	gap: 8px;
	margin-bottom: 16px;

	.el-button {
		flex: 1;
	}
}

.member-footer {
	.join-date {
		font-size: 12px;
		color: var(--el-text-color-secondary);
	}
}

.pagination-wrapper {
	display: flex;
	justify-content: center;
	padding: 24px;
	background: white;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 成员详情对话框样式
.member-detail {
	.detail-section {
		margin-bottom: 24px;

		h3 {
			margin: 0 0 16px 0;
			font-size: 16px;
			font-weight: 600;
			color: var(--el-text-color-primary);
		}
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 16px;

		.stat-card {
			display: flex;
			align-items: center;
			gap: 12px;
			padding: 16px;
			background: var(--el-bg-color-page);
			border-radius: 8px;
			border: 1px solid var(--el-border-color-light);

			.stat-icon {
				font-size: 24px;
			}

			.stat-content {
				.stat-number {
					font-size: 20px;
					font-weight: 600;
					color: var(--el-color-primary);
					margin-bottom: 4px;
				}

				.stat-label {
					font-size: 12px;
					color: var(--el-text-color-regular);
				}
			}
		}
	}

	.task-list {
		.task-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px;
			border: 1px solid var(--el-border-color-light);
			border-radius: 6px;
			margin-bottom: 8px;

			&:last-child {
				margin-bottom: 0;
			}

			.task-info {
				flex: 1;

				.task-title {
					font-weight: 500;
					margin-bottom: 8px;
				}

				.task-meta {
					display: flex;
					align-items: center;
					gap: 8px;

					.task-project {
						font-size: 12px;
						color: var(--el-text-color-secondary);
					}
				}
			}

			.task-date {
				font-size: 12px;
				color: var(--el-text-color-secondary);
			}
		}
	}
}

// 响应式设计
@media (max-width: 768px) {
	.member-list {
		padding: 16px;
	}

	.page-header {
		flex-direction: column;
		gap: 16px;
		align-items: stretch;

		.header-actions {
			justify-content: space-between;
		}
	}

	.member-grid {
		grid-template-columns: 1fr;
		gap: 16px;
	}

	.member-header {
		.member-avatar {
			width: 60px;
			height: 60px;
		}
	}

	.member-stats {
		.stat-item {
			.stat-number {
				font-size: 16px;
			}
		}
	}
}
</style>
