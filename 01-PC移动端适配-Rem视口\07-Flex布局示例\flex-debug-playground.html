<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flex布局调试工具 - 一行三元素左中右排列</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .control-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
            color: #2c3e50;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }

        .demo-container {
            background: white;
            border: 3px solid #3498db;
            border-radius: 8px;
            margin: 20px 0;
            min-height: 80px;
            position: relative;
        }

        .demo-container::before {
            content: attr(data-method);
            position: absolute;
            top: -15px;
            left: 10px;
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        /* 可调试的flex容器 */
        .flex-container {
            display: flex;
            padding: 20px;
            min-height: 60px;
            transition: all 0.3s ease;
        }

        .flex-item {
            padding: 10px 15px;
            margin: 2px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .flex-item:hover {
            transform: scale(1.05);
        }

        .flex-item::after {
            content: attr(data-info);
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .flex-item:hover::after {
            opacity: 1;
        }

        .left-item { background: #e74c3c; }
        .center-item { background: #f39c12; }
        .right-item { background: #27ae60; }

        /* 方法样式 */
        .method-space-between {
            justify-content: space-between;
            align-items: center;
        }

        .method-flex-1 .flex-item {
            flex: 1;
        }
        .method-flex-1 .left-item { text-align: left; }
        .method-flex-1 .center-item { text-align: center; }
        .method-flex-1 .right-item { text-align: right; }

        .method-margin-auto .center-item {
            margin: 0 auto;
        }
        .method-margin-auto .right-item {
            margin-left: auto;
        }

        .method-fixed-width .left-item,
        .method-fixed-width .right-item {
            width: 120px;
            text-align: center;
        }
        .method-fixed-width .center-item {
            flex: 1;
            text-align: center;
            margin: 0 10px;
        }

        .code-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }

        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 4px;
            border-radius: 2px;
        }

        .info-panel {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }

        .responsive-controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }

        .responsive-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background: #3498db;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
        }

        .responsive-btn:hover {
            background: #2980b9;
        }

        .responsive-btn.active {
            background: #e74c3c;
        }

        /* 响应式测试 */
        .mobile-view {
            max-width: 375px;
            margin: 0 auto;
        }

        .tablet-view {
            max-width: 768px;
            margin: 0 auto;
        }

        .desktop-view {
            max-width: 100%;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Flex布局调试工具</h1>
        <p>一行三元素左中右排列 - 实时调试和学习</p>
    </div>

    <div class="control-panel">
        <h3>🔧 调试控制面板</h3>
        
        <div class="control-group">
            <label>布局方法:</label>
            <select id="methodSelect">
                <option value="space-between">justify-content: space-between</option>
                <option value="flex-1">flex: 1 + text-align</option>
                <option value="margin-auto">margin: auto</option>
                <option value="fixed-width">固定宽度 + 弹性中间</option>
            </select>
        </div>

        <div class="control-group">
            <label>对齐方式:</label>
            <select id="alignSelect">
                <option value="center">center (居中)</option>
                <option value="flex-start">flex-start (顶部)</option>
                <option value="flex-end">flex-end (底部)</option>
                <option value="stretch">stretch (拉伸)</option>
            </select>
        </div>

        <div class="control-group">
            <label>容器高度:</label>
            <input type="range" id="heightRange" min="60" max="200" value="80">
            <span id="heightValue">80px</span>
        </div>

        <div class="control-group">
            <label>容器内边距:</label>
            <input type="range" id="paddingRange" min="0" max="50" value="20">
            <span id="paddingValue">20px</span>
        </div>

        <div class="responsive-controls">
            <button class="responsive-btn active" data-view="desktop">🖥️ 桌面</button>
            <button class="responsive-btn" data-view="tablet">📱 平板</button>
            <button class="responsive-btn" data-view="mobile">📱 手机</button>
        </div>
    </div>

    <div class="demo-container" data-method="当前方法: justify-content: space-between">
        <div class="flex-container method-space-between" id="flexContainer">
            <div class="flex-item left-item" data-info="左侧元素">左侧内容</div>
            <div class="flex-item center-item" data-info="中间元素">中间内容</div>
            <div class="flex-item right-item" data-info="右侧元素">右侧内容</div>
        </div>
    </div>

    <div class="code-display" id="codeDisplay">
.flex-container {
    display: flex;
    <span class="highlight">justify-content: space-between;</span>
    align-items: center;
    padding: 20px;
}
    </div>

    <div class="info-panel" id="infoPanel">
        <strong>💡 当前方法说明：</strong><br>
        使用 justify-content: space-between 让元素两端对齐，中间自动分配空间。适合元素宽度不固定的情况。
    </div>

    <script>
        const methodSelect = document.getElementById('methodSelect');
        const alignSelect = document.getElementById('alignSelect');
        const heightRange = document.getElementById('heightRange');
        const paddingRange = document.getElementById('paddingRange');
        const heightValue = document.getElementById('heightValue');
        const paddingValue = document.getElementById('paddingValue');
        const flexContainer = document.getElementById('flexContainer');
        const codeDisplay = document.getElementById('codeDisplay');
        const infoPanel = document.getElementById('infoPanel');
        const demoContainer = document.querySelector('.demo-container');
        const responsiveBtns = document.querySelectorAll('.responsive-btn');

        // 方法配置
        const methods = {
            'space-between': {
                className: 'method-space-between',
                code: `
.flex-container {
    display: flex;
    <span class="highlight">justify-content: space-between;</span>
    align-items: center;
}`,
                info: '使用 justify-content: space-between 让元素两端对齐，中间自动分配空间。适合元素宽度不固定的情况。'
            },
            'flex-1': {
                className: 'method-flex-1',
                code: `
.flex-container {
    display: flex;
    align-items: center;
}
.flex-item {
    <span class="highlight">flex: 1;</span>
}
.left-item { text-align: left; }
.center-item { text-align: center; }
.right-item { text-align: right; }`,
                info: '每个元素占据相等空间，内容在各自区域内对齐。中间元素绝对居中。'
            },
            'margin-auto': {
                className: 'method-margin-auto',
                code: `
.flex-container {
    display: flex;
    align-items: center;
}
.center-item { <span class="highlight">margin: 0 auto;</span> }
.right-item { <span class="highlight">margin-left: auto;</span> }`,
                info: '使用 margin: auto 自动分配空间，中间元素绝对居中，灵活性最高。'
            },
            'fixed-width': {
                className: 'method-fixed-width',
                code: `
.flex-container {
    display: flex;
    align-items: center;
}
.left-item, .right-item { 
    <span class="highlight">width: 120px;</span> 
    text-align: center;
}
.center-item { 
    <span class="highlight">flex: 1;</span> 
    text-align: center;
}`,
                info: '左右固定宽度，中间自适应。适合导航栏等需要固定侧边宽度的场景。'
            }
        };

        // 更新布局
        function updateLayout() {
            const method = methodSelect.value;
            const align = alignSelect.value;
            const height = heightRange.value;
            const padding = paddingRange.value;

            // 移除所有方法类
            Object.values(methods).forEach(m => {
                flexContainer.classList.remove(m.className);
            });

            // 添加当前方法类
            flexContainer.classList.add(methods[method].className);

            // 设置对齐方式
            flexContainer.style.alignItems = align;

            // 设置尺寸
            flexContainer.style.minHeight = height + 'px';
            flexContainer.style.padding = padding + 'px';

            // 更新显示值
            heightValue.textContent = height + 'px';
            paddingValue.textContent = padding + 'px';

            // 更新代码显示
            codeDisplay.innerHTML = methods[method].code;

            // 更新说明
            infoPanel.innerHTML = `<strong>💡 当前方法说明：</strong><br>${methods[method].info}`;

            // 更新容器标题
            demoContainer.setAttribute('data-method', `当前方法: ${methodSelect.options[methodSelect.selectedIndex].text}`);

            console.log('布局已更新:', {
                method,
                align,
                height: height + 'px',
                padding: padding + 'px'
            });
        }

        // 响应式视图切换
        function switchView(view) {
            const container = document.body;
            container.classList.remove('mobile-view', 'tablet-view', 'desktop-view');
            container.classList.add(view + '-view');

            responsiveBtns.forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.view === view) {
                    btn.classList.add('active');
                }
            });

            console.log('切换到视图:', view);
        }

        // 事件监听
        methodSelect.addEventListener('change', updateLayout);
        alignSelect.addEventListener('change', updateLayout);
        heightRange.addEventListener('input', updateLayout);
        paddingRange.addEventListener('input', updateLayout);

        responsiveBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                switchView(btn.dataset.view);
            });
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        methodSelect.value = 'space-between';
                        updateLayout();
                        break;
                    case '2':
                        e.preventDefault();
                        methodSelect.value = 'flex-1';
                        updateLayout();
                        break;
                    case '3':
                        e.preventDefault();
                        methodSelect.value = 'margin-auto';
                        updateLayout();
                        break;
                    case '4':
                        e.preventDefault();
                        methodSelect.value = 'fixed-width';
                        updateLayout();
                        break;
                }
            }
        });

        // 初始化
        updateLayout();

        // 添加调试信息
        console.log('🎯 Flex布局调试工具已加载');
        console.log('💡 快捷键: Ctrl+1/2/3/4 切换布局方法');
        console.log('🔧 可以通过控制面板实时调整参数');
    </script>
</body>
</html>
