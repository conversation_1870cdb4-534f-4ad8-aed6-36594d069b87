# API性能优化最佳实践

## 1. 性能优化概述

### 1.1 性能指标
- **响应时间**: 从请求发送到收到响应的时间
- **吞吐量**: 单位时间内处理的请求数量
- **并发数**: 同时处理的请求数量
- **错误率**: 失败请求占总请求的比例
- **资源利用率**: CPU、内存、网络等资源使用情况

### 1.2 性能瓶颈分析
```javascript
// 性能监控示例
const performanceMonitor = {
  startTime: Date.now(),
  
  measureApiCall: async (apiCall) => {
    const start = performance.now();
    try {
      const result = await apiCall();
      const end = performance.now();
      console.log(`API调用耗时: ${end - start}ms`);
      return result;
    } catch (error) {
      const end = performance.now();
      console.error(`API调用失败，耗时: ${end - start}ms`, error);
      throw error;
    }
  }
};
```

## 2. RESTful API 性能优化

### 2.1 缓存策略

#### HTTP缓存
```javascript
// 服务端设置缓存头
app.get('/users/:id', (req, res) => {
  const user = getUserById(req.params.id);
  
  // 设置缓存控制
  res.set({
    'Cache-Control': 'public, max-age=300', // 5分钟
    'ETag': generateETag(user),
    'Last-Modified': user.updatedAt
  });
  
  // 检查条件请求
  if (req.headers['if-none-match'] === generateETag(user)) {
    return res.status(304).end(); // Not Modified
  }
  
  res.json(user);
});
```

#### Redis缓存
```javascript
const redis = require('redis');
const client = redis.createClient();

// 缓存用户数据
const getCachedUser = async (userId) => {
  const cacheKey = `user:${userId}`;
  
  // 尝试从缓存获取
  const cached = await client.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // 从数据库获取
  const user = await User.findById(userId);
  
  // 存入缓存，设置过期时间
  await client.setex(cacheKey, 300, JSON.stringify(user));
  
  return user;
};
```

### 2.2 数据库优化

#### 查询优化
```sql
-- 添加索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_posts_author_created ON posts(author_id, created_at);

-- 优化查询
-- 避免 SELECT *
SELECT id, name, email FROM users WHERE status = 'active';

-- 使用分页
SELECT * FROM posts 
ORDER BY created_at DESC 
LIMIT 20 OFFSET 0;
```

#### 连接池配置
```javascript
const mysql = require('mysql2');

// 创建连接池
const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: 'password',
  database: 'myapp',
  connectionLimit: 10,        // 最大连接数
  queueLimit: 0,             // 队列限制
  acquireTimeout: 60000,     // 获取连接超时
  timeout: 60000,            // 查询超时
  reconnect: true            // 自动重连
});
```

### 2.3 分页优化

#### 游标分页
```javascript
// 传统分页（性能较差）
app.get('/posts', async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;
  
  const posts = await Post.findAll({
    limit,
    offset,
    order: [['created_at', 'DESC']]
  });
  
  res.json({ data: posts, page, limit });
});

// 游标分页（性能更好）
app.get('/posts', async (req, res) => {
  const cursor = req.query.cursor;
  const limit = parseInt(req.query.limit) || 10;
  
  const whereClause = cursor ? 
    { created_at: { $lt: cursor } } : {};
  
  const posts = await Post.findAll({
    where: whereClause,
    limit: limit + 1, // 多查一条判断是否有下一页
    order: [['created_at', 'DESC']]
  });
  
  const hasNext = posts.length > limit;
  const data = hasNext ? posts.slice(0, -1) : posts;
  const nextCursor = hasNext ? posts[limit - 1].created_at : null;
  
  res.json({ 
    data, 
    nextCursor,
    hasNext 
  });
});
```

### 2.4 压缩和优化

#### Gzip压缩
```javascript
const compression = require('compression');

app.use(compression({
  level: 6,                    // 压缩级别
  threshold: 1024,             // 最小压缩大小
  filter: (req, res) => {
    // 自定义压缩条件
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));
```

#### 响应优化
```javascript
// 字段选择
app.get('/users', (req, res) => {
  const fields = req.query.fields;
  let selectFields = '*';
  
  if (fields) {
    // 只返回请求的字段
    selectFields = fields.split(',').join(', ');
  }
  
  const users = db.query(`SELECT ${selectFields} FROM users`);
  res.json(users);
});

// 数据转换优化
const optimizeResponse = (data) => {
  return data.map(item => ({
    id: item.id,
    name: item.name,
    email: item.email,
    // 移除不必要的字段
    // 格式化日期
    createdAt: item.created_at.toISOString()
  }));
};
```

## 3. GraphQL 性能优化

### 3.1 查询复杂度控制

#### 深度限制
```javascript
const depthLimit = require('graphql-depth-limit');

const server = new ApolloServer({
  typeDefs,
  resolvers,
  validationRules: [depthLimit(7)] // 限制查询深度
});
```

#### 查询复杂度分析
```javascript
const costAnalysis = require('graphql-cost-analysis');

const server = new ApolloServer({
  typeDefs,
  resolvers,
  plugins: [
    costAnalysis({
      maximumCost: 1000,
      defaultCost: 1,
      scalarCost: 1,
      objectCost: 1,
      listFactor: 10,
      introspectionCost: 1000
    })
  ]
});
```

### 3.2 DataLoader解决N+1问题

#### 基本用法
```javascript
const DataLoader = require('dataloader');

// 创建DataLoader
const userLoader = new DataLoader(async (userIds) => {
  const users = await User.findAll({
    where: { id: userIds }
  });
  
  // 确保返回顺序与输入一致
  return userIds.map(id => 
    users.find(user => user.id === id)
  );
});

// 在resolver中使用
const resolvers = {
  Post: {
    author: (post) => userLoader.load(post.authorId)
  }
};
```

#### 高级用法
```javascript
// 带缓存的DataLoader
const createUserLoader = () => new DataLoader(
  async (userIds) => {
    const users = await User.findAll({
      where: { id: userIds }
    });
    return userIds.map(id => users.find(user => user.id === id));
  },
  {
    cache: true,           // 启用缓存
    cacheKeyFn: (key) => `user:${key}`,
    cacheMap: new Map()    // 自定义缓存存储
  }
);

// 在context中提供
const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: () => ({
    userLoader: createUserLoader(),
    postLoader: createPostLoader()
  })
});
```

### 3.3 查询优化

#### 字段级缓存
```javascript
const resolvers = {
  Query: {
    user: async (parent, { id }, { userLoader, redis }) => {
      const cacheKey = `user:${id}`;
      
      // 检查缓存
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
      
      // 从数据库获取
      const user = await userLoader.load(id);
      
      // 缓存结果
      await redis.setex(cacheKey, 300, JSON.stringify(user));
      
      return user;
    }
  }
};
```

#### 查询白名单
```javascript
const queryWhitelist = [
  'query GetUser($id: ID!) { user(id: $id) { id name email } }',
  'query GetPosts { posts { id title content } }'
];

const server = new ApolloServer({
  typeDefs,
  resolvers,
  validationRules: [
    require('graphql-query-whitelist')(queryWhitelist)
  ]
});
```

## 4. 前端性能优化

### 4.1 请求优化

#### 请求合并
```javascript
// RESTful API请求合并
class ApiClient {
  constructor() {
    this.batchQueue = [];
    this.batchTimeout = null;
  }
  
  batchRequest(endpoint, params) {
    return new Promise((resolve, reject) => {
      this.batchQueue.push({ endpoint, params, resolve, reject });
      
      if (!this.batchTimeout) {
        this.batchTimeout = setTimeout(() => {
          this.processBatch();
        }, 10); // 10ms内的请求合并
      }
    });
  }
  
  async processBatch() {
    const batch = [...this.batchQueue];
    this.batchQueue = [];
    this.batchTimeout = null;
    
    try {
      const response = await fetch('/api/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(batch.map(item => ({
          endpoint: item.endpoint,
          params: item.params
        })))
      });
      
      const results = await response.json();
      
      batch.forEach((item, index) => {
        item.resolve(results[index]);
      });
    } catch (error) {
      batch.forEach(item => item.reject(error));
    }
  }
}
```

#### GraphQL查询合并
```javascript
// Apollo Client自动批处理
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { BatchHttpLink } from '@apollo/client/link/batch-http';

const batchLink = new BatchHttpLink({
  uri: '/graphql',
  batchMax: 5,        // 最大批处理数量
  batchInterval: 20   // 批处理间隔(ms)
});

const client = new ApolloClient({
  link: batchLink,
  cache: new InMemoryCache()
});
```

### 4.2 缓存策略

#### Apollo Client缓存
```javascript
import { InMemoryCache } from '@apollo/client';

const cache = new InMemoryCache({
  typePolicies: {
    User: {
      fields: {
        posts: {
          merge(existing = [], incoming) {
            return [...existing, ...incoming];
          }
        }
      }
    }
  }
});

// 查询缓存策略
const { data, loading, error } = useQuery(GET_USERS, {
  fetchPolicy: 'cache-first',     // 优先使用缓存
  errorPolicy: 'all',             // 返回部分数据和错误
  notifyOnNetworkStatusChange: true
});
```

#### 本地存储缓存
```javascript
class CacheManager {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map();
  }
  
  set(key, value, ttl = 300000) { // 默认5分钟
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttl);
  }
  
  get(key) {
    if (this.ttl.get(key) < Date.now()) {
      this.cache.delete(key);
      this.ttl.delete(key);
      return null;
    }
    return this.cache.get(key);
  }
  
  async fetchWithCache(key, fetcher, ttl) {
    const cached = this.get(key);
    if (cached) return cached;
    
    const data = await fetcher();
    this.set(key, data, ttl);
    return data;
  }
}
```

## 5. 监控和分析

### 5.1 性能监控
```javascript
// API性能监控
const performanceMiddleware = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    // 记录性能指标
    console.log({
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent')
    });
    
    // 发送到监控系统
    if (duration > 1000) { // 超过1秒的慢查询
      alertSlowQuery(req, duration);
    }
  });
  
  next();
};
```

### 5.2 错误追踪
```javascript
// 错误监控
const errorHandler = (error, req, res, next) => {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
    userAgent: req.get('User-Agent')
  };
  
  // 发送到错误追踪系统
  sendToErrorTracking(errorInfo);
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
};
```

## 6. 性能测试

### 6.1 负载测试
```javascript
// 使用Artillery进行负载测试
// artillery.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100

scenarios:
  - name: "Get users"
    requests:
      - get:
          url: "/api/users"
          headers:
            Authorization: "Bearer {{ token }}"
```

### 6.2 基准测试
```javascript
// 使用Benchmark.js
const Benchmark = require('benchmark');

const suite = new Benchmark.Suite();

suite
  .add('REST API call', async () => {
    await fetch('/api/users');
  })
  .add('GraphQL query', async () => {
    await fetch('/graphql', {
      method: 'POST',
      body: JSON.stringify({
        query: 'query { users { id name email } }'
      })
    });
  })
  .on('cycle', (event) => {
    console.log(String(event.target));
  })
  .on('complete', function() {
    console.log('Fastest is ' + this.filter('fastest').map('name'));
  })
  .run({ async: true });
```

## 7. 性能优化检查清单

### 7.1 服务端优化
- [ ] 实现适当的缓存策略
- [ ] 优化数据库查询和索引
- [ ] 使用连接池
- [ ] 启用响应压缩
- [ ] 实现分页机制
- [ ] 设置合理的超时时间
- [ ] 使用CDN加速静态资源

### 7.2 客户端优化
- [ ] 实现请求缓存
- [ ] 合并相似请求
- [ ] 使用适当的错误重试策略
- [ ] 实现请求去重
- [ ] 优化网络请求时机
- [ ] 使用懒加载
- [ ] 实现离线缓存

### 7.3 GraphQL特定优化
- [ ] 使用DataLoader解决N+1问题
- [ ] 实现查询复杂度控制
- [ ] 设置查询深度限制
- [ ] 使用查询白名单
- [ ] 实现字段级缓存
- [ ] 优化Schema设计

---

**总结建议**:
1. 性能优化是一个持续的过程，需要定期监控和调整
2. 优先解决影响最大的性能瓶颈
3. 在优化前后都要进行性能测试，确保改进效果
4. 平衡性能和代码复杂度，避免过度优化
5. 建立完善的监控体系，及时发现性能问题
