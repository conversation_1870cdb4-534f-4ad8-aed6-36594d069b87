<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>API功能测试</h1>
    
    <div class="test-section">
        <h2>Mock数据测试</h2>
        <button onclick="testMockData()">测试Mock数据</button>
        <div id="mockResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>RESTful API测试</h2>
        <button onclick="testRestfulAPI()">测试RESTful API</button>
        <div id="restfulResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>GraphQL API测试</h2>
        <button onclick="testGraphQLAPI()">测试GraphQL API</button>
        <div id="graphqlResult" class="result"></div>
    </div>

    <!-- 引入依赖 -->
    <script src="assets/data/mock-data.js"></script>
    <script src="assets/js/restful-api.js"></script>
    <script src="assets/js/graphql-api.js"></script>
    
    <script>
        function testMockData() {
            const result = document.getElementById('mockResult');
            try {
                result.textContent = `Mock数据测试结果：
用户数量: ${window.mockData.users.length}
任务数量: ${window.mockData.tasks.length}
文章数量: ${window.mockData.posts.length}

第一个用户: ${JSON.stringify(window.mockData.users[0], null, 2)}

第一个任务: ${JSON.stringify(window.mockData.tasks[0], null, 2)}`;
            } catch (error) {
                result.textContent = `错误: ${error.message}`;
            }
        }
        
        async function testRestfulAPI() {
            const result = document.getElementById('restfulResult');
            try {
                const api = new RESTfulAPI();
                
                // 测试获取任务
                const tasksResponse = await api.get('/tasks');
                result.textContent = `RESTful API测试结果：
获取任务成功: ${tasksResponse.data.length} 个任务

第一个任务: ${JSON.stringify(tasksResponse.data[0], null, 2)}

API统计: ${JSON.stringify(api.getStats(), null, 2)}`;
            } catch (error) {
                result.textContent = `错误: ${error.message}`;
            }
        }
        
        async function testGraphQLAPI() {
            const result = document.getElementById('graphqlResult');
            try {
                const api = new GraphQLAPI();
                
                // 测试GraphQL查询
                const query = `
                    query {
                        tasks {
                            id
                            title
                            status
                            priority
                        }
                    }
                `;
                
                const response = await api.execute(query);
                result.textContent = `GraphQL API测试结果：
查询成功: ${response.data.tasks.length} 个任务

执行时间: ${response.extensions.executionTime}

第一个任务: ${JSON.stringify(response.data.tasks[0], null, 2)}`;
            } catch (error) {
                result.textContent = `错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
