# API文档编写最佳实践

## 1. 文档编写的重要性

### 1.1 为什么需要API文档？
- **开发效率**: 减少沟通成本，提高开发效率
- **维护性**: 便于后续维护和更新
- **用户体验**: 帮助开发者快速上手
- **团队协作**: 统一团队对API的理解
- **质量保证**: 文档编写过程中发现设计问题

### 1.2 好文档的特征
- **准确性**: 与实际API行为一致
- **完整性**: 覆盖所有端点和用例
- **清晰性**: 结构清晰，易于理解
- **实用性**: 提供可执行的示例
- **时效性**: 与API版本保持同步

## 2. RESTful API 文档规范

### 2.1 基本信息
```yaml
# API基本信息
API名称: 用户管理API
版本: v1.0.0
基础URL: https://api.example.com/v1
认证方式: Bearer Token
内容类型: application/json
```

### 2.2 端点文档模板
```markdown
## GET /users

### 描述
获取用户列表，支持分页和过滤

### 请求参数
| 参数名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| limit | integer | 否 | 10 | 每页数量 |
| status | string | 否 | - | 用户状态过滤 |
| search | string | 否 | - | 搜索关键词 |

### 请求示例
```bash
curl -X GET "https://api.example.com/v1/users?page=1&limit=10&status=active" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

### 响应格式
```json
{
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "status": "active",
      "created_at": "2023-10-21T07:28:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

### 状态码
| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 无权限 |
| 500 | 服务器错误 |

### 错误响应
```json
{
  "error": "validation_failed",
  "message": "Invalid parameters",
  "details": [
    {
      "field": "limit",
      "message": "Must be between 1 and 100"
    }
  ]
}
```
```

### 2.3 完整的RESTful API文档示例
```markdown
# 用户管理API文档

## 概述
用户管理API提供用户的增删改查功能，支持用户注册、登录、信息更新等操作。

## 认证
所有API请求都需要在请求头中包含有效的Bearer Token：
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 端点列表

### 用户相关

#### 1. 获取用户列表
- **端点**: `GET /users`
- **描述**: 获取系统中的用户列表
- **权限**: 需要管理员权限

**查询参数**:
- `page` (integer, 可选): 页码，默认为1
- `limit` (integer, 可选): 每页数量，默认为10，最大100
- `status` (string, 可选): 用户状态 (`active`, `inactive`, `banned`)
- `role` (string, 可选): 用户角色 (`admin`, `user`, `moderator`)
- `search` (string, 可选): 搜索用户名或邮箱

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "username": "johndoe",
      "email": "<EMAIL>",
      "role": "user",
      "status": "active",
      "created_at": "2023-10-21T07:28:00Z",
      "updated_at": "2023-10-21T07:28:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "pages": 15,
    "has_next": true,
    "has_prev": false
  }
}
```

#### 2. 获取单个用户
- **端点**: `GET /users/{id}`
- **描述**: 根据用户ID获取用户详细信息

**路径参数**:
- `id` (integer, 必需): 用户ID

**响应示例**:
```json
{
  "id": 1,
  "username": "johndoe",
  "email": "<EMAIL>",
  "profile": {
    "first_name": "John",
    "last_name": "Doe",
    "avatar": "https://example.com/avatars/1.jpg",
    "bio": "Software Developer"
  },
  "role": "user",
  "status": "active",
  "last_login": "2023-10-21T10:30:00Z",
  "created_at": "2023-10-21T07:28:00Z",
  "updated_at": "2023-10-21T07:28:00Z"
}
```

#### 3. 创建用户
- **端点**: `POST /users`
- **描述**: 创建新用户账户

**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "profile": {
    "first_name": "New",
    "last_name": "User"
  },
  "role": "user"
}
```

**响应示例** (201 Created):
```json
{
  "id": 151,
  "username": "newuser",
  "email": "<EMAIL>",
  "role": "user",
  "status": "active",
  "created_at": "2023-10-21T11:00:00Z"
}
```

#### 4. 更新用户
- **端点**: `PUT /users/{id}`
- **描述**: 更新用户信息

**请求体**:
```json
{
  "email": "<EMAIL>",
  "profile": {
    "first_name": "Updated",
    "last_name": "Name",
    "bio": "Updated bio"
  }
}
```

#### 5. 删除用户
- **端点**: `DELETE /users/{id}`
- **描述**: 删除用户账户
- **响应**: 204 No Content

## 错误处理

### 错误响应格式
```json
{
  "error": "error_code",
  "message": "Human readable error message",
  "details": {
    "field": "specific_field",
    "code": "validation_error"
  },
  "timestamp": "2023-10-21T11:00:00Z",
  "path": "/users"
}
```

### 常见错误码
| 状态码 | 错误码 | 描述 |
|--------|--------|------|
| 400 | `validation_failed` | 请求参数验证失败 |
| 401 | `unauthorized` | 未提供有效的认证信息 |
| 403 | `forbidden` | 没有权限执行此操作 |
| 404 | `not_found` | 请求的资源不存在 |
| 409 | `conflict` | 资源冲突（如邮箱已存在） |
| 422 | `unprocessable_entity` | 请求格式正确但语义错误 |
| 429 | `rate_limit_exceeded` | 请求频率超过限制 |
| 500 | `internal_error` | 服务器内部错误 |

## 速率限制
- 每个IP每小时最多1000次请求
- 认证用户每小时最多5000次请求
- 超过限制时返回429状态码

## 版本控制
- 当前版本: v1
- 版本通过URL路径指定: `/v1/users`
- 向后兼容性保证至少6个月

## SDK和工具
- JavaScript SDK: `npm install @example/api-client`
- Python SDK: `pip install example-api-client`
- Postman Collection: [下载链接]
```

## 3. GraphQL API 文档规范

### 3.1 Schema文档
```graphql
"""
用户类型定义
"""
type User {
  "用户唯一标识"
  id: ID!
  
  "用户名"
  username: String!
  
  "邮箱地址"
  email: String!
  
  "用户角色"
  role: UserRole!
  
  "用户状态"
  status: UserStatus!
  
  "用户发布的文章"
  posts(
    "限制返回数量"
    limit: Int = 10
    "偏移量"
    offset: Int = 0
  ): [Post!]!
  
  "创建时间"
  createdAt: DateTime!
  
  "更新时间"
  updatedAt: DateTime!
}

"""
用户角色枚举
"""
enum UserRole {
  ADMIN
  USER
  MODERATOR
}

"""
用户状态枚举
"""
enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}
```

### 3.2 查询文档示例
```markdown
# GraphQL API 文档

## 查询 (Queries)

### 获取用户列表
```graphql
query GetUsers($limit: Int, $offset: Int, $filter: UserFilter) {
  users(limit: $limit, offset: $offset, filter: $filter) {
    id
    username
    email
    role
    status
    createdAt
  }
}
```

**变量**:
```json
{
  "limit": 10,
  "offset": 0,
  "filter": {
    "status": "ACTIVE",
    "role": "USER"
  }
}
```

**响应**:
```json
{
  "data": {
    "users": [
      {
        "id": "1",
        "username": "johndoe",
        "email": "<EMAIL>",
        "role": "USER",
        "status": "ACTIVE",
        "createdAt": "2023-10-21T07:28:00Z"
      }
    ]
  }
}
```

### 获取用户详情及其文章
```graphql
query GetUserWithPosts($userId: ID!) {
  user(id: $userId) {
    id
    username
    email
    posts(limit: 5) {
      id
      title
      content
      createdAt
    }
  }
}
```

## 修改 (Mutations)

### 创建用户
```graphql
mutation CreateUser($input: CreateUserInput!) {
  createUser(input: $input) {
    id
    username
    email
    role
    createdAt
  }
}
```

**输入变量**:
```json
{
  "input": {
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "role": "USER"
  }
}
```

## 订阅 (Subscriptions)

### 监听新用户创建
```graphql
subscription OnUserCreated {
  userCreated {
    id
    username
    email
    createdAt
  }
}
```

## 错误处理

GraphQL错误响应格式:
```json
{
  "data": null,
  "errors": [
    {
      "message": "User not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["user"],
      "extensions": {
        "code": "USER_NOT_FOUND",
        "userId": "999"
      }
    }
  ]
}
```
```

## 4. 文档工具推荐

### 4.1 RESTful API文档工具

#### Swagger/OpenAPI
```yaml
openapi: 3.0.0
info:
  title: 用户管理API
  version: 1.0.0
  description: 提供用户CRUD操作的RESTful API

paths:
  /users:
    get:
      summary: 获取用户列表
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
      responses:
        '200':
          description: 成功返回用户列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
```

#### Postman文档
```javascript
// Postman Pre-request Script
pm.environment.set("baseUrl", "https://api.example.com/v1");

// Postman Test Script
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has users array", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('data');
    pm.expect(jsonData.data).to.be.an('array');
});
```

### 4.2 GraphQL文档工具

#### GraphQL Playground
```javascript
// 查询示例
{
  users(limit: 10) {
    id
    username
    email
  }
}

// 变量
{
  "limit": 10
}
```

#### Apollo Studio
```javascript
// Schema注册
const { ApolloServer } = require('apollo-server');
const { buildSchema } = require('graphql');

const server = new ApolloServer({
  typeDefs,
  resolvers,
  introspection: true,
  playground: true
});
```

## 5. 文档维护最佳实践

### 5.1 版本控制
- 文档与代码同步更新
- 使用语义化版本控制
- 保留历史版本文档
- 提供迁移指南

### 5.2 自动化生成
```javascript
// 使用注释生成文档
/**
 * @api {get} /users 获取用户列表
 * @apiName GetUsers
 * @apiGroup User
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number} [limit=10] 每页数量
 * @apiSuccess {Object[]} data 用户列表
 * @apiSuccess {Number} data.id 用户ID
 * @apiSuccess {String} data.username 用户名
 */
app.get('/users', (req, res) => {
  // 实现代码
});
```

### 5.3 测试覆盖
```javascript
// API测试示例
describe('GET /users', () => {
  it('should return users list', async () => {
    const response = await request(app)
      .get('/users')
      .expect(200);
    
    expect(response.body).toHaveProperty('data');
    expect(Array.isArray(response.body.data)).toBe(true);
  });
});
```

## 6. 文档质量检查清单

### 6.1 内容完整性
- [ ] 所有端点都有文档
- [ ] 包含请求和响应示例
- [ ] 错误情况有说明
- [ ] 认证方式已说明
- [ ] 速率限制已说明

### 6.2 准确性验证
- [ ] 示例代码可以执行
- [ ] 参数类型正确
- [ ] 状态码准确
- [ ] 与实际API行为一致

### 6.3 用户体验
- [ ] 结构清晰易懂
- [ ] 提供快速开始指南
- [ ] 有搜索功能
- [ ] 支持多种语言示例
- [ ] 提供SDK和工具链接

---

**总结建议**:
1. 文档是API的重要组成部分，需要与代码同等重视
2. 使用工具自动化生成和维护文档
3. 定期检查文档的准确性和完整性
4. 收集用户反馈，持续改进文档质量
5. 提供多种格式和语言的示例代码
