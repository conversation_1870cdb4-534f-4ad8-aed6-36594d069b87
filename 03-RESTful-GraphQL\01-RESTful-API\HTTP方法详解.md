# HTTP方法详解

## 1. HTTP方法概述

HTTP方法（也称为HTTP动词）定义了对资源执行的操作类型。RESTful API充分利用这些方法来实现不同的业务操作。

### 1.1 常用HTTP方法总览

| 方法 | 用途 | 幂等性 | 安全性 | 缓存 | 请求体 |
|------|------|--------|--------|------|--------|
| GET | 获取资源 | ✅ | ✅ | ✅ | ❌ |
| POST | 创建资源 | ❌ | ❌ | ❌ | ✅ |
| PUT | 更新/替换资源 | ✅ | ❌ | ❌ | ✅ |
| PATCH | 部分更新资源 | ❌ | ❌ | ❌ | ✅ |
| DELETE | 删除资源 | ✅ | ❌ | ❌ | ❌ |
| HEAD | 获取资源头信息 | ✅ | ✅ | ✅ | ❌ |
| OPTIONS | 获取支持的方法 | ✅ | ✅ | ✅ | ❌ |

## 2. GET方法详解

### 2.1 基本用法
```http
GET /api/users HTTP/1.1
Host: example.com
Accept: application/json
```

### 2.2 查询参数
```javascript
// 分页查询
GET /api/users?page=1&limit=10

// 过滤查询
GET /api/users?status=active&role=admin

// 排序查询
GET /api/users?sort=created_at&order=desc

// 搜索查询
GET /api/users?search=john&fields=name,email
```

### 2.3 响应示例
```http
HTTP/1.1 200 OK
Content-Type: application/json
Cache-Control: max-age=300
ETag: "abc123"

{
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

### 2.4 最佳实践
- 使用查询参数进行过滤、排序、分页
- 支持字段选择以减少数据传输
- 实现适当的缓存策略
- 返回有意义的错误信息

## 3. POST方法详解

### 3.1 创建资源
```http
POST /api/users HTTP/1.1
Host: example.com
Content-Type: application/json

{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "role": "user"
}
```

### 3.2 响应示例
```http
HTTP/1.1 201 Created
Location: /api/users/123
Content-Type: application/json

{
  "id": 123,
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "role": "user",
  "created_at": "2023-10-21T10:30:00Z"
}
```

### 3.3 批量操作
```javascript
// 批量创建
POST /api/users/batch
{
  "users": [
    {"name": "User1", "email": "<EMAIL>"},
    {"name": "User2", "email": "<EMAIL>"}
  ]
}

// 复杂操作
POST /api/users/123/actions
{
  "action": "activate",
  "reason": "Account verified"
}
```

### 3.4 文件上传
```javascript
// 单文件上传
POST /api/upload
Content-Type: multipart/form-data

// 多文件上传
POST /api/upload/multiple
Content-Type: multipart/form-data
```

## 4. PUT方法详解

### 4.1 完整替换资源
```http
PUT /api/users/123 HTTP/1.1
Host: example.com
Content-Type: application/json

{
  "name": "John Smith",
  "email": "<EMAIL>",
  "role": "admin"
}
```

### 4.2 幂等性特征
```javascript
// 第一次调用
PUT /api/users/123
{"name": "John", "email": "<EMAIL>"}

// 第二次调用（相同结果）
PUT /api/users/123
{"name": "John", "email": "<EMAIL>"}
```

### 4.3 创建或更新
```javascript
// 如果资源不存在，创建它
PUT /api/users/999
{
  "name": "New User",
  "email": "<EMAIL>"
}

// 响应：201 Created 或 200 OK
```

### 4.4 PUT的注意事项
```javascript
// ⚠️ 危险：遗漏字段会导致数据丢失
// 原始数据
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "************",
  "status": "active"
}

// 错误的PUT请求（遗漏了phone和status）
PUT /api/users/123
{
  "name": "John Smith",
  "email": "<EMAIL>"
}

// 结果：phone和status字段可能被删除或重置
{
  "id": 123,
  "name": "John Smith",
  "email": "<EMAIL>",
  "phone": null,
  "status": null
}
```

## 5. PATCH方法详解

### 5.1 部分更新
```http
PATCH /api/users/123 HTTP/1.1
Host: example.com
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### 5.2 JSON Patch格式
```javascript
PATCH /api/users/123
Content-Type: application/json-patch+json

[
  {"op": "replace", "path": "/email", "value": "<EMAIL>"},
  {"op": "add", "path": "/phone", "value": "************"},
  {"op": "remove", "path": "/temp_field"}
]
```

### 5.3 条件更新
```javascript
PATCH /api/users/123
If-Match: "etag-value"

{
  "name": "Updated Name"
}
```

### 5.4 PATCH的优势
```javascript
// ✅ 安全：只更新指定字段
// 原始数据
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "************",
  "status": "active"
}

// PATCH请求
PATCH /api/users/123
{
  "email": "<EMAIL>"
}

// 结果：只有email被更新，其他字段保持不变
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "************",
  "status": "active"
}
```

## 5.5 PUT vs PATCH 详细对比

### 5.5.1 核心差异总结

| 特性 | PUT | PATCH |
|------|-----|-------|
| 更新方式 | 完整替换 | 部分更新 |
| 请求体 | 必须包含所有字段 | 只包含要更新的字段 |
| 幂等性 | ✅ 幂等 | ❌ 通常不幂等 |
| 数据安全 | ⚠️ 可能丢失未提供的字段 | ✅ 只修改指定字段 |
| 带宽使用 | 较高（完整数据） | 较低（部分数据） |
| 并发安全 | ⚠️ 可能覆盖其他修改 | ✅ 更安全 |

### 5.5.2 实际场景对比

```javascript
// 场景：用户资源
const originalUser = {
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "************",
  "address": "123 Main St",
  "status": "active",
  "last_login": "2023-10-20T10:00:00Z"
};

// 需求：只更新邮箱地址

// 方案1：使用PUT（不推荐）
PUT /api/users/123
{
  "name": "John Doe",
  "email": "<EMAIL>",  // 要更新的字段
  "phone": "************",
  "address": "123 Main St",
  "status": "active",
  "last_login": "2023-10-20T10:00:00Z"
}
// 问题：需要提供所有字段，容易出错

// 方案2：使用PATCH（推荐）
PATCH /api/users/123
{
  "email": "<EMAIL>"  // 只提供要更新的字段
}
// 优势：简洁、安全、高效
```

### 5.5.3 并发更新场景

```javascript
// 场景：两个用户同时更新同一资源

// 初始状态
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "************"
}

// 用户A：使用PUT更新姓名
PUT /api/users/123
{
  "name": "John Smith",
  "email": "<EMAIL>",
  "phone": "************"
}

// 用户B：使用PUT更新电话（可能覆盖用户A的修改）
PUT /api/users/123
{
  "name": "John Doe",        // 可能覆盖用户A的修改
  "email": "<EMAIL>",
  "phone": "************"
}

// 更好的方案：使用PATCH
// 用户A：更新姓名
PATCH /api/users/123
{
  "name": "John Smith"
}

// 用户B：更新电话
PATCH /api/users/123
{
  "phone": "************"
}

// 结果：两个修改都保留
{
  "id": 123,
  "name": "John Smith",      // 用户A的修改
  "email": "<EMAIL>",
  "phone": "************"    // 用户B的修改
}
```

### 5.5.4 选择建议

**使用PUT的场景**：
- 客户端拥有资源的完整状态
- 需要确保资源的完整性和一致性
- 实现"保存"功能（如表单的完整提交）
- 资源字段较少且变化频繁

**使用PATCH的场景**：
- 只需要更新部分字段
- 资源字段较多
- 移动端或带宽受限环境
- 多用户并发编辑场景
- 实现"自动保存"功能

## 6. DELETE方法详解

### 6.1 删除单个资源
```http
DELETE /api/users/123 HTTP/1.1
Host: example.com
```

### 6.2 响应选项
```javascript
// 选项1：无内容响应
HTTP/1.1 204 No Content

// 选项2：返回删除的资源
HTTP/1.1 200 OK
{
  "id": 123,
  "name": "Deleted User",
  "deleted_at": "2023-10-21T10:30:00Z"
}
```

### 6.3 软删除 vs 硬删除
```javascript
// 软删除（推荐）
PATCH /api/users/123
{
  "status": "deleted",
  "deleted_at": "2023-10-21T10:30:00Z"
}

// 硬删除
DELETE /api/users/123
```

### 6.4 批量删除
```javascript
// 方案1：查询参数
DELETE /api/users?ids=1,2,3

// 方案2：请求体
DELETE /api/users
{
  "ids": [1, 2, 3]
}
```

## 7. HEAD和OPTIONS方法

### 7.1 HEAD方法详解

HEAD 方法用于获取资源的元数据信息，不返回响应体内容。

#### 7.1.1 基本用法
```javascript
// 检查资源是否存在
HEAD /api/users/123

// 响应（只有头部，无内容）
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 156
Last-Modified: Wed, 21 Oct 2023 07:28:00 GMT
ETag: "abc123"
```

#### 7.1.2 主要用途
```javascript
// 1. 检查文件大小
HEAD /api/files/document.pdf
// 响应头包含 Content-Length

// 2. 验证缓存
HEAD /api/users/123
If-Modified-Since: Wed, 21 Oct 2023 07:28:00 GMT
// 如果未修改返回 304 Not Modified

// 3. 检查资源存在性
HEAD /api/articles/456
// 200 OK = 存在，404 Not Found = 不存在

// 4. 获取支持的操作
HEAD /api/users/123
// 响应头包含 Allow: GET, PUT, PATCH, DELETE
```

#### 7.1.3 实际应用场景
```javascript
// 文件下载前预检查
async function checkFileBeforeDownload(url) {
  const response = await fetch(url, { method: 'HEAD' });
  const fileSize = parseInt(response.headers.get('Content-Length'));

  if (fileSize > 100 * 1024 * 1024) { // 100MB
    return confirm('文件较大，确定下载吗？');
  }
  return true;
}

// API 健康检查
async function healthCheck(endpoint) {
  try {
    const response = await fetch(endpoint, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}
```

#### 7.1.4 与 GET 的关系
```javascript
// HEAD 和 GET 必须返回相同的头部信息
// GET 请求
GET /api/users/123
// 响应：头部 + 响应体

// HEAD 请求
HEAD /api/users/123
// 响应：相同的头部，无响应体
```

### 7.2 OPTIONS方法详解

OPTIONS 方法用于获取目标资源所支持的通信选项，主要有两个用途：**API 能力发现**和 **CORS 预检请求**。

#### 7.2.1 基本用法
```javascript
// 检查资源支持的方法
OPTIONS /api/users/123

// 响应
HTTP/1.1 200 OK
Allow: GET, PUT, PATCH, DELETE
Content-Length: 0
Cache-Control: max-age=604800
```

#### 7.2.2 OPTIONS 的触发机制

**1. 手动调用**
```javascript
// 开发者主动发送 OPTIONS 请求
fetch('/api/users/123', { method: 'OPTIONS' })
  .then(response => {
    console.log('支持的方法:', response.headers.get('Allow'));
  });
```

**2. CORS 预检触发（浏览器自动）**

浏览器在以下情况会自动发送 OPTIONS 预检请求：

- **非简单请求方法**：PUT, DELETE, PATCH 等
- **自定义请求头**：Authorization, X-API-Key 等
- **特殊 Content-Type**：application/json, application/xml 等

```javascript
// 这个请求会触发预检
fetch('https://api.example.com/users', {
  method: 'PUT',                    // 非简单方法
  headers: {
    'Content-Type': 'application/json',  // 非简单 Content-Type
    'Authorization': 'Bearer token123'   // 自定义头部
  },
  body: JSON.stringify(userData)
});

// 浏览器自动发送的预检请求：
// OPTIONS /users HTTP/1.1
// Origin: https://frontend.com
// Access-Control-Request-Method: PUT
// Access-Control-Request-Headers: Content-Type, Authorization
```

#### 7.2.3 CORS 预检请求详解

**预检请求头部**：
```javascript
OPTIONS /api/users/123 HTTP/1.1
Origin: https://frontend.com                           // 请求来源
Access-Control-Request-Method: PUT                     // 实际要使用的方法
Access-Control-Request-Headers: Content-Type, X-Token  // 实际要发送的头部
```

**预检响应头部**：
```javascript
HTTP/1.1 200 OK
Access-Control-Allow-Origin: https://frontend.com      // 允许的源
Access-Control-Allow-Methods: GET, PUT, PATCH, DELETE  // 允许的方法
Access-Control-Allow-Headers: Content-Type, X-Token    // 允许的头部
Access-Control-Allow-Credentials: true                 // 是否允许凭据
Access-Control-Max-Age: 86400                         // 预检缓存时间（秒）
```

#### 7.2.4 简单请求 vs 预检请求

**简单请求（不触发预检）**：
```javascript
// 满足以下所有条件的请求是简单请求：
// 1. 方法：GET, HEAD, POST
// 2. 头部：只能是 Accept, Accept-Language, Content-Language, Content-Type
// 3. Content-Type：只能是 text/plain, multipart/form-data, application/x-www-form-urlencoded

// 简单请求示例
fetch('https://api.example.com/users', {
  method: 'GET',
  headers: {
    'Accept': 'application/json'
  }
});
// 不会触发预检，直接发送请求
```

**复杂请求（触发预检）**：
```javascript
// 以下任一条件都会触发预检：

// 1. 非简单方法
fetch('/api/users/123', { method: 'DELETE' });

// 2. 自定义头部
fetch('/api/users', {
  method: 'POST',
  headers: { 'X-API-Key': 'abc123' }
});

// 3. JSON Content-Type
fetch('/api/users', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(data)
});
```

#### 7.2.5 服务端实现示例

**Node.js + Express 实现**：
```javascript
// OPTIONS 路由处理
app.options('/api/users/:id?', (req, res) => {
  // 设置允许的方法
  res.set('Allow', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');

  // CORS 头部
  res.set({
    'Access-Control-Allow-Origin': req.headers.origin || '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400'
  });

  // OPTIONS 请求通常返回 204 No Content
  res.status(204).end();
});

// 全局 CORS 中间件
app.use((req, res, next) => {
  // 处理所有 OPTIONS 请求
  if (req.method === 'OPTIONS') {
    res.set({
      'Access-Control-Allow-Origin': req.headers.origin || '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': req.headers['access-control-request-headers'] || 'Content-Type',
      'Access-Control-Max-Age': '86400'
    });
    return res.status(204).end();
  }
  next();
});
```

#### 7.2.6 返回内容详解

**状态码**：
- `200 OK` - 成功返回支持的选项
- `204 No Content` - 成功但无响应体（推荐）
- `405 Method Not Allowed` - 不支持 OPTIONS 方法

**响应头部**：
```javascript
// 基本响应头
Allow: GET, POST, PUT, DELETE                    // 支持的 HTTP 方法
Content-Length: 0                               // 通常无响应体

// CORS 相关头部
Access-Control-Allow-Origin: https://app.com    // 允许的源
Access-Control-Allow-Methods: GET, POST, PUT    // 允许的方法
Access-Control-Allow-Headers: Content-Type      // 允许的请求头
Access-Control-Allow-Credentials: true          // 是否允许凭据
Access-Control-Max-Age: 3600                   // 预检缓存时间

// 其他可选头部
Accept-Ranges: bytes                            // 是否支持范围请求
Server: nginx/1.18.0                          // 服务器信息
```

**响应体**：
```javascript
// 通常无响应体（推荐）
// 状态码：204 No Content

// 或者返回详细信息（可选）
// 状态码：200 OK
{
  "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"],
  "headers": ["Content-Type", "Authorization"],
  "cors": {
    "origins": ["https://app.com", "https://admin.com"],
    "credentials": true,
    "maxAge": 86400
  }
}
```

#### 7.2.7 实际应用场景

**1. API 能力发现**
```javascript
// 动态检查 API 支持的操作
async function checkAPICapabilities(endpoint) {
  try {
    const response = await fetch(endpoint, { method: 'OPTIONS' });
    const allowedMethods = response.headers.get('Allow')?.split(', ') || [];

    return {
      canRead: allowedMethods.includes('GET'),
      canCreate: allowedMethods.includes('POST'),
      canUpdate: allowedMethods.includes('PUT') || allowedMethods.includes('PATCH'),
      canDelete: allowedMethods.includes('DELETE'),
      supportedMethods: allowedMethods
    };
  } catch (error) {
    console.error('检查 API 能力失败:', error);
    return null;
  }
}

// 使用示例
const capabilities = await checkAPICapabilities('/api/users/123');
if (capabilities?.canDelete) {
  // 显示删除按钮
  showDeleteButton();
}
```

**2. 前端权限控制**
```javascript
// 基于 OPTIONS 响应动态显示操作按钮
class APIPermissionChecker {
  constructor() {
    this.cache = new Map();
  }

  async getPermissions(resource) {
    if (this.cache.has(resource)) {
      return this.cache.get(resource);
    }

    try {
      const response = await fetch(resource, { method: 'OPTIONS' });
      const permissions = {
        methods: response.headers.get('Allow')?.split(', ') || [],
        corsEnabled: response.headers.has('Access-Control-Allow-Origin'),
        maxAge: parseInt(response.headers.get('Access-Control-Max-Age')) || 0
      };

      this.cache.set(resource, permissions);
      return permissions;
    } catch (error) {
      console.error('获取权限失败:', error);
      return { methods: [], corsEnabled: false, maxAge: 0 };
    }
  }
}

// Vue.js 组件中使用
export default {
  data() {
    return {
      permissions: { methods: [] },
      permissionChecker: new APIPermissionChecker()
    };
  },

  async mounted() {
    this.permissions = await this.permissionChecker.getPermissions('/api/users/123');
  },

  computed: {
    canEdit() {
      return this.permissions.methods.includes('PUT') ||
             this.permissions.methods.includes('PATCH');
    },
    canDelete() {
      return this.permissions.methods.includes('DELETE');
    }
  }
};
```

**3. CORS 预检优化**
```javascript
// 服务端优化预检请求处理
app.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    // 设置较长的缓存时间，减少预检请求频率
    res.set({
      'Access-Control-Allow-Origin': req.headers.origin,
      'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE',
      'Access-Control-Allow-Headers': req.headers['access-control-request-headers'],
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400', // 24小时缓存
      'Vary': 'Origin, Access-Control-Request-Headers'
    });

    return res.status(204).end();
  }
  next();
});
```

#### 7.2.8 调试和监控

**1. 预检请求调试**
```javascript
// 调试 CORS 预检请求
function debugCORSPreflight(url, method, headers = {}) {
  console.log('=== CORS 预检调试 ===');
  console.log('目标 URL:', url);
  console.log('请求方法:', method);
  console.log('自定义头部:', headers);

  // 检查是否会触发预检
  const isSimpleMethod = ['GET', 'HEAD', 'POST'].includes(method);
  const hasCustomHeaders = Object.keys(headers).some(header =>
    !['Accept', 'Accept-Language', 'Content-Language', 'Content-Type'].includes(header)
  );
  const hasComplexContentType = headers['Content-Type'] &&
    !['text/plain', 'multipart/form-data', 'application/x-www-form-urlencoded'].includes(headers['Content-Type']);

  const willPreflight = !isSimpleMethod || hasCustomHeaders || hasComplexContentType;

  console.log('是否触发预检:', willPreflight);
  console.log('原因:', {
    非简单方法: !isSimpleMethod,
    自定义头部: hasCustomHeaders,
    复杂ContentType: hasComplexContentType
  });
}

// 使用示例
debugCORSPreflight('https://api.example.com/users', 'PUT', {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer token123'
});
```

**2. OPTIONS 请求监控**
```javascript
// 服务端监控 OPTIONS 请求
app.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    console.log('OPTIONS 请求:', {
      url: req.url,
      origin: req.headers.origin,
      requestMethod: req.headers['access-control-request-method'],
      requestHeaders: req.headers['access-control-request-headers'],
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString()
    });
  }
  next();
});
```

#### 7.2.9 最佳实践

**1. 服务端配置**
```javascript
// ✅ 推荐的 OPTIONS 处理方式
app.options('*', (req, res) => {
  res.set({
    'Allow': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Origin': req.headers.origin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': req.headers['access-control-request-headers'] || 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
    'Vary': 'Origin'
  });

  res.status(204).end(); // 无内容响应
});

// ❌ 避免的做法
app.options('*', (req, res) => {
  res.json({ message: 'OPTIONS request' }); // 不必要的响应体
});
```

**2. 缓存策略**
```javascript
// 合理设置预检缓存时间
const corsOptions = {
  origin: true,
  credentials: true,
  maxAge: 86400, // 24小时，平衡性能和灵活性
  optionsSuccessStatus: 204 // 兼容旧版浏览器
};
```

**3. 错误处理**
```javascript
// 处理 OPTIONS 请求错误
app.options('*', (req, res, next) => {
  try {
    // CORS 逻辑
    res.set(corsHeaders);
    res.status(204).end();
  } catch (error) {
    console.error('OPTIONS 请求处理失败:', error);
    res.status(500).end();
  }
});
```

## 8. 错误处理

### 8.1 常见错误状态码
```javascript
// 400 Bad Request - 请求格式错误
{
  "error": "validation_failed",
  "message": "Invalid email format",
  "details": {
    "field": "email",
    "code": "invalid_format"
  }
}

// 401 Unauthorized - 未认证
{
  "error": "unauthorized",
  "message": "Authentication required"
}

// 403 Forbidden - 无权限
{
  "error": "forbidden",
  "message": "Insufficient permissions"
}

// 404 Not Found - 资源不存在
{
  "error": "not_found",
  "message": "User not found"
}

// 409 Conflict - 资源冲突
{
  "error": "conflict",
  "message": "Email already exists"
}

// 422 Unprocessable Entity - 验证失败
{
  "error": "validation_failed",
  "message": "Validation errors",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

## 9. 实际应用示例

### 9.1 用户管理API
```javascript
// 获取用户列表
GET /api/users?page=1&limit=10&status=active

// 获取单个用户
GET /api/users/123

// 创建用户
POST /api/users
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword"
}

// 更新用户
PUT /api/users/123
{
  "name": "John Smith",
  "email": "<EMAIL>"
}

// 部分更新
PATCH /api/users/123
{
  "last_login": "2023-10-21T10:30:00Z"
}

// 删除用户
DELETE /api/users/123
```

### 9.2 文章管理API
```javascript
// 获取文章列表
GET /api/articles?category=tech&status=published&sort=created_at

// 获取文章详情
GET /api/articles/456

// 创建文章
POST /api/articles
{
  "title": "GraphQL vs REST",
  "content": "...",
  "category_id": 1,
  "tags": ["api", "graphql", "rest"]
}

// 发布文章
PATCH /api/articles/456
{
  "status": "published",
  "published_at": "2023-10-21T10:30:00Z"
}
```

## 10. 最佳实践总结

### 10.1 方法选择原则
- **GET**: 只用于获取数据，不应有副作用
- **POST**: 用于创建资源或执行复杂操作
- **PUT**: 用于完整替换资源
- **PATCH**: 用于部分更新资源
- **DELETE**: 用于删除资源

### 10.2 设计建议
- 保持方法语义的一致性
- 合理使用状态码
- 实现适当的错误处理
- 考虑幂等性和安全性
- 提供清晰的API文档

### 10.3 性能优化
- 使用适当的缓存策略
- 实现条件请求（ETag, Last-Modified）
- 支持压缩（gzip）
- 合理设计分页机制

---

**实践建议**:
1. 在设计API时严格遵循HTTP方法的语义
2. 提供一致的错误响应格式
3. 实现适当的验证和权限控制
4. 考虑API的向后兼容性
5. 编写详细的API文档和示例
