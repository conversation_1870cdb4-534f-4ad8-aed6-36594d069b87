/**
 * React Hook 版本的 DPR + 响应式 rem 计算方案
 * 
 * 特性：
 * - React Hook 风格
 * - TypeScript 支持
 * - CSS-in-JS 友好
 * - 组件级别的响应式
 * 
 * 使用方法：
 * import { useResponsiveRem, useDPR } from './dpr-responsive-rem-react';
 * 
 * function MyComponent() {
 *     const { deviceType, px2rem, styles } = useResponsiveRem();
 *     const dpr = useDPR();
 *     
 *     return <div style={styles.container}>Content</div>;
 * }
 */

import { useState, useEffect, useCallback, useMemo } from 'react';

// 默认配置
const DEFAULT_CONFIG = {
    mobileDesignWidth: 750,
    pcDesignWidth: 1920,
    mobileBaseSize: 75,
    pcBaseSize: 16,
    breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1920
    },
    enableDPR: true,
    maxWidth: 2560,
    minWidth: 320
};

/**
 * DPR Hook
 * @returns {number} 当前设备像素比
 */
export const useDPR = () => {
    const [dpr, setDPR] = useState(() => {
        if (typeof window === 'undefined') return 1;
        return Math.min(window.devicePixelRatio || 1, 3);
    });
    
    useEffect(() => {
        if (typeof window === 'undefined') return;
        
        const handleDPRChange = () => {
            const newDPR = Math.min(window.devicePixelRatio || 1, 3);
            setDPR(newDPR);
        };
        
        // 监听 DPR 变化
        if (window.matchMedia) {
            try {
                const mediaQuery = window.matchMedia(`(resolution: ${dpr}dppx)`);
                if (mediaQuery.addListener) {
                    mediaQuery.addListener(handleDPRChange);
                    return () => mediaQuery.removeListener(handleDPRChange);
                }
            } catch (e) {
                console.warn('DPR media query not supported');
            }
        }
    }, [dpr]);
    
    return dpr;
};

/**
 * 设备类型 Hook
 * @param {number} breakpoint 断点值
 * @returns {string} 设备类型 'mobile' | 'tablet' | 'desktop'
 */
export const useDeviceType = (breakpoint = 768) => {
    const [deviceType, setDeviceType] = useState(() => {
        if (typeof window === 'undefined') return 'desktop';
        const width = window.innerWidth;
        if (width <= breakpoint) return 'mobile';
        if (width <= 1024) return 'tablet';
        return 'desktop';
    });
    
    useEffect(() => {
        if (typeof window === 'undefined') return;
        
        const handleResize = () => {
            const width = window.innerWidth;
            let type;
            if (width <= breakpoint) type = 'mobile';
            else if (width <= 1024) type = 'tablet';
            else type = 'desktop';
            
            setDeviceType(type);
        };
        
        let resizeTimer = null;
        const debouncedResize = () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(handleResize, 100);
        };
        
        window.addEventListener('resize', debouncedResize);
        window.addEventListener('orientationchange', () => {
            setTimeout(handleResize, 300);
        });
        
        return () => {
            window.removeEventListener('resize', debouncedResize);
            window.removeEventListener('orientationchange', handleResize);
            clearTimeout(resizeTimer);
        };
    }, [breakpoint]);
    
    return deviceType;
};

/**
 * 响应式 Rem Hook
 * @param {Object} options 配置选项
 * @returns {Object} 响应式数据和工具函数
 */
export const useResponsiveRem = (options = {}) => {
    const config = useMemo(() => ({ ...DEFAULT_CONFIG, ...options }), [options]);
    const dpr = useDPR();
    const deviceType = useDeviceType(config.breakpoints.mobile);
    
    const [remBase, setRemBase] = useState(16);
    const [deviceWidth, setDeviceWidth] = useState(() => {
        if (typeof window === 'undefined') return 375;
        return window.innerWidth;
    });
    
    // 计算字体大小
    const calculateFontSize = useCallback((width, type) => {
        let fontSize;
        
        switch (type) {
            case 'mobile':
                const actualWidth = config.enableDPR ? width * dpr : width;
                fontSize = (actualWidth / config.mobileDesignWidth) * config.mobileBaseSize;
                break;
            case 'tablet':
                const mobileMaxSize = (config.breakpoints.mobile / config.mobileDesignWidth) * config.mobileBaseSize;
                const progress = (width - config.breakpoints.mobile) / (config.breakpoints.tablet - config.breakpoints.mobile);
                fontSize = mobileMaxSize + (config.pcBaseSize - mobileMaxSize) * progress;
                break;
            case 'desktop':
                const scale = Math.min(width / config.pcDesignWidth, 1.5);
                fontSize = config.pcBaseSize * scale;
                break;
            default:
                fontSize = 16;
        }
        
        return fontSize;
    }, [config, dpr]);
    
    // 设置 viewport（仅移动端）
    const setViewport = useCallback(() => {
        if (typeof window === 'undefined' || !config.enableDPR || deviceType !== 'mobile') return;
        
        const scale = 1 / dpr;
        let meta = document.querySelector('meta[name="viewport"]');
        
        if (!meta) {
            meta = document.createElement('meta');
            meta.name = 'viewport';
            document.head.appendChild(meta);
        }
        
        meta.content = [
            'width=device-width',
            `initial-scale=${scale}`,
            `maximum-scale=${scale}`,
            `minimum-scale=${scale}`,
            'user-scalable=no'
        ].join(', ');
    }, [dpr, deviceType, config.enableDPR]);
    
    // 设置 rem
    const setRem = useCallback(() => {
        if (typeof window === 'undefined') return;
        
        let width = window.innerWidth;
        width = Math.max(config.minWidth, Math.min(width, config.maxWidth));
        
        const fontSize = calculateFontSize(width, deviceType);
        
        // 设置根字体大小
        document.documentElement.style.fontSize = fontSize + 'px';
        
        // 设置属性和变量
        document.documentElement.setAttribute('data-device', deviceType);
        document.documentElement.setAttribute('data-dpr', dpr);
        document.documentElement.style.setProperty('--rem-base', fontSize + 'px');
        document.documentElement.style.setProperty('--dpr', dpr);
        document.documentElement.style.setProperty('--hairline', (1 / dpr) + 'px');
        
        setRemBase(fontSize);
        setDeviceWidth(width);
    }, [calculateFontSize, deviceType, dpr, config]);
    
    // 初始化和事件监听
    useEffect(() => {
        setViewport();
        setRem();
    }, [setViewport, setRem]);
    
    // px 转 rem 工具函数
    const px2rem = useCallback((px, targetDeviceType = null) => {
        const type = targetDeviceType || deviceType;
        let baseSize;
        
        switch (type) {
            case 'mobile':
                baseSize = config.mobileBaseSize;
                break;
            case 'tablet':
                baseSize = remBase;
                break;
            case 'desktop':
                baseSize = config.pcBaseSize;
                break;
            default:
                baseSize = 16;
        }
        
        return px / baseSize;
    }, [deviceType, remBase, config]);
    
    // 获取实际像素值
    const getActualPx = useCallback((designPx) => {
        if (deviceType === 'mobile' && config.enableDPR) {
            return designPx * dpr;
        }
        return designPx;
    }, [deviceType, dpr, config.enableDPR]);
    
    // 生成样式对象（CSS-in-JS）
    const styles = useMemo(() => {
        const hairline = 1 / dpr;
        
        return {
            container: {
                padding: deviceType === 'mobile' ? `${px2rem(32)}rem` : '2rem',
                maxWidth: deviceType === 'mobile' ? 'none' : '1200px',
                margin: '0 auto'
            },
            
            hairlineBorder: {
                border: `${hairline}px solid #e0e0e0`
            },
            
            text: {
                fontSize: deviceType === 'mobile' ? `${px2rem(28)}rem` : '1rem',
                lineHeight: deviceType === 'mobile' ? 1.4 : 1.6
            },
            
            button: {
                padding: deviceType === 'mobile' ? `${px2rem(24)}rem ${px2rem(48)}rem` : '0.75rem 1.5rem',
                fontSize: deviceType === 'mobile' ? `${px2rem(28)}rem` : '1rem',
                border: `${hairline}px solid #007bff`,
                borderRadius: deviceType === 'mobile' ? `${px2rem(8)}rem` : '0.25rem'
            }
        };
    }, [deviceType, dpr, px2rem]);
    
    return {
        deviceType,
        dpr,
        remBase,
        deviceWidth,
        px2rem,
        getActualPx,
        styles,
        config
    };
};

/**
 * 响应式样式 Hook
 * @param {Object} styleConfig 样式配置
 * @returns {Object} 响应式样式对象
 */
export const useResponsiveStyles = (styleConfig = {}) => {
    const { deviceType, px2rem, dpr } = useResponsiveRem();
    
    return useMemo(() => {
        const hairline = 1 / dpr;
        
        const baseStyles = {
            mobile: styleConfig.mobile || {},
            tablet: styleConfig.tablet || {},
            desktop: styleConfig.desktop || {}
        };
        
        const currentStyles = baseStyles[deviceType] || {};
        
        // 自动转换 px 值为 rem
        const convertedStyles = {};
        Object.keys(currentStyles).forEach(key => {
            const value = currentStyles[key];
            if (typeof value === 'string' && value.endsWith('px')) {
                const pxValue = parseFloat(value);
                convertedStyles[key] = `${px2rem(pxValue)}rem`;
            } else {
                convertedStyles[key] = value;
            }
        });
        
        return {
            ...convertedStyles,
            '--hairline': `${hairline}px`,
            '--dpr': dpr
        };
    }, [styleConfig, deviceType, px2rem, dpr]);
};

/**
 * 媒体查询 Hook
 * @param {string} query 媒体查询字符串
 * @returns {boolean} 是否匹配
 */
export const useMediaQuery = (query) => {
    const [matches, setMatches] = useState(() => {
        if (typeof window === 'undefined') return false;
        return window.matchMedia(query).matches;
    });
    
    useEffect(() => {
        if (typeof window === 'undefined') return;
        
        const mediaQuery = window.matchMedia(query);
        const handleChange = (e) => setMatches(e.matches);
        
        if (mediaQuery.addListener) {
            mediaQuery.addListener(handleChange);
            return () => mediaQuery.removeListener(handleChange);
        } else if (mediaQuery.addEventListener) {
            mediaQuery.addEventListener('change', handleChange);
            return () => mediaQuery.removeEventListener('change', handleChange);
        }
    }, [query]);
    
    return matches;
};

// 导出默认配置
export { DEFAULT_CONFIG };

/**
 * 使用示例：
 * 
 * import React from 'react';
 * import { useResponsiveRem, useResponsiveStyles, useDPR } from './dpr-responsive-rem-react';
 * 
 * function MyComponent() {
 *     const { deviceType, px2rem, styles } = useResponsiveRem({
 *         mobileDesignWidth: 750,
 *         enableDPR: true
 *     });
 *     
 *     const dpr = useDPR();
 *     
 *     const customStyles = useResponsiveStyles({
 *         mobile: {
 *             fontSize: '28px', // 自动转换为 rem
 *             padding: '32px'
 *         },
 *         desktop: {
 *             fontSize: '16px',
 *             padding: '24px'
 *         }
 *     });
 *     
 *     return (
 *         <div style={styles.container}>
 *             <h1 style={customStyles}>
 *                 当前设备: {deviceType}, DPR: {dpr}
 *             </h1>
 *             <button style={styles.button}>
 *                 响应式按钮
 *             </button>
 *         </div>
 *     );
 * }
 * 
 * export default MyComponent;
 */
