<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Grid 布局完整指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 2.5rem;
        }

        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .demo-section h2 {
            margin-bottom: 20px;
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 1.8rem;
        }

        .demo-section h3 {
            margin: 25px 0 15px 0;
            color: #2980b9;
            font-size: 1.3rem;
        }

        /* 通用Grid容器样式 */
        .grid-demo {
            background: #ecf0f1;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            min-height: 120px;
        }

        .grid-item {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .grid-item:hover {
            background: #2980b9;
            transform: translateY(-2px);
            border-color: #fff;
        }

        /* 不同颜色的grid项目 */
        .grid-item:nth-child(1) { background: #e74c3c; }
        .grid-item:nth-child(2) { background: #3498db; }
        .grid-item:nth-child(3) { background: #2ecc71; }
        .grid-item:nth-child(4) { background: #f39c12; }
        .grid-item:nth-child(5) { background: #9b59b6; }
        .grid-item:nth-child(6) { background: #1abc9c; }
        .grid-item:nth-child(7) { background: #34495e; }
        .grid-item:nth-child(8) { background: #e67e22; }
        .grid-item:nth-child(9) { background: #95a5a6; }

        .grid-item:nth-child(1):hover { background: #c0392b; }
        .grid-item:nth-child(2):hover { background: #2980b9; }
        .grid-item:nth-child(3):hover { background: #27ae60; }
        .grid-item:nth-child(4):hover { background: #e67e22; }
        .grid-item:nth-child(5):hover { background: #8e44ad; }
        .grid-item:nth-child(6):hover { background: #16a085; }
        .grid-item:nth-child(7):hover { background: #2c3e50; }
        .grid-item:nth-child(8):hover { background: #d35400; }
        .grid-item:nth-child(9):hover { background: #7f8c8d; }

        /* 代码展示 */
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }

        .code-block .comment { color: #95a5a6; }
        .code-block .property { color: #3498db; }
        .code-block .value { color: #2ecc71; }

        /* 说明文字 */
        .description {
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }

        .highlight {
            background: #f1c40f;
            color: #2c3e50;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* 表格样式 */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #3498db;
            color: white;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        /* 1. 基础Grid设置 */
        .basic-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        /* 2. 显式网格线命名 */
        .named-lines {
            display: grid;
            grid-template-columns: [start] 1fr [middle] 2fr [end];
            grid-template-rows: [header-start] 100px [header-end content-start] 1fr [content-end];
            gap: 10px;
            height: 200px;
        }

        /* 3. Grid区域命名 */
        .grid-areas {
            display: grid;
            grid-template-areas:
                "header header header"
                "sidebar main main"
                "footer footer footer";
            grid-template-columns: 200px 1fr 1fr;
            grid-template-rows: 80px 1fr 60px;
            gap: 10px;
            height: 300px;
        }

        .grid-areas .header { grid-area: header; }
        .grid-areas .sidebar { grid-area: sidebar; }
        .grid-areas .main { grid-area: main; }
        .grid-areas .footer { grid-area: footer; }

        /* 4. 自动放置 */
        .auto-placement {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-auto-rows: 100px;
            gap: 10px;
        }

        /* 5. 密集填充 */
        .dense-packing {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-auto-flow: dense;
            gap: 10px;
        }

        .dense-packing .wide { grid-column: span 2; }
        .dense-packing .tall { grid-row: span 2; }

        /* 6. 对齐方式 */
        .alignment-demo {
            display: grid;
            grid-template-columns: repeat(3, 150px);
            grid-template-rows: repeat(2, 100px);
            gap: 10px;
            height: 300px;
            justify-content: center;
            align-content: center;
            background: #f8f9fa;
        }

        /* 7. 项目对齐 */
        .item-alignment {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 100px);
            gap: 10px;
        }

        .item-alignment .item-1 { justify-self: start; align-self: start; }
        .item-alignment .item-2 { justify-self: center; align-self: center; }
        .item-alignment .item-3 { justify-self: end; align-self: end; }

        /* 8. 响应式Grid */
        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        /* 9. 嵌套Grid */
        .nested-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 15px;
        }

        .nested-grid .nested {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        /* 10. 重叠元素 */
        .overlapping-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 100px);
            gap: 10px;
        }

        .overlapping-grid .overlap-1 {
            grid-column: 1 / 3;
            grid-row: 1 / 3;
            z-index: 1;
        }

        .overlapping-grid .overlap-2 {
            grid-column: 2 / 4;
            grid-row: 2 / 4;
            z-index: 2;
            opacity: 0.9;
        }

        /* 11. 复杂布局示例 */
        .complex-layout {
            display: grid;
            grid-template-areas:
                "logo nav nav nav user"
                "sidebar main main main aside"
                "sidebar content content content aside"
                "footer footer footer footer footer";
            grid-template-columns: 200px 1fr 1fr 1fr 150px;
            grid-template-rows: 60px 100px 1fr 50px;
            gap: 10px;
            height: 400px;
        }

        .complex-layout .logo { grid-area: logo; }
        .complex-layout .nav { grid-area: nav; }
        .complex-layout .user { grid-area: user; }
        .complex-layout .sidebar { grid-area: sidebar; }
        .complex-layout .main { grid-area: main; }
        .complex-layout .aside { grid-area: aside; }
        .complex-layout .content { grid-area: content; }
        .complex-layout .footer { grid-area: footer; }

        /* 12. 动画效果 */
        .animated-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            transition: all 0.3s ease;
        }

        .animated-grid:hover {
            grid-template-columns: 2fr 1fr 1fr;
        }

        .animated-grid .grid-item {
            transition: all 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .grid-areas {
                grid-template-areas:
                    "header"
                    "main"
                    "sidebar"
                    "footer";
                grid-template-columns: 1fr;
                grid-template-rows: 80px 1fr 150px 60px;
            }

            .complex-layout {
                grid-template-areas:
                    "logo user"
                    "nav nav"
                    "main main"
                    "content content"
                    "sidebar aside"
                    "footer footer";
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 60px 50px 100px 1fr 100px 50px;
            }

            .alignment-demo {
                grid-template-columns: repeat(2, 150px);
            }
        }

        @media (max-width: 480px) {
            .responsive-grid {
                grid-template-columns: 1fr;
            }

            .alignment-demo {
                grid-template-columns: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 CSS Grid 布局完整指南</h1>

        <!-- 目录 -->
        <div class="demo-section">
            <h2>📚 内容目录</h2>
            <div style="columns: 2; gap: 30px;">
                <ul style="margin-left: 20px; line-height: 2;">
                    <li><a href="#basic-concepts">基础概念</a></li>
                    <li><a href="#grid-container">Grid容器属性</a></li>
                    <li><a href="#grid-items">Grid项目属性</a></li>
                    <li><a href="#named-lines">命名网格线</a></li>
                    <li><a href="#grid-areas">Grid区域</a></li>
                    <li><a href="#auto-placement">自动放置</a></li>
                    <li><a href="#alignment">对齐方式</a></li>
                    <li><a href="#responsive">响应式Grid</a></li>
                    <li><a href="#advanced">高级技巧</a></li>
                    <li><a href="#real-world">实际应用</a></li>
                </ul>
            </div>
        </div>

        <!-- 基础概念 -->
        <div class="demo-section" id="basic-concepts">
            <h2>📖 基础概念</h2>
            <div class="description">
                CSS Grid是一个二维布局系统，可以同时处理行和列。与Flexbox的一维布局不同，Grid可以创建复杂的网格布局。
            </div>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>术语</th>
                        <th>含义</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Grid容器</strong></td>
                        <td>设置了 <code>display: grid</code> 的元素</td>
                        <td>所有Grid项目的直接父元素</td>
                    </tr>
                    <tr>
                        <td><strong>Grid项目</strong></td>
                        <td>Grid容器的直接子元素</td>
                        <td>会自动成为Grid项目</td>
                    </tr>
                    <tr>
                        <td><strong>网格线</strong></td>
                        <td>构成网格结构的分界线</td>
                        <td>可以是垂直的（列线）或水平的（行线）</td>
                    </tr>
                    <tr>
                        <td><strong>网格轨道</strong></td>
                        <td>两条相邻网格线之间的空间</td>
                        <td>可以是行轨道或列轨道</td>
                    </tr>
                    <tr>
                        <td><strong>网格单元</strong></td>
                        <td>四条网格线围成的空间</td>
                        <td>Grid的最小单位</td>
                    </tr>
                    <tr>
                        <td><strong>网格区域</strong></td>
                        <td>由四条网格线围成的矩形区域</td>
                        <td>可以包含一个或多个网格单元</td>
                    </tr>
                </tbody>
            </table>

            <h3>基础Grid示例</h3>
            <div class="code-block">
<span class="comment">/* 创建3x3的网格 */</span>
.<span class="property">grid-container</span> {
    <span class="property">display</span>: <span class="value">grid</span>;
    <span class="property">grid-template-columns</span>: <span class="value">repeat(3, 1fr)</span>;
    <span class="property">grid-template-rows</span>: <span class="value">repeat(3, 100px)</span>;
    <span class="property">gap</span>: <span class="value">10px</span>;
}
            </div>
            <div class="grid-demo basic-grid">
                <div class="grid-item">1</div>
                <div class="grid-item">2</div>
                <div class="grid-item">3</div>
                <div class="grid-item">4</div>
                <div class="grid-item">5</div>
                <div class="grid-item">6</div>
            </div>
        </div>

        <!-- Grid容器属性 -->
        <div class="demo-section" id="grid-container">
            <h2>🏗️ Grid容器属性</h2>

            <h3>1. display</h3>
            <div class="description">
                定义元素为Grid容器，子元素自动成为Grid项目
            </div>
            <div class="code-block">
.<span class="property">container</span> {
    <span class="property">display</span>: <span class="value">grid</span>;        <span class="comment">/* 块级Grid容器 */</span>
    <span class="property">display</span>: <span class="value">inline-grid</span>; <span class="comment">/* 内联Grid容器 */</span>
}
            </div>

            <h3>2. grid-template-columns / grid-template-rows</h3>
            <div class="description">
                定义网格的列和行的尺寸
            </div>
            <div class="code-block">
.<span class="property">grid</span> {
    <span class="comment">/* 固定尺寸 */</span>
    <span class="property">grid-template-columns</span>: <span class="value">100px 200px 100px</span>;

    <span class="comment">/* 弹性尺寸 */</span>
    <span class="property">grid-template-columns</span>: <span class="value">1fr 2fr 1fr</span>;

    <span class="comment">/* 混合尺寸 */</span>
    <span class="property">grid-template-columns</span>: <span class="value">200px 1fr auto</span>;

    <span class="comment">/* 重复模式 */</span>
    <span class="property">grid-template-columns</span>: <span class="value">repeat(3, 1fr)</span>;
    <span class="property">grid-template-columns</span>: <span class="value">repeat(auto-fit, minmax(200px, 1fr))</span>;
}
            </div>

            <h3>3. gap (grid-gap)</h3>
            <div class="description">
                设置网格项目之间的间距
            </div>
            <div class="code-block">
.<span class="property">grid</span> {
    <span class="property">gap</span>: <span class="value">10px</span>;           <span class="comment">/* 行列间距都是10px */</span>
    <span class="property">gap</span>: <span class="value">10px 20px</span>;     <span class="comment">/* 行间距10px，列间距20px */</span>

    <span class="comment">/* 分别设置 */</span>
    <span class="property">row-gap</span>: <span class="value">10px</span>;
    <span class="property">column-gap</span>: <span class="value">20px</span>;
}
            </div>
        </div>

        <!-- 命名网格线 -->
        <div class="demo-section" id="named-lines">
            <h2>🏷️ 命名网格线</h2>
            <div class="description">
                可以为网格线命名，使布局更加语义化和易于维护
            </div>

            <div class="code-block">
.<span class="property">grid</span> {
    <span class="property">grid-template-columns</span>:
        <span class="value">[start] 1fr [middle] 2fr [end]</span>;
    <span class="property">grid-template-rows</span>:
        <span class="value">[header-start] 100px [header-end content-start] 1fr [content-end]</span>;
}

<span class="comment">/* 使用命名线定位 */</span>
.<span class="property">item</span> {
    <span class="property">grid-column</span>: <span class="value">start / middle</span>;
    <span class="property">grid-row</span>: <span class="value">header-start / content-end</span>;
}
            </div>

            <div class="grid-demo named-lines">
                <div class="grid-item">使用命名线定位</div>
                <div class="grid-item">项目2</div>
            </div>
        </div>

        <!-- Grid区域 -->
        <div class="demo-section" id="grid-areas">
            <h2>🗺️ Grid区域 (grid-template-areas)</h2>
            <div class="description">
                通过命名区域创建直观的布局，特别适合复杂的页面布局
            </div>

            <div class="code-block">
.<span class="property">layout</span> {
    <span class="property">display</span>: <span class="value">grid</span>;
    <span class="property">grid-template-areas</span>:
        <span class="value">"header header header"
        "sidebar main main"
        "footer footer footer"</span>;
    <span class="property">grid-template-columns</span>: <span class="value">200px 1fr 1fr</span>;
    <span class="property">grid-template-rows</span>: <span class="value">80px 1fr 60px</span>;
}

<span class="comment">/* 指定元素到对应区域 */</span>
.<span class="property">header</span> { <span class="property">grid-area</span>: <span class="value">header</span>; }
.<span class="property">sidebar</span> { <span class="property">grid-area</span>: <span class="value">sidebar</span>; }
.<span class="property">main</span> { <span class="property">grid-area</span>: <span class="value">main</span>; }
.<span class="property">footer</span> { <span class="property">grid-area</span>: <span class="value">footer</span>; }
            </div>

            <div class="grid-demo grid-areas">
                <div class="grid-item header">Header</div>
                <div class="grid-item sidebar">Sidebar</div>
                <div class="grid-item main">Main Content</div>
                <div class="grid-item footer">Footer</div>
            </div>
        </div>

        <!-- 自动放置 -->
        <div class="demo-section" id="auto-placement">
            <h2>🔄 自动放置 (grid-auto-*)</h2>

            <h3>1. grid-auto-rows / grid-auto-columns</h3>
            <div class="description">
                定义隐式网格轨道的尺寸（当项目超出显式网格时）
            </div>
            <div class="code-block">
.<span class="property">grid</span> {
    <span class="property">grid-template-columns</span>: <span class="value">repeat(3, 1fr)</span>;
    <span class="property">grid-auto-rows</span>: <span class="value">100px</span>; <span class="comment">/* 自动创建的行高度 */</span>
}
            </div>
            <div class="grid-demo auto-placement">
                <div class="grid-item">1</div>
                <div class="grid-item">2</div>
                <div class="grid-item">3</div>
                <div class="grid-item">4</div>
                <div class="grid-item">5</div>
                <div class="grid-item">6</div>
                <div class="grid-item">7</div>
            </div>

            <h3>2. grid-auto-flow</h3>
            <div class="description">
                控制自动放置算法的工作方式
            </div>
            <div class="code-block">
.<span class="property">grid</span> {
    <span class="property">grid-auto-flow</span>: <span class="value">row</span>;    <span class="comment">/* 按行填充（默认） */</span>
    <span class="property">grid-auto-flow</span>: <span class="value">column</span>; <span class="comment">/* 按列填充 */</span>
    <span class="property">grid-auto-flow</span>: <span class="value">dense</span>;  <span class="comment">/* 密集填充 */</span>
}
            </div>

            <h3>3. 密集填充演示</h3>
            <div class="description">
                <code>dense</code>关键字让Grid尝试填充网格中的空洞
            </div>
            <div class="grid-demo dense-packing">
                <div class="grid-item">1</div>
                <div class="grid-item wide">2 (宽)</div>
                <div class="grid-item">3</div>
                <div class="grid-item tall">4 (高)</div>
                <div class="grid-item">5</div>
                <div class="grid-item">6</div>
                <div class="grid-item">7</div>
                <div class="grid-item">8</div>
            </div>
        </div>

        <!-- 对齐方式 -->
        <div class="demo-section" id="alignment">
            <h2>📐 对齐方式</h2>

            <h3>1. 容器对齐 (justify-content / align-content)</h3>
            <div class="description">
                控制整个网格在容器中的对齐方式
            </div>
            <div class="code-block">
.<span class="property">grid</span> {
    <span class="property">justify-content</span>: <span class="value">start | end | center | stretch | space-around | space-between | space-evenly</span>;
    <span class="property">align-content</span>: <span class="value">start | end | center | stretch | space-around | space-between | space-evenly</span>;
}
            </div>
            <div class="grid-demo alignment-demo">
                <div class="grid-item">1</div>
                <div class="grid-item">2</div>
                <div class="grid-item">3</div>
                <div class="grid-item">4</div>
                <div class="grid-item">5</div>
                <div class="grid-item">6</div>
            </div>

            <h3>2. 项目对齐 (justify-self / align-self)</h3>
            <div class="description">
                控制单个项目在其网格区域中的对齐方式
            </div>
            <div class="code-block">
.<span class="property">item-1</span> { <span class="property">justify-self</span>: <span class="value">start</span>; <span class="property">align-self</span>: <span class="value">start</span>; }
.<span class="property">item-2</span> { <span class="property">justify-self</span>: <span class="value">center</span>; <span class="property">align-self</span>: <span class="value">center</span>; }
.<span class="property">item-3</span> { <span class="property">justify-self</span>: <span class="value">end</span>; <span class="property">align-self</span>: <span class="value">end</span>; }
            </div>
            <div class="grid-demo item-alignment">
                <div class="grid-item item-1">Start</div>
                <div class="grid-item item-2">Center</div>
                <div class="grid-item item-3">End</div>
                <div class="grid-item">Default</div>
                <div class="grid-item">Default</div>
                <div class="grid-item">Default</div>
            </div>
        </div>

        <!-- Grid项目属性 -->
        <div class="demo-section" id="grid-items">
            <h2>📦 Grid项目属性</h2>

            <h3>1. grid-column / grid-row</h3>
            <div class="description">
                指定项目在网格中的位置和跨度
            </div>
            <div class="code-block">
<span class="comment">/* 基于线号定位 */</span>
.<span class="property">item</span> {
    <span class="property">grid-column</span>: <span class="value">1 / 3</span>;     <span class="comment">/* 从第1条线到第3条线 */</span>
    <span class="property">grid-row</span>: <span class="value">2 / 4</span>;        <span class="comment">/* 从第2条线到第4条线 */</span>
}

<span class="comment">/* 使用span关键字 */</span>
.<span class="property">item</span> {
    <span class="property">grid-column</span>: <span class="value">span 2</span>;    <span class="comment">/* 跨越2列 */</span>
    <span class="property">grid-row</span>: <span class="value">span 3</span>;       <span class="comment">/* 跨越3行 */</span>
}

<span class="comment">/* 简写形式 */</span>
.<span class="property">item</span> {
    <span class="property">grid-area</span>: <span class="value">2 / 1 / 4 / 3</span>; <span class="comment">/* row-start / col-start / row-end / col-end */</span>
}
            </div>

            <h3>2. 项目定位示例</h3>
            <div class="grid-demo" style="grid-template-columns: repeat(4, 1fr); grid-template-rows: repeat(3, 80px);">
                <div class="grid-item" style="grid-column: 1 / 3; grid-row: 1 / 2;">跨2列</div>
                <div class="grid-item" style="grid-column: 3 / 5; grid-row: 1 / 3;">跨2行</div>
                <div class="grid-item" style="grid-column: 1 / 2; grid-row: 2 / 4;">跨2行</div>
                <div class="grid-item" style="grid-column: 2 / 4; grid-row: 3 / 4;">跨2列</div>
                <div class="grid-item" style="grid-column: 4 / 5; grid-row: 3 / 4;">单元格</div>
            </div>
        </div>

        <!-- 响应式Grid -->
        <div class="demo-section" id="responsive">
            <h2>📱 响应式Grid</h2>

            <h3>1. auto-fit vs auto-fill</h3>
            <div class="description">
                <code>auto-fit</code>和<code>auto-fill</code>都能创建响应式网格，但行为略有不同
            </div>
            <div class="code-block">
<span class="comment">/* auto-fit: 拉伸项目填充容器 */</span>
<span class="property">grid-template-columns</span>: <span class="value">repeat(auto-fit, minmax(200px, 1fr))</span>;

<span class="comment">/* auto-fill: 保持项目尺寸，可能留空 */</span>
<span class="property">grid-template-columns</span>: <span class="value">repeat(auto-fill, minmax(200px, 1fr))</span>;
            </div>

            <h3>2. 响应式卡片网格</h3>
            <div class="grid-demo responsive-grid">
                <div class="grid-item">卡片 1</div>
                <div class="grid-item">卡片 2</div>
                <div class="grid-item">卡片 3</div>
                <div class="grid-item">卡片 4</div>
                <div class="grid-item">卡片 5</div>
                <div class="grid-item">卡片 6</div>
            </div>
        </div>

        <!-- 高级技巧 -->
        <div class="demo-section" id="advanced">
            <h2>🚀 高级技巧</h2>

            <h3>1. 嵌套Grid</h3>
            <div class="description">
                Grid容器内的项目也可以成为Grid容器，创建嵌套布局
            </div>
            <div class="code-block">
.<span class="property">parent-grid</span> {
    <span class="property">display</span>: <span class="value">grid</span>;
    <span class="property">grid-template-columns</span>: <span class="value">1fr 2fr</span>;
}

.<span class="property">nested-grid</span> {
    <span class="property">display</span>: <span class="value">grid</span>;
    <span class="property">grid-template-columns</span>: <span class="value">1fr 1fr</span>;
}
            </div>
            <div class="grid-demo nested-grid">
                <div class="grid-item">父级项目</div>
                <div class="grid-item nested">
                    <div class="grid-item">嵌套1</div>
                    <div class="grid-item">嵌套2</div>
                    <div class="grid-item">嵌套3</div>
                    <div class="grid-item">嵌套4</div>
                </div>
            </div>

            <h3>2. 重叠元素</h3>
            <div class="description">
                Grid允许元素重叠，通过z-index控制层级
            </div>
            <div class="code-block">
.<span class="property">overlap-1</span> {
    <span class="property">grid-column</span>: <span class="value">1 / 3</span>;
    <span class="property">grid-row</span>: <span class="value">1 / 3</span>;
    <span class="property">z-index</span>: <span class="value">1</span>;
}

.<span class="property">overlap-2</span> {
    <span class="property">grid-column</span>: <span class="value">2 / 4</span>;
    <span class="property">grid-row</span>: <span class="value">2 / 4</span>;
    <span class="property">z-index</span>: <span class="value">2</span>;
}
            </div>
            <div class="grid-demo overlapping-grid">
                <div class="grid-item overlap-1">重叠元素1</div>
                <div class="grid-item overlap-2">重叠元素2</div>
            </div>

            <h3>3. 动画效果</h3>
            <div class="description">
                Grid属性支持CSS过渡动画，可以创建流畅的布局变化
            </div>
            <div class="code-block">
.<span class="property">animated-grid</span> {
    <span class="property">transition</span>: <span class="value">all 0.3s ease</span>;
    <span class="property">grid-template-columns</span>: <span class="value">repeat(3, 1fr)</span>;
}

.<span class="property">animated-grid</span>:<span class="value">hover</span> {
    <span class="property">grid-template-columns</span>: <span class="value">2fr 1fr 1fr</span>;
}
            </div>
            <div class="grid-demo animated-grid">
                <div class="grid-item">悬停看动画</div>
                <div class="grid-item">项目2</div>
                <div class="grid-item">项目3</div>
            </div>
        </div>

        <!-- 实际应用 -->
        <div class="demo-section" id="real-world">
            <h2>🌍 实际应用案例</h2>

            <h3>1. 复杂页面布局</h3>
            <div class="description">
                使用Grid创建复杂的页面布局，包含多个区域和响应式设计
            </div>
            <div class="code-block">
.<span class="property">layout</span> {
    <span class="property">display</span>: <span class="value">grid</span>;
    <span class="property">grid-template-areas</span>:
        <span class="value">"logo nav nav nav user"
        "sidebar main main main aside"
        "sidebar content content content aside"
        "footer footer footer footer footer"</span>;
    <span class="property">grid-template-columns</span>: <span class="value">200px 1fr 1fr 1fr 150px</span>;
    <span class="property">grid-template-rows</span>: <span class="value">60px 100px 1fr 50px</span>;
}
            </div>
            <div class="grid-demo complex-layout">
                <div class="grid-item logo">Logo</div>
                <div class="grid-item nav">Navigation</div>
                <div class="grid-item user">User</div>
                <div class="grid-item sidebar">Sidebar</div>
                <div class="grid-item main">Main Banner</div>
                <div class="grid-item aside">Aside</div>
                <div class="grid-item content">Content Area</div>
                <div class="grid-item footer">Footer</div>
            </div>

            <h3>2. Grid vs Flexbox 对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特性</th>
                        <th>CSS Grid</th>
                        <th>Flexbox</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>维度</strong></td>
                        <td>二维（行和列）</td>
                        <td>一维（行或列）</td>
                    </tr>
                    <tr>
                        <td><strong>布局方式</strong></td>
                        <td>基于网格的布局</td>
                        <td>基于轴的布局</td>
                    </tr>
                    <tr>
                        <td><strong>适用场景</strong></td>
                        <td>页面布局、复杂网格</td>
                        <td>组件布局、一维排列</td>
                    </tr>
                    <tr>
                        <td><strong>项目控制</strong></td>
                        <td>容器和项目都有强控制力</td>
                        <td>主要由容器控制</td>
                    </tr>
                    <tr>
                        <td><strong>浏览器支持</strong></td>
                        <td>现代浏览器</td>
                        <td>更广泛的支持</td>
                    </tr>
                </tbody>
            </table>

            <h3>3. 最佳实践建议</h3>
            <div class="description">
                <strong>🎯 何时使用Grid：</strong>
                <ul style="margin: 10px 0 10px 20px;">
                    <li>需要二维布局（同时控制行和列）</li>
                    <li>复杂的页面布局</li>
                    <li>需要精确控制项目位置</li>
                    <li>响应式网格系统</li>
                </ul>

                <strong>🎯 何时使用Flexbox：</strong>
                <ul style="margin: 10px 0 10px 20px;">
                    <li>一维布局（只需要行或列）</li>
                    <li>组件内部布局</li>
                    <li>需要项目自动分配空间</li>
                    <li>简单的对齐需求</li>
                </ul>

                <strong>💡 组合使用：</strong>
                <ul style="margin: 10px 0 10px 20px;">
                    <li>Grid用于页面整体布局</li>
                    <li>Flexbox用于组件内部布局</li>
                    <li>根据具体需求选择最合适的工具</li>
                </ul>
            </div>

            <h3>4. 常用Grid模式</h3>
            <div class="code-block">
<span class="comment">/* 1. 等宽列 */</span>
<span class="property">grid-template-columns</span>: <span class="value">repeat(4, 1fr)</span>;

<span class="comment">/* 2. 响应式卡片 */</span>
<span class="property">grid-template-columns</span>: <span class="value">repeat(auto-fit, minmax(250px, 1fr))</span>;

<span class="comment">/* 3. 圣杯布局 */</span>
<span class="property">grid-template-columns</span>: <span class="value">200px 1fr 200px</span>;
<span class="property">grid-template-rows</span>: <span class="value">auto 1fr auto</span>;

<span class="comment">/* 4. 12列网格系统 */</span>
<span class="property">grid-template-columns</span>: <span class="value">repeat(12, 1fr)</span>;

<span class="comment">/* 5. 流体网格 */</span>
<span class="property">grid-template-columns</span>: <span class="value">minmax(200px, 1fr) 3fr minmax(150px, 1fr)</span>;
            </div>

            <h3>5. 调试技巧</h3>
            <div class="description">
                <strong>🔧 浏览器开发者工具：</strong>
                <ul style="margin: 10px 0 10px 20px;">
                    <li>Chrome/Edge: 在Elements面板中点击Grid标签</li>
                    <li>Firefox: 在Inspector中查看Grid overlay</li>
                    <li>Safari: 在Elements面板中启用Grid显示</li>
                </ul>

                <strong>🎨 CSS调试：</strong>
                <ul style="margin: 10px 0 10px 20px;">
                    <li>给Grid容器添加背景色和边框</li>
                    <li>给Grid项目添加不同的背景色</li>
                    <li>使用<code>gap</code>属性让网格结构更清晰</li>
                </ul>
            </div>
        </div>

        <!-- 浏览器支持 -->
        <div class="demo-section">
            <h2>🌐 浏览器支持</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>浏览器</th>
                        <th>版本</th>
                        <th>支持程度</th>
                        <th>注意事项</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Chrome</strong></td>
                        <td>57+</td>
                        <td>完全支持</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td><strong>Firefox</strong></td>
                        <td>52+</td>
                        <td>完全支持</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td><strong>Safari</strong></td>
                        <td>10.1+</td>
                        <td>完全支持</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td><strong>Edge</strong></td>
                        <td>16+</td>
                        <td>完全支持</td>
                        <td>旧版本需要前缀</td>
                    </tr>
                    <tr>
                        <td><strong>IE</strong></td>
                        <td>10-11</td>
                        <td>部分支持</td>
                        <td>需要-ms-前缀，语法不同</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== CSS Grid 布局完整指南 ===');
            console.log('💡 提示：');
            console.log('1. 使用浏览器开发者工具查看Grid结构');
            console.log('2. 尝试调整窗口大小观察响应式效果');
            console.log('3. Grid和Flexbox可以组合使用');
            console.log('4. 现代浏览器都支持Grid布局');

            // 添加平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // 添加交互效果
        document.querySelectorAll('.grid-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                this.style.zIndex = '10';
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.zIndex = '';
                }, 300);
            });
        });

        // 检测Grid支持
        if (CSS.supports('display', 'grid')) {
            console.log('✅ 浏览器支持CSS Grid');
        } else {
            console.log('❌ 浏览器不支持CSS Grid');
        }
    </script>
</body>
</html>