<template>
  <div class="task-detail-container">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <div class="title-section">
          <h1>{{ task.title }}</h1>
          <el-tag :type="getStatusType(task.status)">
            {{ getStatusText(task.status) }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="editTask">
          <el-icon><Edit /></el-icon>
          编辑任务
        </el-button>
        <el-button type="danger" @click="deleteTask">
          <el-icon><Delete /></el-icon>
          删除任务
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧主要内容 -->
      <el-col :span="16">
        <!-- 任务信息卡片 -->
        <el-card class="task-info-card">
          <template #header>
            <span>任务详情</span>
          </template>
          
          <div class="task-description">
            <h3>任务描述</h3>
            <p>{{ task.description }}</p>
          </div>
          
          <div class="task-details">
            <h3>基本信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="任务状态">
                <el-tag :type="getStatusType(task.status)">
                  {{ getStatusText(task.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="优先级">
                <el-tag :type="getPriorityType(task.priority)">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="负责人">
                {{ task.assignee?.username || '未分配' }}
              </el-descriptions-item>
              <el-descriptions-item label="所属项目">
                <el-link
                  v-if="task.project"
                  type="primary"
                  @click="viewProject(task.project.id)"
                >
                  {{ task.project.name }}
                </el-link>
                <span v-else>无项目</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(task.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDate(task.updatedAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="截止时间">
                <span :class="{ 'overdue': isOverdue(task.dueDate) }">
                  {{ task.dueDate ? formatDate(task.dueDate) : '未设置' }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="预计工时">
                {{ task.estimatedHours ? `${task.estimatedHours} 小时` : '未设置' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 任务进度卡片 -->
        <el-card class="progress-card">
          <template #header>
            <div class="card-header">
              <span>任务进度</span>
              <el-button size="small" @click="updateProgress">更新进度</el-button>
            </div>
          </template>
          
          <div class="progress-content">
            <div class="progress-info">
              <span class="progress-text">{{ task.progress }}% 完成</span>
              <el-progress
                :percentage="task.progress"
                :stroke-width="12"
                :color="getProgressColor(task.progress)"
              />
            </div>
            
            <div class="time-tracking">
              <h4>时间跟踪</h4>
              <div class="time-stats">
                <div class="time-item">
                  <span class="label">已用时间:</span>
                  <span class="value">{{ task.actualHours || 0 }} 小时</span>
                </div>
                <div class="time-item">
                  <span class="label">预计时间:</span>
                  <span class="value">{{ task.estimatedHours || 0 }} 小时</span>
                </div>
                <div class="time-item">
                  <span class="label">剩余时间:</span>
                  <span class="value">
                    {{ Math.max(0, (task.estimatedHours || 0) - (task.actualHours || 0)) }} 小时
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 评论区域 -->
        <el-card class="comments-card">
          <template #header>
            <span>评论 ({{ task.comments?.length || 0 }})</span>
          </template>
          
          <div class="comment-form">
            <el-input
              v-model="newComment"
              type="textarea"
              :rows="3"
              placeholder="添加评论..."
              class="comment-input"
            />
            <div class="comment-actions">
              <el-button type="primary" @click="addComment" :loading="addingComment">
                发表评论
              </el-button>
            </div>
          </div>
          
          <div class="comments-list">
            <div
              v-for="comment in task.comments"
              :key="comment.id"
              class="comment-item"
            >
              <div class="comment-header">
                <el-avatar :size="32" :src="comment.author.avatar">
                  {{ comment.author.username.charAt(0) }}
                </el-avatar>
                <div class="comment-meta">
                  <span class="author">{{ comment.author.username }}</span>
                  <span class="time">{{ formatTime(comment.createdAt) }}</span>
                </div>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
            </div>
            
            <div v-if="!task.comments?.length" class="empty-comments">
              <p>暂无评论</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧侧边栏 -->
      <el-col :span="8">
        <!-- 快速操作 -->
        <el-card class="actions-card">
          <template #header>
            <span>快速操作</span>
          </template>
          
          <div class="quick-actions">
            <el-button
              v-if="task.status === 'PENDING'"
              type="success"
              @click="startTask"
              block
            >
              开始任务
            </el-button>
            <el-button
              v-if="task.status === 'IN_PROGRESS'"
              type="primary"
              @click="completeTask"
              block
            >
              完成任务
            </el-button>
            <el-button
              v-if="task.status !== 'CANCELLED'"
              type="warning"
              @click="pauseTask"
              block
            >
              暂停任务
            </el-button>
            <el-button type="info" @click="assignTask" block>
              重新分配
            </el-button>
          </div>
        </el-card>

        <!-- 相关任务 -->
        <el-card class="related-tasks-card">
          <template #header>
            <span>相关任务</span>
          </template>
          
          <div class="related-tasks">
            <div
              v-for="relatedTask in relatedTasks"
              :key="relatedTask.id"
              class="related-task-item"
              @click="viewTask(relatedTask.id)"
            >
              <div class="task-info">
                <div class="task-title">{{ relatedTask.title }}</div>
                <el-tag :type="getStatusType(relatedTask.status)" size="small">
                  {{ getStatusText(relatedTask.status) }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="!relatedTasks.length" class="empty-related">
              <p>暂无相关任务</p>
            </div>
          </div>
        </el-card>

        <!-- 任务历史 -->
        <el-card class="history-card">
          <template #header>
            <span>操作历史</span>
          </template>
          
          <div class="history-list">
            <div
              v-for="history in task.history"
              :key="history.id"
              class="history-item"
            >
              <div class="history-time">{{ formatTime(history.createdAt) }}</div>
              <div class="history-content">
                <span class="user">{{ history.user.username }}</span>
                {{ history.action }}
              </div>
            </div>
            
            <div v-if="!task.history?.length" class="empty-history">
              <p>暂无操作记录</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  Delete
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const addingComment = ref(false)
const newComment = ref('')

// 模拟任务数据
const task = ref({
  id: '1',
  title: '用户界面设计',
  description: '设计新的用户界面和用户体验，包括主页、产品页面、用户中心等核心页面的设计。需要考虑响应式设计和用户体验优化。',
  status: 'IN_PROGRESS',
  priority: 'HIGH',
  progress: 65,
  assignee: { id: '1', username: '李四', avatar: '' },
  project: { id: '1', name: '电商平台重构' },
  createdAt: '2024-01-15T00:00:00Z',
  updatedAt: '2024-01-20T00:00:00Z',
  dueDate: '2024-02-01T00:00:00Z',
  estimatedHours: 40,
  actualHours: 26,
  comments: [
    {
      id: '1',
      content: '已完成主页设计，正在进行产品页面设计',
      author: { username: '李四', avatar: '' },
      createdAt: '2024-01-18T10:30:00Z'
    },
    {
      id: '2',
      content: '设计稿看起来不错，建议在移动端适配上再优化一下',
      author: { username: '张三', avatar: '' },
      createdAt: '2024-01-19T14:20:00Z'
    }
  ],
  history: [
    {
      id: '1',
      action: '开始了任务',
      user: { username: '李四' },
      createdAt: '2024-01-15T09:00:00Z'
    },
    {
      id: '2',
      action: '更新了任务进度至 65%',
      user: { username: '李四' },
      createdAt: '2024-01-20T16:30:00Z'
    }
  ]
})

// 相关任务
const relatedTasks = ref([
  {
    id: '2',
    title: 'API接口开发',
    status: 'IN_PROGRESS'
  },
  {
    id: '3',
    title: '前端组件开发',
    status: 'PENDING'
  }
])

// 计算属性
const isOverdue = computed(() => (dueDate: string) => {
  if (!dueDate) return false
  return new Date(dueDate) < new Date()
})

// 方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    PENDING: 'info',
    IN_PROGRESS: 'warning',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    PENDING: '待开始',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return texts[status] || '未知'
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    HIGH: 'danger',
    MEDIUM: 'warning',
    LOW: 'success'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    HIGH: '高优先级',
    MEDIUM: '中优先级',
    LOW: '低优先级'
  }
  return texts[priority] || '未知'
}

const getProgressColor = (progress: number) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const goBack = () => {
  router.go(-1)
}

const editTask = () => {
  // 跳转到编辑页面或打开编辑对话框
  ElMessage.info('编辑功能开发中')
}

const deleteTask = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      type: 'warning'
    })
    
    // 这里应该调用 GraphQL mutation 删除任务
    ElMessage.success('任务删除成功')
    router.push('/tasks')
  } catch {
    // 用户取消删除
  }
}

const updateProgress = async () => {
  try {
    const { value } = await ElMessageBox.prompt('请输入新的进度百分比', '更新进度', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^(100|[1-9]?\d)$/,
      inputErrorMessage: '请输入 0-100 之间的数字'
    })
    
    task.value.progress = parseInt(value)
    ElMessage.success('进度更新成功')
  } catch {
    // 用户取消
  }
}

const startTask = async () => {
  task.value.status = 'IN_PROGRESS'
  ElMessage.success('任务已开始')
}

const completeTask = async () => {
  task.value.status = 'COMPLETED'
  task.value.progress = 100
  ElMessage.success('任务已完成')
}

const pauseTask = async () => {
  task.value.status = 'PENDING'
  ElMessage.success('任务已暂停')
}

const assignTask = () => {
  ElMessage.info('重新分配功能开发中')
}

const addComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  addingComment.value = true
  try {
    // 这里应该调用 GraphQL mutation 添加评论
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const comment = {
      id: Date.now().toString(),
      content: newComment.value,
      author: { username: '当前用户', avatar: '' },
      createdAt: new Date().toISOString()
    }
    
    task.value.comments = task.value.comments || []
    task.value.comments.unshift(comment)
    newComment.value = ''
    
    ElMessage.success('评论发表成功')
  } catch (error) {
    ElMessage.error('评论发表失败')
  } finally {
    addingComment.value = false
  }
}

const viewProject = (projectId: string) => {
  router.push(`/projects/${projectId}`)
}

const viewTask = (taskId: string) => {
  router.push(`/tasks/${taskId}`)
}

// 生命周期
onMounted(() => {
  // 根据路由参数加载任务数据
  const taskId = route.params.id
  // 这里应该调用 GraphQL 查询加载任务详情
  console.log('Loading task:', taskId)
})
</script>

<style scoped lang="scss">
.task-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    .title-section {
      display: flex;
      align-items: center;
      gap: 10px;

      h1 {
        margin: 0;
        color: #303133;
      }
    }
  }

  .header-right {
    display: flex;
    gap: 10px;
  }
}

.task-info-card,
.progress-card,
.comments-card {
  margin-bottom: 20px;

  h3 {
    color: #303133;
    margin: 0 0 15px 0;
    font-size: 16px;
  }

  h4 {
    color: #303133;
    margin: 15px 0 10px 0;
    font-size: 14px;
  }
}

.task-description p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.overdue {
  color: #f56c6c;
  font-weight: 500;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-content {
  .progress-info {
    margin-bottom: 20px;

    .progress-text {
      display: block;
      margin-bottom: 10px;
      color: #606266;
      font-weight: 500;
    }
  }

  .time-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .time-item {
      display: flex;
      justify-content: space-between;

      .label {
        color: #909399;
      }

      .value {
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

.comment-form {
  margin-bottom: 20px;

  .comment-input {
    margin-bottom: 10px;
  }

  .comment-actions {
    display: flex;
    justify-content: flex-end;
  }
}

.comments-list {
  .comment-item {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .comment-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .comment-meta {
        .author {
          font-weight: 500;
          color: #303133;
          margin-right: 10px;
        }

        .time {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .comment-content {
      color: #606266;
      line-height: 1.5;
      margin-left: 42px;
    }
  }
}

.actions-card,
.related-tasks-card,
.history-card {
  margin-bottom: 20px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.related-tasks {
  .related-task-item {
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .task-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .task-title {
        font-size: 14px;
        color: #303133;
      }
    }
  }
}

.history-list {
  .history-item {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .history-time {
      font-size: 12px;
      color: #909399;
      margin-bottom: 5px;
    }

    .history-content {
      color: #606266;
      font-size: 14px;

      .user {
        font-weight: 500;
        color: #303133;
      }
    }
  }
}

.empty-comments,
.empty-related,
.empty-history {
  text-align: center;
  padding: 20px;
  color: #909399;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .el-row {
    flex-direction: column;
  }
}
</style>
