# Git 分支合并详解

## 🔄 合并策略概览

Git 提供了多种合并策略，每种适用于不同的场景：

```
Fast-forward 合并    # 快进合并，线性历史
Non-fast-forward 合并 # 非快进合并，保留分支历史
Squash 合并          # 压缩合并，将多个提交合并为一个
Rebase 合并          # 变基合并，重写提交历史
```

## ⚡ Fast-forward 合并

### 📋 什么是 Fast-forward
```bash
# 当目标分支没有新提交时，Git 可以直接移动指针
# 合并前：
# main:    A---B
# feature:     C---D

# 合并后：
# main:    A---B---C---D
# feature:         C---D
```

### 🔄 Fast-forward 操作
```bash
# 检查是否可以快进合并
git merge-base main feature-branch
git log --oneline main..feature-branch

# 执行快进合并
git checkout main
git merge feature-branch

# 强制快进合并（如果可能）
git merge --ff-only feature-branch

# 禁用快进合并
git merge --no-ff feature-branch
```

### ✅ Fast-forward 的优缺点
```bash
# 优点：
# - 保持线性历史
# - 简洁的提交图
# - 没有额外的合并提交

# 缺点：
# - 丢失分支信息
# - 难以追踪功能开发过程
# - 无法回滚整个功能
```

## 🔀 Non-fast-forward 合并

### 📋 什么是 Non-fast-forward
```bash
# 当目标分支有新提交时，需要创建合并提交
# 合并前：
# main:    A---B---E---F
# feature:     C---D

# 合并后：
# main:    A---B---E---F---M
#              \         /
# feature:      C---D---/
```

### 🔄 Non-fast-forward 操作
```bash
# 标准合并（创建合并提交）
git checkout main
git merge feature-branch

# 强制创建合并提交
git merge --no-ff feature-branch

# 自定义合并提交信息
git merge --no-ff -m "Merge feature: user authentication" feature-branch

# 合并时不自动提交
git merge --no-commit feature-branch
# 检查合并结果...
git commit -m "Custom merge message"
```

### 🎯 合并提交信息规范
```bash
# 标准格式
git merge --no-ff -m "Merge branch 'feature/user-auth' into main

Features added:
- User login functionality
- Password reset feature
- OAuth integration

Closes #123, #124" feature/user-auth

# 使用编辑器编写详细信息
git merge --no-ff feature-branch
# 在编辑器中编写详细的合并信息
```

## 📦 Squash 合并

### 📋 什么是 Squash 合并
```bash
# 将分支上的所有提交压缩为一个提交
# 合并前：
# main:    A---B
# feature:     C---D---E

# 合并后：
# main:    A---B---CDE
# feature:     C---D---E（保持不变）
```

### 🔄 Squash 操作
```bash
# 执行 squash 合并
git checkout main
git merge --squash feature-branch

# 查看暂存的修改
git status
git diff --staged

# 提交 squash 合并
git commit -m "feat: implement user authentication

- Add login form component
- Implement password validation
- Add OAuth integration
- Add user session management

Squashed commits from feature/user-auth"

# 清理功能分支
git branch -d feature-branch
```

### ✅ Squash 合并的优缺点
```bash
# 优点：
# - 保持主分支历史简洁
# - 每个功能一个提交
# - 便于回滚整个功能
# - 适合代码审查

# 缺点：
# - 丢失详细的开发历史
# - 无法追踪具体的修改过程
# - 不适合长期功能分支
```

## 🔄 Rebase 合并

### 📋 什么是 Rebase
```bash
# 将分支的提交重新应用到目标分支上
# 变基前：
# main:    A---B---E---F
# feature:     C---D

# 变基后：
# main:    A---B---E---F
# feature:             C'---D'
```

### 🔄 Rebase 操作
```bash
# 基本变基
git checkout feature-branch
git rebase main

# 交互式变基
git rebase -i main

# 变基到指定提交
git rebase --onto main~2 main~1 feature-branch

# 变基后合并
git checkout main
git merge feature-branch  # 这将是快进合并
```

### 🎯 交互式变基
```bash
# 启动交互式变基
git rebase -i HEAD~3

# 编辑器中的选项：
# pick abc123 First commit
# squash def456 Second commit  # 压缩到上一个提交
# edit ghi789 Third commit     # 暂停以编辑提交
# drop jkl012 Fourth commit    # 删除这个提交

# 常用操作：
# pick (p) - 使用提交
# reword (r) - 使用提交但编辑提交信息
# edit (e) - 使用提交但暂停以修改
# squash (s) - 使用提交但合并到前一个提交
# fixup (f) - 类似 squash 但丢弃提交信息
# drop (d) - 删除提交
```

### ⚠️ Rebase 注意事项
```bash
# 🚨 黄金法则：不要对已推送的提交进行变基！

# 安全的变基实践：
# 1. 只对本地分支进行变基
# 2. 变基前先备份分支
git branch backup-feature feature-branch

# 3. 如果必须变基已推送的分支
git push --force-with-lease origin feature-branch

# 4. 团队协作时避免变基共享分支
```

## 🚨 冲突解决

### ⚔️ 合并冲突处理
```bash
# 1. 合并时发生冲突
git merge feature-branch
# Auto-merging file.txt
# CONFLICT (content): Merge conflict in file.txt

# 2. 查看冲突状态
git status
# Unmerged paths:
#   both modified:   file.txt

# 3. 查看冲突内容
git diff
# <<<<<<< HEAD
# 主分支的内容
# =======
# 功能分支的内容
# >>>>>>> feature-branch

# 4. 解决冲突
# 编辑文件，选择保留的内容...

# 5. 标记冲突已解决
git add file.txt

# 6. 完成合并
git commit
```

### 🔧 冲突解决工具
```bash
# 使用合并工具
git mergetool

# 配置合并工具
git config --global merge.tool vimdiff
git config --global merge.tool vscode

# VS Code 作为合并工具
git config --global merge.tool vscode
git config --global mergetool.vscode.cmd 'code --wait $MERGED'

# 查看冲突的不同版本
git show :1:file.txt  # 共同祖先版本
git show :2:file.txt  # 当前分支版本
git show :3:file.txt  # 合并分支版本
```

### 🔄 变基冲突处理
```bash
# 1. 变基时发生冲突
git rebase main
# CONFLICT (content): Merge conflict in file.txt

# 2. 解决冲突
# 编辑冲突文件...
git add file.txt

# 3. 继续变基
git rebase --continue

# 4. 跳过当前提交
git rebase --skip

# 5. 中止变基
git rebase --abort
```

## 🎯 合并策略选择

### 📊 策略对比表
```bash
| 场景 | Fast-forward | Non-fast-forward | Squash | Rebase |
|------|-------------|------------------|--------|--------|
| 功能分支 | ❌ | ✅ | ✅ | ✅ |
| 热修复 | ✅ | ✅ | ✅ | ❌ |
| 实验性功能 | ❌ | ❌ | ✅ | ❌ |
| 长期分支 | ❌ | ✅ | ❌ | ❌ |
| 保持历史 | ✅ | ✅ | ❌ | ✅ |
| 简洁历史 | ✅ | ❌ | ✅ | ✅ |
```

### 🎯 推荐策略
```bash
# 1. 功能开发（推荐 squash）
git checkout main
git merge --squash feature/user-auth
git commit -m "feat: add user authentication"

# 2. 热修复（推荐 non-fast-forward）
git checkout main
git merge --no-ff hotfix/security-fix

# 3. 发布分支（推荐 non-fast-forward）
git checkout main
git merge --no-ff release/v1.2.0

# 4. 个人功能分支（推荐 rebase + fast-forward）
git checkout feature-branch
git rebase main
git checkout main
git merge feature-branch
```

## 🔧 高级合并技巧

### 🎯 部分合并
```bash
# 只合并特定文件
git checkout main
git checkout feature-branch -- specific-file.txt
git commit -m "Merge specific file from feature branch"

# 只合并特定目录
git checkout feature-branch -- src/components/
git commit -m "Merge components from feature branch"
```

### 🔄 三方合并
```bash
# 使用特定的合并策略
git merge -s ours feature-branch      # 忽略功能分支的修改
git merge -s theirs feature-branch    # 优先使用功能分支的修改
git merge -s recursive feature-branch # 默认递归策略

# 合并时优先选择某一方的修改
git merge -X ours feature-branch      # 冲突时优先当前分支
git merge -X theirs feature-branch    # 冲突时优先功能分支
```

### 📦 子树合并
```bash
# 合并外部项目作为子目录
git remote add external-project https://github.com/user/project.git
git fetch external-project
git merge -s ours --no-commit --allow-unrelated-histories external-project/main
git read-tree --prefix=external/ -u external-project/main
git commit -m "Merge external project as subtree"
```

## 💡 最佳实践

### ✅ 合并前检查
```bash
# 1. 确保分支是最新的
git checkout feature-branch
git fetch origin
git rebase origin/main

# 2. 运行测试
npm test
npm run lint

# 3. 检查差异
git diff main...feature-branch

# 4. 确认提交历史
git log --oneline main..feature-branch
```

### 🔒 安全合并流程
```bash
# 1. 创建备份
git branch backup-$(date +%Y%m%d) feature-branch

# 2. 在测试分支上先合并
git checkout -b test-merge main
git merge feature-branch
# 测试合并结果...

# 3. 如果测试通过，在主分支上合并
git checkout main
git merge feature-branch

# 4. 清理
git branch -d test-merge
git branch -d backup-$(date +%Y%m%d)
```

### 📝 合并信息规范
```bash
# 好的合并信息示例
git merge --no-ff -m "Merge feature: user authentication system

This merge introduces a complete user authentication system including:

Features:
- User registration and login
- Password reset functionality
- OAuth integration (Google, GitHub)
- Session management
- Role-based access control

Technical details:
- Added JWT token handling
- Implemented password hashing with bcrypt
- Added input validation and sanitization
- Comprehensive test coverage (95%)

Breaking changes:
- API endpoint /auth moved to /api/v1/auth
- User model schema updated

Closes #123, #124, #125
Reviewed-by: @reviewer1, @reviewer2" feature/user-auth
```

---

## 🚀 下一步学习

掌握分支合并后，建议学习：
1. **冲突解决** - 深入学习复杂冲突的处理
2. **分支策略** - Git Flow、GitHub Flow 等工作流程
3. **高级技巧** - cherry-pick、subtree、worktree 等

**记住**: 选择合适的合并策略对项目的历史管理和团队协作至关重要！ 🔀
