import express from 'express';
import { ApolloServer } from 'apollo-server-express';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { useServer } from 'graphql-ws/lib/use/ws';
import { makeExecutableSchema } from '@graphql-tools/schema';
import cors from 'cors';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import { typeDefs } from './schema/typeDefs';
import { resolvers } from './resolvers';
import { db } from './data/mockData';
import { Context, User } from './models';
import { contextInjectionPlugin } from './plugins/contextInjectionPlugin';

// 加载环境变量
dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const PORT = process.env.PORT || 4000;

// 创建可执行的GraphQL Schema
const schema = makeExecutableSchema({
    typeDefs,
    resolvers,
});

// 认证中间件
const getUser = async (token: string): Promise<User | null> => {
    try {
        if (!token) return null;

        // 移除Bearer前缀
        const cleanToken = token.replace('Bearer ', '');
        const decoded = jwt.verify(cleanToken, JWT_SECRET) as { userId: string };

        const user = db.getUserById(decoded.userId);
        return user || null;
    } catch (error) {
        console.error('Token verification failed:', error);
        return null;
    }
};

// 创建上下文函数
const createContext = async ({ req }: { req: express.Request }): Promise<Context> => {
    const token = req.headers.authorization || '';
    const user = await getUser(token);

    const context: Context = { token };
    if (user) {
        context.user = user;
    }

    return context;
};

async function startServer() {
    // 创建Express应用
    const app = express();

    // 启用CORS
    app.use(cors({
        origin: ['http://localhost:3000', 'http://localhost:5173'], // Vue开发服务器
        credentials: true
    }));

    // 创建Apollo Server
    const server = new ApolloServer({
        schema,
        context: createContext,
        introspection: true,
        plugins: [
            // 🎯 上下文注入插件 - 为每个请求注入共享数据
            contextInjectionPlugin,

            // 🎯 增强版日志插件
            {
                requestDidStart() {
                    return {
                        didResolveOperation(requestContext) {
                            const { operationName, variables } = requestContext.request;
                            const { user, requestId } = requestContext.context;

                            console.log(`🔍 [${requestId}] Operation: ${operationName || 'Anonymous'} by ${user?.username || 'Guest'}`);

                            // 开发环境显示变量
                            if (process.env.NODE_ENV === 'development' && variables) {
                                console.log(`📝 Variables:`, variables);
                            }
                        },
                        didEncounterErrors(requestContext) {
                            const { requestId } = requestContext.context;
                            console.error(`❌ [${requestId}] GraphQL Errors:`, requestContext.errors.map(e => ({
                                message: e.message,
                                path: e.path,
                                code: e.extensions?.code
                            })));
                        }
                    };
                }
            }
        ]
    });

    // 启动Apollo Server
    await server.start();

    // 应用Apollo GraphQL中间件
    server.applyMiddleware({
        app,
        path: '/graphql',
        cors: false // 我们已经在Express级别配置了CORS
    });

    // 创建HTTP服务器
    const httpServer = createServer(app);

    // 创建WebSocket服务器用于订阅
    const wsServer = new WebSocketServer({
        server: httpServer,
        path: '/graphql',
    });

    // 配置GraphQL WebSocket服务器
    const serverCleanup = useServer({
        schema,
        context: async (ctx) => {
            // WebSocket连接的上下文
            const token = ctx.connectionParams?.authorization as string || '';
            const user = await getUser(token);

            return {
                user,
                token
            };
        },
        onConnect: async (ctx) => {
            console.log('🔌 WebSocket connected');
            return true;
        },
        onDisconnect: () => {
            console.log('🔌 WebSocket disconnected');
        }
    }, wsServer);

    // 健康检查端点
    app.get('/health', (req, res) => {
        res.json({
            status: 'OK',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.version
        });
    });

    // API信息端点
    app.get('/api/info', (req, res) => {
        res.json({
            name: 'Vue GraphQL Server',
            version: '1.0.0',
            graphql: {
                endpoint: '/graphql',
                playground: '/graphql',
                subscriptions: '/graphql'
            },
            features: [
                'User Authentication',
                'Project Management',
                'Task Management',
                'Real-time Subscriptions',
                'Role-based Access Control'
            ]
        });
    });

    // 统计端点
    app.get('/api/stats', (req, res) => {
        const users = db.getUsers();
        const projects = db.getProjects();
        const tasks = db.getTasks();
        const comments = db.getComments();

        res.json({
            users: users.length,
            projects: projects.length,
            tasks: tasks.length,
            comments: comments.length,
            timestamp: new Date().toISOString()
        });
    });

    // 错误处理中间件
    app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
        console.error('❌ Server Error:', err);
        res.status(500).json({
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
        });
    });

    // 404处理
    app.use('*', (req, res) => {
        res.status(404).json({
            error: 'Not Found',
            message: `Route ${req.originalUrl} not found`,
            availableEndpoints: [
                '/graphql - GraphQL API',
                '/health - Health check',
                '/api/info - API information',
                '/api/stats - Database statistics'
            ]
        });
    });

    // 启动服务器
    httpServer.listen(PORT, () => {
        console.log('🚀 Server is running!');
        console.log(`📊 GraphQL Playground: http://localhost:${PORT}/graphql`);
        console.log(`🔗 GraphQL Endpoint: http://localhost:${PORT}/graphql`);
        console.log(`🔌 WebSocket Subscriptions: ws://localhost:${PORT}/graphql`);
        console.log(`💚 Health Check: http://localhost:${PORT}/health`);
        console.log(`📋 API Info: http://localhost:${PORT}/api/info`);
        console.log(`📈 Stats: http://localhost:${PORT}/api/stats`);
        console.log('');
        console.log('🎯 Ready for connections!');

        // 打印一些示例查询
        console.log('\n📝 Example Queries:');
        console.log('1. Get all users: query { users { users { id username email } } }');
        console.log('2. Get dashboard stats: query { dashboardStats { taskStats { total done } } }');
        console.log('3. Login: mutation { login(input: { email: "<EMAIL>", password: "admin123" }) { token user { username } } }');
    });

    // 优雅关闭
    process.on('SIGTERM', async () => {
        console.log('🛑 SIGTERM received, shutting down gracefully...');
        serverCleanup.dispose();
        await server.stop();
        httpServer.close(() => {
            console.log('✅ Server closed');
            process.exit(0);
        });
    });

    process.on('SIGINT', async () => {
        console.log('🛑 SIGINT received, shutting down gracefully...');
        serverCleanup.dispose();
        await server.stop();
        httpServer.close(() => {
            console.log('✅ Server closed');
            process.exit(0);
        });
    });
}

// 启动服务器
startServer().catch(error => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
});
