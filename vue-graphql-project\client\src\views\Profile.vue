<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>个人资料</span>
          <el-button type="primary" @click="editMode = !editMode">
            {{ editMode ? '取消编辑' : '编辑资料' }}
          </el-button>
        </div>
      </template>

      <div class="profile-content">
        <div class="avatar-section">
          <el-avatar :size="120" :src="userProfile.avatar" class="avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <el-button v-if="editMode" type="text" class="change-avatar">
            更换头像
          </el-button>
        </div>

        <el-form
          ref="profileForm"
          :model="userProfile"
          :rules="rules"
          label-width="100px"
          class="profile-form"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="userProfile.username"
              :disabled="!editMode"
              placeholder="请输入用户名"
            />
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="userProfile.email"
              :disabled="!editMode"
              placeholder="请输入邮箱"
            />
          </el-form-item>

          <el-form-item label="角色">
            <el-tag :type="getRoleType(userProfile.role)">
              {{ getRoleText(userProfile.role) }}
            </el-tag>
          </el-form-item>

          <el-form-item label="注册时间">
            <span>{{ formatDate(userProfile.createdAt) }}</span>
          </el-form-item>

          <el-form-item v-if="editMode">
            <el-button type="primary" @click="saveProfile" :loading="saving">
              保存修改
            </el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 修改密码卡片 -->
    <el-card class="password-card">
      <template #header>
        <span>修改密码</span>
      </template>

      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="changePassword" :loading="changingPassword">
            修改密码
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User } from '@element-plus/icons-vue'

// 响应式数据
const editMode = ref(false)
const saving = ref(false)
const changingPassword = ref(false)

const userProfile = reactive({
  id: '',
  username: '',
  email: '',
  role: 'USER',
  avatar: '',
  createdAt: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const getRoleType = (role: string) => {
  const roleTypes: Record<string, string> = {
    ADMIN: 'danger',
    MODERATOR: 'warning',
    USER: 'success',
    GUEST: 'info'
  }
  return roleTypes[role] || 'info'
}

const getRoleText = (role: string) => {
  const roleTexts: Record<string, string> = {
    ADMIN: '管理员',
    MODERATOR: '版主',
    USER: '用户',
    GUEST: '访客'
  }
  return roleTexts[role] || '未知'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const loadUserProfile = async () => {
  try {
    // 这里应该调用 GraphQL 查询获取用户信息
    // 暂时使用模拟数据
    Object.assign(userProfile, {
      id: '1',
      username: '张三',
      email: '<EMAIL>',
      role: 'USER',
      avatar: '',
      createdAt: '2024-01-01T00:00:00Z'
    })
  } catch (error) {
    ElMessage.error('加载用户信息失败')
  }
}

const saveProfile = async () => {
  // 表单验证逻辑
  saving.value = true
  try {
    // 这里应该调用 GraphQL mutation 更新用户信息
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success('保存成功')
    editMode.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  // 重置表单到原始状态
  loadUserProfile()
}

const changePassword = async () => {
  changingPassword.value = true
  try {
    // 这里应该调用 GraphQL mutation 修改密码
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success('密码修改成功')
    // 清空密码表单
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  } catch (error) {
    ElMessage.error('密码修改失败')
  } finally {
    changingPassword.value = false
  }
}

// 生命周期
onMounted(() => {
  loadUserProfile()
})
</script>

<style scoped lang="scss">
.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.profile-card, .password-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-content {
  display: flex;
  gap: 40px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.avatar {
  border: 2px solid #e4e7ed;
}

.change-avatar {
  font-size: 12px;
}

.profile-form {
  flex: 1;
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    gap: 20px;
  }
}
</style>
