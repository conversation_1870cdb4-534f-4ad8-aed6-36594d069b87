import { defineStore } from 'pinia'
import type { ThemeConfig, Notification } from '@/types'

interface AppState {
  theme: ThemeConfig
  notifications: Notification[]
  loading: boolean
  sidebarCollapsed: boolean
  breadcrumbs: Array<{ name: string; path?: string }>
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    theme: {
      primaryColor: '#409EFF',
      isDark: false,
      sidebarCollapsed: false,
      language: 'zh-CN'
    },
    notifications: [],
    loading: false,
    sidebarCollapsed: false,
    breadcrumbs: []
  }),

  getters: {
    unreadNotifications: (state) => state.notifications.filter(n => !n.id.includes('read')),
    notificationCount: (state) => state.unreadNotifications.length
  },

  actions: {
    // 主题相关
    toggleTheme() {
      this.theme.isDark = !this.theme.isDark
      this.saveTheme()
    },

    setPrimaryColor(color: string) {
      this.theme.primaryColor = color
      this.saveTheme()
    },

    setLanguage(language: 'zh-CN' | 'en-US') {
      this.theme.language = language
      this.saveTheme()
    },

    saveTheme() {
      localStorage.setItem('theme', JSON.stringify(this.theme))
    },

    loadTheme() {
      const saved = localStorage.getItem('theme')
      if (saved) {
        try {
          this.theme = { ...this.theme, ...JSON.parse(saved) }
        } catch (error) {
          console.error('Failed to load theme:', error)
        }
      }
    },

    // 侧边栏
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
      localStorage.setItem('sidebarCollapsed', String(this.sidebarCollapsed))
    },

    setSidebarCollapsed(collapsed: boolean) {
      this.sidebarCollapsed = collapsed
      localStorage.setItem('sidebarCollapsed', String(collapsed))
    },

    loadSidebarState() {
      const saved = localStorage.getItem('sidebarCollapsed')
      if (saved !== null) {
        this.sidebarCollapsed = saved === 'true'
      }
    },

    // 面包屑导航
    setBreadcrumbs(breadcrumbs: Array<{ name: string; path?: string }>) {
      this.breadcrumbs = breadcrumbs
    },

    // 通知相关
    addNotification(notification: Omit<Notification, 'id' | 'timestamp'>) {
      const id = Date.now().toString()
      const newNotification: Notification = {
        ...notification,
        id,
        timestamp: new Date().toISOString(),
        duration: notification.duration || 4500
      }

      this.notifications.unshift(newNotification)

      // 自动移除通知
      if (newNotification.duration > 0) {
        setTimeout(() => {
          this.removeNotification(id)
        }, newNotification.duration)
      }

      return id
    },

    removeNotification(id: string) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },

    clearNotifications() {
      this.notifications = []
    },

    // 便捷的通知方法
    notifySuccess(title: string, message: string = '') {
      return this.addNotification({
        type: 'success',
        title,
        message
      })
    },

    notifyError(title: string, message: string = '') {
      return this.addNotification({
        type: 'error',
        title,
        message,
        duration: 6000
      })
    },

    notifyWarning(title: string, message: string = '') {
      return this.addNotification({
        type: 'warning',
        title,
        message
      })
    },

    notifyInfo(title: string, message: string = '') {
      return this.addNotification({
        type: 'info',
        title,
        message
      })
    },

    // 全局加载状态
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // 初始化应用状态
    initApp() {
      this.loadTheme()
      this.loadSidebarState()
    },

    // 重置应用状态
    resetApp() {
      this.notifications = []
      this.loading = false
      this.breadcrumbs = []
    }
  }
})
