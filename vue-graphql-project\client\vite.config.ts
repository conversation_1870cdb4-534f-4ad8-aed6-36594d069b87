import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
    plugins: [
        vue(),
        AutoImport({
            imports: [
                'vue',
                'vue-router',
                'pinia',
                {
                    '@vue/apollo-composable': [
                        'useQuery',
                        'useMutation',
                        'useSubscription',
                        'useApolloClient',
                        'useResult',
                        'useLazyQuery'
                    ]
                }
            ],
            resolvers: [ElementPlusResolver()],
            dts: true
        }),
        Components({
            resolvers: [ElementPlusResolver()],
            dts: true
        })
    ],
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
            '@/components': resolve(__dirname, 'src/components'),
            '@/views': resolve(__dirname, 'src/views'),
            '@/stores': resolve(__dirname, 'src/stores'),
            '@/graphql': resolve(__dirname, 'src/graphql'),
            '@/types': resolve(__dirname, 'src/types'),
            '@/utils': resolve(__dirname, 'src/utils')
        }
    },
    server: {
        port: 3000,
        open: true,
        cors: true,
        proxy: {
            '/graphql': {
                target: 'http://localhost:4000',
                changeOrigin: true,
                ws: true
            }
        }
    },
    build: {
        target: 'es2015',
        outDir: 'dist',
        assetsDir: 'assets',
        sourcemap: false,
        rollupOptions: {
            output: {
                chunkFileNames: 'js/[name]-[hash].js',
                entryFileNames: 'js/[name]-[hash].js',
                assetFileNames: '[ext]/[name]-[hash].[ext]'
            }
        }
    },
    optimizeDeps: {
        include: [
            'vue',
            'vue-router',
            'pinia',
            '@apollo/client',
            '@vue/apollo-composable',
            'element-plus',
            'dayjs',
            'lodash-es'
        ]
    }
})
