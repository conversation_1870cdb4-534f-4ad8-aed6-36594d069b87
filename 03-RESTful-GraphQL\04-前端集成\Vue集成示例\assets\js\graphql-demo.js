/**
 * Vue.js + GraphQL 集成示例
 * 主要应用逻辑
 */

import { mockUsers, mockPosts, mockTasks, graphqlSchema } from '../data/mock-data.js';

const { createApp, ref, reactive, computed, onMounted } = Vue;

createApp({
    setup() {
        // 响应式数据
        const loading = ref(false);
        const error = ref('');
        const success = ref('');
        const result = ref('');
        const activeTab = ref('query');
        const totalQueries = ref(0);
        const avgResponseTime = ref(0);
        const successRate = ref(100);
        const dataSize = ref(0);

        // 标签页配置
        const tabs = reactive([
            {
                id: 'query',
                name: 'Query (查询)',
                query: `query GetUsers($limit: Int) {
  users(limit: $limit) {
    id
    name
    email
    posts {
      id
      title
    }
  }
}`,
                variables: `{
  "limit": 5
}`,
                placeholder: '在这里输入GraphQL查询...'
            },
            {
                id: 'mutation',
                name: 'Mutation (修改)',
                query: `mutation CreateUser($input: CreateUserInput!) {
  createUser(input: $input) {
    id
    name
    email
    createdAt
  }
}`,
                variables: `{
  "input": {
    "name": "新用户",
    "email": "<EMAIL>"
  }
}`,
                placeholder: '在这里输入GraphQL修改操作...'
            },
            {
                id: 'subscription',
                name: 'Subscription (订阅)',
                query: `subscription OnUserCreated {
  userCreated {
    id
    name
    email
    createdAt
  }
}`,
                variables: '{}',
                placeholder: '在这里输入GraphQL订阅...'
            }
        ]);

        // 示例查询
        const examples = ref([
            {
                id: 1,
                title: '获取所有用户',
                query: `query {
  users {
    id
    name
    email
  }
}`,
                variables: '{}'
            },
            {
                id: 2,
                title: '获取用户及其文章',
                query: `query GetUserWithPosts($userId: ID!) {
  user(id: $userId) {
    name
    email
    posts {
      title
      content
      createdAt
    }
  }
}`,
                variables: '{"userId": "1"}'
            },
            {
                id: 3,
                title: '创建新文章',
                query: `mutation CreatePost($input: CreatePostInput!) {
  createPost(input: $input) {
    id
    title
    content
    author {
      name
    }
  }
}`,
                variables: `{
  "input": {
    "title": "GraphQL入门",
    "content": "GraphQL是一种强大的查询语言...",
    "authorId": "1"
  }
}`
            },
            {
                id: 4,
                title: '搜索用户和文章',
                query: `query Search($query: String!) {
  search(query: $query) {
    ... on User {
      id
      name
      email
    }
    ... on Post {
      id
      title
      content
    }
  }
}`,
                variables: '{"query": "GraphQL"}'
            }
        ]);

        // 计算属性
        const currentTab = computed(() => {
            return tabs.find(tab => tab.id === activeTab.value);
        });

        const schema = computed(() => graphqlSchema);

        // 方法
        const executeQuery = async (tab) => {
            loading.value = true;
            error.value = '';
            success.value = '';
            
            const startTime = Date.now();
            
            try {
                // 模拟GraphQL查询执行
                await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
                
                const queryResult = mockExecuteGraphQL(tab.query, tab.variables);
                
                result.value = JSON.stringify(queryResult, null, 2);
                success.value = '查询执行成功！';
                
                // 更新统计信息
                totalQueries.value++;
                const responseTime = Date.now() - startTime;
                avgResponseTime.value = Math.round((avgResponseTime.value + responseTime) / 2);
                dataSize.value = Math.round(JSON.stringify(queryResult).length / 1024 * 100) / 100;
                
                // 清除成功消息
                setTimeout(() => {
                    success.value = '';
                }, 3000);
                
            } catch (err) {
                error.value = err.message;
                result.value = JSON.stringify({ errors: [{ message: err.message }] }, null, 2);
                
                // 更新失败率
                const totalAttempts = totalQueries.value + 1;
                successRate.value = Math.round((totalQueries.value / totalAttempts) * 100);
            } finally {
                loading.value = false;
            }
        };

        const mockExecuteGraphQL = (query, variables) => {
            try {
                const vars = variables ? JSON.parse(variables) : {};
                
                // 简单的查询解析和响应生成
                if (query.includes('users') && !query.includes('user(')) {
                    const limit = vars.limit || mockUsers.length;
                    return {
                        data: {
                            users: mockUsers.slice(0, limit).map(user => ({
                                id: user.id,
                                name: user.name,
                                email: user.email,
                                ...(query.includes('posts') && { 
                                    posts: mockPosts.filter(p => p.author_id === user.id)
                                        .map(p => ({ id: p.id, title: p.title }))
                                })
                            }))
                        }
                    };
                }
                
                if (query.includes('user(id:') || query.includes('user(id: ')) {
                    const userId = vars.userId || "1";
                    const user = mockUsers.find(u => u.id === parseInt(userId));
                    
                    if (!user) {
                        throw new Error("User not found");
                    }
                    
                    return {
                        data: {
                            user: {
                                id: user.id,
                                name: user.name,
                                email: user.email,
                                ...(query.includes('posts') && { 
                                    posts: mockPosts.filter(p => p.author_id === user.id)
                                        .map(p => ({ 
                                            title: p.title, 
                                            content: p.content,
                                            createdAt: p.created_at
                                        }))
                                })
                            }
                        }
                    };
                }
                
                if (query.includes('createUser')) {
                    const input = vars.input;
                    const newUser = {
                        id: String(mockUsers.length + 1),
                        name: input.name,
                        email: input.email,
                        createdAt: new Date().toISOString()
                    };
                    
                    mockUsers.push({
                        ...newUser,
                        id: parseInt(newUser.id),
                        role: 'user',
                        status: 'active',
                        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`,
                        created_at: newUser.createdAt,
                        updated_at: newUser.createdAt
                    });
                    
                    return {
                        data: {
                            createUser: newUser
                        }
                    };
                }
                
                if (query.includes('createPost')) {
                    const input = vars.input;
                    const author = mockUsers.find(u => u.id === parseInt(input.authorId));
                    
                    if (!author) {
                        throw new Error("Author not found");
                    }
                    
                    const newPost = {
                        id: String(mockPosts.length + 1),
                        title: input.title,
                        content: input.content,
                        author: {
                            name: author.name
                        },
                        createdAt: new Date().toISOString()
                    };
                    
                    mockPosts.push({
                        ...newPost,
                        id: parseInt(newPost.id),
                        author_id: parseInt(input.authorId),
                        category_id: 1,
                        status: 'published',
                        tags: [],
                        created_at: newPost.createdAt,
                        updated_at: newPost.createdAt,
                        published_at: newPost.createdAt,
                        views: 0,
                        likes: 0
                    });
                    
                    return {
                        data: {
                            createPost: newPost
                        }
                    };
                }
                
                if (query.includes('search')) {
                    const searchQuery = vars.query.toLowerCase();
                    const results = [];
                    
                    // 搜索用户
                    mockUsers.forEach(user => {
                        if (user.name.toLowerCase().includes(searchQuery) || 
                            user.email.toLowerCase().includes(searchQuery)) {
                            results.push({
                                __typename: 'User',
                                id: user.id,
                                name: user.name,
                                email: user.email
                            });
                        }
                    });
                    
                    // 搜索文章
                    mockPosts.forEach(post => {
                        if (post.title.toLowerCase().includes(searchQuery) ||
                            post.content.toLowerCase().includes(searchQuery)) {
                            results.push({
                                __typename: 'Post',
                                id: post.id,
                                title: post.title,
                                content: post.content
                            });
                        }
                    });
                    
                    return {
                        data: {
                            search: results
                        }
                    };
                }
                
                if (query.includes('userCreated')) {
                    return {
                        data: {
                            userCreated: {
                                id: "999",
                                name: "实时用户",
                                email: "<EMAIL>",
                                createdAt: new Date().toISOString()
                            }
                        }
                    };
                }
                
                // 默认响应
                throw new Error("查询暂不支持，这是一个演示环境");
                
            } catch (err) {
                throw new Error(`GraphQL解析错误: ${err.message}`);
            }
        };

        const clearQuery = (tab) => {
            tab.query = '';
            tab.variables = '{}';
        };

        const loadExample = (example) => {
            const currentTabObj = currentTab.value;
            if (currentTabObj) {
                currentTabObj.query = example.query;
                currentTabObj.variables = example.variables;
            }
        };

        // 生命周期
        onMounted(() => {
            console.log('🚀 Vue.js + GraphQL 示例已加载');
            console.log('💡 功能包括：');
            console.log('- GraphQL查询编辑器');
            console.log('- Query、Mutation、Subscription示例');
            console.log('- 实时结果显示');
            console.log('- 错误处理');
            console.log('- 性能统计');
        });

        return {
            loading,
            error,
            success,
            result,
            activeTab,
            totalQueries,
            avgResponseTime,
            successRate,
            dataSize,
            tabs,
            examples,
            schema,
            currentTab,
            executeQuery,
            clearQuery,
            loadExample
        };
    }
}).mount('#app');
