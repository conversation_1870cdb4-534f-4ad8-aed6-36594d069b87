# 移动端适配常见问题解决方案

## 1. 1px边框问题

### 1.1 问题描述
在高DPR设备上，CSS中的1px边框会显示为多个物理像素，看起来比较粗。

### 1.2 解决方案

#### 方案一：transform缩放
```css
.border-1px {
  position: relative;
}

.border-1px::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  border: 1px solid #ddd;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  pointer-events: none;
}

/* 针对不同DPR的优化 */
@media (-webkit-min-device-pixel-ratio: 3) {
  .border-1px::after {
    width: 300%;
    height: 300%;
    transform: scale(0.333);
  }
}
```

#### 方案二：使用0.5px
```css
.border-hairline {
  border: 1px solid #ddd;
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .border-hairline {
    border-width: 0.5px;
  }
}

@media (-webkit-min-device-pixel-ratio: 3) {
  .border-hairline {
    border-width: 0.333px;
  }
}
```

#### 方案三：使用box-shadow
```css
.border-shadow {
  box-shadow: 0 0 0 0.5px #ddd;
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .border-shadow {
    box-shadow: 0 0 0 0.5px #ddd;
  }
}
```

#### 方案四：使用SVG
```css
.border-svg {
  border: none;
  background: url("data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m0,100 l100,0' stroke='%23ddd' stroke-width='1'/%3e%3c/svg%3e") repeat-x bottom;
  background-size: 100% 1px;
}
```

## 2. 图片模糊问题

### 2.1 问题描述
普通分辨率的图片在高DPR设备上显示模糊。

### 2.2 解决方案

#### 方案一：使用srcset属性
```html
<img src="<EMAIL>" 
     srcset="<EMAIL> 1x, <EMAIL> 2x, <EMAIL> 3x"
     alt="响应式图片">
```

#### 方案二：CSS媒体查询
```css
.responsive-bg {
  background-image: url('<EMAIL>');
  background-size: cover;
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .responsive-bg {
    background-image: url('<EMAIL>');
  }
}

@media (-webkit-min-device-pixel-ratio: 3) {
  .responsive-bg {
    background-image: url('<EMAIL>');
  }
}
```

#### 方案三：JavaScript动态加载
```javascript
function loadResponsiveImage(element, baseName, extension) {
  const dpr = window.devicePixelRatio || 1;
  let suffix = '@1x';
  
  if (dpr >= 3) {
    suffix = '@3x';
  } else if (dpr >= 2) {
    suffix = '@2x';
  }
  
  const imagePath = `${baseName}${suffix}.${extension}`;
  
  if (element.tagName === 'IMG') {
    element.src = imagePath;
  } else {
    element.style.backgroundImage = `url(${imagePath})`;
  }
}

// 使用示例
const img = document.querySelector('.responsive-image');
loadResponsiveImage(img, 'assets/hero', 'jpg');
```

## 3. iOS Safari兼容性问题

### 3.1 视口高度问题
```css
/* 问题：iOS Safari地址栏会影响100vh */
.full-height-wrong {
  height: 100vh; /* 在iOS Safari中可能不准确 */
}

/* 解决方案：使用CSS变量 */
.full-height-correct {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}
```

```javascript
// JavaScript计算真实视口高度
function setViewportHeight() {
  let vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// 初始设置
setViewportHeight();

// 监听resize事件
window.addEventListener('resize', setViewportHeight);

// 监听orientationchange事件
window.addEventListener('orientationchange', () => {
  setTimeout(setViewportHeight, 100);
});
```

### 3.2 安全区域适配
```css
/* iPhone X等设备的安全区域 */
.safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 兼容性写法 */
.safe-area-compat {
  /* iOS 11.0-11.2 */
  padding-top: constant(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  
  /* iOS 11.2+ */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
```

### 3.3 滚动回弹问题
```css
/* 禁用iOS滚动回弹 */
body {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* 或者使用 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow: auto;
}

/* 完全禁用回弹 */
.no-bounce {
  position: fixed;
  overflow: hidden;
}
```

## 4. Android兼容性问题

### 4.1 软键盘问题
```javascript
// 检测软键盘弹出
function detectKeyboard() {
  const initialHeight = window.innerHeight;
  
  window.addEventListener('resize', () => {
    const currentHeight = window.innerHeight;
    const heightDiff = initialHeight - currentHeight;
    
    if (heightDiff > 150) {
      // 软键盘弹出
      document.body.classList.add('keyboard-open');
    } else {
      // 软键盘收起
      document.body.classList.remove('keyboard-open');
    }
  });
}

// 调用检测
if (/Android/i.test(navigator.userAgent)) {
  detectKeyboard();
}
```

```css
/* 软键盘弹出时的样式调整 */
.keyboard-open {
  /* 调整布局以适应软键盘 */
}

.keyboard-open .fixed-bottom {
  position: static; /* 避免被软键盘遮挡 */
}
```

### 4.2 WebView兼容性
```javascript
// 检测是否在WebView中
function isWebView() {
  const userAgent = navigator.userAgent;
  
  // Android WebView
  if (/wv/.test(userAgent)) {
    return true;
  }
  
  // iOS WebView
  if (/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(userAgent)) {
    return true;
  }
  
  return false;
}

// 针对WebView的特殊处理
if (isWebView()) {
  // WebView特殊处理逻辑
  document.body.classList.add('webview');
}
```

## 5. 字体和文字问题

### 5.1 字体大小适配
```css
/* 基础字体设置 */
body {
  font-size: 16px;
  font-size: 1rem;
  
  /* 禁用iOS字体自动调整 */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 响应式字体 */
.responsive-text {
  font-size: calc(14px + 0.5vw);
  font-size: clamp(14px, 4vw, 18px);
}

/* 不同设备的字体调整 */
@media screen and (max-width: 375px) {
  body {
    font-size: 14px;
  }
}

@media screen and (min-width: 768px) {
  body {
    font-size: 16px;
  }
}
```

### 5.2 文字溢出处理
```css
/* 单行文字溢出 */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 多行文字溢出 */
.text-ellipsis-multiline {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 兼容性更好的多行溢出 */
.text-ellipsis-compat {
  position: relative;
  line-height: 1.4;
  max-height: 2.8em; /* 2行的高度 */
  overflow: hidden;
}

.text-ellipsis-compat::after {
  content: '...';
  position: absolute;
  bottom: 0;
  right: 0;
  background: white;
  padding-left: 0.5em;
}
```

## 6. 布局问题

### 6.1 Flex布局兼容性
```css
/* 兼容性更好的flex布局 */
.flex-container {
  display: -webkit-box;      /* 老版本语法 */
  display: -webkit-flex;     /* 新版本语法 */
  display: -ms-flexbox;      /* IE10 */
  display: flex;             /* 标准语法 */
  
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
```

### 6.2 Grid布局降级
```css
/* Grid布局的降级方案 */
.grid-container {
  /* 降级方案：使用flex */
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.grid-item {
  flex: 1 1 calc(33.333% - 1rem);
  min-width: 250px;
}

/* 支持Grid的浏览器 */
@supports (display: grid) {
  .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .grid-item {
    flex: none;
  }
}
```

## 7. 性能优化问题

### 7.1 避免重绘和回流
```css
/* 使用transform代替改变位置 */
.animate-position {
  /* 避免 */
  /* left: 100px; */
  
  /* 推荐 */
  transform: translateX(100px);
  will-change: transform;
}

/* 使用opacity代替visibility */
.animate-opacity {
  /* 避免 */
  /* visibility: hidden; */
  
  /* 推荐 */
  opacity: 0;
  will-change: opacity;
}
```

### 7.2 图片懒加载
```javascript
// 图片懒加载实现
function lazyLoadImages() {
  const images = document.querySelectorAll('img[data-src]');
  
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  });
  
  images.forEach(img => imageObserver.observe(img));
}

// 页面加载后执行
document.addEventListener('DOMContentLoaded', lazyLoadImages);
```

```css
/* 懒加载图片样式 */
img.lazy {
  opacity: 0;
  transition: opacity 0.3s;
}

img.lazy.loaded {
  opacity: 1;
}
```

## 8. 调试技巧

### 8.1 移动端调试
```javascript
// 移动端调试信息显示
function createMobileDebugger() {
  const debugger = document.createElement('div');
  debugger.id = 'mobile-debugger';
  debugger.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px;
    font-size: 12px;
    z-index: 9999;
    font-family: monospace;
    max-width: 300px;
    word-wrap: break-word;
  `;
  
  function updateDebugInfo() {
    const info = {
      viewport: `${window.innerWidth}×${window.innerHeight}`,
      screen: `${screen.width}×${screen.height}`,
      dpr: window.devicePixelRatio,
      userAgent: navigator.userAgent.substring(0, 50) + '...',
      orientation: screen.orientation?.angle || 'unknown'
    };
    
    debugger.innerHTML = Object.entries(info)
      .map(([key, value]) => `${key}: ${value}`)
      .join('<br>');
  }
  
  updateDebugInfo();
  document.body.appendChild(debugger);
  
  // 双击隐藏/显示
  debugger.addEventListener('dblclick', () => {
    debugger.style.display = debugger.style.display === 'none' ? 'block' : 'none';
  });
  
  // 定期更新
  setInterval(updateDebugInfo, 1000);
}

// 在移动设备上启用
if (/Mobi|Android/i.test(navigator.userAgent)) {
  createMobileDebugger();
}
```

### 8.2 错误捕获
```javascript
// 移动端错误捕获
window.addEventListener('error', (event) => {
  console.error('JavaScript错误:', event.error);
  
  // 可以发送到服务器进行分析
  // sendErrorToServer(event.error);
});

// Promise错误捕获
window.addEventListener('unhandledrejection', (event) => {
  console.error('Promise错误:', event.reason);
});
```

## 9. 测试策略

### 9.1 设备测试清单
```javascript
// 常见设备尺寸测试
const testDevices = [
  { name: 'iPhone SE', width: 375, height: 667, dpr: 2 },
  { name: 'iPhone 12', width: 390, height: 844, dpr: 3 },
  { name: 'iPhone 12 Pro Max', width: 428, height: 926, dpr: 3 },
  { name: 'Samsung Galaxy S21', width: 384, height: 854, dpr: 3 },
  { name: 'iPad', width: 768, height: 1024, dpr: 2 },
  { name: 'iPad Pro', width: 1024, height: 1366, dpr: 2 }
];

// 自动化测试函数
function testResponsiveDesign() {
  testDevices.forEach(device => {
    console.log(`测试设备: ${device.name}`);
    
    // 模拟设备尺寸
    window.resizeTo(device.width, device.height);
    
    // 检查布局是否正常
    setTimeout(() => {
      const issues = checkLayoutIssues();
      if (issues.length > 0) {
        console.warn(`${device.name} 发现问题:`, issues);
      } else {
        console.log(`${device.name} 测试通过`);
      }
    }, 100);
  });
}

function checkLayoutIssues() {
  const issues = [];
  
  // 检查水平滚动
  if (document.body.scrollWidth > window.innerWidth) {
    issues.push('存在水平滚动');
  }
  
  // 检查元素溢出
  const elements = document.querySelectorAll('*');
  elements.forEach(el => {
    const rect = el.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
      issues.push(`元素溢出: ${el.tagName}.${el.className}`);
    }
  });
  
  return issues;
}
```

---

**总结建议**:
1. 在真实设备上测试是最重要的
2. 建立完整的测试设备清单
3. 使用自动化工具辅助测试
4. 关注用户体验，不只是技术实现
5. 定期更新兼容性方案
