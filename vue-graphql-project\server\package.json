{"name": "vue-graphql-server", "version": "1.0.0", "description": "GraphQL server for Vue GraphQL project", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "type-check": "tsc --noEmit"}, "dependencies": {"@apollo/server": "^4.9.5", "apollo-server-express": "^3.12.1", "cors": "^2.8.5", "express": "^4.18.2", "graphql": "^16.8.1", "graphql-subscriptions": "^2.0.0", "graphql-ws": "^5.14.2", "ws": "^8.14.2", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.8.10", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.8", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "keywords": ["graphql", "apollo", "vue", "typescript", "server"], "author": "Your Name", "license": "MIT"}