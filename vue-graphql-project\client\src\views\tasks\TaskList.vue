<template>
  <div class="task-list-container">
    <div class="page-header">
      <div class="header-left">
        <h1>任务管理</h1>
        <p>管理和跟踪所有任务</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建任务
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索任务标题或描述"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="任务状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="待开始" value="PENDING" />
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="priorityFilter" placeholder="优先级" clearable>
            <el-option label="全部" value="" />
            <el-option label="高" value="HIGH" />
            <el-option label="中" value="MEDIUM" />
            <el-option label="低" value="LOW" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="assigneeFilter" placeholder="负责人" clearable>
            <el-option label="全部" value="" />
            <el-option label="我的任务" value="mine" />
            <el-option label="未分配" value="unassigned" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
        <el-col :span="3">
          <el-button @click="toggleView">
            <el-icon><Grid /></el-icon>
            {{ viewMode === 'grid' ? '列表视图' : '卡片视图' }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 任务列表 - 表格视图 -->
    <el-card v-if="viewMode === 'table'" class="task-table">
      <el-table :data="filteredTasks" style="width: 100%">
        <el-table-column prop="title" label="任务标题" min-width="200">
          <template #default="{ row }">
            <div class="task-title-cell" @click="viewTask(row.id)">
              <span class="task-title">{{ row.title }}</span>
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="assignee" label="负责人" width="120">
          <template #default="{ row }">
            {{ row.assignee?.username || '未分配' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="project" label="所属项目" width="150">
          <template #default="{ row }">
            {{ row.project?.name || '无项目' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="dueDate" label="截止时间" width="120">
          <template #default="{ row }">
            {{ row.dueDate ? formatDate(row.dueDate) : '无' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editTask(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteTask(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 任务列表 - 卡片视图 -->
    <el-card v-else class="task-grid">
      <el-row :gutter="20">
        <el-col
          v-for="task in filteredTasks"
          :key="task.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
        >
          <div class="task-card" @click="viewTask(task.id)">
            <div class="task-header">
              <h3 class="task-title">{{ task.title }}</h3>
              <el-tag :type="getStatusType(task.status)" size="small">
                {{ getStatusText(task.status) }}
              </el-tag>
            </div>
            
            <p class="task-description">{{ task.description }}</p>
            
            <div class="task-meta">
              <div class="meta-item">
                <el-icon><User /></el-icon>
                <span>{{ task.assignee?.username || '未分配' }}</span>
              </div>
              <div class="meta-item">
                <el-icon><Calendar /></el-icon>
                <span>{{ task.dueDate ? formatDate(task.dueDate) : '无截止时间' }}</span>
              </div>
              <div class="meta-item">
                <el-icon><Folder /></el-icon>
                <span>{{ task.project?.name || '无项目' }}</span>
              </div>
            </div>
            
            <div class="task-priority">
              <el-tag
                :type="getPriorityType(task.priority)"
                size="small"
                effect="plain"
              >
                {{ getPriorityText(task.priority) }}
              </el-tag>
            </div>
            
            <div class="task-actions">
              <el-button size="small" @click.stop="editTask(task)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click.stop="deleteTask(task.id)"
              >
                删除
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <div v-if="filteredTasks.length === 0" class="empty-state">
        <el-empty description="暂无任务数据" />
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48, 96]"
        :total="totalTasks"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingTask ? '编辑任务' : '新建任务'"
      width="600px"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskRules"
        label-width="100px"
      >
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="任务状态" prop="status">
          <el-select v-model="taskForm.status" placeholder="请选择状态">
            <el-option label="待开始" value="PENDING" />
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="taskForm.priority" placeholder="请选择优先级">
            <el-option label="高" value="HIGH" />
            <el-option label="中" value="MEDIUM" />
            <el-option label="低" value="LOW" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="负责人" prop="assigneeId">
          <el-select v-model="taskForm.assigneeId" placeholder="请选择负责人" clearable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="所属项目" prop="projectId">
          <el-select v-model="taskForm.projectId" placeholder="请选择项目" clearable>
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="截止时间" prop="dueDate">
          <el-date-picker
            v-model="taskForm.dueDate"
            type="datetime"
            placeholder="选择截止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTask" :loading="saving">
          {{ editingTask ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Grid,
  User,
  Calendar,
  Folder
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const editingTask = ref(null)
const viewMode = ref('grid') // 'grid' | 'table'
const searchQuery = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const assigneeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const totalTasks = ref(0)

const taskFormRef = ref()
const taskForm = reactive({
  title: '',
  description: '',
  status: 'PENDING',
  priority: 'MEDIUM',
  assigneeId: '',
  projectId: '',
  dueDate: ''
})

// 模拟数据
const tasks = ref([
  {
    id: '1',
    title: '用户界面设计',
    description: '设计新的用户界面和用户体验',
    status: 'COMPLETED',
    priority: 'HIGH',
    assignee: { id: '1', username: '李四' },
    project: { id: '1', name: '电商平台重构' },
    dueDate: '2024-02-01T00:00:00Z',
    createdAt: '2024-01-15T00:00:00Z'
  },
  {
    id: '2',
    title: 'API接口开发',
    description: '开发GraphQL API接口',
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    assignee: { id: '2', username: '王五' },
    project: { id: '1', name: '电商平台重构' },
    dueDate: '2024-02-15T00:00:00Z',
    createdAt: '2024-01-16T00:00:00Z'
  },
  {
    id: '3',
    title: '前端组件开发',
    description: '使用Vue3开发前端组件',
    status: 'PENDING',
    priority: 'MEDIUM',
    assignee: { id: '3', username: '赵六' },
    project: { id: '1', name: '电商平台重构' },
    dueDate: '2024-02-28T00:00:00Z',
    createdAt: '2024-01-17T00:00:00Z'
  }
])

const users = ref([
  { id: '1', username: '李四' },
  { id: '2', username: '王五' },
  { id: '3', username: '赵六' }
])

const projects = ref([
  { id: '1', name: '电商平台重构' },
  { id: '2', name: '移动端APP开发' },
  { id: '3', name: '数据分析系统' }
])

// 表单验证规则
const taskRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择任务状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const filteredTasks = computed(() => {
  let filtered = tasks.value

  if (searchQuery.value) {
    filtered = filtered.filter(task =>
      task.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      task.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }

  if (priorityFilter.value) {
    filtered = filtered.filter(task => task.priority === priorityFilter.value)
  }

  if (assigneeFilter.value) {
    if (assigneeFilter.value === 'mine') {
      // 这里应该根据当前用户过滤
      filtered = filtered.filter(task => task.assignee?.id === '1')
    } else if (assigneeFilter.value === 'unassigned') {
      filtered = filtered.filter(task => !task.assignee)
    }
  }

  return filtered
})

// 方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    PENDING: 'info',
    IN_PROGRESS: 'warning',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    PENDING: '待开始',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return texts[status] || '未知'
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    HIGH: 'danger',
    MEDIUM: 'warning',
    LOW: 'success'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    HIGH: '高优先级',
    MEDIUM: '中优先级',
    LOW: '低优先级'
  }
  return texts[priority] || '未知'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  priorityFilter.value = ''
  assigneeFilter.value = ''
}

const toggleView = () => {
  viewMode.value = viewMode.value === 'grid' ? 'table' : 'grid'
}

const viewTask = (taskId: string) => {
  router.push(`/tasks/${taskId}`)
}

const editTask = (task: any) => {
  editingTask.value = task
  Object.assign(taskForm, {
    ...task,
    assigneeId: task.assignee?.id || '',
    projectId: task.project?.id || ''
  })
  showCreateDialog.value = true
}

const deleteTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      type: 'warning'
    })
    
    // 这里应该调用 GraphQL mutation 删除任务
    tasks.value = tasks.value.filter(t => t.id !== taskId)
    ElMessage.success('任务删除成功')
  } catch {
    // 用户取消删除
  }
}

const saveTask = async () => {
  if (!taskFormRef.value) return

  try {
    await taskFormRef.value.validate()
    saving.value = true

    // 这里应该调用 GraphQL mutation 保存任务
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingTask.value) {
      // 更新任务
      const index = tasks.value.findIndex(t => t.id === editingTask.value.id)
      if (index !== -1) {
        const assignee = users.value.find(u => u.id === taskForm.assigneeId)
        const project = projects.value.find(p => p.id === taskForm.projectId)
        
        tasks.value[index] = {
          ...tasks.value[index],
          ...taskForm,
          assignee,
          project
        }
      }
      ElMessage.success('任务更新成功')
    } else {
      // 创建新任务
      const assignee = users.value.find(u => u.id === taskForm.assigneeId)
      const project = projects.value.find(p => p.id === taskForm.projectId)
      
      const newTask = {
        id: Date.now().toString(),
        ...taskForm,
        assignee,
        project,
        createdAt: new Date().toISOString()
      }
      tasks.value.unshift(newTask)
      ElMessage.success('任务创建成功')
    }

    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingTask.value = null
  Object.assign(taskForm, {
    title: '',
    description: '',
    status: 'PENDING',
    priority: 'MEDIUM',
    assigneeId: '',
    projectId: '',
    dueDate: ''
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 生命周期
onMounted(() => {
  totalTasks.value = tasks.value.length
})
</script>

<style scoped lang="scss">
.task-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    h1 {
      margin: 0 0 5px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #909399;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
}

.task-table,
.task-grid {
  margin-bottom: 20px;
}

.task-title-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;

  .task-title {
    font-weight: 500;
    color: #409eff;

    &:hover {
      text-decoration: underline;
    }
  }
}

.task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .task-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.task-description {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #909399;
    font-size: 12px;
  }
}

.task-priority {
  margin-bottom: 15px;
}

.task-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .filter-card .el-row {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
