# Git 实战项目 - 任务管理系统

## 🎯 项目概述

这是一个完整的 Git 实战项目，通过开发一个简单的任务管理系统来演示 Git 的各种功能和最佳实践。

### 📋 项目特点
- **前端**: HTML + CSS + JavaScript (Vue.js)
- **功能**: 任务的增删改查、状态管理
- **Git 实践**: 完整的 Git 工作流程演示
- **团队协作**: 多人协作开发模式

### 🎯 学习目标
通过这个项目，您将学会：
1. Git 仓库的初始化和配置
2. 分支管理和功能开发
3. 代码提交和推送
4. 合并冲突的解决
5. 版本标签和发布管理
6. 团队协作工作流程

## 🚀 项目初始化步骤

### 第一步：创建项目目录
```bash
# 创建项目目录
mkdir git-task-manager
cd git-task-manager

# 初始化 Git 仓库
git init

# 配置用户信息（如果还没有全局配置）
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

### 第二步：创建基础文件结构
```bash
# 创建项目结构
mkdir -p src/{css,js,components}
mkdir -p docs
mkdir -p tests

# 创建基础文件
touch README.md
touch .gitignore
touch src/index.html
touch src/css/style.css
touch src/js/app.js
touch src/js/task-manager.js
```

### 第三步：设置 .gitignore
```bash
# 创建 .gitignore 文件
cat > .gitignore << 'EOF'
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
*.min.js
*.min.css

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器配置
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 测试覆盖率
coverage/
.nyc_output/

# 备份文件
*.bak
*.backup
EOF
```

### 第四步：创建项目文档
```bash
# 创建详细的 README.md
cat > README.md << 'EOF'
# 任务管理系统

一个简单而功能完整的任务管理系统，用于演示 Git 工作流程和最佳实践。

## 功能特性

- ✅ 添加新任务
- ✅ 标记任务完成/未完成
- ✅ 编辑任务内容
- ✅ 删除任务
- ✅ 任务过滤（全部/进行中/已完成）
- ✅ 本地存储持久化

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **框架**: Vue.js 3
- **样式**: CSS Grid, Flexbox
- **存储**: LocalStorage
- **版本控制**: Git

## 快速开始

1. 克隆仓库
```bash
git clone <repository-url>
cd git-task-manager
```

2. 打开项目
```bash
# 使用 Live Server 或直接在浏览器中打开
open src/index.html
```

## 项目结构

```
git-task-manager/
├── README.md
├── .gitignore
├── docs/
│   ├── CHANGELOG.md
│   └── CONTRIBUTING.md
├── src/
│   ├── index.html
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   ├── app.js
│   │   └── task-manager.js
│   └── components/
└── tests/
    └── task-manager.test.js
```

## 开发指南

### 分支策略

- `main` - 主分支，稳定版本
- `develop` - 开发分支，集成最新功能
- `feature/*` - 功能分支，开发新功能
- `bugfix/*` - 修复分支，修复 bug
- `hotfix/*` - 热修复分支，紧急修复

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 修复 bug
- `docs:` 文档更新
- `style:` 代码格式化
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

示例：
```bash
git commit -m "feat: add task editing functionality"
git commit -m "fix: resolve task deletion bug"
git commit -m "docs: update README with installation guide"
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 版本历史

查看 [CHANGELOG.md](docs/CHANGELOG.md) 了解详细的版本历史。

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
EOF
```

### 第五步：创建基础 HTML 文件
```bash
# 创建 index.html
cat > src/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理系统</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <header class="header">
            <h1>📋 任务管理系统</h1>
            <p>使用 Git 工作流程开发的示例项目</p>
        </header>

        <main class="main">
            <section class="task-input">
                <div class="input-group">
                    <input 
                        type="text" 
                        v-model="newTask" 
                        @keyup.enter="addTask"
                        placeholder="输入新任务..."
                        class="task-input-field"
                    >
                    <button @click="addTask" class="add-btn">添加任务</button>
                </div>
            </section>

            <section class="task-filters">
                <button 
                    @click="filter = 'all'" 
                    :class="{ active: filter === 'all' }"
                    class="filter-btn"
                >
                    全部 ({{ tasks.length }})
                </button>
                <button 
                    @click="filter = 'active'" 
                    :class="{ active: filter === 'active' }"
                    class="filter-btn"
                >
                    进行中 ({{ activeTasks.length }})
                </button>
                <button 
                    @click="filter = 'completed'" 
                    :class="{ active: filter === 'completed' }"
                    class="filter-btn"
                >
                    已完成 ({{ completedTasks.length }})
                </button>
            </section>

            <section class="task-list">
                <div v-if="filteredTasks.length === 0" class="empty-state">
                    <p>{{ emptyMessage }}</p>
                </div>
                <div v-else>
                    <div 
                        v-for="task in filteredTasks" 
                        :key="task.id"
                        class="task-item"
                        :class="{ completed: task.completed }"
                    >
                        <input 
                            type="checkbox" 
                            v-model="task.completed"
                            @change="saveToStorage"
                            class="task-checkbox"
                        >
                        <span 
                            v-if="!task.editing"
                            @dblclick="editTask(task)"
                            class="task-text"
                        >
                            {{ task.text }}
                        </span>
                        <input 
                            v-else
                            v-model="task.text"
                            @blur="finishEdit(task)"
                            @keyup.enter="finishEdit(task)"
                            @keyup.esc="cancelEdit(task)"
                            class="task-edit-input"
                            ref="editInput"
                        >
                        <button 
                            @click="deleteTask(task.id)"
                            class="delete-btn"
                            title="删除任务"
                        >
                            🗑️
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>Git 实战项目演示 | 版本: v1.0.0</p>
        </footer>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
EOF
```

### 第六步：首次提交
```bash
# 添加所有文件到暂存区
git add .

# 查看状态
git status

# 首次提交
git commit -m "feat: initial project setup

- Add project structure and basic files
- Create README.md with project documentation
- Add .gitignore for common files
- Create basic HTML template for task manager
- Set up project foundation for Git workflow demonstration"

# 查看提交历史
git log --oneline
```

## 📊 项目开发计划

### 🎯 开发阶段

#### 阶段 1: 基础功能 (v0.1.0)
- [x] 项目初始化
- [ ] 基础样式设计
- [ ] 任务添加功能
- [ ] 任务显示功能

#### 阶段 2: 核心功能 (v0.2.0)
- [ ] 任务完成状态切换
- [ ] 任务删除功能
- [ ] 任务过滤功能
- [ ] 本地存储

#### 阶段 3: 高级功能 (v0.3.0)
- [ ] 任务编辑功能
- [ ] 任务优先级
- [ ] 任务分类
- [ ] 搜索功能

#### 阶段 4: 优化和发布 (v1.0.0)
- [ ] 代码优化
- [ ] 测试添加
- [ ] 文档完善
- [ ] 正式发布

## 🔄 Git 工作流程演示

### 功能开发流程
```bash
# 1. 创建功能分支
git checkout -b feature/task-styling

# 2. 开发功能
# ... 编写代码 ...

# 3. 提交更改
git add .
git commit -m "feat: add basic task styling"

# 4. 推送分支
git push -u origin feature/task-styling

# 5. 合并到主分支
git checkout main
git merge feature/task-styling

# 6. 删除功能分支
git branch -d feature/task-styling
```

## 🚀 下一步

项目初始化完成后，您可以：

1. **开发基础样式** - 创建 `feature/basic-styling` 分支
2. **实现核心功能** - 创建 `feature/task-operations` 分支
3. **添加测试** - 创建 `feature/testing` 分支
4. **优化性能** - 创建 `feature/performance` 分支

每个功能都将在独立的分支中开发，演示完整的 Git 工作流程。

---

**记住**: 这个项目不仅是学习 Git 的工具，也是实践前端开发技能的好机会！
