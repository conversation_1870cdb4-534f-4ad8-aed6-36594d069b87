<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <div class="logo">
          <el-icon><Grid /></el-icon>
          <h1>Vue GraphQL</h1>
        </div>
        <p class="subtitle">现代化的项目管理系统</p>
      </div>

      <el-card class="login-card">
        <template #header>
          <div class="card-header">
            <h2>登录</h2>
            <p>欢迎回来，请登录您的账户</p>
          </div>
        </template>

        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          @submit.prevent="handleLogin"
          size="large"
        >
          <el-form-item prop="email">
            <el-input
              v-model="form.email"
              placeholder="邮箱地址"
              prefix-icon="Message"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="密码"
              prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="form.remember">记住我</el-checkbox>
              <el-link type="primary" :underline="false">忘记密码？</el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="authStore.loading"
              @click="handleLogin"
              class="login-button"
            >
              {{ authStore.loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <p>
            还没有账户？
            <router-link to="/register" class="register-link">立即注册</router-link>
          </p>
        </div>
      </el-card>

      <!-- 演示账户 -->
      <div class="demo-accounts">
        <h3>演示账户</h3>
        <div class="demo-list">
          <div
            v-for="account in demoAccounts"
            :key="account.email"
            class="demo-item"
            @click="fillDemoAccount(account)"
          >
            <div class="demo-role">{{ account.role }}</div>
            <div class="demo-email">{{ account.email }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginForm } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<LoginForm>({
  email: '',
  password: '',
  remember: false
})

// 表单验证规则
const rules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 演示账户
const demoAccounts = [
  { role: '管理员', email: '<EMAIL>', password: 'admin123' },
  { role: '项目经理', email: '<EMAIL>', password: 'password123' },
  { role: '开发者', email: '<EMAIL>', password: 'password123' },
  { role: '设计师', email: '<EMAIL>', password: 'password123' },
  { role: '测试员', email: '<EMAIL>', password: 'password123' }
]

// 填充演示账户
const fillDemoAccount = (account: any) => {
  form.email = account.email
  form.password = account.password
}

// 处理登录
const handleLogin = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    const result = await authStore.login({
      email: form.email,
      password: form.password
    })

    if (result.success) {
      appStore.notifySuccess('登录成功', `欢迎回来，${result.user?.firstName}！`)
      
      // 跳转到首页或之前访问的页面
      const redirect = router.currentRoute.value.query.redirect as string
      router.push(redirect || '/')
    } else {
      appStore.notifyError('登录失败', result.message || '登录失败，请检查用户名和密码')
    }
  } catch (error: any) {
    console.error('Login error:', error)
    appStore.notifyError('登录失败', error.message || '网络错误，请稍后重试')
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
  color: white;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;

    .el-icon {
      font-size: 32px;
    }

    h1 {
      font-size: 28px;
      font-weight: 600;
      margin: 0;
    }
  }

  .subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
  }
}

.login-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
  margin-bottom: 24px;

  :deep(.el-card__header) {
    padding: 24px 24px 0;
    border: none;
  }

  :deep(.el-card__body) {
    padding: 24px;
  }
}

.card-header {
  text-align: center;
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px;
    color: var(--el-text-color-primary);
  }

  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.login-footer {
  text-align: center;
  margin-top: 24px;

  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }

  .register-link {
    color: var(--el-color-primary);
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

.demo-accounts {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  color: white;

  h3 {
    text-align: center;
    margin: 0 0 16px;
    font-size: 16px;
    font-weight: 500;
  }

  .demo-list {
    display: grid;
    gap: 8px;
  }

  .demo-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-1px);
    }

    .demo-role {
      font-weight: 500;
    }

    .demo-email {
      opacity: 0.8;
      font-size: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-page {
    padding: 16px;
  }

  .login-container {
    max-width: 100%;
  }

  .login-header {
    margin-bottom: 24px;

    .logo {
      h1 {
        font-size: 24px;
      }
    }

    .subtitle {
      font-size: 14px;
    }
  }

  .card-header {
    h2 {
      font-size: 20px;
    }
  }

  .demo-accounts {
    .demo-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
</style>
