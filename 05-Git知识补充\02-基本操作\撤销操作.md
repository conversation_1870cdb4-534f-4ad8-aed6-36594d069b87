# Git 撤销操作详解

## 🔄 撤销操作概览

Git 提供了多种撤销操作，每种适用于不同的场景：

```
工作区修改 → [git checkout] → 撤销工作区修改
暂存区修改 → [git reset] → 撤销暂存区修改
已提交修改 → [git revert] → 创建反向提交
已提交修改 → [git reset] → 重置到指定提交
```

## 🔧 git checkout - 撤销工作区修改

### 📁 撤销文件修改
```bash
# 撤销单个文件的修改
git checkout -- filename.txt

# 撤销多个文件的修改
git checkout -- file1.txt file2.txt

# 撤销当前目录所有文件的修改
git checkout -- .

# 撤销指定目录的修改
git checkout -- src/
```

### 🔄 从指定提交恢复文件
```bash
# 从最后一次提交恢复文件
git checkout HEAD -- filename.txt

# 从指定提交恢复文件
git checkout commit-hash -- filename.txt

# 从指定分支恢复文件
git checkout branch-name -- filename.txt

# 恢复删除的文件
git checkout HEAD~1 -- deleted-file.txt
```

### ⚠️ 注意事项
```bash
# ⚠️ 这个操作会永久丢失工作区的修改！
# 使用前确保不需要保留当前修改

# 安全做法：先查看要撤销的内容
git diff filename.txt

# 或者先备份
cp filename.txt filename.txt.backup
git checkout -- filename.txt
```

## 🔄 git reset - 重置操作

### 📋 reset 的三种模式

#### 1. --soft 模式（软重置）
```bash
# 重置到指定提交，保留暂存区和工作区
git reset --soft HEAD~1

# 效果：
# - 提交历史回退
# - 暂存区保持不变
# - 工作区保持不变
# 适用场景：修改最后一次提交
```

#### 2. --mixed 模式（默认模式）
```bash
# 重置到指定提交，清空暂存区，保留工作区
git reset HEAD~1
git reset --mixed HEAD~1

# 效果：
# - 提交历史回退
# - 暂存区被清空
# - 工作区保持不变
# 适用场景：撤销 git add 操作
```

#### 3. --hard 模式（硬重置）
```bash
# 重置到指定提交，清空暂存区和工作区
git reset --hard HEAD~1

# 效果：
# - 提交历史回退
# - 暂存区被清空
# - 工作区被清空
# ⚠️ 适用场景：完全放弃当前修改
```

### 🎯 常用 reset 操作
```bash
# 撤销最后一次提交（保留修改）
git reset --soft HEAD~1

# 撤销最后一次提交（不保留修改）
git reset --hard HEAD~1

# 撤销暂存区的文件
git reset HEAD filename.txt

# 撤销所有暂存区的文件
git reset HEAD

# 重置到指定提交
git reset --hard commit-hash

# 重置到远程分支状态
git reset --hard origin/main
```

### 🔍 reset 的引用方式
```bash
# 使用相对引用
git reset HEAD~1    # 上一个提交
git reset HEAD~2    # 上两个提交
git reset HEAD^     # 上一个提交（等同于 HEAD~1）
git reset HEAD^^    # 上两个提交（等同于 HEAD~2）

# 使用提交哈希
git reset abc123

# 使用分支名
git reset origin/main

# 使用标签
git reset v1.0.0
```

## ↩️ git revert - 创建反向提交

### 🔄 基本用法
```bash
# 撤销最后一次提交
git revert HEAD

# 撤销指定提交
git revert commit-hash

# 撤销多个提交
git revert HEAD~2..HEAD

# 撤销合并提交
git revert -m 1 merge-commit-hash
```

### 🎯 revert 选项
```bash
# 不自动提交，只修改工作区和暂存区
git revert --no-commit HEAD

# 编辑提交信息
git revert --edit HEAD

# 不编辑提交信息
git revert --no-edit HEAD

# 撤销多个提交但只创建一个反向提交
git revert --no-commit HEAD~3..HEAD
git commit -m "Revert multiple commits"
```

### 🔍 revert vs reset
```bash
# reset：改变历史（危险，不适合已推送的提交）
git reset --hard HEAD~1

# revert：保留历史，创建新提交（安全，适合已推送的提交）
git revert HEAD
```

## 🔧 git restore - 新的撤销命令（Git 2.23+）

### 📁 恢复工作区文件
```bash
# 恢复工作区文件（替代 git checkout --）
git restore filename.txt

# 恢复多个文件
git restore file1.txt file2.txt

# 恢复所有文件
git restore .

# 从指定提交恢复
git restore --source=HEAD~1 filename.txt
```

### 📋 恢复暂存区文件
```bash
# 从暂存区移除文件（替代 git reset HEAD）
git restore --staged filename.txt

# 移除所有暂存的文件
git restore --staged .

# 同时恢复工作区和暂存区
git restore --staged --worktree filename.txt
```

## 🔄 撤销特定场景

### 📝 修改最后一次提交
```bash
# 场景1：修改提交信息
git commit --amend -m "New commit message"

# 场景2：添加遗漏的文件
git add forgotten-file.txt
git commit --amend --no-edit

# 场景3：修改文件内容
# 编辑文件...
git add modified-file.txt
git commit --amend --no-edit
```

### 🔄 撤销已推送的提交
```bash
# 方法1：使用 revert（推荐）
git revert HEAD
git push origin main

# 方法2：强制推送（危险，需要团队协调）
git reset --hard HEAD~1
git push --force-with-lease origin main
```

### 🗑️ 撤销文件删除
```bash
# 恢复误删除的文件
git checkout HEAD -- deleted-file.txt

# 或使用 restore
git restore deleted-file.txt

# 从指定提交恢复
git checkout commit-hash -- deleted-file.txt
```

### 🔄 撤销合并
```bash
# 撤销合并提交（如果还没推送）
git reset --hard HEAD~1

# 撤销合并提交（如果已经推送）
git revert -m 1 merge-commit-hash

# 撤销正在进行的合并
git merge --abort
```

## 🚨 紧急救援

### 💊 找回丢失的提交
```bash
# 查看引用日志
git reflog

# 恢复到指定的引用
git checkout commit-hash

# 创建新分支保存恢复的提交
git checkout -b recovery-branch commit-hash

# 或者直接重置到该提交
git reset --hard commit-hash
```

### 🔍 查找丢失的文件
```bash
# 在历史中搜索删除的文件
git log --diff-filter=D --summary | grep filename

# 查看文件的完整历史
git log --follow -- filename.txt

# 从历史中恢复文件
git checkout commit-hash~1 -- filename.txt
```

### 🔄 撤销错误的 reset
```bash
# 查看 reset 前的状态
git reflog

# 恢复到 reset 前的状态
git reset --hard HEAD@{1}

# 或者使用具体的提交哈希
git reset --hard commit-hash
```

## 🛡️ 安全撤销实践

### ✅ 撤销前的检查
```bash
# 1. 查看当前状态
git status

# 2. 查看将要撤销的内容
git diff
git diff --staged

# 3. 查看提交历史
git log --oneline -5

# 4. 备份重要修改
git stash push -m "Backup before reset"
```

### 🔒 安全的撤销流程
```bash
# 1. 创建备份分支
git branch backup-$(date +%Y%m%d-%H%M%S)

# 2. 执行撤销操作
git reset --hard HEAD~1

# 3. 如果需要恢复
git checkout backup-20231201-143000
```

### ⚠️ 危险操作警告
```bash
# 🚨 这些操作会永久丢失数据，使用前三思！

# 硬重置（丢失所有修改）
git reset --hard HEAD~1

# 强制推送（影响其他开发者）
git push --force origin main

# 清理未跟踪文件
git clean -fd
```

## 💡 最佳实践

### ✅ 撤销操作建议

#### 1. 选择合适的撤销方式
```bash
# 工作区修改 → checkout/restore
git restore filename.txt

# 暂存区修改 → reset/restore --staged
git restore --staged filename.txt

# 本地提交 → reset
git reset --soft HEAD~1

# 已推送提交 → revert
git revert HEAD
```

#### 2. 使用安全的命令
```bash
# 优先使用 --force-with-lease 而不是 --force
git push --force-with-lease origin main

# 优先使用 revert 而不是 reset（对于已推送的提交）
git revert HEAD

# 使用 stash 临时保存修改
git stash push -m "Work in progress"
```

#### 3. 建立撤销习惯
```bash
# 撤销前先查看
git diff
git status

# 重要操作前先备份
git branch backup-branch

# 使用引用日志恢复
git reflog
```

## 🚀 下一步学习

掌握撤销操作后，建议学习：
1. **分支管理** - 分支创建、合并、删除
2. **远程仓库** - push、pull、fetch 等
3. **高级技巧** - rebase、cherry-pick 等

---

**记住**: 撤销操作是 Git 的强大功能，但也可能造成数据丢失。始终要谨慎操作，在不确定时先备份！ ⚠️
