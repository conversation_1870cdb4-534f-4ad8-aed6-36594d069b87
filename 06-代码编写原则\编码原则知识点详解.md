# 编码原则知识点详解

## 目录

1. [OAOO 原则 - 一次且仅一次](#oaoo原则)
2. [KISS 原则 - 保持简洁](#kiss原则)
3. [SOLID 原则详解](#solid原则详解)
    - [单一功能原则 (SRP)](#单一功能原则-srp)
    - [开闭原则 (OCP)](#开闭原则-ocp)
    - [里氏替换原则 (LSP)](#里氏替换原则-lsp)
    - [接口隔离原则 (ISP)](#接口隔离原则-isp)
    - [依赖反转原则 (DIP)](#依赖反转原则-dip)

---

## OAOO 原则

### 核心概念

-   **定义**: Once and Only Once，一次且仅一次
-   **别名**: DRY 原则 (Don't Repeat Yourself)
-   **目标**: 消除代码重复，提高可维护性

### 实践要点

#### 1. 识别重复代码

```javascript
// ❌ 违反OAOO原则
function calculateUserTax(user) {
	if (user.age >= 18 && user.income > 50000) {
		return user.income * 0.2
	}
	return 0
}

function calculateCompanyTax(company) {
	if (company.age >= 18 && company.income > 50000) {
		return company.income * 0.2
	}
	return 0
}

// ✅ 遵循OAOO原则
function calculateTax(entity) {
	if (entity.age >= 18 && entity.income > 50000) {
		return entity.income * 0.2
	}
	return 0
}
```

#### 2. 提取公共逻辑

-   将重复的业务逻辑提取为独立函数
-   使用配置文件替代硬编码值
-   创建工具类或工具函数

#### 3. 数据结构复用

```javascript
// ❌ 重复的数据验证
const validateUser = (user) => {
	if (!user.name || user.name.length < 2) return false
	if (!user.email || !user.email.includes('@')) return false
	return true
}

const validateAdmin = (admin) => {
	if (!admin.name || admin.name.length < 2) return false
	if (!admin.email || !admin.email.includes('@')) return false
	return true
}

// ✅ 统一的验证逻辑
const validatePerson = (person) => {
	if (!person.name || person.name.length < 2) return false
	if (!person.email || !person.email.includes('@')) return false
	return true
}
```

### 应用场景

-   数据验证逻辑
-   业务规则计算
-   数据格式化
-   错误处理
-   配置管理

---

## KISS 原则

### 核心概念

-   **定义**: Keep It Simple, Stupid
-   **目标**: 保持代码简洁、易懂、易维护
-   **原则**: 简单胜过复杂

### 实践要点

#### 1. 避免过度设计

让我们详细分析这个例子，看看为什么复杂的设计违反了 KISS 原则：

```javascript
// ❌ 过度复杂 - 违反KISS原则的原因分析
class AbstractFactoryManagerSingleton {
    private static instance: AbstractFactoryManagerSingleton;
    private factories: Map<string, AbstractFactory>;

    private constructor() {
        this.factories = new Map();
    }

    public static getInstance(): AbstractFactoryManagerSingleton {
        if (!this.instance) {
            this.instance = new AbstractFactoryManagerSingleton();
        }
        return this.instance;
    }

    public registerFactory(type: string, factory: AbstractFactory): void {
        this.factories.set(type, factory);
    }

    public createProduct(type: string, config: ProductConfig): Product {
        const factory = this.factories.get(type);
        if (!factory) {
            throw new Error(`Factory for type ${type} not found`);
        }
        return factory.createProduct(config);
    }
}

/*
这个设计的问题：
1. 过度抽象：使用了抽象工厂模式 + 单例模式 + 注册机制
2. 复杂性过高：需要理解多个设计模式才能使用
3. 过度工程化：对于简单的对象创建，引入了不必要的复杂性
4. 难以测试：单例模式使得单元测试变得困难
5. 违反YAGNI：You Aren't Gonna Need It - 可能永远不需要这么复杂的功能
*/

// ✅ 简单直接 - 遵循KISS原则
const productCreators = {
    user: (data) => new User(data),
    admin: (data) => new Admin(data),
    guest: (data) => new Guest(data)
};

function createProduct(type, data) {
    const creator = productCreators[type];
    if (!creator) {
        throw new Error(`Unknown product type: ${type}`);
    }
    return creator(data);
}

/*
简单设计的优点：
1. 易于理解：任何开发者都能快速理解这段代码
2. 易于测试：可以轻松进行单元测试
3. 易于维护：添加新类型只需要在对象中添加一个属性
4. 性能更好：没有额外的抽象层开销
5. 满足需求：完成了相同的功能，但更简洁
*/
```

#### 2. 清晰的命名

```javascript
// ❌ 不清晰的命名
const d = new Date()
const u = users.filter((x) => x.a > 18)
const calc = (a, b, c) => a * b * c

// ✅ 清晰的命名
const currentDate = new Date()
const adultUsers = users.filter((user) => user.age > 18)
const calculateVolume = (length, width, height) => length * width * height
```

#### 3. 单一职责的函数

```javascript
// ❌ 函数职责过多
function processUserData(userData) {
	// 验证数据
	if (!userData.email || !userData.name) {
		throw new Error('Invalid data')
	}

	// 格式化数据
	userData.email = userData.email.toLowerCase()
	userData.name = userData.name.trim()

	// 保存到数据库
	database.save(userData)

	// 发送邮件
	emailService.sendWelcome(userData.email)

	// 记录日志
	logger.info(`User ${userData.name} created`)

	return userData
}

// ✅ 职责分离
function validateUserData(userData) {
	if (!userData.email || !userData.name) {
		throw new Error('Invalid data')
	}
}

function formatUserData(userData) {
	return {
		...userData,
		email: userData.email.toLowerCase(),
		name: userData.name.trim(),
	}
}

function createUser(userData) {
	validateUserData(userData)
	const formattedData = formatUserData(userData)
	const savedUser = database.save(formattedData)
	emailService.sendWelcome(savedUser.email)
	logger.info(`User ${savedUser.name} created`)
	return savedUser
}
```

### 应用指南

-   优先选择简单的解决方案
-   避免不必要的抽象层
-   使用清晰的变量和函数命名
-   保持函数简短（一般不超过 20 行）
-   避免深层嵌套（不超过 3 层）

---

## SOLID 原则详解

### 单一功能原则 (SRP)

#### 核心概念

-   一个类应该只有一个引起它变化的原因
-   一个类应该只有一个职责

#### 实践示例

```javascript
// ❌ 违反SRP - 用户类承担了太多职责
class User {
	constructor(name, email) {
		this.name = name
		this.email = email
	}

	// 用户数据管理
	getName() {
		return this.name
	}
	getEmail() {
		return this.email
	}

	// 数据库操作
	save() {
		database.save(this)
	}

	delete() {
		database.delete(this.id)
	}

	// 邮件发送
	sendWelcomeEmail() {
		emailService.send(this.email, 'Welcome!')
	}

	// 数据验证
	validate() {
		return this.email.includes('@') && this.name.length > 0
	}
}

// ✅ 遵循SRP - 职责分离
class User {
	constructor(name, email) {
		this.name = name
		this.email = email
	}

	getName() {
		return this.name
	}
	getEmail() {
		return this.email
	}
}

class UserRepository {
	save(user) {
		database.save(user)
	}

	delete(userId) {
		database.delete(userId)
	}
}

class UserValidator {
	validate(user) {
		return user.getEmail().includes('@') && user.getName().length > 0
	}
}

class UserNotificationService {
	sendWelcomeEmail(user) {
		emailService.send(user.getEmail(), 'Welcome!')
	}
}
```

#### 识别违反 SRP 的信号

-   类名包含"And"、"Or"等连接词
-   类有多个不相关的方法
-   类的修改原因有多个
-   类的依赖项过多

---

### 开闭原则 (OCP)

#### 核心概念

-   对扩展开放，对修改封闭
-   通过扩展而非修改来实现新功能

#### 实践示例

```javascript
// ❌ 违反OCP - 每次添加新形状都要修改计算器
class AreaCalculator {
    calculateArea(shapes) {
        let totalArea = 0;

        for (let shape of shapes) {
            if (shape.type === 'rectangle') {
                totalArea += shape.width * shape.height;
            } else if (shape.type === 'circle') {
                totalArea += Math.PI * shape.radius * shape.radius;
            } else if (shape.type === 'triangle') {
                totalArea += 0.5 * shape.base * shape.height;
            }
            // 每次添加新形状都需要修改这里
        }

        return totalArea;
    }
}

// ✅ 遵循OCP - 通过接口扩展
interface Shape {
    calculateArea(): number;
}

class Rectangle implements Shape {
    constructor(private width: number, private height: number) {}

    calculateArea(): number {
        return this.width * this.height;
    }
}

class Circle implements Shape {
    constructor(private radius: number) {}

    calculateArea(): number {
        return Math.PI * this.radius * this.radius;
    }
}

class Triangle implements Shape {
    constructor(private base: number, private height: number) {}

    calculateArea(): number {
        return 0.5 * this.base * this.height;
    }
}

class AreaCalculator {
    calculateArea(shapes: Shape[]): number {
        return shapes.reduce((total, shape) => total + shape.calculateArea(), 0);
    }
}

// 添加新形状无需修改现有代码
class Pentagon implements Shape {
    constructor(private side: number) {}

    calculateArea(): number {
        return (5 * this.side * this.side) / (4 * Math.tan(Math.PI / 5));
    }
}
```

#### 实现策略

-   使用抽象类或接口
-   策略模式
-   模板方法模式
-   依赖注入

---

### 里氏替换原则 (LSP)

#### 核心概念

-   子类对象应该能够替换父类对象
-   子类不应该改变父类的行为契约

#### 实践示例

```javascript
// ❌ 违反LSP - 企鹅不能飞
class Bird {
    fly() {
        console.log('Flying...');
    }
}

class Duck extends Bird {
    fly() {
        console.log('Duck flying...');
    }
}

class Penguin extends Bird {
    fly() {
        throw new Error('Penguins cannot fly!'); // 违反了父类的契约
    }
}

// ✅ 遵循LSP - 重新设计继承结构
abstract class Bird {
    abstract move(): void;
}

abstract class FlyingBird extends Bird {
    abstract fly(): void;

    move(): void {
        this.fly();
    }
}

abstract class WalkingBird extends Bird {
    abstract walk(): void;

    move(): void {
        this.walk();
    }
}

class Duck extends FlyingBird {
    fly(): void {
        console.log('Duck flying...');
    }
}

class Penguin extends WalkingBird {
    walk(): void {
        console.log('Penguin walking...');
    }
}

// 现在可以安全地替换
function makeBirdMove(bird: Bird) {
    bird.move(); // 无论是什么鸟，都能正确移动
}
```

#### 检查 LSP 的方法

-   子类是否抛出父类不会抛出的异常？
-   子类是否加强了前置条件？
-   子类是否削弱了后置条件？
-   子类是否改变了父类方法的预期行为？

---

### 接口隔离原则 (ISP)

#### 核心概念

-   客户端不应该依赖它不需要的接口
-   接口应该小而专一

#### 实践示例

```javascript
// ❌ 违反ISP - 胖接口
interface Worker {
	work(): void;
	eat(): void;
	sleep(): void;
	code(): void;
	design(): void;
	test(): void;
}

class Developer implements Worker {
	work() {
		this.code()
	}
	eat() {
		console.log('Eating...')
	}
	sleep() {
		console.log('Sleeping...')
	}
	code() {
		console.log('Coding...')
	}
	design() {
		throw new Error('Developers do not design')
	} // 不需要的方法
	test() {
		console.log('Testing...')
	}
}

class Designer implements Worker {
	work() {
		this.design()
	}
	eat() {
		console.log('Eating...')
	}
	sleep() {
		console.log('Sleeping...')
	}
	code() {
		throw new Error('Designers do not code')
	} // 不需要的方法
	design() {
		console.log('Designing...')
	}
	test() {
		throw new Error('Designers do not test')
	} // 不需要的方法
}

// ✅ 遵循ISP - 接口隔离
interface Workable {
	work(): void;
}

interface Eatable {
	eat(): void;
}

interface Sleepable {
	sleep(): void;
}

interface Codeable {
	code(): void;
}

interface Designable {
	design(): void;
}

interface Testable {
	test(): void;
}

class Developer implements Workable, Eatable, Sleepable, Codeable, Testable {
	work() {
		this.code()
	}
	eat() {
		console.log('Eating...')
	}
	sleep() {
		console.log('Sleeping...')
	}
	code() {
		console.log('Coding...')
	}
	test() {
		console.log('Testing...')
	}
}

class Designer implements Workable, Eatable, Sleepable, Designable {
	work() {
		this.design()
	}
	eat() {
		console.log('Eating...')
	}
	sleep() {
		console.log('Sleeping...')
	}
	design() {
		console.log('Designing...')
	}
}
```

#### 应用策略

-   将大接口拆分为多个小接口
-   使用组合而非继承
-   按客户端需求设计接口

---

### 依赖反转原则 (DIP)

#### 核心概念

-   高层模块不应依赖低层模块，都应依赖抽象
-   抽象不应依赖细节，细节应依赖抽象

#### 实践示例

```javascript
// ❌ 违反DIP - 高层模块依赖具体实现
class MySQLDatabase {
    save(data) {
        console.log('Saving to MySQL database');
    }
}

class UserService {
    constructor() {
        this.database = new MySQLDatabase(); // 直接依赖具体实现
    }

    createUser(userData) {
        // 业务逻辑
        this.database.save(userData);
    }
}

// ✅ 遵循DIP - 依赖抽象
interface Database {
    save(data: any): void;
}

class MySQLDatabase implements Database {
    save(data: any): void {
        console.log('Saving to MySQL database');
    }
}

class MongoDatabase implements Database {
    save(data: any): void {
        console.log('Saving to MongoDB');
    }
}

class UserService {
    constructor(private database: Database) {} // 依赖抽象

    createUser(userData: any): void {
        // 业务逻辑
        this.database.save(userData);
    }
}

// 依赖注入
const mysqlDb = new MySQLDatabase();
const userService = new UserService(mysqlDb);

// 可以轻松切换数据库实现
const mongoDb = new MongoDatabase();
const userServiceWithMongo = new UserService(mongoDb);
```

#### 实现方式

-   依赖注入 (Dependency Injection)
-   工厂模式
-   服务定位器模式
-   控制反转容器 (IoC Container)
