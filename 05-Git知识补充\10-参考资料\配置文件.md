# Git 配置文件详解

## 📋 概述

Git 配置文件是控制 Git 行为的重要文件，包括全局配置、仓库配置、忽略文件等。本文档详细介绍各种配置文件的用法和最佳实践。

## ⚙️ Git 配置文件层级

### 配置文件优先级
1. **系统级配置** (`/etc/gitconfig`)
2. **全局配置** (`~/.gitconfig` 或 `~/.config/git/config`)
3. **仓库配置** (`.git/config`)
4. **工作区配置** (`.git/config.worktree`)

### 配置查看和设置
```bash
# 查看所有配置
git config --list

# 查看配置来源
git config --list --show-origin

# 设置不同级别的配置
git config --system user.name "System User"    # 系统级
git config --global user.name "Global User"    # 全局
git config --local user.name "Local User"      # 仓库级
```

## 🌐 全局配置文件 (.gitconfig)

### 基础用户配置
```ini
[user]
    name = Your Name
    email = <EMAIL>
    signingkey = GPG_KEY_ID

[core]
    editor = vim
    autocrlf = input          # Linux/Mac: input, Windows: true
    safecrlf = true
    quotepath = false         # 支持中文文件名
    ignorecase = false        # 区分大小写
    filemode = true
    
[init]
    defaultBranch = main      # 默认分支名
```

### 别名配置
```ini
[alias]
    # 基础别名
    st = status
    co = checkout
    br = branch
    ci = commit
    df = diff
    
    # 高级别名
    unstage = reset HEAD --
    last = log -1 HEAD
    visual = !gitk
    
    # 日志别名
    lg = log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit
    lol = log --graph --decorate --pretty=oneline --abbrev-commit
    lola = log --graph --decorate --pretty=oneline --abbrev-commit --all
    
    # 分支操作
    co-pr = !sh -c 'git fetch origin pull/$1/head:pr-$1 && git checkout pr-$1' -
    delete-merged = !git branch --merged | grep -v '\\*\\|main\\|develop' | xargs -n 1 git branch -d
    
    # 统计信息
    contributors = shortlog --summary --numbered
    count = !git shortlog -sn
```

### 网络和性能配置
```ini
[http]
    postBuffer = 524288000    # 500MB
    maxRequestBuffer = 100M
    lowSpeedLimit = 0
    lowSpeedTime = 999999
    
[https]
    postBuffer = 524288000
    
[transfer]
    fsckObjects = false
    
[fetch]
    fsckObjects = false
    parallel = 4              # 并行获取
    
[receive]
    fsckObjects = false
    
[pack]
    compression = 9
    
[gc]
    auto = 256
    autopacklimit = 4
```

### 推送和拉取配置
```ini
[push]
    default = simple          # 推送策略
    followTags = true         # 自动推送标签
    
[pull]
    rebase = false           # 拉取时是否变基
    ff = only                # 只允许快进合并
    
[merge]
    tool = vimdiff           # 合并工具
    conflictstyle = diff3    # 冲突显示样式
    
[rebase]
    autoStash = true         # 自动储藏
    autoSquash = true        # 自动压缩
```

### 颜色配置
```ini
[color]
    ui = auto
    
[color "branch"]
    current = yellow reverse
    local = yellow
    remote = green
    
[color "diff"]
    meta = yellow bold
    frag = magenta bold
    old = red bold
    new = green bold
    
[color "status"]
    added = yellow
    changed = green
    untracked = cyan
```

## 📁 仓库配置文件 (.git/config)

### 基础仓库配置
```ini
[core]
    repositoryformatversion = 0
    filemode = true
    bare = false
    logallrefupdates = true
    ignorecase = true
    precomposeunicode = true

[remote "origin"]
    url = https://github.com/user/repo.git
    fetch = +refs/heads/*:refs/remotes/origin/*

[branch "main"]
    remote = origin
    merge = refs/heads/main

[branch "develop"]
    remote = origin
    merge = refs/heads/develop
    rebase = true
```

### 子模块配置
```ini
[submodule "lib/awesome"]
    path = lib/awesome
    url = https://github.com/user/awesome.git
    branch = main
    update = rebase

[submodule "themes/default"]
    path = themes/default
    url = https://github.com/user/theme.git
    ignore = dirty
```

## 🚫 忽略文件配置 (.gitignore)

### 通用忽略模式
```gitignore
# 编译产物
*.o
*.so
*.dylib
*.dll
*.exe
*.out
*.app

# 包管理器
node_modules/
vendor/
bower_components/
*.egg-info/

# 构建目录
build/
dist/
target/
bin/
obj/

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.orig
```

### 语言特定忽略
```gitignore
# Node.js
node_modules/
npm-debug.log
.nyc_output/
coverage/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# Java
*.class
*.jar
*.war
*.ear
hs_err_pid*
target/

# C/C++
*.o
*.a
*.so
*.exe
*.out

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
vendor/

# Rust
target/
Cargo.lock

# PHP
vendor/
composer.phar
.env

# Ruby
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/
.bundle/
vendor/bundle
```

## 📝 属性文件配置 (.gitattributes)

### 基础属性配置
```gitattributes
# 自动检测文本文件并进行行尾转换
* text=auto

# 明确指定文本文件
*.txt text
*.md text
*.html text
*.css text
*.js text
*.json text
*.xml text
*.yml text
*.yaml text

# 明确指定二进制文件
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.zip binary
*.tar.gz binary
*.exe binary
*.dll binary
*.so binary

# 设置特定文件的行尾
*.sh text eol=lf
*.bat text eol=crlf

# 语言特定设置
*.c text diff=c
*.h text diff=c
*.cpp text diff=cpp
*.java text diff=java
*.php text diff=php
*.py text diff=python
*.rb text diff=ruby
*.js text diff=javascript
*.css text diff=css
*.html text diff=html
```

### 合并策略配置
```gitattributes
# 数据库文件使用二进制合并
*.db merge=binary

# 配置文件使用自定义合并策略
*.config merge=ours

# 文档文件
*.md merge=union
CHANGELOG.md merge=union

# 自动生成的文件
package-lock.json merge=binary
yarn.lock merge=binary
Gemfile.lock merge=binary
```

### 导出配置
```gitattributes
# 导出时排除的文件
.gitattributes export-ignore
.gitignore export-ignore
.github/ export-ignore
tests/ export-ignore
docs/ export-ignore
*.test.js export-ignore
```

## 🔧 钩子配置

### 客户端钩子示例

**pre-commit 钩子**:
```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "运行 pre-commit 检查..."

# 检查代码风格
if command -v eslint >/dev/null 2>&1; then
    echo "检查 JavaScript 代码风格..."
    git diff --cached --name-only --diff-filter=ACM | grep '\.js$' | xargs eslint
    if [ $? -ne 0 ]; then
        echo "ESLint 检查失败，请修复后再提交"
        exit 1
    fi
fi

# 运行测试
if [ -f "package.json" ] && grep -q '"test"' package.json; then
    echo "运行测试..."
    npm test
    if [ $? -ne 0 ]; then
        echo "测试失败，请修复后再提交"
        exit 1
    fi
fi

echo "pre-commit 检查通过"
exit 0
```

**commit-msg 钩子**:
```bash
#!/bin/sh
# .git/hooks/commit-msg

commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "提交信息格式不正确！"
    echo "格式: type(scope): description"
    echo "类型: feat, fix, docs, style, refactor, test, chore"
    echo "示例: feat(auth): 添加用户登录功能"
    exit 1
fi

echo "提交信息格式正确"
exit 0
```

## 🔐 安全配置

### GPG 签名配置
```ini
[user]
    signingkey = YOUR_GPG_KEY_ID

[commit]
    gpgsign = true

[tag]
    gpgsign = true

[gpg]
    program = gpg
```

### SSH 配置
```bash
# ~/.ssh/config
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519
    IdentitiesOnly yes

Host gitlab.com
    HostName gitlab.com
    User git
    IdentityFile ~/.ssh/id_rsa_gitlab
    IdentitiesOnly yes

Host company-git
    HostName git.company.com
    User git
    Port 2222
    IdentityFile ~/.ssh/id_rsa_company
```

## 📊 配置模板

### 开发团队配置模板
```ini
# 团队标准 .gitconfig 模板
[user]
    # 请设置您的姓名和邮箱
    name = 
    email = 

[core]
    editor = code --wait
    autocrlf = input
    quotepath = false
    ignorecase = false

[init]
    defaultBranch = main

[push]
    default = simple
    followTags = true

[pull]
    rebase = false

[alias]
    st = status
    co = checkout
    br = branch
    ci = commit
    lg = log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit

[color]
    ui = auto
```

### 项目配置模板
```ini
# 项目 .git/config 模板
[core]
    repositoryformatversion = 0
    filemode = true
    bare = false
    logallrefupdates = true

[remote "origin"]
    url = https://github.com/company/project.git
    fetch = +refs/heads/*:refs/remotes/origin/*

[branch "main"]
    remote = origin
    merge = refs/heads/main

[branch "develop"]
    remote = origin
    merge = refs/heads/develop
```

---

通过合理配置这些文件，可以大大提升 Git 的使用体验和团队协作效率。建议根据项目需求和团队规范进行定制化配置。
