/**
 * DPR + 响应式 rem 配套 CSS 样式
 * 
 * 特性：
 * - DPR 相关样式处理
 * - 1px 边框解决方案
 * - 响应式容器和组件
 * - CSS 变量支持
 * - 设备类型适配
 * 
 * 使用方法：
 * 1. 引入对应的 JavaScript 文件
 * 2. 引入此 CSS 文件
 * 3. 使用预定义的类名或 CSS 变量
 */

/* ===== 基础重置 ===== */
* {
    box-sizing: border-box;
}

html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
}

/* ===== DPR 相关变量 ===== */
:root {
    --hairline-width: 1px;
    --border-radius-small: 4px;
    --border-radius-medium: 8px;
    --border-radius-large: 12px;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* DPR 1 */
[data-dpr="1"] {
    --hairline-width: 1px;
    --border-radius-small: 4px;
    --border-radius-medium: 8px;
    --border-radius-large: 12px;
}

/* DPR 2 */
[data-dpr="2"] {
    --hairline-width: 0.5px;
    --border-radius-small: 2px;
    --border-radius-medium: 4px;
    --border-radius-large: 6px;
}

/* DPR 3 */
[data-dpr="3"] {
    --hairline-width: 0.33px;
    --border-radius-small: 1.33px;
    --border-radius-medium: 2.67px;
    --border-radius-large: 4px;
}

/* ===== 设备类型相关样式 ===== */

/* 移动端 */
[data-device="mobile"] {
    --container-padding: 0.4rem;
    --font-size-xs: 0.2rem;
    --font-size-sm: 0.24rem;
    --font-size-base: 0.28rem;
    --font-size-lg: 0.32rem;
    --font-size-xl: 0.36rem;
    --font-size-2xl: 0.4rem;
    --line-height: 1.4;
    --spacing-xs: 0.16rem;
    --spacing-sm: 0.24rem;
    --spacing-md: 0.32rem;
    --spacing-lg: 0.4rem;
    --spacing-xl: 0.48rem;
}

/* 平板端 */
[data-device="tablet"] {
    --container-padding: 1.5rem;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --line-height: 1.5;
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.25rem;
    --spacing-xl: 1.5rem;
}

/* 桌面端 */
[data-device="desktop"] {
    --container-padding: 2rem;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --line-height: 1.6;
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
}

/* ===== 响应式容器 ===== */
.container {
    width: 100%;
    padding-left: var(--container-padding);
    padding-right: var(--container-padding);
    margin-left: auto;
    margin-right: auto;
}

.device-mobile .container {
    max-width: none;
}

.device-tablet .container {
    max-width: 1024px;
}

.device-desktop .container {
    max-width: 1200px;
}

/* ===== 1px 边框解决方案 ===== */
.hairline-border {
    position: relative;
}

.hairline-border::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: var(--hairline-width) solid #e0e0e0;
    box-sizing: border-box;
    pointer-events: none;
}

[data-dpr="2"] .hairline-border::after {
    width: 200%;
    height: 200%;
    transform: scale(0.5);
    transform-origin: 0 0;
}

[data-dpr="3"] .hairline-border::after {
    width: 300%;
    height: 300%;
    transform: scale(0.33);
    transform-origin: 0 0;
}

/* 单边边框 */
.hairline-border-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--hairline-width);
    background-color: #e0e0e0;
    transform-origin: 0 0;
}

[data-dpr="2"] .hairline-border-top::before {
    width: 200%;
    transform: scaleY(0.5);
}

[data-dpr="3"] .hairline-border-top::before {
    width: 300%;
    transform: scaleY(0.33);
}

.hairline-border-bottom::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: var(--hairline-width);
    background-color: #e0e0e0;
    transform-origin: 0 100%;
}

[data-dpr="2"] .hairline-border-bottom::after {
    width: 200%;
    transform: scaleY(0.5);
}

[data-dpr="3"] .hairline-border-bottom::after {
    width: 300%;
    transform: scaleY(0.33);
}

/* ===== 字体大小类 ===== */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }

/* ===== 间距类 ===== */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* ===== 响应式组件 ===== */

/* 按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: var(--line-height);
    border: var(--hairline-width) solid transparent;
    border-radius: var(--border-radius-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    user-select: none;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

.btn-outline {
    background-color: transparent;
    border-color: #007bff;
    color: #007bff;
}

/* 卡片 */
.card {
    background-color: #fff;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-md);
    border-bottom: var(--hairline-width) solid #e0e0e0;
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    border-top: var(--hairline-width) solid #e0e0e0;
    background-color: #f8f9fa;
}

/* 表单 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: #495057;
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm);
    font-size: var(--font-size-base);
    line-height: var(--line-height);
    color: #495057;
    background-color: #fff;
    border: var(--hairline-width) solid #ced4da;
    border-radius: var(--border-radius-medium);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 网格系统 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: calc(var(--spacing-sm) * -1);
    margin-right: calc(var(--spacing-sm) * -1);
}

.col {
    flex: 1;
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ===== 高清屏图片处理 ===== */
.img-responsive {
    max-width: 100%;
    height: auto;
}

[data-dpr="2"] .img-responsive {
    image-rendering: -webkit-optimize-contrast;
}

[data-dpr="3"] .img-responsive {
    image-rendering: -webkit-optimize-contrast;
}

/* ===== 工具类 ===== */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.text-primary { color: #007bff !important; }
.text-secondary { color: #6c757d !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }

.bg-primary { background-color: #007bff !important; }
.bg-secondary { background-color: #6c757d !important; }
.bg-light { background-color: #f8f9fa !important; }
.bg-dark { background-color: #343a40 !important; }

/* ===== 响应式显示/隐藏 ===== */
.show-mobile { display: none; }
.show-tablet { display: none; }
.show-desktop { display: none; }

.device-mobile .show-mobile { display: block; }
.device-mobile .hide-mobile { display: none; }

.device-tablet .show-tablet { display: block; }
.device-tablet .hide-tablet { display: none; }

.device-desktop .show-desktop { display: block; }
.device-desktop .hide-desktop { display: none; }

/* ===== 动画和过渡 ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 媒体查询补充 ===== */
@media screen and (max-width: 767px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
    .container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media screen and (min-width: 1024px) {
    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* ===== 打印样式 ===== */
@media print {
    .no-print { display: none !important; }
    
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
