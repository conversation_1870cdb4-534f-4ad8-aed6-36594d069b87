# Apollo Server 插件系统详解

## 📖 概述

Apollo Server 插件系统是一个强大的扩展机制，允许你在 GraphQL 请求的生命周期中插入自定义逻辑。插件可以用于日志记录、性能监控、认证授权、缓存控制、错误处理等多种场景。

## 🔧 基本配置

```typescript
import { ApolloServer } from 'apollo-server-express'

const server = new ApolloServer({
	typeDefs,
	resolvers,
	plugins: [
		// 插件数组
		myCustomPlugin,
		anotherPlugin(),
	],
})
```

## 🔄 插件生命周期

### 📋 钩子函数速查表（按执行顺序排列）

| 执行顺序 | 钩子函数              | 调用时机               | 主要用途                   | 可访问的数据   |
| -------- | --------------------- | ---------------------- | -------------------------- | -------------- |
| 1️⃣       | `serverWillStart`     | 服务器启动时           | 初始化全局资源、连接数据库 | 服务器配置     |
| 2️⃣       | `requestDidStart`     | 每个请求开始           | 初始化请求数据、设置追踪   | 基本请求信息   |
| 3️⃣       | `parsingDidStart`     | 开始解析查询           | 语法检查、性能监控         | 查询字符串     |
| 4️⃣       | `validationDidStart`  | 开始验证查询           | Schema 验证、自定义规则    | AST 和 Schema  |
| 5️⃣       | `didResolveOperation` | 操作解析完成           | 权限检查、操作记录         | 完整请求上下文 |
| 6️⃣       | `executionDidStart`   | 开始执行操作           | 执行监控、上下文注入       | 执行计划       |
| 7️⃣       | `executionDidEnd`     | 执行完成               | 性能记录、资源清理         | 执行结果       |
| ❌       | `didEncounterErrors`  | 遇到错误时（任意阶段） | 错误处理、监控告警         | 错误详情       |
| 8️⃣       | `willSendResponse`    | 发送响应前             | 响应修改、缓存设置         | 完整响应数据   |
| 🔚       | `serverWillStop`      | 服务器停止时           | 清理资源、关闭连接         | 服务器状态     |

> **注意**: `didEncounterErrors` 可能在解析、验证或执行阶段的任何时候触发，不是固定顺序。

### 🔄 详细执行流程

```
🚀 服务器启动
├── serverWillStart()
│
📨 收到 GraphQL 请求
├── requestDidStart()
│
📝 解析查询字符串
├── parsingDidStart()
├── ✅ 解析成功 / ❌ 解析失败 → didEncounterErrors()
│
🔍 验证查询是否符合 Schema
├── validationDidStart()
├── ✅ 验证成功 / ❌ 验证失败 → didEncounterErrors()
│
🎯 确定要执行的操作
├── didResolveOperation()
│
⚡ 执行 GraphQL 操作
├── executionDidStart()
│   ├── 🎯 **最佳上下文注入时机**
│   ├── 执行所有 resolvers...
│   └── executionDidEnd()
│       ├── ✅ 执行成功 / ❌ 执行失败 → didEncounterErrors()
│
📤 准备发送响应
├── willSendResponse()
│
📨 发送 HTTP 响应给客户端
│
🛑 服务器关闭时
└── serverWillStop()
```

### ⚠️ 错误处理特殊说明

`didEncounterErrors` 钩子比较特殊，它不在固定位置执行，而是在以下任何阶段遇到错误时触发：

-   **解析阶段错误**: 查询语法错误
-   **验证阶段错误**: 查询不符合 Schema
-   **执行阶段错误**: Resolver 抛出异常
-   **其他错误**: 网络错误、权限错误等

### 📦 requestContext 对象详解

`requestContext` 是插件钩子中最重要的参数，包含了请求的完整信息：

````typescript
interface GraphQLRequestContext {
  // 🎯 请求信息
  request: {
    query: string;           // GraphQL查询字符串
    variables?: object;      // 查询变量
    operationName?: string;  // 操作名称
    extensions?: object;     // 扩展信息
    http?: HTTPGraphQLRequest; // HTTP请求详情
  };

  // 🎯 响应信息
  response: {
    body?: object;          // 响应体
    http?: {
      status?: number;      // HTTP状态码
      headers?: Headers;    // HTTP响应头
    };
  };

  // 🎯 上下文信息
  context: {
    user?: User;           // 当前用户信息
    token?: string;        // 认证令牌
    requestId?: string;    // 请求追踪ID
    loaders?: object;      // DataLoader实例
    // ... 其他自定义上下文数据
  };

  // 🎯 错误信息（仅在didEncounterErrors中可用）
  errors?: GraphQLError[];

  // 🎯 Schema和文档信息
  schema?: GraphQLSchema;
  document?: DocumentNode;
}

### 服务器级别钩子

```typescript
export const serverLifecyclePlugin = {
	// 🎯 Apollo Server启动时调用（在server.start()时）
	// 用途: 初始化全局资源、连接数据库、启动后台任务、注册清理函数
	// 返回值: 可选的清理函数对象
	serverWillStart() {
		console.log('🚀 Server is starting...')

		// 可以在这里执行:
		// - 数据库连接初始化
		// - 缓存预热
		// - 外部服务连接
		// - 定时任务启动
		// - 监控系统初始化

		return {
			// 🎯 Apollo Server停止时调用（在server.stop()时或进程退出时）
			// 用途: 清理资源、关闭数据库连接、停止后台任务、保存状态
			serverWillStop() {
				console.log('🛑 Server is stopping...')

				// 可以在这里执行:
				// - 关闭数据库连接
				// - 清理缓存
				// - 停止定时任务
				// - 保存应用状态
				// - 发送关闭通知
			},
		}
	},
}
````

### 请求级别钩子

```typescript
export const requestLifecyclePlugin = {
	// 🎯 每个GraphQL请求开始时调用
	// 用途: 初始化请求级别的数据、设置追踪ID、记录请求开始时间
	requestDidStart() {
		console.log('📨 Request started')

		return {
			// 🎯 GraphQL操作解析和确定后调用
			// 用途: 记录操作名称、检查权限、验证操作类型、设置缓存策略
			// requestContext包含: request(查询、变量、操作名)、context(用户信息等)
			didResolveOperation(requestContext) {
				const { operationName, query, variables } =
					requestContext.request
				const { user } = requestContext.context
				console.log(`🔍 Operation: ${operationName}`)
				// 可访问: 操作名、查询字符串、变量、用户上下文
			},

			// 🎯 开始解析GraphQL查询字符串为AST时调用
			// 用途: 监控解析性能、记录解析错误、实现查询白名单
			parsingDidStart() {
				const startTime = Date.now()
				return (err, result) => {
					const duration = Date.now() - startTime
					if (err) {
						console.error('❌ Parsing error:', err)
						// 处理语法错误、恶意查询等
					} else {
						console.log(`✅ Parsing completed in ${duration}ms`)
						// 记录解析性能
					}
				}
			},

			// 🎯 开始验证GraphQL查询是否符合Schema时调用
			// 用途: 监控验证性能、记录验证错误、添加自定义验证规则
			validationDidStart() {
				const startTime = Date.now()
				return (err, result) => {
					const duration = Date.now() - startTime
					if (err) {
						console.error('❌ Validation error:', err)
						// 处理Schema不匹配、字段不存在等错误
					} else {
						console.log(`✅ Validation completed in ${duration}ms`)
						// 记录验证性能
					}
				}
			},

			// 🎯 开始执行GraphQL操作时调用
			// 用途: 监控执行性能、设置执行上下文、记录执行开始
			// ⭐ 重要: 可以在这里为上下文注入请求级别的共享数据
			executionDidStart(requestContext) {
				const startTime = Date.now()

				// 🎯 为上下文注入请求级别的共享数据
				// 用途: DataLoader实例、请求追踪、缓存、数据库事务等
				requestContext.context.requestStartTime = startTime
				requestContext.context.requestId = `req_${Date.now()}_${Math.random()
					.toString(36)
					.substr(2, 9)}`

				// 🎯 初始化DataLoader实例（解决N+1问题）
				requestContext.context.loaders = {
					userLoader: new DataLoader((userIds) =>
						batchLoadUsers(userIds)
					),
					taskLoader: new DataLoader((taskIds) =>
						batchLoadTasks(taskIds)
					),
					projectLoader: new DataLoader((projectIds) =>
						batchLoadProjects(projectIds)
					),
				}

				// 🎯 初始化请求级别的缓存
				requestContext.context.requestCache = new Map()

				// 🎯 设置数据库事务（如果需要）
				// requestContext.context.transaction = await db.beginTransaction()

				console.log(
					`🚀 Execution starting for request: ${requestContext.context.requestId}`
				)

				return {
					// 🎯 GraphQL操作执行完成时调用
					// 用途: 记录执行时间、清理资源、记录执行结果
					executionDidEnd(err) {
						const duration = Date.now() - startTime
						if (err) {
							console.error('❌ Execution error:', err)
							// 处理resolver错误、数据库连接错误等
						} else {
							console.log(
								`✅ Execution completed in ${duration}ms`
							)
							// 记录成功执行的性能数据
						}

						// 🎯 清理请求级别的资源
						// 清理DataLoader缓存
						if (requestContext.context.loaders) {
							Object.values(
								requestContext.context.loaders
							).forEach((loader) => {
								if (loader.clearAll) loader.clearAll()
							})
						}

						// 清理请求缓存
						if (requestContext.context.requestCache) {
							requestContext.context.requestCache.clear()
						}

						// 提交或回滚事务
						// if (requestContext.context.transaction) {
						//   if (err) {
						//     await requestContext.context.transaction.rollback()
						//   } else {
						//     await requestContext.context.transaction.commit()
						//   }
						// }
					},
				}
			},

			// 🎯 GraphQL执行过程中遇到任何错误时调用
			// 用途: 错误日志记录、错误监控、错误格式化、发送告警
			// 包括: resolver错误、验证错误、解析错误等所有类型的错误
			didEncounterErrors(requestContext) {
				const { errors, request, context } = requestContext
				console.error(
					'❌ Errors:',
					errors.map((e) => ({
						message: e.message,
						path: e.path, // 错误发生的字段路径
						locations: e.locations, // 错误在查询中的位置
						extensions: e.extensions, // 错误的额外信息
					}))
				)
				// 可以发送到错误监控服务、记录用户行为等
			},

			// 🎯 即将发送HTTP响应给客户端时调用
			// 用途: 修改响应头、添加CORS头、记录响应大小、设置缓存
			willSendResponse(requestContext) {
				const { response, request } = requestContext
				console.log('📤 Sending response')

				// 可以修改响应:
				// response.http.headers - HTTP头部
				// response.body - 响应体(谨慎修改)
				// response.http.status - HTTP状态码

				// 常用场景: 添加自定义头部、CORS设置、缓存控制
				response.http?.headers?.set('X-Custom-Header', 'value')
			},
		}
	},
}
```

## 💡 上下文注入最佳实践

### 🎯 在 executionDidStart 中注入共享数据

`executionDidStart` 是注入请求级别共享数据的最佳位置，因为：

-   此时查询已经解析和验证完成
-   即将开始执行 resolvers
-   可以安全地修改 `requestContext.context`
-   所有 resolvers 都能访问注入的数据

### 📝 实际应用示例

```typescript
export const contextInjectionPlugin = {
	requestDidStart() {
		return {
			executionDidStart(requestContext) {
				// 🎯 1. 注入请求追踪信息
				requestContext.context.requestId = `req_${Date.now()}_${Math.random()
					.toString(36)
					.substr(2, 9)}`
				requestContext.context.startTime = Date.now()

				// 🎯 2. 初始化 DataLoader 实例（解决 N+1 问题）
				requestContext.context.loaders = {
					// 用户数据加载器
					userLoader: new DataLoader(async (userIds) => {
						const users = await db.users.findMany({
							where: { id: { in: userIds } },
						})
						return userIds.map((id) =>
							users.find((user) => user.id === id)
						)
					}),

					// 任务数据加载器
					taskLoader: new DataLoader(async (taskIds) => {
						const tasks = await db.tasks.findMany({
							where: { id: { in: taskIds } },
						})
						return taskIds.map((id) =>
							tasks.find((task) => task.id === id)
						)
					}),

					// 项目数据加载器
					projectLoader: new DataLoader(async (projectIds) => {
						const projects = await db.projects.findMany({
							where: { id: { in: projectIds } },
						})
						return projectIds.map((id) =>
							projects.find((project) => project.id === id)
						)
					}),
				}

				// 🎯 3. 初始化请求级别缓存
				requestContext.context.cache = new Map()

				// 🎯 4. 设置数据库事务（适用于需要事务的操作）
				if (
					requestContext.request.operationName?.includes('create') ||
					requestContext.request.operationName?.includes('update') ||
					requestContext.request.operationName?.includes('delete')
				) {
					// requestContext.context.transaction = await db.beginTransaction()
				}

				// 🎯 5. 注入用户权限信息
				if (requestContext.context.user) {
					requestContext.context.permissions =
						await getUserPermissions(requestContext.context.user.id)
				}

				// 🎯 6. 初始化请求统计
				requestContext.context.stats = {
					resolverCalls: 0,
					dbQueries: 0,
					cacheHits: 0,
					cacheMisses: 0,
				}

				console.log(
					`🚀 Context initialized for request: ${requestContext.context.requestId}`
				)

				return {
					executionDidEnd(err) {
						// 🎯 清理资源
						if (requestContext.context.loaders) {
							Object.values(
								requestContext.context.loaders
							).forEach((loader) => {
								if (loader.clearAll) loader.clearAll()
							})
						}

						if (requestContext.context.cache) {
							requestContext.context.cache.clear()
						}

						// 记录请求统计
						console.log(
							`📊 Request stats:`,
							requestContext.context.stats
						)
					},
				}
			},
		}
	},
}
```

### 🔧 在 Resolvers 中使用注入的数据

```typescript
// 在你的 resolvers 中使用注入的数据
const resolvers = {
	Query: {
		user: async (parent, { id }, context) => {
			// 🎯 使用 DataLoader 避免 N+1 问题
			const user = await context.loaders.userLoader.load(id)

			// 🎯 更新统计信息
			context.stats.resolverCalls++

			// 🎯 使用请求级别缓存
			const cacheKey = `user_${id}`
			if (context.cache.has(cacheKey)) {
				context.stats.cacheHits++
				return context.cache.get(cacheKey)
			}

			context.stats.cacheMisses++
			context.cache.set(cacheKey, user)

			return user
		},

		tasks: async (parent, args, context) => {
			// 🎯 检查权限
			if (!context.permissions?.includes('read:tasks')) {
				throw new Error('Insufficient permissions')
			}

			// 🎯 使用请求 ID 进行日志追踪
			console.log(
				`[${context.requestId}] Fetching tasks for user: ${context.user?.id}`
			)

			return await db.tasks.findMany({
				where: { userId: context.user.id },
			})
		},
	},

	User: {
		tasks: async (parent, args, context) => {
			// 🎯 使用 DataLoader 批量加载用户的任务
			return await context.loaders.taskLoader.loadMany(parent.taskIds)
		},
	},
}
```

### 🎯 常见的上下文注入场景

| 注入内容       | 用途           | 示例                         |
| -------------- | -------------- | ---------------------------- |
| **DataLoader** | 解决 N+1 问题  | `userLoader`, `taskLoader`   |
| **请求追踪**   | 日志关联、调试 | `requestId`, `traceId`       |
| **缓存实例**   | 请求级别缓存   | `requestCache`, `redisCache` |
| **数据库事务** | 数据一致性     | `transaction`, `session`     |
| **用户权限**   | 权限检查       | `permissions`, `roles`       |
| **统计信息**   | 性能监控       | `stats`, `metrics`           |
| **配置信息**   | 功能开关       | `featureFlags`, `config`     |

## 🛠️ 实用插件示例

### 1. 日志记录插件

```typescript
export const loggingPlugin = {
	requestDidStart() {
		const startTime = Date.now() // 记录请求开始时间用于计算耗时

		return {
			// 🎯 记录操作信息和请求参数
			// 用途: 调试、审计、监控特定操作的调用频率
			didResolveOperation(requestContext) {
				const { request } = requestContext
				console.log(
					`🔍 GraphQL Operation: ${
						request.operationName || 'Anonymous'
					}`
				)
				console.log(`📝 Variables:`, request.variables)
				// 可以记录: 用户ID、IP地址、User-Agent等
			},

			// 🎯 记录所有GraphQL错误
			// 用途: 错误监控、调试、统计错误率
			didEncounterErrors(requestContext) {
				console.error(
					'❌ GraphQL Errors:',
					requestContext.errors.map((err) => err.message)
				)
				// 可以发送到日志系统: ELK、Splunk、CloudWatch等
			},

			// 🎯 记录请求完成信息和性能数据
			// 用途: 性能监控、响应时间统计、慢查询识别
			willSendResponse(requestContext) {
				const duration = Date.now() - startTime
				console.log(`⏱️  Request completed in ${duration}ms`)
				// 可以记录: 响应大小、缓存命中率、数据库查询次数等
			},
		}
	},
}
```

### 2. 性能监控插件

```typescript
export const performancePlugin = {
	requestDidStart() {
		const startTime = process.hrtime.bigint() // 高精度时间测量

		return {
			// 🎯 监控请求响应时间和性能指标
			// 用途: 性能分析、SLA监控、慢查询告警、性能优化
			willSendResponse(requestContext) {
				const endTime = process.hrtime.bigint()
				const duration = Number(endTime - startTime) / 1000000 // 转换为毫秒

				// 🎯 添加性能相关的HTTP响应头
				// 用途: 客户端性能监控、调试、缓存策略
				requestContext.response.http?.headers?.set(
					'X-Response-Time',
					`${duration.toFixed(2)}ms`
				)

				// 🎯 识别和记录慢查询
				// 用途: 性能优化、容量规划、告警通知
				if (duration > 1000) {
					console.warn(`🐌 Slow query: ${duration.toFixed(2)}ms`, {
						operation: requestContext.request.operationName,
						variables: requestContext.request.variables,
					})
					// 可以发送到监控系统: Prometheus、Grafana、DataDog等
				}
			},
		}
	},
}
```

### 3. 认证插件

```typescript
export const authPlugin = {
	requestDidStart() {
		return {
			// 🎯 在操作执行前进行认证检查
			// 用途: 保护敏感操作、实现基于操作的权限控制、审计日志
			didResolveOperation(requestContext) {
				const { request, context } = requestContext

				// 🎯 定义需要认证的操作列表
				// 用途: 细粒度权限控制、操作级别的安全策略
				const protectedOperations = [
					'createTask',
					'updateTask',
					'deleteTask',
				]

				// 🎯 检查操作是否需要认证
				// 用途: 防止未授权访问、确保数据安全
				if (protectedOperations.includes(request.operationName)) {
					if (!context.user) {
						throw new Error('Authentication required')
						// 可以抛出自定义错误类型，包含错误码等信息
					}

					// 🎯 记录认证成功的操作
					// 用途: 审计日志、用户行为分析、安全监控
					console.log(
						`🔐 Authenticated operation: ${request.operationName} by ${context.user.username}`
					)
					// 可以记录: IP地址、时间戳、操作参数等
				}
			},
		}
	},
}
```

### 4. 缓存控制插件

```typescript
export const cacheControlPlugin = {
	requestDidStart() {
		return {
			willSendResponse(requestContext) {
				const { request, response } = requestContext

				// 查询操作设置缓存
				if (request.query?.includes('query')) {
					response.http?.headers?.set(
						'Cache-Control',
						'public, max-age=300'
					)
				}

				// 变更操作禁用缓存
				if (request.query?.includes('mutation')) {
					response.http?.headers?.set(
						'Cache-Control',
						'no-cache, no-store, must-revalidate'
					)
				}
			},
		}
	},
}
```

### 5. 请求限制插件

```typescript
export const rateLimitPlugin = {
	requestDidStart() {
		const requestCounts = new Map()

		return {
			didResolveOperation(requestContext) {
				const userId = requestContext.context.user?.id || 'anonymous'
				const now = Date.now()
				const windowMs = 60 * 1000 // 1分钟窗口
				const maxRequests = 100

				const userRequests = requestCounts.get(userId)

				if (!userRequests || now > userRequests.resetTime) {
					requestCounts.set(userId, {
						count: 1,
						resetTime: now + windowMs,
					})
				} else {
					userRequests.count++
					if (userRequests.count > maxRequests) {
						throw new Error('Rate limit exceeded')
					}
				}
			},
		}
	},
}
```

## 🔍 内置插件

Apollo Server 提供了一些内置插件：

### 1. 使用报告插件

```typescript
import { ApolloServerPluginUsageReporting } from 'apollo-server-core'

const server = new ApolloServer({
	plugins: [
		ApolloServerPluginUsageReporting({
			sendVariableValues: { all: true },
			sendHeaders: { all: true },
		}),
	],
})
```

### 2. 缓存控制插件

```typescript
import { ApolloServerPluginCacheControl } from 'apollo-server-core'

const server = new ApolloServer({
	plugins: [
		ApolloServerPluginCacheControl({
			defaultMaxAge: 300,
			calculateHttpHeaders: true,
		}),
	],
})
```

### 3. 着陆页插件

```typescript
import { ApolloServerPluginLandingPageGraphQLPlayground } from 'apollo-server-core'

const server = new ApolloServer({
	plugins: [
		process.env.NODE_ENV === 'production'
			? ApolloServerPluginLandingPageDisabled()
			: ApolloServerPluginLandingPageGraphQLPlayground(),
	],
})
```

## 📊 在你的项目中使用

基于你当前的配置，可以这样扩展：

```typescript
const server = new ApolloServer({
	schema,
	context: createContext,
	introspection: true,
	plugins: [
		// 🎯 增强版日志插件 - 替换你现有的简单日志
		{
			requestDidStart() {
				const startTime = Date.now()

				return {
					// 🎯 记录操作详情和用户信息
					didResolveOperation(requestContext) {
						const { operationName, variables } =
							requestContext.request
						const { user } = requestContext.context

						console.log(
							`🔍 [${operationName || 'Anonymous'}] by ${
								user?.username || 'Guest'
							}`
						)
						if (process.env.NODE_ENV === 'development') {
							console.log('📝 Variables:', variables)
						}
					},

					// 🎯 详细错误记录
					didEncounterErrors(requestContext) {
						const { errors, request, context } = requestContext
						console.error('❌ GraphQL Errors:', {
							operation: request.operationName,
							user: context.user?.username,
							errors: errors.map((e) => ({
								message: e.message,
								path: e.path,
								code: e.extensions?.code,
							})),
						})
					},

					// 🎯 性能监控
					willSendResponse(requestContext) {
						const duration = Date.now() - startTime
						console.log(
							`⏱️  [${requestContext.request.operationName}] ${duration}ms`
						)

						// 添加性能头部
						requestContext.response.http?.headers?.set(
							'X-Response-Time',
							`${duration}ms`
						)
					},
				}
			},
		},

		// 🎯 认证和授权插件
		{
			requestDidStart() {
				return {
					didResolveOperation(requestContext) {
						const { operationName } = requestContext.request
						const { user } = requestContext.context

						// 需要认证的操作
						const protectedOps = [
							'createTask',
							'updateTask',
							'deleteTask',
							'createProject',
						]

						if (protectedOps.includes(operationName)) {
							if (!user) {
								throw new Error('Authentication required')
							}
							console.log(
								`🔐 Authenticated: ${operationName} by ${user.username}`
							)
						}
					},
				}
			},
		},

		// 🎯 开发环境专用调试插件
		...(process.env.NODE_ENV === 'development'
			? [
					{
						requestDidStart() {
							return {
								didResolveOperation(requestContext) {
									console.log('🔧 Debug Context:', {
										hasUser: !!requestContext.context.user,
										userAgent:
											requestContext.request.http?.headers?.get(
												'user-agent'
											),
										origin: requestContext.request.http?.headers?.get(
											'origin'
										),
									})
								},
							}
						},
					},
			  ]
			: []),
	],
})
```

## 🎯 最佳实践

1. **模块化插件**: 将不同功能的插件分离到不同文件
2. **环境区分**: 开发和生产环境使用不同的插件配置
3. **错误处理**: 插件中的错误不应影响正常请求处理
4. **性能考虑**: 避免在插件中执行耗时操作
5. **日志级别**: 根据环境调整日志详细程度

## 🚀 高级插件示例

### 6. 错误处理和监控插件

```typescript
export const errorTrackingPlugin = {
	requestDidStart() {
		return {
			didEncounterErrors(requestContext) {
				const { errors, request, context } = requestContext

				errors.forEach((error) => {
					// 构建错误上下文
					const errorContext = {
						message: error.message,
						operation: request.operationName,
						variables: request.variables,
						user: context.user?.id,
						timestamp: new Date().toISOString(),
						path: error.path,
						stack:
							process.env.NODE_ENV === 'development'
								? error.stack
								: undefined,
					}

					// 发送到错误监控服务（如 Sentry）
					console.error('GraphQL Error:', errorContext)

					// 根据错误类型进行不同处理
					if (error.message.includes('Authentication')) {
						// 认证错误处理
					} else if (error.message.includes('Authorization')) {
						// 授权错误处理
					} else {
						// 其他错误处理
					}
				})
			},
		}
	},
}
```

### 7. 数据加载器清理插件

```typescript
export const dataLoaderPlugin = {
	requestDidStart() {
		return {
			willSendResponse(requestContext) {
				// 清理请求级别的数据加载器缓存
				const { context } = requestContext

				if (context.loaders) {
					// 清理所有数据加载器
					Object.values(context.loaders).forEach((loader: any) => {
						if (typeof loader.clearAll === 'function') {
							loader.clearAll()
						}
					})

					console.log('🧹 DataLoader caches cleared')
				}
			},
		}
	},
}
```

### 8. 请求追踪插件

```typescript
import { v4 as uuidv4 } from 'uuid'

export const requestTracingPlugin = {
	requestDidStart() {
		const requestId = uuidv4()
		const startTime = Date.now()

		return {
			didResolveOperation(requestContext) {
				// 添加请求ID到上下文
				requestContext.context.requestId = requestId

				console.log(
					`🔍 [${requestId}] Operation: ${requestContext.request.operationName}`
				)
			},

			willSendResponse(requestContext) {
				const duration = Date.now() - startTime

				// 添加追踪头部
				requestContext.response.http?.headers?.set(
					'X-Request-ID',
					requestId
				)
				requestContext.response.http?.headers?.set(
					'X-Response-Time',
					`${duration}ms`
				)

				console.log(`✅ [${requestId}] Completed in ${duration}ms`)
			},

			didEncounterErrors(requestContext) {
				console.error(
					`❌ [${requestId}] Errors:`,
					requestContext.errors.map((e) => e.message)
				)
			},
		}
	},
}
```

### 9. 查询复杂度分析插件

```typescript
export const queryComplexityPlugin = {
	requestDidStart() {
		return {
			didResolveOperation(requestContext) {
				const { request } = requestContext
				const query = request.query

				// 简单的复杂度计算（实际项目中可使用 graphql-query-complexity）
				const complexity = calculateQueryComplexity(query)
				const maxComplexity = 1000

				if (complexity > maxComplexity) {
					throw new Error(
						`Query complexity ${complexity} exceeds maximum allowed complexity ${maxComplexity}`
					)
				}

				console.log(`📊 Query complexity: ${complexity}`)

				// 添加复杂度到响应头
				requestContext.request.http?.headers?.set(
					'X-Query-Complexity',
					complexity.toString()
				)
			},
		}
	},
}

function calculateQueryComplexity(query: string): number {
	// 简化的复杂度计算
	const fieldCount = (query.match(/\w+\s*{/g) || []).length
	const depthCount = (query.match(/{/g) || []).length
	return fieldCount * depthCount
}
```

### 10. 开发环境调试插件

```typescript
export const developmentDebugPlugin = {
	requestDidStart() {
		if (process.env.NODE_ENV !== 'development') {
			return {}
		}

		return {
			didResolveOperation(requestContext) {
				console.log('🔧 Debug Info:', {
					operation: requestContext.request.operationName,
					variables: requestContext.request.variables,
					headers: Object.fromEntries(
						Object.entries(
							requestContext.request.http?.headers || {}
						).filter(
							([key]) =>
								!key.toLowerCase().includes('authorization')
						)
					),
					context: {
						user: requestContext.context.user?.username,
						timestamp: new Date().toISOString(),
					},
				})
			},

			willSendResponse(requestContext) {
				console.log('📤 Response Debug:', {
					hasErrors: !!requestContext.errors?.length,
					errorCount: requestContext.errors?.length || 0,
					responseSize: JSON.stringify(requestContext.response.body)
						.length,
				})
			},
		}
	},
}
```

## 🔧 插件组合和配置

### 创建插件工厂函数

```typescript
// 可配置的日志插件
export function createLoggingPlugin(options: {
	logLevel: 'debug' | 'info' | 'warn' | 'error'
	includeVariables: boolean
	includeHeaders: boolean
}) {
	return {
		requestDidStart() {
			return {
				didResolveOperation(requestContext) {
					if (options.logLevel === 'debug') {
						console.log(
							`🔍 Operation: ${requestContext.request.operationName}`
						)

						if (options.includeVariables) {
							console.log(
								'Variables:',
								requestContext.request.variables
							)
						}

						if (options.includeHeaders) {
							console.log(
								'Headers:',
								requestContext.request.http?.headers
							)
						}
					}
				},
			}
		},
	}
}

// 使用示例
const server = new ApolloServer({
	plugins: [
		createLoggingPlugin({
			logLevel: 'debug',
			includeVariables: true,
			includeHeaders: false,
		}),
	],
})
```

### 条件插件加载

```typescript
function getPlugins() {
	const plugins = [
		// 基础插件
		loggingPlugin,
		performancePlugin,
	]

	// 开发环境插件
	if (process.env.NODE_ENV === 'development') {
		plugins.push(developmentDebugPlugin, queryComplexityPlugin)
	}

	// 生产环境插件
	if (process.env.NODE_ENV === 'production') {
		plugins.push(errorTrackingPlugin, rateLimitPlugin)
	}

	// 启用认证的环境
	if (process.env.ENABLE_AUTH === 'true') {
		plugins.push(authPlugin)
	}

	return plugins
}

const server = new ApolloServer({
	typeDefs,
	resolvers,
	plugins: getPlugins(),
})
```

## 📝 插件开发注意事项

### 1. 错误处理

```typescript
export const safePlugin = {
	requestDidStart() {
		return {
			didResolveOperation(requestContext) {
				try {
					// 插件逻辑
					console.log('Processing operation...')
				} catch (error) {
					// 插件错误不应影响请求处理
					console.error('Plugin error:', error)
				}
			},
		}
	},
}
```

### 2. 异步操作

```typescript
export const asyncPlugin = {
	requestDidStart() {
		return {
			async willSendResponse(requestContext) {
				try {
					// 异步操作
					await someAsyncOperation()
				} catch (error) {
					console.error('Async plugin error:', error)
				}
			},
		}
	},
}
```

### 3. 内存管理

```typescript
export const memoryAwarePlugin = {
	requestDidStart() {
		// 避免在插件中创建大量对象或保持长期引用
		const requestData = new WeakMap() // 使用 WeakMap 避免内存泄漏

		return {
			didResolveOperation(requestContext) {
				requestData.set(requestContext, { startTime: Date.now() })
			},

			willSendResponse(requestContext) {
				const data = requestData.get(requestContext)
				if (data) {
					console.log(`Duration: ${Date.now() - data.startTime}ms`)
				}
				// WeakMap 会自动清理引用
			},
		}
	},
}
```

## 🔗 相关资源

-   [Apollo Server 插件官方文档](https://www.apollographql.com/docs/apollo-server/integrations/plugins/)
-   [插件事件参考](https://www.apollographql.com/docs/apollo-server/integrations/plugins-event-reference/)
-   [内置插件列表](https://www.apollographql.com/docs/apollo-server/builtin-plugins/)
-   [GraphQL 查询复杂度分析](https://github.com/slicknode/graphql-query-complexity)
