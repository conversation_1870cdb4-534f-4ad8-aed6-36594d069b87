# Git 工作流程实战指南

## 🚀 常见工作流程

### 📋 Git Flow 工作流程

#### 🌿 分支结构
```bash
main/master     # 主分支，存放稳定版本
develop         # 开发分支，集成最新功能
feature/*       # 功能分支，开发新功能
release/*       # 发布分支，准备新版本
hotfix/*        # 热修复分支，紧急修复
```

#### 🔄 完整流程示例
```bash
# 1. 初始化项目
git checkout -b develop main

# 2. 开发新功能
git checkout -b feature/user-login develop
# 开发代码...
git add .
git commit -m "feat: add user login functionality"
git checkout develop
git merge --no-ff feature/user-login
git branch -d feature/user-login

# 3. 准备发布
git checkout -b release/1.0.0 develop
# 修复 bug，更新版本号...
git add .
git commit -m "chore: bump version to 1.0.0"

# 4. 完成发布
git checkout main
git merge --no-ff release/1.0.0
git tag -a v1.0.0 -m "Release version 1.0.0"
git checkout develop
git merge --no-ff release/1.0.0
git branch -d release/1.0.0

# 5. 紧急修复
git checkout -b hotfix/security-fix main
# 修复代码...
git add .
git commit -m "fix: resolve security vulnerability"
git checkout main
git merge --no-ff hotfix/security-fix
git tag -a v1.0.1 -m "Hotfix version 1.0.1"
git checkout develop
git merge --no-ff hotfix/security-fix
git branch -d hotfix/security-fix
```

### 🔄 GitHub Flow 工作流程

#### 📝 简化流程
```bash
# 1. 创建功能分支
git checkout main
git pull origin main
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat: implement new feature"
git push -u origin feature/new-feature

# 3. 创建 Pull Request
# 在 GitHub 上创建 PR

# 4. 代码审查和合并
# 审查通过后合并到 main

# 5. 清理分支
git checkout main
git pull origin main
git branch -d feature/new-feature
```

### ⚡ GitLab Flow 工作流程

#### 🌊 环境分支
```bash
# 分支结构
main            # 生产环境
pre-production  # 预生产环境
staging         # 测试环境
feature/*       # 功能分支

# 部署流程
git checkout -b feature/new-feature main
# 开发...
git push -u origin feature/new-feature
# 合并到 staging 进行测试
# 测试通过后合并到 pre-production
# 最终合并到 main 部署生产
```

## 👥 团队协作实战

### 🚀 新成员加入项目
```bash
# 1. 克隆项目
git clone https://github.com/company/project.git
cd project

# 2. 配置个人信息
git config user.name "Your Name"
git config user.email "<EMAIL>"

# 3. 了解项目结构
git branch -a                    # 查看所有分支
git log --oneline -10            # 查看最近提交
cat README.md                    # 阅读项目说明

# 4. 设置上游仓库（如果是 fork）
git remote add upstream https://github.com/original/project.git
git fetch upstream
```

### 💻 日常开发流程
```bash
# 1. 开始新任务
git checkout main
git pull origin main
git checkout -b feature/JIRA-123-user-profile

# 2. 开发过程中定期同步
git fetch origin
git rebase origin/main           # 保持分支最新

# 3. 提交代码
git add .
git commit -m "feat(profile): add user avatar upload

- Add file upload component
- Implement image validation
- Add progress indicator

Closes JIRA-123"

# 4. 推送分支
git push -u origin feature/JIRA-123-user-profile

# 5. 创建 Pull Request
# 在 GitHub/GitLab 上创建 PR
```

### 🔍 代码审查流程
```bash
# 审查者操作
git fetch origin
git checkout feature/JIRA-123-user-profile
git log --oneline origin/main..HEAD  # 查看新提交
git diff origin/main...HEAD          # 查看所有差异

# 本地测试
npm install
npm test
npm run build

# 提供反馈后，开发者修改
git add .
git commit -m "fix: address code review comments"
git push origin feature/JIRA-123-user-profile
```

### 🔄 合并和清理
```bash
# 合并后清理（开发者）
git checkout main
git pull origin main
git branch -d feature/JIRA-123-user-profile
git remote prune origin

# 清理已合并的本地分支
git branch --merged | grep -v "\*\|main\|develop" | xargs -n 1 git branch -d
```

## 🚨 冲突解决实战

### ⚔️ 合并冲突处理
```bash
# 1. 发生冲突时
git merge feature-branch
# Auto-merging file.txt
# CONFLICT (content): Merge conflict in file.txt
# Automatic merge failed; fix conflicts and then commit the result.

# 2. 查看冲突文件
git status
# Unmerged paths:
#   both modified:   file.txt

# 3. 编辑冲突文件
# <<<<<<< HEAD
# 当前分支的内容
# =======
# 要合并分支的内容
# >>>>>>> feature-branch

# 4. 解决冲突后
git add file.txt
git commit -m "resolve merge conflict in file.txt"
```

### 🔄 变基冲突处理
```bash
# 1. 变基时发生冲突
git rebase main
# CONFLICT (content): Merge conflict in file.txt
# error: could not apply abc123... commit message

# 2. 解决冲突
# 编辑冲突文件...
git add file.txt

# 3. 继续变基
git rebase --continue

# 4. 如果需要中止
git rebase --abort
```

## 🔧 高级协作技巧

### 🍒 Cherry-pick 使用
```bash
# 将特定提交应用到当前分支
git cherry-pick commit-hash

# 挑选多个提交
git cherry-pick commit1 commit2 commit3

# 挑选提交范围
git cherry-pick start-commit..end-commit

# 只应用修改，不自动提交
git cherry-pick --no-commit commit-hash
```

### 📦 Stash 管理
```bash
# 暂存当前修改
git stash push -m "Work in progress on feature X"

# 查看暂存列表
git stash list

# 应用最新暂存
git stash pop

# 应用指定暂存
git stash apply stash@{1}

# 删除暂存
git stash drop stash@{0}

# 清空所有暂存
git stash clear
```

### 🔄 子模块管理
```bash
# 添加子模块
git submodule add https://github.com/user/repo.git libs/repo

# 初始化子模块
git submodule init
git submodule update

# 或者一次性操作
git submodule update --init --recursive

# 更新子模块
git submodule update --remote

# 推送包含子模块的更改
git push --recurse-submodules=on-demand
```

## 📊 项目维护

### 🧹 仓库清理
```bash
# 清理已合并的分支
git branch --merged main | grep -v "main\|master" | xargs git branch -d

# 清理远程跟踪分支
git remote prune origin

# 垃圾回收
git gc --aggressive --prune=now

# 检查仓库完整性
git fsck --full
```

### 📈 项目统计
```bash
# 贡献者统计
git shortlog -sn

# 代码行数统计
git ls-files | xargs wc -l

# 文件修改频率
git log --name-only --pretty=format: | sort | uniq -c | sort -rg

# 提交活跃度
git log --format='%ad' --date=short | sort | uniq -c | sort -rg
```

### 🔍 问题排查
```bash
# 二分查找问题提交
git bisect start
git bisect bad                    # 当前版本有问题
git bisect good v1.0.0           # v1.0.0 版本正常
# Git 会自动切换到中间提交，测试后标记
git bisect good/bad
# 重复直到找到问题提交
git bisect reset                 # 结束二分查找

# 查找引入特定代码的提交
git log -S "function_name" --oneline

# 查看文件的详细变更历史
git log -p --follow filename.txt
```

## 💡 最佳实践

### ✅ 提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

<body>

<footer>

# 示例
feat(auth): add OAuth2 integration

Implement OAuth2 authentication flow with Google and GitHub providers.
Add user session management and token refresh mechanism.

Closes #123
Breaking change: API endpoint /auth changed to /api/auth
```

### 🔒 安全实践
```bash
# 使用 GPG 签名提交
git config --global user.signingkey YOUR_GPG_KEY
git config --global commit.gpgsign true

# 验证提交签名
git log --show-signature

# 使用 SSH 密钥
ssh-keygen -t ed25519 -C "<EMAIL>"
# 添加到 GitHub/GitLab
```

### 🚀 效率提升
```bash
# 设置有用的别名
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'

# 自动设置上游分支
git config --global push.default current
git config --global push.autoSetupRemote true
```

---

## 🎯 总结

掌握这些工作流程和协作技巧将让你在团队开发中更加高效：

1. **选择合适的工作流程** - 根据团队规模和项目需求选择
2. **保持良好的提交习惯** - 清晰的提交信息和原子性提交
3. **及时同步和沟通** - 定期拉取更新，主动解决冲突
4. **代码审查** - 提高代码质量，分享知识
5. **持续学习** - Git 功能强大，需要不断学习和实践

**记住**: 好的 Git 工作流程是团队协作成功的基础！ 🚀
