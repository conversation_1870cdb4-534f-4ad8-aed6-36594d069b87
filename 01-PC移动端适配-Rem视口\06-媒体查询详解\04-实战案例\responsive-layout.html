<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式布局 - 方向适配示例</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* 头部导航 */
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #3498db;
        }

        .menu-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* 主要内容区域 */
        .main {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 2rem;
            padding: 2rem 0;
            min-height: calc(100vh - 120px);
        }

        .content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .sidebar {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            height: fit-content;
        }

        .sidebar h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin-bottom: 0.5rem;
        }

        .sidebar a {
            color: #666;
            text-decoration: none;
        }

        .sidebar a:hover {
            color: #3498db;
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        /* 响应式媒体查询 */
        
        /* 平板竖屏 */
        @media (max-width: 1024px) and (orientation: portrait) {
            .main {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                order: -1;
            }
            
            .card-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 手机竖屏 */
        @media (max-width: 768px) and (orientation: portrait) {
            .nav-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: #2c3e50;
                flex-direction: column;
                padding: 1rem;
                gap: 1rem;
            }

            .nav-menu.active {
                display: flex;
            }

            .menu-toggle {
                display: block;
            }

            .main {
                padding: 1rem 0;
            }

            .content {
                padding: 1rem;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 手机横屏 */
        @media (max-width: 768px) and (orientation: landscape) {
            .header {
                padding: 0.5rem 0;
            }

            .logo {
                font-size: 1.2rem;
            }

            .main {
                grid-template-columns: 1fr;
                padding: 1rem 0;
                gap: 1rem;
            }

            .content {
                padding: 1rem;
            }

            .sidebar {
                display: none; /* 横屏时隐藏侧边栏节省空间 */
            }

            .card-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .card {
                padding: 1rem;
            }
        }

        /* 小屏幕横屏特殊处理 */
        @media (max-height: 500px) and (orientation: landscape) {
            .header {
                padding: 0.25rem 0;
            }

            .main {
                padding: 0.5rem 0;
            }

            .content {
                padding: 0.5rem;
            }
        }

        /* 打印样式 */
        @media print {
            .header, .sidebar, .menu-toggle {
                display: none !important;
            }

            .main {
                grid-template-columns: 1fr !important;
                padding: 0 !important;
            }

            .content {
                box-shadow: none !important;
                padding: 0 !important;
            }

            .card-grid {
                grid-template-columns: 1fr !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">ResponsiveDemo</div>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#about">关于</a></li>
                    <li><a href="#services">服务</a></li>
                    <li><a href="#contact">联系</a></li>
                </ul>
                <button class="menu-toggle" id="menuToggle">☰</button>
            </nav>
        </div>
    </header>

    <div class="container">
        <main class="main">
            <div class="content">
                <h1>响应式布局示例</h1>
                <p>这个页面演示了如何使用媒体查询来适配不同的设备方向和屏幕尺寸。</p>
                
                <h2>特性说明</h2>
                <ul>
                    <li><strong>竖屏适配</strong>：侧边栏移到内容上方，卡片变为单列布局</li>
                    <li><strong>横屏适配</strong>：隐藏侧边栏节省空间，卡片保持双列</li>
                    <li><strong>导航优化</strong>：小屏幕使用汉堡菜单</li>
                    <li><strong>打印友好</strong>：隐藏导航和侧边栏，优化打印效果</li>
                </ul>

                <div class="card-grid">
                    <div class="card">
                        <h3>卡片 1</h3>
                        <p>这是第一个示例卡片，展示响应式网格布局的效果。</p>
                    </div>
                    <div class="card">
                        <h3>卡片 2</h3>
                        <p>在不同屏幕尺寸和方向下，卡片的排列会自动调整。</p>
                    </div>
                    <div class="card">
                        <h3>卡片 3</h3>
                        <p>横屏时显示两列，竖屏时根据屏幕大小调整列数。</p>
                    </div>
                    <div class="card">
                        <h3>卡片 4</h3>
                        <p>这种布局方式能够提供最佳的用户体验。</p>
                    </div>
                </div>
            </div>

            <aside class="sidebar">
                <h3>侧边栏</h3>
                <ul>
                    <li><a href="#link1">相关链接 1</a></li>
                    <li><a href="#link2">相关链接 2</a></li>
                    <li><a href="#link3">相关链接 3</a></li>
                    <li><a href="#link4">相关链接 4</a></li>
                </ul>

                <h3>提示</h3>
                <p>尝试旋转设备或调整浏览器窗口大小，观察布局的变化。</p>
            </aside>
        </main>
    </div>

    <script>
        // 移动端菜单切换
        const menuToggle = document.getElementById('menuToggle');
        const navMenu = document.getElementById('navMenu');

        menuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });

        // 监听方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                // 方向变化后关闭菜单
                navMenu.classList.remove('active');
                
                // 可以在这里添加其他方向变化后的处理逻辑
                console.log('设备方向已改变');
            }, 100);
        });

        // 显示当前方向信息（仅用于演示）
        function showOrientationInfo() {
            const orientation = window.innerHeight > window.innerWidth ? '竖屏' : '横屏';
            console.log(`当前方向: ${orientation}, 尺寸: ${window.innerWidth}x${window.innerHeight}`);
        }

        window.addEventListener('resize', showOrientationInfo);
        showOrientationInfo(); // 初始显示
    </script>
</body>
</html>
