<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rem单位基础使用教程</title>
    <style>
        /* 设置根元素字体大小 - 这是rem的基准 */
        html {
            font-size: 16px; /* 1rem = 16px */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 75rem; /* 75 × 16px = 1200px */
            margin: 0 auto;
            padding: 2rem; /* 2 × 16px = 32px */
            background: white;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem; /* 3 × 16px = 48px */
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem; /* 0.5 × 16px = 8px */
        }

        .header h1 {
            font-size: 2.5rem; /* 2.5 × 16px = 40px */
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.125rem; /* 1.125 × 16px = 18px */
        }

        .section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 0.5rem;
            border-left: 0.25rem solid #007bff;
        }

        .section h2 {
            font-size: 1.875rem; /* 1.875 × 16px = 30px */
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .section h3 {
            font-size: 1.5rem; /* 1.5 × 16px = 24px */
            margin-bottom: 0.75rem;
            color: #34495e;
        }

        .section p {
            font-size: 1rem; /* 1 × 16px = 16px */
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        /* 演示不同rem值的效果 */
        .demo-box {
            margin: 1rem 0;
            padding: 1rem;
            border: 0.125rem solid #ddd; /* 0.125 × 16px = 2px */
            border-radius: 0.25rem;
            background: white;
        }

        .size-demo {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            border-radius: 0.25rem;
            text-align: center;
        }

        .size-0-5 { font-size: 0.5rem; } /* 8px */
        .size-0-75 { font-size: 0.75rem; } /* 12px */
        .size-1 { font-size: 1rem; } /* 16px */
        .size-1-25 { font-size: 1.25rem; } /* 20px */
        .size-1-5 { font-size: 1.5rem; } /* 24px */
        .size-2 { font-size: 2rem; } /* 32px */

        /* 布局演示 */
        .layout-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(15rem, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .card {
            padding: 1.5rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.1);
            border: 0.0625rem solid #e9ecef;
        }

        .card h4 {
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
            color: #495057;
        }

        .card p {
            font-size: 0.875rem;
            color: #6c757d;
        }

        /* 控制面板 */
        .control-panel {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
            border: 0.0625rem solid #ddd;
            z-index: 1000;
        }

        .control-panel h3 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .control-panel input {
            width: 100%;
            padding: 0.5rem;
            border: 0.0625rem solid #ddd;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }

        .control-panel button {
            width: 100%;
            padding: 0.5rem;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
            margin-bottom: 0.25rem;
        }

        .control-panel button:hover {
            background: #0056b3;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .info-table th,
        .info-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 0.0625rem solid #ddd;
        }

        .info-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .code-example {
            background: #f8f9fa;
            border: 0.0625rem solid #e9ecef;
            border-radius: 0.25rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            margin: 1rem 0;
            overflow-x: auto;
        }

        /* 响应式调整 */
        @media screen and (max-width: 48rem) { /* 768px */
            .container {
                padding: 1rem;
            }
            
            .control-panel {
                position: static;
                margin-bottom: 2rem;
            }
            
            .layout-demo {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📏 Rem单位基础使用教程</h1>
            <p>学习如何使用rem单位创建可缩放的响应式布局</p>
        </div>

        <div class="control-panel">
            <h3>🎛️ 根字体大小控制</h3>
            <input type="range" id="fontSizeSlider" min="10" max="24" value="16" step="1">
            <div>当前: <span id="currentSize">16</span>px</div>
            <button onclick="resetFontSize()">重置为16px</button>
            <button onclick="setMobileSize()">移动端(14px)</button>
            <button onclick="setDesktopSize()">桌面端(18px)</button>
        </div>

        <div class="section">
            <h2>1. Rem单位基础概念</h2>
            <p>Rem (Root em) 是相对于根元素(html)字体大小的单位。当前根字体大小为 <strong id="rootFontSize">16px</strong>。</p>
            
            <div class="demo-box">
                <h3>字体大小演示</h3>
                <div class="size-demo size-0-5">0.5rem (8px)</div>
                <div class="size-demo size-0-75">0.75rem (12px)</div>
                <div class="size-demo size-1">1rem (16px)</div>
                <div class="size-demo size-1-25">1.25rem (20px)</div>
                <div class="size-demo size-1-5">1.5rem (24px)</div>
                <div class="size-demo size-2">2rem (32px)</div>
            </div>

            <table class="info-table">
                <thead>
                    <tr>
                        <th>Rem值</th>
                        <th>计算公式</th>
                        <th>当前像素值</th>
                        <th>常用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.5rem</td>
                        <td>0.5 × <span class="root-size">16</span>px</td>
                        <td class="calculated-px">8px</td>
                        <td>小图标、边框</td>
                    </tr>
                    <tr>
                        <td>0.75rem</td>
                        <td>0.75 × <span class="root-size">16</span>px</td>
                        <td class="calculated-px">12px</td>
                        <td>小字体、说明文字</td>
                    </tr>
                    <tr>
                        <td>1rem</td>
                        <td>1 × <span class="root-size">16</span>px</td>
                        <td class="calculated-px">16px</td>
                        <td>正文字体</td>
                    </tr>
                    <tr>
                        <td>1.5rem</td>
                        <td>1.5 × <span class="root-size">16</span>px</td>
                        <td class="calculated-px">24px</td>
                        <td>小标题</td>
                    </tr>
                    <tr>
                        <td>2rem</td>
                        <td>2 × <span class="root-size">16</span>px</td>
                        <td class="calculated-px">32px</td>
                        <td>大标题、间距</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>2. 布局中的Rem应用</h2>
            <p>使用rem单位可以让整个布局随根字体大小成比例缩放：</p>
            
            <div class="layout-demo">
                <div class="card">
                    <h4>卡片标题 1</h4>
                    <p>这是一个使用rem单位的卡片组件。所有的尺寸都会随着根字体大小的改变而缩放。</p>
                </div>
                <div class="card">
                    <h4>卡片标题 2</h4>
                    <p>尝试调整右上角的根字体大小滑块，观察整个布局的变化。</p>
                </div>
                <div class="card">
                    <h4>卡片标题 3</h4>
                    <p>这种方式特别适合创建响应式设计和可访问性友好的界面。</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>3. CSS代码示例</h2>
            
            <div class="code-example">
/* 设置根字体大小 */
html {
    font-size: 16px; /* 基准大小 */
}

/* 使用rem单位的样式 */
.container {
    max-width: 75rem;    /* 1200px */
    padding: 2rem;       /* 32px */
    margin: 0 auto;
}

.title {
    font-size: 2.5rem;   /* 40px */
    margin-bottom: 1rem; /* 16px */
}

.text {
    font-size: 1rem;     /* 16px */
    line-height: 1.5;    /* 相对行高 */
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
    html {
        font-size: 14px; /* 移动端缩小基准 */
    }
}

@media screen and (min-width: 1200px) {
    html {
        font-size: 18px; /* 大屏幕放大基准 */
    }
}
            </div>
        </div>

        <div class="section">
            <h2>4. Rem vs 其他单位对比</h2>
            <table class="info-table">
                <thead>
                    <tr>
                        <th>单位</th>
                        <th>相对基准</th>
                        <th>优点</th>
                        <th>缺点</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>px</td>
                        <td>绝对单位</td>
                        <td>精确控制</td>
                        <td>不够灵活</td>
                        <td>边框、阴影</td>
                    </tr>
                    <tr>
                        <td>em</td>
                        <td>父元素字体</td>
                        <td>相对灵活</td>
                        <td>嵌套复杂</td>
                        <td>组件内部</td>
                    </tr>
                    <tr>
                        <td>rem</td>
                        <td>根元素字体</td>
                        <td>统一基准</td>
                        <td>需要计算</td>
                        <td>整体布局</td>
                    </tr>
                    <tr>
                        <td>%</td>
                        <td>父元素尺寸</td>
                        <td>响应式好</td>
                        <td>计算复杂</td>
                        <td>宽度布局</td>
                    </tr>
                    <tr>
                        <td>vw/vh</td>
                        <td>视口尺寸</td>
                        <td>视口相关</td>
                        <td>兼容性</td>
                        <td>全屏布局</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        const currentSizeSpan = document.getElementById('currentSize');
        const rootFontSizeSpan = document.getElementById('rootFontSize');
        const rootSizeSpans = document.querySelectorAll('.root-size');
        const calculatedPxSpans = document.querySelectorAll('.calculated-px');

        function updateFontSize(size) {
            document.documentElement.style.fontSize = size + 'px';
            currentSizeSpan.textContent = size;
            rootFontSizeSpan.textContent = size + 'px';
            
            // 更新表格中的计算值
            rootSizeSpans.forEach(span => {
                span.textContent = size;
            });
            
            // 更新计算后的像素值
            const remValues = [0.5, 0.75, 1, 1.5, 2];
            calculatedPxSpans.forEach((span, index) => {
                span.textContent = (remValues[index] * size) + 'px';
            });
        }

        fontSizeSlider.addEventListener('input', function() {
            updateFontSize(this.value);
        });

        function resetFontSize() {
            fontSizeSlider.value = 16;
            updateFontSize(16);
        }

        function setMobileSize() {
            fontSizeSlider.value = 14;
            updateFontSize(14);
        }

        function setDesktopSize() {
            fontSizeSlider.value = 18;
            updateFontSize(18);
        }

        // 初始化
        updateFontSize(16);

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        setMobileSize();
                        break;
                    case '2':
                        e.preventDefault();
                        resetFontSize();
                        break;
                    case '3':
                        e.preventDefault();
                        setDesktopSize();
                        break;
                }
            }
        });

        // 显示快捷键提示
        console.log('💡 快捷键提示:');
        console.log('Ctrl/Cmd + 1: 移动端大小 (14px)');
        console.log('Ctrl/Cmd + 2: 默认大小 (16px)');
        console.log('Ctrl/Cmd + 3: 桌面端大小 (18px)');
    </script>
</body>
</html>
