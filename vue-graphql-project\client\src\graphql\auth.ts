import { gql } from '@apollo/client/core'

// 用户片段
export const USER_FRAGMENT = gql`
  fragment UserInfo on User {
    id
    username
    firstName
    lastName
  }
`


// email
//     avatar
//     role
//     status
//     createdAt
//     updatedAt

// 认证相关查询和变更
export const LOGIN_MUTATION = gql`
  ${USER_FRAGMENT}
  mutation Login($input: LoginInput!) {
    login(input: $input) {
      token
      user {
        ...UserInfo
      }
    }
  }
`

export const REGISTER_MUTATION = gql`
  ${USER_FRAGMENT}
  mutation Register($input: CreateUserInput!) {
    register(input: $input) {
      token
      user {
        ...UserInfo
      }
    }
  }
`
// 
export const ME_QUERY = gql`
  ${USER_FRAGMENT}
  query Me {
    me {
      ...UserInfo
      tasks {   
        id
        title
        status
        priority
        dueDate
        project {
          id
          name
        }
      }
      assignedTasks {
        id
        title
        status
        priority
        dueDate
        project {
          id
          name
        }
      }
      projects {
        id
        name
        status
        priority
      }
    }
  }
`


export const GET_USER_QUERY = gql`
    ${USER_FRAGMENT}
    query GetUser($userId: ID!) {
        user(id: $userId) {
            ...UserInfo
            projects {
                id
                name
                status
                priority
            }
        }
    }
`

export const UPDATE_USER_MUTATION = gql`
  ${USER_FRAGMENT}
  mutation UpdateUser($id: ID!, $input: UpdateUserInput!) {
    updateUser(id: $id, input: $input) {
      ...UserInfo
    }
  }
`
