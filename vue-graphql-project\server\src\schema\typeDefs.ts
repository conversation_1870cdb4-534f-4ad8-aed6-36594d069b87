import { gql } from 'apollo-server-express';

export const typeDefs = gql`
  scalar DateTime

  # 用户类型
  type User {
    id: ID!
    username: String!
    email: String!
    firstName: String!
    lastName: String!
    avatar: String
    role: UserRole!
    status: UserStatus!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # 关联数据
    tasks: [Task!]!
    assignedTasks: [Task!]!
    projects: [Project!]!
    comments: [Comment!]!
  }

  # 项目类型
  type Project {
    id: ID!
    name: String!
    description: String
    status: ProjectStatus!
    priority: Priority!
    startDate: DateTime
    endDate: DateTime
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # 关联数据
    owner: User!
    members: [User!]!
    tasks: [Task!]!
  }

  # 任务类型
  type Task {
    id: ID!
    title: String!
    description: String
    status: TaskStatus!
    priority: Priority!
    dueDate: DateTime
    estimatedHours: Float
    actualHours: Float
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # 关联数据
    project: Project!
    creator: User!
    assignee: User
    comments: [Comment!]!
    tags: [String!]!
  }

  # 评论类型
  type Comment {
    id: ID!
    content: String!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # 关联数据
    author: User!
    task: Task!
  }

  # 枚举类型
  enum UserRole {
    ADMIN
    MANAGER
    DEVELOPER
    DESIGNER
    TESTER
  }

  enum UserStatus {
    ACTIVE
    INACTIVE
    SUSPENDED
  }

  enum ProjectStatus {
    PLANNING
    ACTIVE
    ON_HOLD
    COMPLETED
    CANCELLED
  }

  enum TaskStatus {
    TODO
    IN_PROGRESS
    IN_REVIEW
    TESTING
    DONE
    CANCELLED
  }

  enum Priority {
    LOW
    MEDIUM
    HIGH
    URGENT
  }

  # 输入类型
  input CreateUserInput {
    username: String!
    email: String!
    password: String!
    firstName: String!
    lastName: String!
    role: UserRole = DEVELOPER
  }

  input UpdateUserInput {
    username: String
    email: String
    firstName: String
    lastName: String
    avatar: String
    role: UserRole
    status: UserStatus
  }

  input CreateProjectInput {
    name: String!
    description: String
    priority: Priority = MEDIUM
    startDate: DateTime
    endDate: DateTime
    memberIds: [ID!]
  }

  input UpdateProjectInput {
    name: String
    description: String
    status: ProjectStatus
    priority: Priority
    startDate: DateTime
    endDate: DateTime
    memberIds: [ID!]
  }

  input CreateTaskInput {
    title: String!
    description: String
    projectId: ID!
    assigneeId: ID
    priority: Priority = MEDIUM
    dueDate: DateTime
    estimatedHours: Float
    tags: [String!]
  }

  input UpdateTaskInput {
    title: String
    description: String
    status: TaskStatus
    priority: Priority
    assigneeId: ID
    dueDate: DateTime
    estimatedHours: Float
    actualHours: Float
    tags: [String!]
  }

  input CreateCommentInput {
    content: String!
    taskId: ID!
  }

  # 过滤器输入
  input TaskFilter {
    status: TaskStatus
    priority: Priority
    assigneeId: ID
    projectId: ID
    search: String
  }

  input ProjectFilter {
    status: ProjectStatus
    priority: Priority
    ownerId: ID
    search: String
  }

  # 分页输入
  input PaginationInput {
    page: Int = 1
    limit: Int = 10
  }

  # 分页结果类型
  type PaginationInfo {
    page: Int!
    limit: Int!
    total: Int!
    pages: Int!
    hasNext: Boolean!
    hasPrev: Boolean!
  }

  type TaskConnection {
    tasks: [Task!]!
    pagination: PaginationInfo!
  }

  type ProjectConnection {
    projects: [Project!]!
    pagination: PaginationInfo!
  }

  type UserConnection {
    users: [User!]!
    pagination: PaginationInfo!
  }

  # 统计类型
  type TaskStats {
    total: Int!
    todo: Int!
    inProgress: Int!
    inReview: Int!
    testing: Int!
    done: Int!
    cancelled: Int!
  }

  type ProjectStats {
    total: Int!
    planning: Int!
    active: Int!
    onHold: Int!
    completed: Int!
    cancelled: Int!
  }

  type DashboardStats {
    taskStats: TaskStats!
    projectStats: ProjectStats!
    userCount: Int!
    recentTasks: [Task!]!
    recentProjects: [Project!]!
  }

  # 认证类型
  type AuthPayload {
    token: String!
    user: User!
  }

  input LoginInput {
    email: String!
    password: String!
  }

  # 查询
  type Query {
    # 认证
    me: User
    
    # 用户查询
    users(pagination: PaginationInput, search: String): UserConnection!
    user(id: ID!): User
    
    # 项目查询
    projects(filter: ProjectFilter, pagination: PaginationInput): ProjectConnection!
    project(id: ID!): Project
    myProjects: [Project!]!
    
    # 任务查询
    tasks(filter: TaskFilter, pagination: PaginationInput): TaskConnection!
    task(id: ID!): Task
    myTasks: [Task!]!
    
    # 统计查询
    dashboardStats: DashboardStats!
    taskStats(projectId: ID): TaskStats!
    projectStats: ProjectStats!
  }

  # 变更
  type Mutation {
    # 认证
    register(input: CreateUserInput!): AuthPayload!
    login(input: LoginInput!): AuthPayload!
    
    # 用户管理
    createUser(input: CreateUserInput!): User!
    updateUser(id: ID!, input: UpdateUserInput!): User!
    deleteUser(id: ID!): Boolean!
    
    # 项目管理
    createProject(input: CreateProjectInput!): Project!
    updateProject(id: ID!, input: UpdateProjectInput!): Project!
    deleteProject(id: ID!): Boolean!
    addProjectMember(projectId: ID!, userId: ID!): Project!
    removeProjectMember(projectId: ID!, userId: ID!): Project!
    
    # 任务管理
    createTask(input: CreateTaskInput!): Task!
    updateTask(id: ID!, input: UpdateTaskInput!): Task!
    deleteTask(id: ID!): Boolean!
    
    # 评论管理
    createComment(input: CreateCommentInput!): Comment!
    deleteComment(id: ID!): Boolean!
  }

  # 订阅
  type Subscription {
    # 任务订阅
    taskCreated: Task!
    taskUpdated: Task!
    taskDeleted: ID!
    
    # 项目订阅
    projectCreated: Project!
    projectUpdated: Project!
    
    # 评论订阅
    commentAdded(taskId: ID!): Comment!
    
    # 用户状态订阅
    userStatusChanged: User!
  }
`;
