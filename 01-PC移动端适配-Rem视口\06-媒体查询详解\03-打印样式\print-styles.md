# 打印样式优化指南

## 概述

打印样式是Web开发中经常被忽视但非常重要的一环。良好的打印样式可以让用户获得清晰、易读的打印文档，提升用户体验。

## 基础语法

```css
/* 打印专用样式 */
@media print {
    /* 打印时应用的CSS规则 */
}

/* 屏幕和打印通用样式 */
@media screen, print {
    /* 通用样式 */
}

/* 排除打印的样式 */
@media screen {
    /* 仅屏幕显示的样式 */
}
```

## 核心原则

### 1. 隐藏不必要元素

```css
@media print {
    /* 隐藏导航、侧边栏、广告等 */
    .navbar,
    .sidebar,
    .advertisement,
    .social-share,
    .comments-section,
    .back-to-top {
        display: none !important;
    }
    
    /* 隐藏交互元素 */
    button,
    input[type="submit"],
    input[type="button"],
    .btn {
        display: none !important;
    }
}
```

### 2. 优化颜色和背景

```css
@media print {
    /* 使用黑白色调，节省墨水 */
    * {
        color: black !important;
        background: white !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    /* 保留重要的背景色（如表格斑马纹） */
    .table-striped tr:nth-child(even) {
        background: #f9f9f9 !important;
    }
    
    /* 边框优化 */
    table, th, td {
        border: 1px solid black !important;
    }
}
```

### 3. 字体和排版优化

```css
@media print {
    /* 使用适合打印的字体 */
    body {
        font-family: "Times New Roman", serif !important;
        font-size: 12pt !important;
        line-height: 1.5 !important;
        color: black !important;
    }
    
    /* 标题字体 */
    h1, h2, h3, h4, h5, h6 {
        font-family: Arial, sans-serif !important;
        color: black !important;
        page-break-after: avoid;
    }
    
    h1 { font-size: 18pt !important; }
    h2 { font-size: 16pt !important; }
    h3 { font-size: 14pt !important; }
    h4 { font-size: 12pt !important; }
    
    /* 段落间距 */
    p {
        margin: 0.5em 0 !important;
        orphans: 3;
        widows: 3;
    }
}
```

## 分页控制

### page-break 属性

```css
@media print {
    /* 强制分页 */
    .page-break {
        page-break-before: always;
    }
    
    /* 避免分页 */
    .keep-together {
        page-break-inside: avoid;
    }
    
    /* 标题后避免分页 */
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    /* 表格行避免分页 */
    tr {
        page-break-inside: avoid;
    }
    
    /* 图片避免分页 */
    img {
        page-break-inside: avoid;
        page-break-after: avoid;
    }
}
```

### 现代分页控制

```css
@media print {
    /* 使用现代 break 属性 */
    .page-break {
        break-before: page;
    }
    
    .avoid-break {
        break-inside: avoid;
    }
    
    .column-break {
        break-before: column;
    }
}
```

## 页面设置

### @page 规则

```css
@media print {
    @page {
        /* 页面边距 */
        margin: 2cm;
        
        /* 页面大小 */
        size: A4;
        
        /* 页面方向 */
        /* size: A4 landscape; */
    }
    
    /* 首页特殊设置 */
    @page :first {
        margin-top: 3cm;
    }
    
    /* 左右页不同设置 */
    @page :left {
        margin-left: 3cm;
        margin-right: 2cm;
    }
    
    @page :right {
        margin-left: 2cm;
        margin-right: 3cm;
    }
}
```

### 页眉页脚

```css
@media print {
    @page {
        margin: 2cm;
        
        /* 页眉 */
        @top-center {
            content: "文档标题";
            font-size: 10pt;
        }
        
        /* 页脚 */
        @bottom-right {
            content: "第 " counter(page) " 页，共 " counter(pages) " 页";
            font-size: 10pt;
        }
        
        @bottom-left {
            content: "© 2024 公司名称";
            font-size: 10pt;
        }
    }
}
```

## 实用技巧

### 1. 显示链接地址

```css
@media print {
    /* 在链接后显示URL */
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
    
    /* 排除内部链接 */
    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: "";
    }
    
    /* 邮箱链接 */
    a[href^="mailto:"]:after {
        content: " (" attr(href) ")";
    }
}
```

### 2. 表格优化

```css
@media print {
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 10pt !important;
    }
    
    th, td {
        padding: 4pt 6pt !important;
        border: 1px solid black !important;
        text-align: left !important;
    }
    
    th {
        background: #f0f0f0 !important;
        font-weight: bold !important;
    }
    
    /* 表格标题重复 */
    thead {
        display: table-header-group;
    }
    
    tfoot {
        display: table-footer-group;
    }
}
```

### 3. 图片处理

```css
@media print {
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
    }
    
    /* 装饰性图片隐藏 */
    .decorative-img,
    .background-img {
        display: none !important;
    }
    
    /* 重要图片保留 */
    .content-img {
        display: block !important;
        margin: 0.5em 0 !important;
    }
}
```

## 完整示例

```css
/* 屏幕样式 */
.print-only { display: none; }
.no-print { display: block; }

/* 打印样式 */
@media print {
    /* 页面设置 */
    @page {
        margin: 2cm;
        size: A4;
    }
    
    /* 基础重置 */
    * {
        color: black !important;
        background: white !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    /* 字体设置 */
    body {
        font-family: "Times New Roman", serif !important;
        font-size: 12pt !important;
        line-height: 1.5 !important;
    }
    
    /* 显示/隐藏元素 */
    .print-only { display: block !important; }
    .no-print { display: none !important; }
    
    /* 隐藏交互元素 */
    .navbar, .sidebar, .footer,
    button, input, .btn,
    .advertisement, .social-share {
        display: none !important;
    }
    
    /* 标题样式 */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        font-family: Arial, sans-serif !important;
    }
    
    /* 段落设置 */
    p {
        orphans: 3;
        widows: 3;
    }
    
    /* 链接处理 */
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
    
    a[href^="#"]:after {
        content: "";
    }
    
    /* 表格优化 */
    table {
        border-collapse: collapse !important;
        width: 100% !important;
    }
    
    th, td {
        border: 1px solid black !important;
        padding: 4pt 6pt !important;
    }
    
    /* 分页控制 */
    .page-break { page-break-before: always; }
    .avoid-break { page-break-inside: avoid; }
    
    /* 图片处理 */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
    }
}
```

## 测试和调试

1. **浏览器打印预览**：使用 Ctrl+P 查看效果
2. **开发者工具**：Chrome DevTools 可以模拟打印媒体
3. **真实打印测试**：最终要在实际打印机上测试
4. **PDF 生成**：测试保存为PDF的效果

## 最佳实践

1. **内容优先**：确保核心内容清晰可读
2. **简化设计**：去除装饰性元素，专注内容
3. **节省资源**：使用黑白色调，减少图片
4. **分页合理**：避免重要内容被分割
5. **测试充分**：在不同浏览器和打印机上测试
