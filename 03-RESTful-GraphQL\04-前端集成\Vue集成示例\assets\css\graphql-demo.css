/* Vue.js + GraphQL 集成示例样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px;
    background: linear-gradient(135deg, #e91e63 0%, #9c27b0 100%);
    color: white;
    border-radius: 10px;
}

.layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.query-editor {
    width: 100%;
    height: 300px;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: #1e1e1e;
    color: #d4d4d4;
    resize: vertical;
}

.variables-editor {
    width: 100%;
    height: 120px;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: #1e1e1e;
    color: #d4d4d4;
    margin-top: 15px;
}

.result-display {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 20px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    max-height: 400px;
    overflow-y: auto;
    min-height: 200px;
}

.btn {
    background: #e91e63;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin: 10px 5px;
    transition: background 0.3s;
}

.btn:hover {
    background: #c2185b;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.tabs {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 20px;
}

.tab {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    background: none;
    border: none;
    font-size: 14px;
}

.tab.active {
    color: #e91e63;
    border-bottom-color: #e91e63;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.example-queries {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
}

.example-item {
    margin: 10px 0;
    padding: 10px;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #e91e63;
}

.example-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.example-code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 3px;
    margin: 5px 0;
}

.try-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

.try-btn:hover {
    background: #218838;
}

.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #e91e63;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    color: #e74c3c;
    background: #fdf2f2;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #e74c3c;
    margin: 15px 0;
}

.success {
    color: #27ae60;
    background: #f0f9f4;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #27ae60;
    margin: 15px 0;
}

.schema-display {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 20px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-line;
}

.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
}

.stat-number {
    font-size: 1.5em;
    font-weight: bold;
    color: #e91e63;
}

.stat-label {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .layout {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 10px;
    }
    
    .stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .query-editor {
        height: 200px;
    }
    
    .variables-editor {
        height: 100px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 1.5em;
    }
    
    .section {
        padding: 15px;
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
}
