# GraphQL 常见问题解答 (FAQ)

## 1. 基础概念问题

### Q1: GraphQL和REST的主要区别是什么？
**A:** 主要区别包括：

| 方面 | REST | GraphQL |
|------|------|---------|
| **端点** | 多个端点 | 单一端点 |
| **数据获取** | 固定数据结构 | 按需获取 |
| **请求次数** | 可能需要多次请求 | 单次请求获取所有数据 |
| **缓存** | HTTP缓存简单 | 缓存较复杂 |
| **学习曲线** | 相对简单 | 较陡峭 |

### Q2: 什么时候应该选择GraphQL而不是REST？
**A:** 考虑使用GraphQL的场景：
- 移动应用（需要减少网络请求）
- 前端需要灵活的数据获取
- 有多个客户端（Web、移动、桌面）
- 数据关系复杂
- 团队有足够的技术能力

### Q3: GraphQL的主要优势是什么？
**A:** 
- **精确数据获取**：只获取需要的字段
- **强类型系统**：编译时错误检查
- **单一端点**：简化API管理
- **实时订阅**：内置实时功能
- **自文档化**：Schema即文档

## 2. Schema设计问题

### Q4: 如何设计一个好的GraphQL Schema？
**A:** 最佳实践：
```graphql
# ✅ 好的设计
type User {
  id: ID!                    # 使用非空类型
  name: String!              # 描述性字段名
  email: String!
  posts: [Post!]!            # 明确关系
  createdAt: DateTime!       # 使用自定义标量
}

# ❌ 避免的设计
type User {
  user_id: String            # 避免下划线命名
  data: String               # 避免模糊的字段名
  info: JSON                 # 避免过于通用的类型
}
```

### Q5: 什么时候使用Interface vs Union？
**A:** 
- **Interface**：当类型有共同字段时
```graphql
interface Node {
  id: ID!
  createdAt: DateTime!
}

type User implements Node {
  id: ID!
  createdAt: DateTime!
  name: String!
}
```

- **Union**：当类型完全不同时
```graphql
union SearchResult = User | Post | Comment
```

### Q6: 如何处理分页？
**A:** 推荐使用Relay规范的基于游标的分页：
```graphql
type Query {
  posts(first: Int, after: String): PostConnection!
}

type PostConnection {
  edges: [PostEdge!]!
  pageInfo: PageInfo!
}

type PostEdge {
  node: Post!
  cursor: String!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}
```

## 3. 性能问题

### Q7: 什么是N+1查询问题？如何解决？
**A:** N+1问题是指查询N个对象时，需要执行1+N次数据库查询。

**问题示例：**
```javascript
// ❌ N+1问题
const resolvers = {
  Query: {
    users: () => User.findAll() // 1次查询
  },
  User: {
    posts: (user) => Post.findByUserId(user.id) // N次查询
  }
};
```

**解决方案：使用DataLoader**
```javascript
// ✅ 使用DataLoader
import DataLoader from 'dataloader';

const postLoader = new DataLoader(async (userIds) => {
  const posts = await Post.findByUserIds(userIds);
  return userIds.map(id => posts.filter(post => post.userId === id));
});

const resolvers = {
  User: {
    posts: (user) => postLoader.load(user.id)
  }
};
```

### Q8: 如何防止恶意的复杂查询？
**A:** 多种防护措施：

1. **查询深度限制**
```javascript
import depthLimit from 'graphql-depth-limit';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  validationRules: [depthLimit(10)]
});
```

2. **查询复杂度分析**
```javascript
import { costAnalysis } from 'graphql-cost-analysis';

const server = new ApolloServer({
  plugins: [
    costAnalysis({
      maximumCost: 1000,
      defaultCost: 1
    })
  ]
});
```

3. **查询白名单**
```javascript
const allowedQueries = ['GetUser', 'GetPosts'];

const server = new ApolloServer({
  validationRules: [
    require('graphql-query-complexity').createComplexityLimitRule(1000)
  ]
});
```

### Q9: GraphQL如何实现缓存？
**A:** GraphQL缓存比REST复杂，但有几种策略：

1. **查询级缓存**
```javascript
const server = new ApolloServer({
  cache: new RedisCache(),
  cacheControl: {
    defaultMaxAge: 300
  }
});
```

2. **字段级缓存**
```graphql
type Query {
  user(id: ID!): User @cacheControl(maxAge: 600)
  posts: [Post!]! @cacheControl(maxAge: 300)
}
```

3. **客户端缓存**
```javascript
// Apollo Client自动缓存
const client = new ApolloClient({
  cache: new InMemoryCache({
    typePolicies: {
      Product: {
        fields: {
          reviews: {
            merge(existing, incoming) {
              return [...existing, ...incoming];
            }
          }
        }
      }
    }
  })
});
```

## 4. 安全问题

### Q10: 如何在GraphQL中实现认证和授权？
**A:** 

1. **认证（Authentication）**
```javascript
const server = new ApolloServer({
  context: ({ req }) => {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const user = verifyToken(token);
    return { user };
  }
});
```

2. **授权（Authorization）**
```javascript
// Schema级授权
const typeDefs = gql`
  type Query {
    adminUsers: [User!]! @auth(requires: ADMIN)
    profile: User @auth
  }
`;

// Resolver级授权
const resolvers = {
  Query: {
    users: (parent, args, { user }) => {
      if (!user || user.role !== 'ADMIN') {
        throw new ForbiddenError('需要管理员权限');
      }
      return User.findAll();
    }
  }
};
```

### Q11: 如何防止GraphQL的安全漏洞？
**A:** 

1. **输入验证**
```javascript
import Joi from 'joi';

const createUserSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required()
});

const resolvers = {
  Mutation: {
    createUser: async (parent, { input }) => {
      const { error } = createUserSchema.validate(input);
      if (error) {
        throw new UserInputError('输入验证失败');
      }
      return User.create(input);
    }
  }
};
```

2. **禁用内省（生产环境）**
```javascript
const server = new ApolloServer({
  introspection: process.env.NODE_ENV !== 'production',
  playground: process.env.NODE_ENV !== 'production'
});
```

3. **限制查询复杂度**
```javascript
const server = new ApolloServer({
  validationRules: [
    depthLimit(10),
    costAnalysis({ maximumCost: 1000 })
  ]
});
```

## 5. 实现问题

### Q12: 如何处理文件上传？
**A:** GraphQL本身不支持文件上传，需要特殊处理：

1. **使用multipart/form-data**
```graphql
scalar Upload

type Mutation {
  uploadFile(file: Upload!): File!
}
```

```javascript
// 服务端处理
const resolvers = {
  Mutation: {
    uploadFile: async (parent, { file }) => {
      const { createReadStream, filename, mimetype } = await file;
      const stream = createReadStream();
      
      // 保存文件逻辑
      const path = await saveFile(stream, filename);
      
      return {
        filename,
        mimetype,
        path
      };
    }
  }
};
```

2. **客户端上传**
```javascript
const UPLOAD_FILE = gql`
  mutation UploadFile($file: Upload!) {
    uploadFile(file: $file) {
      filename
      path
    }
  }
`;

function FileUpload() {
  const [uploadFile] = useMutation(UPLOAD_FILE);
  
  const handleUpload = async (event) => {
    const file = event.target.files[0];
    await uploadFile({ variables: { file } });
  };
  
  return <input type="file" onChange={handleUpload} />;
}
```

### Q13: 如何实现实时功能？
**A:** 使用Subscriptions：

1. **服务端实现**
```javascript
import { PubSub } from 'graphql-subscriptions';

const pubsub = new PubSub();

const resolvers = {
  Subscription: {
    messageAdded: {
      subscribe: () => pubsub.asyncIterator(['MESSAGE_ADDED'])
    }
  },
  
  Mutation: {
    sendMessage: async (parent, { input }) => {
      const message = await Message.create(input);
      pubsub.publish('MESSAGE_ADDED', { messageAdded: message });
      return message;
    }
  }
};
```

2. **客户端使用**
```javascript
const MESSAGE_SUBSCRIPTION = gql`
  subscription {
    messageAdded {
      id
      content
      user {
        name
      }
    }
  }
`;

function Chat() {
  const { data } = useSubscription(MESSAGE_SUBSCRIPTION);
  
  return (
    <div>
      {data && <div>新消息: {data.messageAdded.content}</div>}
    </div>
  );
}
```

### Q14: 如何处理错误？
**A:** GraphQL有统一的错误格式：

```javascript
// 自定义错误类
class UserNotFoundError extends Error {
  constructor(userId) {
    super(`用户 ${userId} 不存在`);
    this.extensions = {
      code: 'USER_NOT_FOUND',
      userId
    };
  }
}

// 在Resolver中抛出错误
const resolvers = {
  Query: {
    user: async (parent, { id }) => {
      const user = await User.findById(id);
      if (!user) {
        throw new UserNotFoundError(id);
      }
      return user;
    }
  }
};

// 错误响应格式
{
  "data": null,
  "errors": [
    {
      "message": "用户 123 不存在",
      "extensions": {
        "code": "USER_NOT_FOUND",
        "userId": "123"
      },
      "path": ["user"]
    }
  ]
}
```

## 6. 工具和生态

### Q15: 推荐的GraphQL工具有哪些？
**A:** 

**服务端：**
- Apollo Server（功能最全）
- GraphQL Yoga（简单易用）
- Hasura（自动生成API）
- Prisma（数据库工具）

**客户端：**
- Apollo Client（功能强大）
- Relay（Facebook官方）
- urql（轻量级）
- graphql-request（最简单）

**开发工具：**
- GraphQL Playground
- GraphiQL
- Apollo Studio

### Q16: 如何选择GraphQL客户端？
**A:** 

- **Apollo Client**：功能最全，适合复杂应用
- **Relay**：性能最好，但学习曲线陡峭
- **urql**：轻量级，适合中小型项目
- **graphql-request**：最简单，适合简单需求

---

**总结：**
GraphQL是一个强大的API查询语言，但需要考虑其复杂性和学习成本。选择GraphQL还是REST应该基于项目的具体需求、团队技术能力和长期维护考虑。
