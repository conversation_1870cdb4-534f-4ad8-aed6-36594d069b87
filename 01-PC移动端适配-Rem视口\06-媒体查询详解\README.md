# 媒体查询详解

## 概述

媒体查询（Media Queries）是CSS3的重要特性，允许我们根据设备特性应用不同的样式规则。本章节重点讲解设备方向（纵向/横向）和打印样式的媒体查询应用。

## 目录结构

```
06-媒体查询详解/
├── README.md                    # 本文件
├── 01-基础语法/
│   ├── media-query-syntax.md   # 媒体查询基础语法
│   └── examples/               # 基础示例
├── 02-设备方向/
│   ├── orientation-guide.md    # 设备方向详解
│   └── examples/               # 方向适配示例
├── 03-打印样式/
│   ├── print-styles.md         # 打印样式指南
│   └── examples/               # 打印样式示例
├── 04-实战案例/
│   ├── responsive-layout.html   # 响应式布局完整案例
│   ├── print-friendly.html     # 打印友好页面
│   └── mobile-first.html       # 移动优先设计
└── 05-设备像素比/
    ├── device-pixel-ratio-guide.md  # DPR媒体查询指南
    └── examples/
        ├── dpr-demo.html            # DPR基础演示
        └── dpr-responsive-combo.html # DPR+响应式综合示例
```

## 核心知识点

### 1. 设备方向检测
- `orientation: portrait` - 纵向（高度 > 宽度）
- `orientation: landscape` - 横向（宽度 > 高度）

### 2. 打印样式优化
- `@media print` - 打印专用样式
- 隐藏不必要元素
- 优化字体和颜色
- 控制分页

### 3. 设备像素比适配
- `(-webkit-min-device-pixel-ratio: 2)` - 高分辨率屏幕检测
- `(min-resolution: 192dpi)` - 标准分辨率语法
- 图片资源优化和字体渲染优化

### 4. 常用断点设计
- 移动端：320px - 768px
- 平板：768px - 1024px
- 桌面：1024px+

## 快速开始

1. 查看基础语法：`01-基础语法/media-query-syntax.md`
2. 学习设备方向：`02-设备方向/orientation-guide.md`
3. 掌握打印样式：`03-打印样式/print-styles.md`
4. 了解设备像素比：`05-设备像素比/device-pixel-ratio-guide.md`
5. 实践完整案例：`04-实战案例/`

## 最佳实践

1. **移动优先**：从小屏幕开始设计，逐步增强
2. **渐进增强**：确保基础功能在所有设备上可用
3. **性能优化**：避免过多的媒体查询嵌套
4. **测试充分**：在真实设备上测试效果
