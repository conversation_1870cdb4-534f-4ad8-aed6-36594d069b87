/**
 * Rem计算工具库
 * 提供rem单位相关的计算和转换功能
 */

class RemCalculator {
    constructor(options = {}) {
        this.designWidth = options.designWidth || 375; // 设计稿宽度
        this.baseFontSize = options.baseFontSize || 16; // 基准字体大小
        this.maxFontSize = options.maxFontSize || 20;   // 最大字体大小
        this.minFontSize = options.minFontSize || 12;   // 最小字体大小
        
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.setRootFontSize();
        this.bindEvents();
        this.createDebugPanel();
    }

    /**
     * 设置根字体大小
     */
    setRootFontSize() {
        const deviceWidth = document.documentElement.clientWidth;
        let fontSize = (deviceWidth / this.designWidth) * this.baseFontSize;
        
        // 限制字体大小范围
        fontSize = Math.max(this.minFontSize, Math.min(fontSize, this.maxFontSize));
        
        document.documentElement.style.fontSize = fontSize + 'px';
        
        // 触发自定义事件
        this.dispatchEvent('fontSizeChanged', { fontSize, deviceWidth });
        
        return fontSize;
    }

    /**
     * px转rem
     * @param {number} px - 像素值
     * @param {number} baseFontSize - 基准字体大小（可选）
     * @returns {number} rem值
     */
    pxToRem(px, baseFontSize = null) {
        const base = baseFontSize || this.getCurrentFontSize();
        return parseFloat((px / base).toFixed(4));
    }

    /**
     * rem转px
     * @param {number} rem - rem值
     * @param {number} baseFontSize - 基准字体大小（可选）
     * @returns {number} 像素值
     */
    remToPx(rem, baseFontSize = null) {
        const base = baseFontSize || this.getCurrentFontSize();
        return parseFloat((rem * base).toFixed(2));
    }

    /**
     * 获取当前根字体大小
     * @returns {number} 当前字体大小
     */
    getCurrentFontSize() {
        return parseFloat(getComputedStyle(document.documentElement).fontSize);
    }

    /**
     * 根据设计稿尺寸计算rem值
     * @param {number} designPx - 设计稿中的像素值
     * @returns {number} 对应的rem值
     */
    designToRem(designPx) {
        return parseFloat((designPx / this.baseFontSize).toFixed(4));
    }

    /**
     * 生成响应式字体大小
     * @param {number} minSize - 最小字体大小(px)
     * @param {number} maxSize - 最大字体大小(px)
     * @param {number} minWidth - 最小视口宽度(px)
     * @param {number} maxWidth - 最大视口宽度(px)
     * @returns {string} CSS calc表达式
     */
    responsiveFontSize(minSize, maxSize, minWidth = 320, maxWidth = 1200) {
        const slope = (maxSize - minSize) / (maxWidth - minWidth);
        const yAxisIntersection = -minWidth * slope + minSize;
        
        return `clamp(${minSize}px, ${yAxisIntersection.toFixed(4)}px + ${(slope * 100).toFixed(4)}vw, ${maxSize}px)`;
    }

    /**
     * 创建媒体查询断点
     * @param {Array} breakpoints - 断点配置数组
     * @returns {string} CSS媒体查询字符串
     */
    createMediaQueries(breakpoints) {
        return breakpoints.map(bp => {
            const fontSize = (bp.width / this.designWidth) * this.baseFontSize;
            return `@media screen and (min-width: ${bp.width}px) {
                html { font-size: ${fontSize}px; }
            }`;
        }).join('\n');
    }

    /**
     * 获取设备信息
     * @returns {Object} 设备信息对象
     */
    getDeviceInfo() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            dpr: window.devicePixelRatio || 1,
            fontSize: this.getCurrentFontSize(),
            orientation: this.getOrientation(),
            userAgent: navigator.userAgent
        };
    }

    /**
     * 获取设备方向
     * @returns {string} 设备方向
     */
    getOrientation() {
        if (screen.orientation) {
            return screen.orientation.angle === 0 || screen.orientation.angle === 180 ? 'portrait' : 'landscape';
        }
        return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 窗口大小改变
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.setRootFontSize();
            }, 100);
        });

        // 设备方向改变
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.setRootFontSize();
            }, 100);
        });
    }

    /**
     * 触发自定义事件
     * @param {string} eventName - 事件名称
     * @param {Object} detail - 事件详情
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(`rem:${eventName}`, { detail });
        document.dispatchEvent(event);
    }

    /**
     * 创建调试面板
     */
    createDebugPanel() {
        if (this.debugPanel) return;

        this.debugPanel = document.createElement('div');
        this.debugPanel.id = 'rem-debug-panel';
        this.debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
            display: none;
            min-width: 200px;
        `;

        // 创建切换按钮
        this.debugToggle = document.createElement('button');
        this.debugToggle.textContent = 'REM';
        this.debugToggle.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            z-index: 10000;
        `;

        this.debugToggle.addEventListener('click', () => {
            this.toggleDebugPanel();
        });

        document.body.appendChild(this.debugPanel);
        document.body.appendChild(this.debugToggle);

        // 定期更新调试信息
        setInterval(() => {
            if (this.debugPanel.style.display !== 'none') {
                this.updateDebugPanel();
            }
        }, 1000);
    }

    /**
     * 切换调试面板显示状态
     */
    toggleDebugPanel() {
        const isVisible = this.debugPanel.style.display !== 'none';
        this.debugPanel.style.display = isVisible ? 'none' : 'block';
        
        if (!isVisible) {
            this.updateDebugPanel();
        }
    }

    /**
     * 更新调试面板内容
     */
    updateDebugPanel() {
        const info = this.getDeviceInfo();
        const remValue = this.pxToRem(100); // 100px对应的rem值
        
        this.debugPanel.innerHTML = `
            <div><strong>Rem调试信息</strong></div>
            <div>设计稿宽度: ${this.designWidth}px</div>
            <div>当前宽度: ${info.width}px</div>
            <div>根字体大小: ${info.fontSize}px</div>
            <div>设备像素比: ${info.dpr}</div>
            <div>设备方向: ${info.orientation}</div>
            <div>100px = ${remValue}rem</div>
            <div>1rem = ${this.remToPx(1)}px</div>
            <hr style="margin: 5px 0; border: 1px solid #666;">
            <div style="font-size: 10px;">
                <div>转换工具:</div>
                <input type="number" id="px-input" placeholder="px" style="width: 60px; margin: 2px;">
                <span> = </span>
                <span id="rem-output">0rem</span>
            </div>
        `;

        // 添加实时转换功能
        const pxInput = this.debugPanel.querySelector('#px-input');
        const remOutput = this.debugPanel.querySelector('#rem-output');
        
        if (pxInput && remOutput) {
            pxInput.addEventListener('input', (e) => {
                const px = parseFloat(e.target.value) || 0;
                const rem = this.pxToRem(px);
                remOutput.textContent = `${rem}rem`;
            });
        }
    }

    /**
     * 生成CSS工具类
     * @param {Array} sizes - 尺寸数组
     * @returns {string} CSS类字符串
     */
    generateUtilityClasses(sizes = [4, 8, 12, 16, 20, 24, 32, 40, 48, 64]) {
        const classes = [];
        
        sizes.forEach(size => {
            const rem = this.designToRem(size);
            classes.push(`
.m-${size} { margin: ${rem}rem; }
.mt-${size} { margin-top: ${rem}rem; }
.mr-${size} { margin-right: ${rem}rem; }
.mb-${size} { margin-bottom: ${rem}rem; }
.ml-${size} { margin-left: ${rem}rem; }
.p-${size} { padding: ${rem}rem; }
.pt-${size} { padding-top: ${rem}rem; }
.pr-${size} { padding-right: ${rem}rem; }
.pb-${size} { padding-bottom: ${rem}rem; }
.pl-${size} { padding-left: ${rem}rem; }
.w-${size} { width: ${rem}rem; }
.h-${size} { height: ${rem}rem; }
.text-${size} { font-size: ${rem}rem; }
            `);
        });
        
        return classes.join('\n');
    }

    /**
     * 应用工具类到页面
     * @param {Array} sizes - 尺寸数组
     */
    applyUtilityClasses(sizes) {
        const css = this.generateUtilityClasses(sizes);
        const style = document.createElement('style');
        style.textContent = css;
        document.head.appendChild(style);
    }

    /**
     * 销毁实例
     */
    destroy() {
        if (this.debugPanel) {
            this.debugPanel.remove();
        }
        if (this.debugToggle) {
            this.debugToggle.remove();
        }
        
        // 移除事件监听器
        window.removeEventListener('resize', this.setRootFontSize);
        window.removeEventListener('orientationchange', this.setRootFontSize);
    }
}

// 预设配置
const RemPresets = {
    // 移动端优先
    mobile: {
        designWidth: 375,
        baseFontSize: 16,
        maxFontSize: 18,
        minFontSize: 14
    },
    
    // 平板适配
    tablet: {
        designWidth: 768,
        baseFontSize: 16,
        maxFontSize: 20,
        minFontSize: 14
    },
    
    // 桌面端
    desktop: {
        designWidth: 1200,
        baseFontSize: 16,
        maxFontSize: 24,
        minFontSize: 16
    }
};

// 工具函数
const RemUtils = {
    /**
     * 快速创建rem计算器
     * @param {string} preset - 预设名称
     * @param {Object} options - 额外选项
     * @returns {RemCalculator} 计算器实例
     */
    create(preset = 'mobile', options = {}) {
        const config = { ...RemPresets[preset], ...options };
        return new RemCalculator(config);
    },

    /**
     * 简单的px转rem函数
     * @param {number} px - 像素值
     * @param {number} baseFontSize - 基准字体大小
     * @returns {string} rem值字符串
     */
    px2rem(px, baseFontSize = 16) {
        return (px / baseFontSize).toFixed(4) + 'rem';
    },

    /**
     * 简单的rem转px函数
     * @param {number} rem - rem值
     * @param {number} baseFontSize - 基准字体大小
     * @returns {string} 像素值字符串
     */
    rem2px(rem, baseFontSize = 16) {
        return (rem * baseFontSize).toFixed(2) + 'px';
    },

    /**
     * 生成响应式字体大小
     * @param {number} min - 最小字体大小
     * @param {number} max - 最大字体大小
     * @returns {string} CSS clamp表达式
     */
    fluidFont(min, max) {
        return `clamp(${min}px, ${min}px + ${max - min} * ((100vw - 320px) / (1200 - 320)), ${max}px)`;
    }
};

// 如果在浏览器环境中，自动创建全局实例
if (typeof window !== 'undefined') {
    // 创建全局实例
    window.remCalculator = RemUtils.create('mobile');
    
    // 添加到全局对象
    window.RemCalculator = RemCalculator;
    window.RemUtils = RemUtils;
    window.RemPresets = RemPresets;
    
    console.log('📏 Rem计算工具已加载');
    console.log('💡 使用方法:');
    console.log('- window.remCalculator: 默认计算器实例');
    console.log('- RemUtils.px2rem(16): 快速转换px到rem');
    console.log('- 点击右上角"REM"按钮查看调试信息');
}

// 导出模块（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        RemCalculator,
        RemUtils,
        RemPresets
    };
}
