<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js + GraphQL 集成示例</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/graphql@16.8.1/index.js"></script>
    <link rel="stylesheet" href="assets/css/graphql-demo.css">
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 头部 -->
            <div class="header">
                <h1>🚀 Vue.js + GraphQL 集成示例</h1>
                <p>学习如何在Vue.js中使用GraphQL进行数据查询和操作</p>
            </div>

            <!-- 统计信息 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{{ totalQueries }}</div>
                    <div class="stat-label">查询次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ avgResponseTime }}ms</div>
                    <div class="stat-label">平均响应时间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ successRate }}%</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ dataSize }}KB</div>
                    <div class="stat-label">数据传输量</div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="layout">
                <!-- 查询编辑器 -->
                <div class="section">
                    <h2>📝 GraphQL 查询编辑器</h2>
                    
                    <div class="tabs">
                        <button 
                            v-for="tab in tabs" 
                            :key="tab.id"
                            class="tab"
                            :class="{ active: activeTab === tab.id }"
                            @click="activeTab = tab.id"
                        >
                            {{ tab.name }}
                        </button>
                    </div>

                    <div v-for="tab in tabs" :key="tab.id" class="tab-content" :class="{ active: activeTab === tab.id }">
                        <textarea 
                            v-model="tab.query" 
                            class="query-editor" 
                            :placeholder="tab.placeholder"
                        ></textarea>
                        
                        <textarea 
                            v-model="tab.variables" 
                            class="variables-editor" 
                            placeholder="变量 (JSON格式)"
                        ></textarea>
                        
                        <button 
                            class="btn" 
                            @click="executeQuery(tab)" 
                            :disabled="loading"
                        >
                            {{ loading ? '执行中...' : '▶️ 执行查询' }}
                            <span v-if="loading" class="loading" style="margin-left: 10px;"></span>
                        </button>
                        
                        <button 
                            class="btn btn-secondary" 
                            @click="clearQuery(tab)"
                        >
                            🗑️ 清空
                        </button>
                    </div>

                    <!-- 示例查询 -->
                    <div class="example-queries">
                        <h3>📚 示例查询</h3>
                        <div v-for="example in examples" :key="example.id" class="example-item">
                            <div class="example-title">{{ example.title }}</div>
                            <div class="example-code">{{ example.query }}</div>
                            <button class="try-btn" @click="loadExample(example)">试试看</button>
                        </div>
                    </div>
                </div>

                <!-- 结果显示 -->
                <div class="section">
                    <h2>📊 查询结果</h2>
                    
                    <div v-if="error" class="error">
                        <strong>错误:</strong> {{ error }}
                    </div>
                    
                    <div v-if="success" class="success">
                        <strong>成功:</strong> {{ success }}
                    </div>
                    
                    <div class="result-display">{{ result || '点击"执行查询"按钮查看结果...' }}</div>
                </div>
            </div>

            <!-- GraphQL Schema -->
            <div class="section">
                <h2>📋 GraphQL Schema</h2>
                <div class="schema-display">{{ schema }}</div>
            </div>
        </div>
    </div>

    <!-- 引入外部JavaScript文件 -->
    <script type="module" src="assets/js/graphql-demo.js"></script>
</body>
</html>
