<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#ff6b6b">
    <title>移动商城 - Rem适配实战</title>
    <style>
        /* 动态设置根字体大小 */
        html {
            font-size: 16px; /* 默认基准 */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 安全区域适配 */
        .safe-area {
            padding-top: constant(safe-area-inset-top);
            padding-top: env(safe-area-inset-top);
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* 头部导航 */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 3rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 1rem;
            box-shadow: 0 0.125rem 0.5rem rgba(0,0,0,0.1);
        }

        .header-left,
        .header-right {
            flex: 0 0 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-center {
            flex: 1;
            text-align: center;
            font-size: 1.125rem;
            font-weight: 600;
        }

        .header-icon {
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: background 0.3s;
        }

        .header-icon:hover {
            background: rgba(255,255,255,0.2);
        }

        /* 主要内容区域 */
        .main {
            margin-top: 3rem;
            padding-bottom: 4rem;
        }

        /* 轮播图 */
        .banner {
            height: 12rem;
            position: relative;
            overflow: hidden;
        }

        .banner-slide {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        /* 快捷导航 */
        .quick-nav {
            background: white;
            padding: 1.5rem 1rem;
            margin: 0.75rem;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.5rem rgba(0,0,0,0.05);
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #333;
            padding: 0.75rem 0.5rem;
            border-radius: 0.5rem;
            transition: background 0.3s;
        }

        .nav-item:hover {
            background: #f8f9fa;
        }

        .nav-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }

        .nav-text {
            font-size: 0.75rem;
            text-align: center;
        }

        /* 商品分类 */
        .category-section {
            margin: 0.75rem;
        }

        .section-title {
            background: white;
            padding: 1rem;
            border-radius: 0.75rem 0.75rem 0 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .category-grid {
            background: white;
            padding: 1rem;
            border-radius: 0 0 0.75rem 0.75rem;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .category-item {
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
            text-decoration: none;
            color: #333;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .category-item:hover {
            transform: translateY(-0.125rem);
            box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.1);
        }

        .category-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .category-name {
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* 推荐商品 */
        .products-section {
            margin: 0.75rem;
        }

        .products-grid {
            background: white;
            padding: 1rem;
            border-radius: 0 0 0.75rem 0.75rem;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .product-card {
            background: #fff;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .product-card:hover {
            transform: translateY(-0.125rem);
            box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 8rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .product-info {
            padding: 0.75rem;
        }

        .product-name {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #333;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-price {
            color: #ff6b6b;
            font-size: 1rem;
            font-weight: 600;
        }

        .product-original-price {
            color: #999;
            font-size: 0.75rem;
            text-decoration: line-through;
            margin-left: 0.5rem;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3.5rem;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            z-index: 1000;
        }

        .bottom-nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: #666;
            font-size: 0.75rem;
            transition: color 0.3s;
        }

        .bottom-nav-item.active {
            color: #ff6b6b;
        }

        .bottom-nav-icon {
            font-size: 1.25rem;
            margin-bottom: 0.125rem;
        }

        /* 搜索框 */
        .search-section {
            padding: 1rem;
            background: white;
            margin: 0.75rem;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.5rem rgba(0,0,0,0.05);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 2.5rem;
            border: 1px solid #e9ecef;
            border-radius: 1.25rem;
            padding: 0 1rem 0 2.5rem;
            font-size: 0.875rem;
            background: #f8f9fa;
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1rem;
        }

        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .spinner {
            width: 2rem;
            height: 2rem;
            border: 0.125rem solid #f3f3f3;
            border-top: 0.125rem solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式适配 */
        @media screen and (max-width: 320px) {
            html { font-size: 14px; }
            .nav-grid { grid-template-columns: repeat(4, 1fr); }
            .products-grid { grid-template-columns: 1fr; }
        }

        @media screen and (min-width: 375px) {
            html { font-size: 16px; }
        }

        @media screen and (min-width: 414px) {
            html { font-size: 17px; }
            .products-grid { grid-template-columns: repeat(3, 1fr); }
        }

        @media screen and (min-width: 768px) {
            html { font-size: 18px; }
            .nav-grid { grid-template-columns: repeat(5, 1fr); }
            .products-grid { grid-template-columns: repeat(4, 1fr); }
        }

        /* 横屏适配 */
        @media screen and (orientation: landscape) and (max-height: 500px) {
            .banner { height: 8rem; }
            .header { height: 2.5rem; }
            .main { margin-top: 2.5rem; }
        }

        /* 1px边框解决方案 */
        .border-1px {
            position: relative;
        }

        .border-1px::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 200%;
            height: 200%;
            border: 1px solid #e9ecef;
            transform: scale(0.5);
            transform-origin: 0 0;
            box-sizing: border-box;
            pointer-events: none;
        }

        @media (-webkit-min-device-pixel-ratio: 3) {
            .border-1px::after {
                width: 300%;
                height: 300%;
                transform: scale(0.333);
            }
        }
    </style>
</head>
<body class="safe-area">
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-left">
            <span class="header-icon">📍</span>
        </div>
        <div class="header-center">移动商城</div>
        <div class="header-right">
            <span class="header-icon">🔍</span>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main">
        <!-- 轮播图 -->
        <section class="banner">
            <div class="banner-slide">
                🎉 新品上市，限时优惠
            </div>
        </section>

        <!-- 搜索框 -->
        <section class="search-section">
            <div class="search-box">
                <span class="search-icon">🔍</span>
                <input type="text" class="search-input" placeholder="搜索商品、品牌、店铺">
            </div>
        </section>

        <!-- 快捷导航 -->
        <section class="quick-nav">
            <div class="nav-grid">
                <a href="#" class="nav-item">
                    <div class="nav-icon">📱</div>
                    <span class="nav-text">手机数码</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">👕</div>
                    <span class="nav-text">服装鞋包</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span class="nav-text">家居生活</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🍎</div>
                    <span class="nav-text">生鲜食品</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">💄</div>
                    <span class="nav-text">美妆护肤</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">📚</div>
                    <span class="nav-text">图书文具</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🎮</div>
                    <span class="nav-text">运动户外</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🎁</div>
                    <span class="nav-text">更多分类</span>
                </a>
            </div>
        </section>

        <!-- 商品分类 -->
        <section class="category-section">
            <div class="section-title">热门分类</div>
            <div class="category-grid">
                <a href="#" class="category-item">
                    <span class="category-icon">📱</span>
                    <span class="category-name">智能手机</span>
                </a>
                <a href="#" class="category-item">
                    <span class="category-icon">💻</span>
                    <span class="category-name">笔记本电脑</span>
                </a>
                <a href="#" class="category-item">
                    <span class="category-icon">🎧</span>
                    <span class="category-name">耳机音响</span>
                </a>
                <a href="#" class="category-item">
                    <span class="category-icon">📷</span>
                    <span class="category-name">摄影摄像</span>
                </a>
            </div>
        </section>

        <!-- 推荐商品 -->
        <section class="products-section">
            <div class="section-title">为你推荐</div>
            <div class="products-grid" id="productsGrid">
                <!-- 商品将通过JavaScript动态加载 -->
            </div>
        </section>

        <!-- 加载更多 -->
        <div class="loading" id="loading" style="display: none;">
            <div class="spinner"></div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="#" class="bottom-nav-item active">
            <span class="bottom-nav-icon">🏠</span>
            <span>首页</span>
        </a>
        <a href="#" class="bottom-nav-item">
            <span class="bottom-nav-icon">📂</span>
            <span>分类</span>
        </a>
        <a href="#" class="bottom-nav-item">
            <span class="bottom-nav-icon">🛒</span>
            <span>购物车</span>
        </a>
        <a href="#" class="bottom-nav-item">
            <span class="bottom-nav-icon">👤</span>
            <span>我的</span>
        </a>
    </nav>

    <script>
        // 动态设置根字体大小
        function setRootFontSize() {
            const designWidth = 375; // 设计稿宽度
            const deviceWidth = document.documentElement.clientWidth;
            const fontSize = Math.min(deviceWidth / designWidth * 16, 20); // 最大20px
            document.documentElement.style.fontSize = fontSize + 'px';
        }

        // 模拟商品数据
        const products = [
            { id: 1, name: 'iPhone 14 Pro 深空黑色 128GB', price: 7999, originalPrice: 8999, icon: '📱' },
            { id: 2, name: 'MacBook Air M2 芯片 13英寸', price: 8999, originalPrice: 9999, icon: '💻' },
            { id: 3, name: 'AirPods Pro 第二代 主动降噪', price: 1899, originalPrice: 2199, icon: '🎧' },
            { id: 4, name: 'iPad Air 第五代 64GB WiFi版', price: 4399, originalPrice: 4999, icon: '📱' },
            { id: 5, name: 'Apple Watch Series 8 GPS版', price: 2999, originalPrice: 3399, icon: '⌚' },
            { id: 6, name: 'Sony WH-1000XM4 无线降噪耳机', price: 1999, originalPrice: 2399, icon: '🎧' },
            { id: 7, name: 'Nintendo Switch OLED游戏机', price: 2599, originalPrice: 2999, icon: '🎮' },
            { id: 8, name: 'Canon EOS R6 全画幅微单相机', price: 15999, originalPrice: 17999, icon: '📷' }
        ];

        // 渲染商品列表
        function renderProducts(productList) {
            const grid = document.getElementById('productsGrid');
            
            productList.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.innerHTML = `
                    <div class="product-image">${product.icon}</div>
                    <div class="product-info">
                        <div class="product-name">${product.name}</div>
                        <div class="product-price">
                            ¥${product.price}
                            <span class="product-original-price">¥${product.originalPrice}</span>
                        </div>
                    </div>
                `;
                
                // 添加点击事件
                productCard.addEventListener('click', () => {
                    showProductDetail(product);
                });
                
                grid.appendChild(productCard);
            });
        }

        // 显示商品详情
        function showProductDetail(product) {
            alert(`商品详情\n\n名称: ${product.name}\n价格: ¥${product.price}\n原价: ¥${product.originalPrice}`);
        }

        // 模拟加载更多商品
        function loadMoreProducts() {
            const loading = document.getElementById('loading');
            loading.style.display = 'flex';
            
            setTimeout(() => {
                // 模拟网络延迟
                const moreProducts = products.map(p => ({
                    ...p,
                    id: p.id + 100,
                    name: p.name + ' (更多选择)'
                }));
                
                renderProducts(moreProducts);
                loading.style.display = 'none';
            }, 1500);
        }

        // 底部导航切换
        function setupBottomNav() {
            const navItems = document.querySelectorAll('.bottom-nav-item');
            
            navItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // 移除所有active类
                    navItems.forEach(nav => nav.classList.remove('active'));
                    
                    // 添加active类到当前项
                    item.classList.add('active');
                    
                    // 这里可以添加页面切换逻辑
                    const text = item.querySelector('span:last-child').textContent;
                    console.log(`切换到: ${text}`);
                });
            });
        }

        // 搜索功能
        function setupSearch() {
            const searchInput = document.querySelector('.search-input');
            
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.toLowerCase();
                console.log(`搜索: ${query}`);
                
                // 这里可以添加搜索逻辑
                if (query.length > 2) {
                    // 模拟搜索
                    console.log('执行搜索...');
                }
            });
        }

        // 无限滚动
        function setupInfiniteScroll() {
            let loading = false;
            
            window.addEventListener('scroll', () => {
                if (loading) return;
                
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const windowHeight = window.innerHeight;
                const documentHeight = document.documentElement.scrollHeight;
                
                // 距离底部100px时加载更多
                if (scrollTop + windowHeight >= documentHeight - 100) {
                    loading = true;
                    loadMoreProducts();
                    
                    setTimeout(() => {
                        loading = false;
                    }, 2000);
                }
            });
        }

        // 处理设备方向变化
        function handleOrientationChange() {
            setTimeout(() => {
                setRootFontSize();
                console.log('屏幕方向已改变，重新计算字体大小');
            }, 100);
        }

        // 页面初始化
        function init() {
            // 设置根字体大小
            setRootFontSize();
            
            // 渲染初始商品
            renderProducts(products);
            
            // 设置底部导航
            setupBottomNav();
            
            // 设置搜索功能
            setupSearch();
            
            // 设置无限滚动
            setupInfiniteScroll();
            
            // 监听窗口大小变化
            window.addEventListener('resize', setRootFontSize);
            
            // 监听设备方向变化
            window.addEventListener('orientationchange', handleOrientationChange);
            
            console.log('📱 移动商城页面初始化完成');
            console.log('💡 提示：');
            console.log('1. 调整窗口大小观察响应式效果');
            console.log('2. 滚动到底部体验无限加载');
            console.log('3. 点击商品查看详情');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);

        // 防止iOS Safari的回弹效果
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('.main')) {
                // 允许主内容区域滚动
                return;
            }
            e.preventDefault();
        }, { passive: false });

        // 处理iOS Safari的视口高度问题
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
    </script>
</body>
</html>
