<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印样式演示</title>
    <style>
        /* 屏幕样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 2rem;
            margin: -2rem -2rem 2rem -2rem;
            border-radius: 12px 12px 0 0;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .toolbar {
            background: #f8f9fa;
            padding: 1rem;
            margin: 0 -2rem 2rem -2rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .social-share {
            margin-left: auto;
            display: flex;
            gap: 0.5rem;
        }

        .social-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .social-btn:hover {
            transform: scale(1.1);
        }

        .facebook { background: #3b5998; }
        .twitter { background: #1da1f2; }
        .linkedin { background: #0077b5; }

        .content h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5rem;
            margin: 2rem 0 1rem 0;
        }

        .content h3 {
            color: #34495e;
            margin: 1.5rem 0 1rem 0;
        }

        .highlight-box {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-left: 5px solid #2196f3;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .warning-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 5px solid #ffc107;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .data-table th,
        .data-table td {
            border: 1px solid #dee2e6;
            padding: 1rem;
            text-align: left;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .data-table tr:hover {
            background: #e9ecef;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .image-item {
            text-align: center;
        }

        .content-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .decorative-elements {
            display: flex;
            justify-content: space-around;
            margin: 2rem 0;
        }

        .decorative-circle {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .footer {
            background: #343a40;
            color: white;
            padding: 2rem;
            margin: 2rem -2rem -2rem -2rem;
            border-radius: 0 0 12px 12px;
            text-align: center;
        }

        .print-only {
            display: none;
        }

        .no-print {
            /* 这个类的元素在打印时会被隐藏 */
        }

        /* 打印样式 */
        @media print {
            /* 页面设置 */
            @page {
                margin: 2cm;
                size: A4;
                
                @top-center {
                    content: "打印样式演示文档";
                    font-size: 10pt;
                    color: #666;
                    border-bottom: 1pt solid #ddd;
                    padding-bottom: 5pt;
                }
                
                @bottom-right {
                    content: "第 " counter(page) " 页，共 " counter(pages) " 页";
                    font-size: 9pt;
                    color: #666;
                }
                
                @bottom-left {
                    content: "© 2024 打印样式演示";
                    font-size: 9pt;
                    color: #666;
                }
            }

            /* 基础重置 */
            * {
                color: black !important;
                background: white !important;
                box-shadow: none !important;
                text-shadow: none !important;
            }

            body {
                font-family: "Times New Roman", serif !important;
                font-size: 12pt !important;
                line-height: 1.5 !important;
                background: white !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            .container {
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
                border-radius: 0 !important;
            }

            /* 隐藏不需要打印的元素 */
            .toolbar,
            .social-share,
            .footer,
            .btn,
            .decorative-elements,
            .no-print {
                display: none !important;
            }

            /* 显示打印专用内容 */
            .print-only {
                display: block !important;
                background: white !important;
                border: 1pt solid black !important;
                padding: 10pt !important;
                margin: 10pt 0 !important;
            }

            /* 标题优化 */
            .header {
                background: white !important;
                color: black !important;
                padding: 0 !important;
                margin: 0 0 20pt 0 !important;
                border-radius: 0 !important;
                text-align: left !important;
                border-bottom: 2pt solid black !important;
                padding-bottom: 10pt !important;
            }

            .header h1 {
                font-size: 20pt !important;
                font-family: Arial, sans-serif !important;
                margin: 0 !important;
                text-shadow: none !important;
            }

            /* 内容标题 */
            .content h2 {
                font-size: 16pt !important;
                font-family: Arial, sans-serif !important;
                color: black !important;
                border-bottom: 1pt solid black !important;
                padding-bottom: 5pt !important;
                margin: 20pt 0 10pt 0 !important;
                page-break-after: avoid;
            }

            .content h3 {
                font-size: 14pt !important;
                font-family: Arial, sans-serif !important;
                color: black !important;
                margin: 15pt 0 8pt 0 !important;
                page-break-after: avoid;
            }

            /* 段落设置 */
            p {
                margin: 8pt 0 !important;
                orphans: 3;
                widows: 3;
                text-align: justify;
            }

            /* 高亮框优化 */
            .highlight-box,
            .warning-box {
                background: white !important;
                border: 1pt solid black !important;
                border-left: 3pt solid black !important;
                padding: 10pt !important;
                margin: 12pt 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                page-break-inside: avoid;
            }

            /* 表格优化 */
            .data-table {
                border-collapse: collapse !important;
                width: 100% !important;
                font-size: 10pt !important;
                margin: 12pt 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            }

            .data-table th,
            .data-table td {
                border: 1pt solid black !important;
                padding: 6pt 8pt !important;
                text-align: left !important;
            }

            .data-table th {
                background: #f0f0f0 !important;
                font-weight: bold !important;
                color: black !important;
            }

            .data-table tr:nth-child(even) {
                background: #f9f9f9 !important;
            }

            .data-table tr:hover {
                background: inherit !important;
            }

            /* 表格标题重复 */
            .data-table thead {
                display: table-header-group;
            }

            /* 图片处理 */
            .image-gallery {
                display: block !important;
                margin: 12pt 0 !important;
            }

            .image-item {
                margin: 8pt 0 !important;
                page-break-inside: avoid;
            }

            .content-image {
                width: 100% !important;
                height: 100pt !important;
                background: white !important;
                border: 1pt solid black !important;
                border-radius: 0 !important;
                color: black !important;
                margin-bottom: 5pt !important;
            }

            /* 链接处理 */
            a[href]:after {
                content: " (" attr(href) ")";
                font-size: 9pt;
                color: #666;
            }

            a[href^="#"]:after,
            a[href^="javascript:"]:after {
                content: "";
            }

            /* 分页控制 */
            .page-break {
                page-break-before: always;
            }

            .avoid-break {
                page-break-inside: avoid;
            }

            /* 列表优化 */
            ul, ol {
                margin: 8pt 0 !important;
                padding-left: 20pt !important;
            }

            li {
                margin: 3pt 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>打印样式演示</h1>
            <p>这个页面展示了如何优化网页的打印效果</p>
        </header>

        <div class="print-only">
            <strong>打印版本说明：</strong>本文档已针对打印进行优化，包括：
            <ul>
                <li>隐藏了导航、按钮、装饰元素等不必要的内容</li>
                <li>调整了字体为适合打印的 Times New Roman</li>
                <li>将彩色背景改为黑白，节省墨水</li>
                <li>优化了表格和图片的显示效果</li>
                <li>添加了页眉页脚信息</li>
            </ul>
        </div>

        <div class="toolbar no-print">
            <button class="btn" onclick="window.print()">🖨️ 打印页面</button>
            <button class="btn btn-secondary" onclick="window.location.reload()">🔄 刷新页面</button>
            <a href="#" class="btn btn-secondary">📧 发送邮件</a>
            <a href="#" class="btn btn-secondary">💾 保存</a>
            
            <div class="social-share">
                <a href="#" class="social-btn facebook">f</a>
                <a href="#" class="social-btn twitter">t</a>
                <a href="#" class="social-btn linkedin">in</a>
            </div>
        </div>

        <main class="content">
            <h2>打印样式的重要性</h2>
            <p>在数字化时代，虽然大多数内容都在屏幕上阅读，但打印需求依然存在。良好的打印样式可以确保用户在打印网页时获得清晰、易读且节省资源的文档。</p>

            <div class="highlight-box avoid-break">
                <strong>重要提示：</strong>这个蓝色高亮框在屏幕上有渐变背景和阴影效果，但在打印时会变为简单的黑白边框样式，既保持了信息的重要性，又节省了彩色墨水。
            </div>

            <h3>核心优化原则</h3>
            <ol>
                <li><strong>隐藏不必要元素：</strong>导航栏、按钮、广告、社交分享等交互元素在打印时没有意义</li>
                <li><strong>优化颜色方案：</strong>使用黑白色调，避免浪费彩色墨水</li>
                <li><strong>调整字体和大小：</strong>使用适合打印的字体，如 Times New Roman</li>
                <li><strong>控制分页：</strong>避免重要内容被分割到不同页面</li>
                <li><strong>显示链接地址：</strong>在打印版本中显示完整的URL</li>
            </ol>

            <div class="warning-box avoid-break">
                <strong>注意事项：</strong>这个黄色警告框同样会在打印时转换为黑白样式。所有的渐变背景、阴影效果都会被移除，确保打印效果的清晰度。
            </div>

            <h2>实际应用示例</h2>

            <h3>1. 数据表格优化</h3>
            <p>表格是打印文档中的重要元素，需要特别优化以确保在纸张上的可读性：</p>

            <table class="data-table avoid-break">
                <thead>
                    <tr>
                        <th>元素类型</th>
                        <th>屏幕显示</th>
                        <th>打印显示</th>
                        <th>优化说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>背景色</td>
                        <td>彩色渐变</td>
                        <td>黑白/灰色</td>
                        <td>节省彩色墨水，提高对比度</td>
                    </tr>
                    <tr>
                        <td>字体</td>
                        <td>系统字体</td>
                        <td>Times New Roman</td>
                        <td>更适合打印的衬线字体</td>
                    </tr>
                    <tr>
                        <td>字号</td>
                        <td>相对单位 (rem/em)</td>
                        <td>绝对单位 (pt)</td>
                        <td>确保打印尺寸的一致性</td>
                    </tr>
                    <tr>
                        <td>边框</td>
                        <td>细边框或无边框</td>
                        <td>明显的黑色边框</td>
                        <td>增强表格结构的可读性</td>
                    </tr>
                </tbody>
            </table>

            <h3>2. 图片和媒体处理</h3>
            <p>在打印版本中，我们需要区分内容图片和装饰图片：</p>

            <div class="image-gallery">
                <div class="image-item">
                    <div class="content-image">内容图片 1</div>
                    <p>重要的内容图片会保留</p>
                </div>
                <div class="image-item">
                    <div class="content-image">内容图片 2</div>
                    <p>但会去除装饰效果</p>
                </div>
                <div class="image-item">
                    <div class="content-image">内容图片 3</div>
                    <p>确保打印清晰度</p>
                </div>
            </div>

            <div class="decorative-elements no-print">
                <div class="decorative-circle">装饰1</div>
                <div class="decorative-circle">装饰2</div>
                <div class="decorative-circle">装饰3</div>
            </div>
            <p class="no-print">上面的装饰性圆圈在打印时会被完全隐藏。</p>

            <h3>3. 链接处理示例</h3>
            <p>在打印版本中，链接会显示完整的URL地址，方便读者访问。例如：</p>
            <ul>
                <li>MDN文档: <a href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/@media">CSS媒体查询</a></li>
                <li>W3C规范: <a href="https://www.w3.org/TR/css3-mediaqueries/">Media Queries Level 3</a></li>
                <li>内部链接: <a href="#top">返回顶部</a>（内部链接不显示URL）</li>
            </ul>

            <h2 class="page-break">测试打印效果</h2>
            <p>要查看这个页面的打印效果，你可以：</p>
            <ol>
                <li><strong>打印预览：</strong>按 Ctrl+P（Windows）或 Cmd+P（Mac）</li>
                <li><strong>浏览器工具：</strong>在开发者工具中切换到打印媒体模式</li>
                <li><strong>实际打印：</strong>打印到纸张或保存为PDF文件</li>
            </ol>

            <div class="highlight-box avoid-break">
                <strong>观察要点：</strong>在打印预览中，你会发现所有的彩色背景都消失了，装饰元素被隐藏，字体变为适合打印的样式，整个布局变得更加简洁和专业。
            </div>

            <h3>最佳实践总结</h3>
            <ul>
                <li>使用 <code>@media print</code> 定义打印专用样式</li>
                <li>通过 <code>display: none !important</code> 隐藏不必要元素</li>
                <li>使用 <code>page-break-*</code> 属性控制分页</li>
                <li>为重要内容添加 <code>page-break-inside: avoid</code></li>
                <li>使用绝对单位（pt）而非相对单位（px, em）</li>
                <li>在链接后显示URL地址</li>
                <li>优化表格的边框和间距</li>
                <li>测试实际打印效果</li>
            </ul>
        </main>

        <footer class="footer no-print">
            <p>&copy; 2024 打印样式演示. 这个页脚在打印时会被隐藏.</p>
            <p>在屏幕上，这里有彩色背景和装饰效果。</p>
        </footer>
    </div>

    <script>
        // 打印功能
        function printPage() {
            window.print();
        }

        // 监听打印事件
        window.addEventListener('beforeprint', function() {
            console.log('准备打印...');
            // 可以在这里添加打印前的处理逻辑
        });

        window.addEventListener('afterprint', function() {
            console.log('打印完成或取消');
            // 可以在这里添加打印后的处理逻辑
        });

        // 检测是否支持打印样式
        if (window.matchMedia) {
            const printQuery = window.matchMedia('print');
            printQuery.addListener(function(mq) {
                if (mq.matches) {
                    console.log('切换到打印模式');
                } else {
                    console.log('切换到屏幕模式');
                }
            });
        }

        // 提供快捷键打印
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                // 浏览器会自动处理 Ctrl+P，这里只是示例
                console.log('检测到打印快捷键');
            }
        });
    </script>
</body>
</html>
