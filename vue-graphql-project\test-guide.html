<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue GraphQL 项目测试指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .url-list {
            list-style: none;
        }
        
        .url-list li {
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .url-list a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .url-list a:hover {
            text-decoration: underline;
        }
        
        .account-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .account-table th,
        .account-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .account-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        .account-table tr:hover {
            background: #f8f9fa;
        }
        
        .copy-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-btn:hover {
            background: #5a6fd8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .checklist {
            list-style: none;
        }
        
        .checklist li {
            margin-bottom: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .checklist li:hover {
            background: #e9ecef;
        }
        
        .checklist input[type="checkbox"] {
            margin-right: 10px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .account-table {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Vue GraphQL 项目测试指南</h1>
            <p>现代化的项目管理系统 - 快速测试和体验</p>
        </div>

        <div class="grid">
            <!-- 访问地址 -->
            <div class="card">
                <h3>🌐 访问地址</h3>
                <ul class="url-list">
                    <li>
                        <span><span class="status-indicator" id="frontend-status"></span>前端应用</span>
                        <a href="http://localhost:3000" target="_blank">localhost:3000</a>
                    </li>
                    <li>
                        <span><span class="status-indicator" id="graphql-status"></span>GraphQL API</span>
                        <a href="http://localhost:4000/graphql" target="_blank">localhost:4000/graphql</a>
                    </li>
                    <li>
                        <span><span class="status-indicator" id="api-status"></span>API信息</span>
                        <a href="http://localhost:4000/api/info" target="_blank">localhost:4000/api/info</a>
                    </li>
                    <li>
                        <span><span class="status-indicator" id="health-status"></span>健康检查</span>
                        <a href="http://localhost:4000/health" target="_blank">localhost:4000/health</a>
                    </li>
                </ul>
            </div>

            <!-- 启动命令 -->
            <div class="card">
                <h3>🚀 启动项目</h3>
                <p><strong>Windows:</strong></p>
                <div class="code-block">start.bat</div>
                
                <p><strong>Linux/macOS:</strong></p>
                <div class="code-block">chmod +x start.sh && ./start.sh</div>
                
                <p><strong>手动启动:</strong></p>
                <div class="code-block">npm run install:all && npm run dev</div>
            </div>
        </div>

        <!-- 演示账户 -->
        <div class="card">
            <h3>👥 演示账户</h3>
            <table class="account-table">
                <thead>
                    <tr>
                        <th>角色</th>
                        <th>邮箱</th>
                        <th>密码</th>
                        <th>权限说明</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>管理员</strong></td>
                        <td><EMAIL></td>
                        <td>admin123</td>
                        <td>最高权限，管理所有功能</td>
                        <td><button class="copy-btn" onclick="copyAccount('<EMAIL>', 'admin123')">复制</button></td>
                    </tr>
                    <tr>
                        <td><strong>项目经理</strong></td>
                        <td><EMAIL></td>
                        <td>password123</td>
                        <td>项目管理，团队协调</td>
                        <td><button class="copy-btn" onclick="copyAccount('<EMAIL>', 'password123')">复制</button></td>
                    </tr>
                    <tr>
                        <td><strong>开发者</strong></td>
                        <td><EMAIL></td>
                        <td>password123</td>
                        <td>任务开发，代码实现</td>
                        <td><button class="copy-btn" onclick="copyAccount('<EMAIL>', 'password123')">复制</button></td>
                    </tr>
                    <tr>
                        <td><strong>设计师</strong></td>
                        <td><EMAIL></td>
                        <td>password123</td>
                        <td>UI设计，用户体验</td>
                        <td><button class="copy-btn" onclick="copyAccount('<EMAIL>', 'password123')">复制</button></td>
                    </tr>
                    <tr>
                        <td><strong>测试员</strong></td>
                        <td><EMAIL></td>
                        <td>password123</td>
                        <td>质量测试，问题反馈</td>
                        <td><button class="copy-btn" onclick="copyAccount('<EMAIL>', 'password123')">复制</button></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="grid">
            <!-- 测试清单 -->
            <div class="card">
                <h3>✅ 功能测试清单</h3>
                <ul class="checklist">
                    <li><input type="checkbox"> 访问前端应用并登录</li>
                    <li><input type="checkbox"> 查看仪表板统计数据</li>
                    <li><input type="checkbox"> 创建和管理项目</li>
                    <li><input type="checkbox"> 创建和管理任务</li>
                    <li><input type="checkbox"> 测试搜索和筛选功能</li>
                    <li><input type="checkbox"> 添加任务评论</li>
                    <li><input type="checkbox"> 测试权限控制</li>
                    <li><input type="checkbox"> 测试实时更新功能</li>
                    <li><input type="checkbox"> 测试移动端响应式</li>
                    <li><input type="checkbox"> 测试GraphQL API</li>
                </ul>
            </div>

            <!-- GraphQL示例 -->
            <div class="card">
                <h3>🔧 GraphQL 示例查询</h3>
                <p><strong>登录获取Token:</strong></p>
                <div class="code-block">mutation {
  login(input: {
    email: "<EMAIL>"
    password: "admin123"
  }) {
    token
    user { username role }
  }
}</div>

                <p><strong>获取仪表板数据:</strong></p>
                <div class="code-block">query {
  dashboardStats {
    taskStats { total done }
    projectStats { total active }
    userCount
  }
}</div>
            </div>
        </div>

        <div class="highlight">
            <strong>💡 提示:</strong> 
            <ul>
                <li>首次启动需要安装依赖，请耐心等待</li>
                <li>数据存储在内存中，重启后会重置</li>
                <li>确保端口3000和4000空闲</li>
                <li>建议使用Chrome或Firefox浏览器</li>
            </ul>
        </div>

        <div class="footer">
            <p>📚 详细文档请查看 <strong>TESTING.md</strong> | 🔗 项目地址: <a href="https://github.com/yourusername/vue-graphql-project">GitHub</a></p>
        </div>
    </div>

    <script>
        // 检查服务状态
        async function checkServiceStatus() {
            const services = [
                { id: 'frontend-status', url: 'http://localhost:3000' },
                { id: 'graphql-status', url: 'http://localhost:4000/graphql' },
                { id: 'api-status', url: 'http://localhost:4000/api/info' },
                { id: 'health-status', url: 'http://localhost:4000/health' }
            ];

            for (const service of services) {
                try {
                    const response = await fetch(service.url, { mode: 'no-cors' });
                    document.getElementById(service.id).className = 'status-indicator status-online';
                } catch (error) {
                    document.getElementById(service.id).className = 'status-indicator status-offline';
                }
            }
        }

        // 复制账户信息
        function copyAccount(email, password) {
            const text = `邮箱: ${email}\n密码: ${password}`;
            navigator.clipboard.writeText(text).then(() => {
                alert('账户信息已复制到剪贴板！');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('账户信息已复制到剪贴板！');
            });
        }

        // 页面加载完成后检查服务状态
        document.addEventListener('DOMContentLoaded', () => {
            checkServiceStatus();
            // 每30秒检查一次服务状态
            setInterval(checkServiceStatus, 30000);
        });

        // 保存测试进度
        document.querySelectorAll('.checklist input[type="checkbox"]').forEach(checkbox => {
            const key = `test_${checkbox.parentElement.textContent.trim()}`;
            checkbox.checked = localStorage.getItem(key) === 'true';
            
            checkbox.addEventListener('change', () => {
                localStorage.setItem(key, checkbox.checked);
            });
        });
    </script>
</body>
</html>
