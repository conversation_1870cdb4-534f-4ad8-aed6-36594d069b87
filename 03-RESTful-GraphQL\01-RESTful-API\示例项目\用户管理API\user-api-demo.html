<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESTful API 用户管理演示</title>
    <link rel="stylesheet" href="assets/css/user-api-demo.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 RESTful API 用户管理演示</h1>
            <p>学习和测试RESTful API的完整示例</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalRequests">0</div>
                <div class="stat-label">总请求数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRequests">0</div>
                <div class="stat-label">成功请求</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="errorRequests">0</div>
                <div class="stat-label">失败请求</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgResponseTime">0</div>
                <div class="stat-label">平均响应时间(ms)</div>
            </div>
        </div>

        <!-- 请求状态 -->
        <div class="panel">
            <h2>📡 请求状态</h2>
            <div id="requestStatus">
                <span class="status-indicator status-success"></span>
                <span class="status-text">就绪</span>
                <span class="loading" style="display: none;"></span>
            </div>
        </div>

        <!-- API测试区域 -->
        <div class="main-grid">
            <!-- 左侧：API操作 -->
            <div class="panel">
                <h2>🔧 API 操作</h2>

                <!-- GET 用户列表 -->
                <div class="api-section">
                    <h3><span class="method-tag method-get">GET</span>获取用户列表</h3>
                    <div class="endpoint">GET /api/v1/users</div>
                    <button class="btn" id="getUsersBtn">获取所有用户</button>
                </div>

                <!-- GET 单个用户 -->
                <div class="api-section">
                    <h3><span class="method-tag method-get">GET</span>获取单个用户</h3>
                    <div class="endpoint">GET /api/v1/users/{id}</div>
                    <div class="form-group">
                        <label for="getUserId">用户ID:</label>
                        <input type="number" id="getUserId" placeholder="输入用户ID" value="1">
                    </div>
                    <button class="btn" id="getUserBtn">获取用户</button>
                </div>

                <!-- POST 创建用户 -->
                <div class="api-section">
                    <h3><span class="method-tag method-post">POST</span>创建用户</h3>
                    <div class="endpoint">POST /api/v1/users</div>
                    <div class="form-group">
                        <label for="createName">姓名:</label>
                        <input type="text" id="createName" placeholder="输入姓名">
                    </div>
                    <div class="form-group">
                        <label for="createEmail">邮箱:</label>
                        <input type="email" id="createEmail" placeholder="输入邮箱">
                    </div>
                    <div class="form-group">
                        <label for="createRole">角色:</label>
                        <select id="createRole">
                            <option value="">选择角色</option>
                            <option value="admin">管理员</option>
                            <option value="user">普通用户</option>
                            <option value="moderator">版主</option>
                        </select>
                    </div>
                    <button class="btn" id="fillCreateData">填充示例数据</button>
                    <button class="btn" id="createUserBtn">创建用户</button>
                </div>

                <!-- PUT 更新用户 -->
                <div class="api-section">
                    <h3><span class="method-tag method-put">PUT</span>更新用户</h3>
                    <div class="endpoint">PUT /api/v1/users/{id}</div>
                    <div class="form-group">
                        <label for="updateUserId">用户ID:</label>
                        <input type="number" id="updateUserId" placeholder="输入用户ID">
                    </div>
                    <div class="form-group">
                        <label for="updateName">姓名:</label>
                        <input type="text" id="updateName" placeholder="输入新姓名">
                    </div>
                    <div class="form-group">
                        <label for="updateEmail">邮箱:</label>
                        <input type="email" id="updateEmail" placeholder="输入新邮箱">
                    </div>
                    <div class="form-group">
                        <label for="updateRole">角色:</label>
                        <select id="updateRole">
                            <option value="">选择角色</option>
                            <option value="admin">管理员</option>
                            <option value="user">普通用户</option>
                            <option value="moderator">版主</option>
                        </select>
                    </div>
                    <button class="btn" id="fillUpdateData">填充示例数据</button>
                    <button class="btn" id="updateUserBtn">更新用户</button>
                </div>

                <!-- DELETE 删除用户 -->
                <div class="api-section">
                    <h3><span class="method-tag method-delete">DELETE</span>删除用户</h3>
                    <div class="endpoint">DELETE /api/v1/users/{id}</div>
                    <div class="form-group">
                        <label for="deleteUserId">用户ID:</label>
                        <input type="number" id="deleteUserId" placeholder="输入用户ID">
                    </div>
                    <button class="btn" id="deleteUserBtn">删除用户</button>
                </div>
            </div>

            <!-- 右侧：响应结果 -->
            <div class="panel">
                <h2>📋 API 响应</h2>
                <div class="response-area" id="responseArea">
点击左侧按钮执行API请求，这里将显示响应结果...
                </div>
            </div>
        </div>

        <!-- API文档 -->
        <div class="api-docs">
            <h2>📚 API 文档</h2>
            <div class="doc-section">
                <h3>基础信息</h3>
                <p>基础URL: <code>https://api.example.com/v1</code></p>
                <p>认证方式: Bearer Token</p>
                <p>内容类型: <code>application/json</code></p>
            </div>

            <div class="doc-section">
                <h3>状态码说明</h3>
                <ul>
                    <li><code>200</code> - 请求成功</li>
                    <li><code>201</code> - 资源创建成功</li>
                    <li><code>400</code> - 请求参数错误</li>
                    <li><code>404</code> - 资源不存在</li>
                    <li><code>422</code> - 数据验证失败</li>
                    <li><code>500</code> - 服务器内部错误</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 引入外部JavaScript文件 -->
    <script type="module" src="assets/js/user-api-demo.js"></script>
</body>
</html>
