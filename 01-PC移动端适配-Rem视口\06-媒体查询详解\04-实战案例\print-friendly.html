<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印友好页面示例</title>
    <style>
        /* 屏幕样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            margin: -2rem -2rem 2rem -2rem;
            border-radius: 8px 8px 0 0;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5rem;
        }

        .header .subtitle {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .toolbar {
            background: #e9ecef;
            padding: 1rem;
            margin: 0 -2rem 2rem -2rem;
            border-radius: 0;
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .social-share {
            display: flex;
            gap: 0.5rem;
            margin-left: auto;
        }

        .social-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-weight: bold;
        }

        .facebook { background: #3b5998; }
        .twitter { background: #1da1f2; }
        .linkedin { background: #0077b5; }

        .content h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }

        .content h3 {
            color: #34495e;
            margin-top: 1.5rem;
        }

        .highlight-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin: 1.5rem 0;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1.5rem 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            background: white;
        }

        .data-table th,
        .data-table td {
            border: 1px solid #dee2e6;
            padding: 0.75rem;
            text-align: left;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .image-container {
            text-align: center;
            margin: 2rem 0;
        }

        .content-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .decorative-image {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: inline-block;
            margin: 0 1rem;
        }

        .footer {
            background: #343a40;
            color: white;
            padding: 2rem;
            margin: 2rem -2rem -2rem -2rem;
            border-radius: 0 0 8px 8px;
            text-align: center;
        }

        .print-info {
            display: none;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }

        .no-print {
            /* 这个类的元素在打印时会被隐藏 */
        }

        /* 打印样式 */
        @media print {
            /* 页面设置 */
            @page {
                margin: 2cm;
                size: A4;
                
                @top-center {
                    content: "打印友好页面示例";
                    font-size: 10pt;
                    color: #666;
                }
                
                @bottom-right {
                    content: "第 " counter(page) " 页";
                    font-size: 10pt;
                    color: #666;
                }
            }

            /* 基础重置 */
            * {
                color: black !important;
                background: white !important;
                box-shadow: none !important;
                text-shadow: none !important;
            }

            body {
                font-family: "Times New Roman", serif !important;
                font-size: 12pt !important;
                line-height: 1.5 !important;
                background: white !important;
                padding: 0 !important;
            }

            .container {
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
                border-radius: 0 !important;
            }

            /* 隐藏不需要打印的元素 */
            .toolbar,
            .social-share,
            .footer,
            .btn,
            .decorative-image,
            .no-print {
                display: none !important;
            }

            /* 显示打印专用信息 */
            .print-info {
                display: block !important;
                background: white !important;
                border: 1px solid black !important;
                padding: 10pt !important;
                margin: 10pt 0 !important;
            }

            /* 标题优化 */
            .header {
                background: white !important;
                color: black !important;
                padding: 0 !important;
                margin: 0 0 20pt 0 !important;
                border-radius: 0 !important;
                border-bottom: 2pt solid black !important;
            }

            .header h1 {
                font-size: 18pt !important;
                font-family: Arial, sans-serif !important;
                margin: 0 0 5pt 0 !important;
            }

            .header .subtitle {
                font-size: 12pt !important;
                color: #666 !important;
                margin: 0 !important;
            }

            /* 内容标题 */
            .content h2 {
                font-size: 14pt !important;
                font-family: Arial, sans-serif !important;
                color: black !important;
                border-bottom: 1pt solid black !important;
                padding-bottom: 3pt !important;
                margin: 15pt 0 10pt 0 !important;
                page-break-after: avoid;
            }

            .content h3 {
                font-size: 12pt !important;
                font-family: Arial, sans-serif !important;
                color: black !important;
                margin: 12pt 0 8pt 0 !important;
                page-break-after: avoid;
            }

            /* 段落设置 */
            p {
                margin: 6pt 0 !important;
                orphans: 3;
                widows: 3;
            }

            /* 高亮框优化 */
            .highlight-box,
            .warning-box {
                background: white !important;
                border: 1pt solid black !important;
                border-left: 3pt solid black !important;
                padding: 8pt !important;
                margin: 10pt 0 !important;
                page-break-inside: avoid;
            }

            /* 表格优化 */
            .data-table {
                border-collapse: collapse !important;
                width: 100% !important;
                font-size: 10pt !important;
                margin: 10pt 0 !important;
            }

            .data-table th,
            .data-table td {
                border: 1pt solid black !important;
                padding: 4pt 6pt !important;
                text-align: left !important;
            }

            .data-table th {
                background: #f0f0f0 !important;
                font-weight: bold !important;
            }

            .data-table tr:nth-child(even) {
                background: #f9f9f9 !important;
            }

            /* 表格标题重复 */
            .data-table thead {
                display: table-header-group;
            }

            /* 图片处理 */
            .content-image {
                max-width: 100% !important;
                height: auto !important;
                border: 1pt solid black !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                page-break-inside: avoid;
            }

            .image-container {
                text-align: center !important;
                margin: 10pt 0 !important;
                page-break-inside: avoid;
            }

            /* 链接处理 */
            a[href]:after {
                content: " (" attr(href) ")";
                font-size: 8pt;
                color: #666;
            }

            a[href^="#"]:after,
            a[href^="javascript:"]:after {
                content: "";
            }

            /* 分页控制 */
            .page-break {
                page-break-before: always;
            }

            .avoid-break {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>打印友好页面示例</h1>
            <p class="subtitle">演示如何创建适合打印的网页内容</p>
        </header>

        <div class="print-info">
            <strong>打印版本信息：</strong>本文档已针对打印进行优化，隐藏了不必要的装饰元素，调整了字体和布局以获得最佳打印效果。
        </div>

        <div class="toolbar no-print">
            <button class="btn" onclick="window.print()">🖨️ 打印页面</button>
            <button class="btn btn-secondary" onclick="window.location.reload()">🔄 刷新</button>
            <a href="#" class="btn btn-secondary">📧 发送邮件</a>
            
            <div class="social-share">
                <a href="#" class="social-btn facebook">f</a>
                <a href="#" class="social-btn twitter">t</a>
                <a href="#" class="social-btn linkedin">in</a>
            </div>
        </div>

        <main class="content">
            <h2>什么是打印友好设计？</h2>
            <p>打印友好设计是指针对打印输出优化的网页设计。它确保用户在打印网页时能够获得清晰、易读且节省纸张和墨水的文档。</p>

            <div class="highlight-box">
                <strong>重要提示：</strong>这个页面演示了多种打印优化技术，包括隐藏不必要元素、优化字体、调整颜色和布局等。
            </div>

            <h3>核心原则</h3>
            <ul>
                <li>隐藏导航、广告、社交分享等交互元素</li>
                <li>使用黑白色调，节省彩色墨水</li>
                <li>优化字体大小和行距，提高可读性</li>
                <li>控制分页，避免重要内容被分割</li>
                <li>显示链接的完整URL地址</li>
            </ul>

            <h2>实施技巧</h2>

            <h3>1. 媒体查询设置</h3>
            <p>使用 <code>@media print</code> 来定义打印专用样式。这些样式只在打印时生效，不会影响屏幕显示。</p>

            <div class="warning-box">
                <strong>注意：</strong>打印样式中要使用 <code>!important</code> 来确保样式优先级，覆盖屏幕样式。
            </div>

            <h3>2. 元素显示控制</h3>
            <p>通过 <code>display: none !important</code> 隐藏不需要打印的元素，如导航栏、按钮、装饰图片等。</p>

            <div class="decorative-image"></div>
            <span>这个装饰性圆圈在打印时会被隐藏</span>
            <div class="decorative-image"></div>

            <h3>3. 数据表格优化</h3>
            <p>表格是打印文档中的重要元素，需要特别优化：</p>

            <table class="data-table avoid-break">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>屏幕显示</th>
                        <th>打印显示</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>背景色</td>
                        <td>彩色渐变</td>
                        <td>白色</td>
                        <td>节省彩色墨水</td>
                    </tr>
                    <tr>
                        <td>字体</td>
                        <td>系统字体</td>
                        <td>Times New Roman</td>
                        <td>打印友好字体</td>
                    </tr>
                    <tr>
                        <td>字号</td>
                        <td>相对单位</td>
                        <td>12pt</td>
                        <td>打印标准大小</td>
                    </tr>
                    <tr>
                        <td>边框</td>
                        <td>细边框</td>
                        <td>1pt 黑色</td>
                        <td>清晰的表格结构</td>
                    </tr>
                </tbody>
            </table>

            <h3>4. 图片处理</h3>
            <p>内容相关的图片会保留并优化，装饰性图片会被隐藏：</p>

            <div class="image-container avoid-break">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzwvdGV4dD4KPC9zdmc+" 
                     alt="示例图片" 
                     class="content-image">
                <p><em>图片说明：这张图片在打印时会保留，但会去除阴影和圆角效果。</em></p>
            </div>

            <h2 class="page-break">测试打印效果</h2>
            <p>要测试这个页面的打印效果，你可以：</p>
            <ol>
                <li>按 Ctrl+P（Windows）或 Cmd+P（Mac）打开打印预览</li>
                <li>在浏览器开发者工具中模拟打印媒体</li>
                <li>实际打印到纸张或PDF文件</li>
            </ol>

            <div class="highlight-box avoid-break">
                <strong>提示：</strong>在打印预览中，你会发现导航栏、按钮、装饰元素都消失了，字体变为适合打印的样式，颜色变为黑白，布局也进行了优化。
            </div>

            <h3>链接处理示例</h3>
            <p>在打印版本中，链接会显示完整的URL地址。例如：
            <a href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/@media">MDN媒体查询文档</a> 
            和 <a href="#top">页面内链接</a>（内部链接不显示URL）。</p>

            <h2>总结</h2>
            <p>通过合理使用CSS媒体查询和打印样式，我们可以创建既适合屏幕浏览又适合打印的网页。这不仅提升了用户体验，也体现了对环境友好的设计理念。</p>
        </main>

        <footer class="footer no-print">
            <p>&copy; 2024 打印友好设计示例. 保留所有权利.</p>
            <p>这个页脚在打印时会被隐藏</p>
        </footer>
    </div>

    <script>
        // 打印按钮功能
        function printPage() {
            window.print();
        }

        // 监听打印事件
        window.addEventListener('beforeprint', function() {
            console.log('准备打印...');
        });

        window.addEventListener('afterprint', function() {
            console.log('打印完成或取消');
        });
    </script>
</body>
</html>
