<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS flex 属性详解</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .demo-section h2 {
            margin-bottom: 20px;
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .demo-section h3 {
            margin: 20px 0 15px 0;
            color: #2980b9;
        }

        /* 通用flex容器样式 */
        .flex-container {
            display: flex;
            background: #ecf0f1;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            min-height: 80px;
            gap: 10px;
        }

        .flex-item {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            transition: all 0.3s ease;
        }

        .flex-item:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        /* 不同颜色的flex项目 */
        .flex-item:nth-child(1) { background: #e74c3c; }
        .flex-item:nth-child(2) { background: #3498db; }
        .flex-item:nth-child(3) { background: #2ecc71; }
        .flex-item:nth-child(4) { background: #f39c12; }
        .flex-item:nth-child(5) { background: #9b59b6; }

        .flex-item:nth-child(1):hover { background: #c0392b; }
        .flex-item:nth-child(2):hover { background: #2980b9; }
        .flex-item:nth-child(3):hover { background: #27ae60; }
        .flex-item:nth-child(4):hover { background: #e67e22; }
        .flex-item:nth-child(5):hover { background: #8e44ad; }

        /* 代码展示 */
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }

        .code-block .comment {
            color: #95a5a6;
        }

        .code-block .property {
            color: #3498db;
        }

        .code-block .value {
            color: #2ecc71;
        }

        /* 具体演示样式 */
        
        /* 1. flex: 1 演示 */
        .demo-flex-1 .flex-item {
            flex: 1;
        }

        /* 2. flex: auto 演示 */
        .demo-flex-auto .flex-item {
            flex: auto;
        }

        /* 3. flex: none 演示 */
        .demo-flex-none .flex-item {
            flex: none;
        }

        /* 4. flex: 0 1 auto 演示 */
        .demo-flex-default .flex-item {
            flex: 0 1 auto;
        }

        /* 5. 不同比例演示 */
        .demo-different-ratios .item-1 { flex: 1; }
        .demo-different-ratios .item-2 { flex: 2; }
        .demo-different-ratios .item-3 { flex: 3; }

        /* 6. flex-basis 演示 */
        .demo-flex-basis .item-1 { flex: 1 1 100px; }
        .demo-flex-basis .item-2 { flex: 1 1 200px; }
        .demo-flex-basis .item-3 { flex: 1 1 150px; }

        /* 7. flex-shrink 演示 */
        .demo-flex-shrink {
            width: 400px; /* 固定宽度，强制收缩 */
        }
        .demo-flex-shrink .item-1 { flex: 0 1 200px; }
        .demo-flex-shrink .item-2 { flex: 0 2 200px; } /* 收缩比例更大 */
        .demo-flex-shrink .item-3 { flex: 0 0 100px; } /* 不收缩 */

        /* 8. 两个值演示 */
        .demo-two-values .item-1 { flex: 1 0; }    /* grow=1, shrink=0 */
        .demo-two-values .item-2 { flex: 2 1; }    /* grow=2, shrink=1 */
        .demo-two-values .item-3 { flex: 1 200px; } /* grow=1, basis=200px */

        /* 9. 三个值演示 */
        .demo-three-values .item-1 { flex: 1 0 100px; }
        .demo-three-values .item-2 { flex: 2 1 150px; }
        .demo-three-values .item-3 { flex: 1 2 80px; }

        /* 说明文字 */
        .description {
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }

        .highlight {
            background: #f1c40f;
            color: #2c3e50;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* 表格样式 */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #3498db;
            color: white;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        /* 交互控制 */
        .controls {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .control-group {
            margin-bottom: 10px;
        }

        .control-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }

        .control-group input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }

        .control-group span {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .demo-flex-shrink {
                width: 300px;
            }
            
            .controls {
                text-align: center;
            }
            
            .control-group label {
                display: block;
                width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CSS flex 属性详解</h1>

        <!-- 基础概念 -->
        <div class="demo-section">
            <h2>📖 基础概念</h2>
            <p><code>flex</code> 是一个简写属性，用于设置弹性项目的伸缩行为。</p>
            
            <div class="code-block">
<span class="comment">/* flex 简写语法 */</span>
.<span class="property">flex-item</span> {
    <span class="property">flex</span>: <span class="value">&lt;flex-grow&gt; &lt;flex-shrink&gt; &lt;flex-basis&gt;</span>;
}

<span class="comment">/* 等价于 */</span>
.<span class="property">flex-item</span> {
    <span class="property">flex-grow</span>: <span class="value">0</span>;     <span class="comment">/* 扩展比例 */</span>
    <span class="property">flex-shrink</span>: <span class="value">1</span>;   <span class="comment">/* 收缩比例 */</span>
    <span class="property">flex-basis</span>: <span class="value">auto</span>;  <span class="comment">/* 基础尺寸 */</span>
}
            </div>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>属性</th>
                        <th>作用</th>
                        <th>默认值</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>flex-grow</code></td>
                        <td>扩展比例</td>
                        <td>0</td>
                        <td>定义项目的放大比例，0表示不放大</td>
                    </tr>
                    <tr>
                        <td><code>flex-shrink</code></td>
                        <td>收缩比例</td>
                        <td>1</td>
                        <td>定义项目的缩小比例，0表示不缩小</td>
                    </tr>
                    <tr>
                        <td><code>flex-basis</code></td>
                        <td>基础尺寸</td>
                        <td>auto</td>
                        <td>分配多余空间之前的基础尺寸</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 常用简写值 -->
        <div class="demo-section">
            <h2>🎯 常用简写值</h2>

            <h3>💡 flex: 1 详解</h3>
            <div class="description">
                当你写 <code>flex: 1</code> 时，这个 <span class="highlight">1</span> 表示的是 <code>flex-grow: 1</code><br>
                <strong>完整等价于：</strong> <code>flex: 1 1 0%</code><br>
                <strong>含义：</strong> flex-grow=1, flex-shrink=1, flex-basis=0%
            </div>

            <div class="code-block">
<span class="comment">/* 当你写 */</span>
.<span class="property">item</span> { <span class="property">flex</span>: <span class="value">1</span>; }

<span class="comment">/* CSS 自动解析为 */</span>
.<span class="property">item</span> {
    <span class="property">flex-grow</span>: <span class="value">1</span>;    <span class="comment">/* ← 这个1就是你写的那个1 */</span>
    <span class="property">flex-shrink</span>: <span class="value">1</span>;  <span class="comment">/* 默认值 */</span>
    <span class="property">flex-basis</span>: <span class="value">0%</span>;   <span class="comment">/* 默认值 */</span>
}
            </div>

            <h3>1. flex: 1 (等分空间)</h3>
            <div class="description">
                <strong>效果：</strong> 所有项目等分容器空间，忽略内容宽度
            </div>
            <div class="flex-container demo-flex-1">
                <div class="flex-item">项目1</div>
                <div class="flex-item">项目2 (内容较长)</div>
                <div class="flex-item">项目3</div>
            </div>

            <h3>2. flex: auto (自适应)</h3>
            <div class="description">
                <strong>等价于：</strong> <code>flex: 1 1 auto</code><br>
                <strong>效果：</strong> 项目可以扩展和收缩，基于内容宽度分配空间
            </div>
            <div class="flex-container demo-flex-auto">
                <div class="flex-item">短</div>
                <div class="flex-item">中等长度内容</div>
                <div class="flex-item">很长很长的内容文字</div>
            </div>

            <h3>3. flex: none (固定尺寸)</h3>
            <div class="description">
                <strong>等价于：</strong> <code>flex: 0 0 auto</code><br>
                <strong>效果：</strong> 项目不扩展也不收缩，保持原始尺寸
            </div>
            <div class="flex-container demo-flex-none">
                <div class="flex-item">固定1</div>
                <div class="flex-item">固定2</div>
                <div class="flex-item">固定3</div>
            </div>

            <h3>4. flex: 0 1 auto (默认值)</h3>
            <div class="description">
                <strong>效果：</strong> 项目不扩展但可以收缩，基于内容宽度
            </div>
            <div class="flex-container demo-flex-default">
                <div class="flex-item">默认1</div>
                <div class="flex-item">默认2</div>
                <div class="flex-item">默认3</div>
            </div>
        </div>

        <!-- flex简写规则演示 -->
        <div class="demo-section">
            <h2>📝 flex 简写规则</h2>
            <div class="description">
                CSS会根据你写的值的类型自动判断是哪个属性
            </div>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>你写的</th>
                        <th>CSS解析为</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>flex: 1</code></td>
                        <td><code>flex: 1 1 0%</code></td>
                        <td>无单位数字 → flex-grow</td>
                    </tr>
                    <tr>
                        <td><code>flex: 2</code></td>
                        <td><code>flex: 2 1 0%</code></td>
                        <td>无单位数字 → flex-grow</td>
                    </tr>
                    <tr>
                        <td><code>flex: 100px</code></td>
                        <td><code>flex: 1 1 100px</code></td>
                        <td>有单位值 → flex-basis</td>
                    </tr>
                    <tr>
                        <td><code>flex: auto</code></td>
                        <td><code>flex: 1 1 auto</code></td>
                        <td>关键字</td>
                    </tr>
                    <tr>
                        <td><code>flex: none</code></td>
                        <td><code>flex: 0 0 auto</code></td>
                        <td>关键字</td>
                    </tr>
                </tbody>
            </table>

            <h3>⚖️ 不同扩展比例演示</h3>
            <div class="description">
                通过设置不同的数字，实际上是在设置不同的 <code>flex-grow</code> 值
            </div>

            <div class="code-block">
.<span class="property">item-1</span> { <span class="property">flex</span>: <span class="value">1</span>; } <span class="comment">/* flex-grow: 1，占 1 份 */</span>
.<span class="property">item-2</span> { <span class="property">flex</span>: <span class="value">2</span>; } <span class="comment">/* flex-grow: 2，占 2 份 */</span>
.<span class="property">item-3</span> { <span class="property">flex</span>: <span class="value">3</span>; } <span class="comment">/* flex-grow: 3，占 3 份 */</span>
            </div>

            <div class="flex-container demo-different-ratios">
                <div class="flex-item item-1">flex: 1</div>
                <div class="flex-item item-2">flex: 2</div>
                <div class="flex-item item-3">flex: 3</div>
            </div>
        </div>

        <!-- flex-basis 演示 -->
        <div class="demo-section">
            <h2>📏 flex-basis 基础尺寸</h2>
            <div class="description">
                <code>flex-basis</code> 设置项目在分配剩余空间之前的基础尺寸
            </div>
            
            <div class="code-block">
.<span class="property">item-1</span> { <span class="property">flex</span>: <span class="value">1 1 100px</span>; } <span class="comment">/* 基础宽度 100px */</span>
.<span class="property">item-2</span> { <span class="property">flex</span>: <span class="value">1 1 200px</span>; } <span class="comment">/* 基础宽度 200px */</span>
.<span class="property">item-3</span> { <span class="property">flex</span>: <span class="value">1 1 150px</span>; } <span class="comment">/* 基础宽度 150px */</span>
            </div>

            <div class="flex-container demo-flex-basis">
                <div class="flex-item item-1">100px</div>
                <div class="flex-item item-2">200px</div>
                <div class="flex-item item-3">150px</div>
            </div>
        </div>

        <!-- flex-shrink 演示 -->
        <div class="demo-section">
            <h2>🔄 flex-shrink 收缩比例</h2>
            <div class="description">
                当容器空间不足时，<code>flex-shrink</code> 决定项目的收缩比例
            </div>

            <div class="code-block">
.<span class="property">container</span> { <span class="property">width</span>: <span class="value">400px</span>; } <span class="comment">/* 容器宽度固定 */</span>
.<span class="property">item-1</span> { <span class="property">flex</span>: <span class="value">0 1 200px</span>; } <span class="comment">/* 正常收缩 */</span>
.<span class="property">item-2</span> { <span class="property">flex</span>: <span class="value">0 2 200px</span>; } <span class="comment">/* 收缩比例 x2 */</span>
.<span class="property">item-3</span> { <span class="property">flex</span>: <span class="value">0 0 100px</span>; } <span class="comment">/* 不收缩 */</span>
            </div>

            <div class="flex-container demo-flex-shrink">
                <div class="flex-item item-1">收缩1x</div>
                <div class="flex-item item-2">收缩2x</div>
                <div class="flex-item item-3">不收缩</div>
            </div>
        </div>

        <!-- 两个值演示 -->
        <div class="demo-section">
            <h2>✌️ flex 两个值的情况</h2>
            <div class="description">
                当设置两个值时，CSS会根据值的类型自动判断含义
            </div>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>写法</th>
                        <th>解析为</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>flex: 1 0</code></td>
                        <td><code>flex: 1 0 0%</code></td>
                        <td>两个无单位数字 → grow + shrink</td>
                    </tr>
                    <tr>
                        <td><code>flex: 2 1</code></td>
                        <td><code>flex: 2 1 0%</code></td>
                        <td>两个无单位数字 → grow + shrink</td>
                    </tr>
                    <tr>
                        <td><code>flex: 1 200px</code></td>
                        <td><code>flex: 1 1 200px</code></td>
                        <td>数字 + 长度值 → grow + basis</td>
                    </tr>
                </tbody>
            </table>

            <div class="code-block">
.<span class="property">item-1</span> { <span class="property">flex</span>: <span class="value">1 0</span>; }      <span class="comment">/* 扩展但不收缩 */</span>
.<span class="property">item-2</span> { <span class="property">flex</span>: <span class="value">2 1</span>; }      <span class="comment">/* 扩展2倍，正常收缩 */</span>
.<span class="property">item-3</span> { <span class="property">flex</span>: <span class="value">1 200px</span>; }  <span class="comment">/* 扩展，基础宽度200px */</span>
            </div>

            <div class="flex-container demo-two-values">
                <div class="flex-item item-1">1 0<br>(不收缩)</div>
                <div class="flex-item item-2">2 1<br>(扩展2倍)</div>
                <div class="flex-item item-3">1 200px<br>(基础200px)</div>
            </div>
        </div>

        <!-- 三个值演示 -->
        <div class="demo-section">
            <h2>🎯 flex 三个值的情况</h2>
            <div class="description">
                三个值时，顺序固定：<code>flex-grow</code> <code>flex-shrink</code> <code>flex-basis</code>
            </div>

            <div class="code-block">
<span class="comment">/* 完整语法：grow shrink basis */</span>
.<span class="property">item-1</span> { <span class="property">flex</span>: <span class="value">1 0 100px</span>; }  <span class="comment">/* 扩展1倍，不收缩，基础100px */</span>
.<span class="property">item-2</span> { <span class="property">flex</span>: <span class="value">2 1 150px</span>; }  <span class="comment">/* 扩展2倍，正常收缩，基础150px */</span>
.<span class="property">item-3</span> { <span class="property">flex</span>: <span class="value">1 2 80px</span>; }   <span class="comment">/* 扩展1倍，收缩2倍，基础80px */</span>
            </div>

            <div class="flex-container demo-three-values">
                <div class="flex-item item-1">1 0 100px</div>
                <div class="flex-item item-2">2 1 150px</div>
                <div class="flex-item item-3">1 2 80px</div>
            </div>
        </div>

        <!-- 交互演示 -->
        <div class="demo-section">
            <h2>🎮 交互演示</h2>
            <div class="description">
                调整下面的滑块来实时看到 flex 属性的效果
            </div>

            <div class="controls">
                <div class="control-group">
                    <label>flex-grow:</label>
                    <input type="range" id="flexGrow" min="0" max="5" step="0.1" value="1">
                    <span id="flexGrowValue">1</span>
                </div>
                <div class="control-group">
                    <label>flex-shrink:</label>
                    <input type="range" id="flexShrink" min="0" max="3" step="0.1" value="1">
                    <span id="flexShrinkValue">1</span>
                </div>
                <div class="control-group">
                    <label>flex-basis:</label>
                    <input type="range" id="flexBasis" min="0" max="300" step="10" value="100">
                    <span id="flexBasisValue">100px</span>
                </div>
            </div>

            <div class="flex-container" id="interactiveDemo">
                <div class="flex-item" id="interactiveItem">可调节项目</div>
                <div class="flex-item">固定项目1</div>
                <div class="flex-item">固定项目2</div>
            </div>

            <div class="code-block" id="currentFlexCode">
<span class="property">flex</span>: <span class="value">1 1 100px</span>;
            </div>
        </div>

        <!-- 实际应用场景 -->
        <div class="demo-section">
            <h2>🎯 实际应用场景</h2>
            <ul style="margin-left: 20px; line-height: 2;">
                <li><strong>flex: 1</strong> - 等分布局（导航栏、网格系统）</li>
                <li><strong>flex: auto</strong> - 自适应布局（按钮组、标签列表）</li>
                <li><strong>flex: none</strong> - 固定尺寸（图标、固定宽度侧边栏）</li>
                <li><strong>flex: 0 1 auto</strong> - 默认行为（普通内容）</li>
                <li><strong>自定义比例</strong> - 黄金分割、特定比例布局</li>
            </ul>
        </div>
    </div>

    <script>
        // 交互演示功能
        const flexGrowSlider = document.getElementById('flexGrow');
        const flexShrinkSlider = document.getElementById('flexShrink');
        const flexBasisSlider = document.getElementById('flexBasis');
        
        const flexGrowValue = document.getElementById('flexGrowValue');
        const flexShrinkValue = document.getElementById('flexShrinkValue');
        const flexBasisValue = document.getElementById('flexBasisValue');
        
        const interactiveItem = document.getElementById('interactiveItem');
        const currentFlexCode = document.getElementById('currentFlexCode');

        function updateFlexProperty() {
            const grow = flexGrowSlider.value;
            const shrink = flexShrinkSlider.value;
            const basis = flexBasisSlider.value + 'px';
            
            // 更新显示值
            flexGrowValue.textContent = grow;
            flexShrinkValue.textContent = shrink;
            flexBasisValue.textContent = basis;
            
            // 应用样式
            interactiveItem.style.flex = `${grow} ${shrink} ${basis}`;
            
            // 更新代码显示
            currentFlexCode.innerHTML = `<span class="property">flex</span>: <span class="value">${grow} ${shrink} ${basis}</span>;`;
        }

        // 绑定事件监听器
        flexGrowSlider.addEventListener('input', updateFlexProperty);
        flexShrinkSlider.addEventListener('input', updateFlexProperty);
        flexBasisSlider.addEventListener('input', updateFlexProperty);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateFlexProperty();
            
            console.log('=== CSS flex 属性演示 ===');
            console.log('💡 提示：');
            console.log('1. 调整交互演示中的滑块观察效果');
            console.log('2. 尝试调整浏览器窗口大小');
            console.log('3. 观察不同 flex 值的表现差异');
        });

        // 添加一些有趣的交互效果
        document.querySelectorAll('.flex-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
