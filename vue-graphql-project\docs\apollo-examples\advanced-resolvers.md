# Apollo Server Express 高级 Resolvers 示例

## 1. 复杂查询 Resolvers

### 分页查询示例

```typescript
// 分页查询用户
const resolvers = {
  Query: {
    usersPaginated: async (parent, { pagination, filter, sort }, context) => {
      // 权限检查
      if (!context.user || context.user.role !== 'ADMIN') {
        throw new ForbiddenError('需要管理员权限');
      }

      let users = db.getUsers();

      // 应用过滤器
      if (filter) {
        if (filter.role) {
          users = users.filter(user => user.role === filter.role);
        }
        if (filter.status) {
          users = users.filter(user => user.status === filter.status);
        }
        if (filter.search) {
          const searchLower = filter.search.toLowerCase();
          users = users.filter(user => 
            user.username.toLowerCase().includes(searchLower) ||
            user.email.toLowerCase().includes(searchLower) ||
            user.firstName.toLowerCase().includes(searchLower) ||
            user.lastName.toLowerCase().includes(searchLower)
          );
        }
        if (filter.createdAfter) {
          users = users.filter(user => user.createdAt >= filter.createdAfter);
        }
      }

      // 应用排序
      if (sort) {
        users.sort((a, b) => {
          const aValue = a[sort.field];
          const bValue = b[sort.field];
          
          if (sort.direction === 'DESC') {
            return bValue > aValue ? 1 : -1;
          }
          return aValue > bValue ? 1 : -1;
        });
      }

      // 分页
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const offset = (page - 1) * limit;
      const total = users.length;
      const totalPages = Math.ceil(total / limit);

      const paginatedUsers = users.slice(offset, offset + limit);

      return {
        users: paginatedUsers,
        pageInfo: {
          currentPage: page,
          totalPages,
          totalItems: total,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
          itemsPerPage: limit
        }
      };
    },

    // 搜索功能
    search: async (parent, { query, types, limit = 20 }, context) => {
      if (!context.user) {
        throw new AuthenticationError('需要登录');
      }

      const results = [];
      const searchLower = query.toLowerCase();

      // 搜索用户
      if (!types || types.includes('USER')) {
        const users = db.getUsers()
          .filter(user => 
            user.username.toLowerCase().includes(searchLower) ||
            user.email.toLowerCase().includes(searchLower)
          )
          .slice(0, limit)
          .map(user => ({ ...user, __typename: 'User' }));
        results.push(...users);
      }

      // 搜索项目
      if (!types || types.includes('PROJECT')) {
        const projects = db.getProjects()
          .filter(project => 
            project.name.toLowerCase().includes(searchLower) ||
            project.description?.toLowerCase().includes(searchLower)
          )
          .filter(project => 
            project.ownerId === context.user.id ||
            project.memberIds.includes(context.user.id)
          )
          .slice(0, limit)
          .map(project => ({ ...project, __typename: 'Project' }));
        results.push(...projects);
      }

      // 搜索任务
      if (!types || types.includes('TASK')) {
        const tasks = db.getTasks()
          .filter(task => 
            task.title.toLowerCase().includes(searchLower) ||
            task.description?.toLowerCase().includes(searchLower)
          )
          .filter(task => {
            const project = db.getProjectById(task.projectId);
            return project && (
              project.ownerId === context.user.id ||
              project.memberIds.includes(context.user.id) ||
              task.assigneeId === context.user.id
            );
          })
          .slice(0, limit)
          .map(task => ({ ...task, __typename: 'Task' }));
        results.push(...tasks);
      }

      return results.slice(0, limit);
    }
  }
};
```

### 聚合查询示例

```typescript
const resolvers = {
  Query: {
    // 仪表板统计
    dashboardStats: async (parent, args, context) => {
      if (!context.user) {
        throw new AuthenticationError('需要登录');
      }

      const userId = context.user.id;
      const userRole = context.user.role;

      // 获取用户相关数据
      const userTasks = db.getTasks().filter(task => 
        task.assigneeId === userId || task.creatorId === userId
      );
      
      const userProjects = db.getProjects().filter(project => 
        project.ownerId === userId || project.memberIds.includes(userId)
      );

      // 任务统计
      const taskStats = {
        total: userTasks.length,
        todo: userTasks.filter(t => t.status === 'TODO').length,
        inProgress: userTasks.filter(t => t.status === 'IN_PROGRESS').length,
        done: userTasks.filter(t => t.status === 'DONE').length,
        overdue: userTasks.filter(t => 
          t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'DONE'
        ).length
      };

      // 项目统计
      const projectStats = {
        total: userProjects.length,
        active: userProjects.filter(p => p.status === 'ACTIVE').length,
        completed: userProjects.filter(p => p.status === 'COMPLETED').length,
        onHold: userProjects.filter(p => p.status === 'ON_HOLD').length
      };

      // 最近活动
      const recentTasks = userTasks
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, 5);

      // 管理员额外统计
      let adminStats = null;
      if (userRole === 'ADMIN') {
        const allUsers = db.getUsers();
        const allProjects = db.getProjects();
        const allTasks = db.getTasks();

        adminStats = {
          totalUsers: allUsers.length,
          activeUsers: allUsers.filter(u => u.status === 'ACTIVE').length,
          totalProjects: allProjects.length,
          totalTasks: allTasks.length,
          completionRate: allTasks.length > 0 ? 
            (allTasks.filter(t => t.status === 'DONE').length / allTasks.length * 100).toFixed(2) : 0
        };
      }

      return {
        taskStats,
        projectStats,
        recentTasks,
        adminStats
      };
    },

    // 项目详细统计
    projectAnalytics: async (parent, { projectId }, context) => {
      const project = db.getProjectById(projectId);
      if (!project) {
        throw new UserInputError('项目不存在');
      }

      // 权限检查
      if (!context.user || (
        project.ownerId !== context.user.id &&
        !project.memberIds.includes(context.user.id) &&
        context.user.role !== 'ADMIN'
      )) {
        throw new ForbiddenError('无权访问此项目');
      }

      const tasks = db.getTasks().filter(task => task.projectId === projectId);
      const members = db.getUsers().filter(user => 
        user.id === project.ownerId || project.memberIds.includes(user.id)
      );

      // 任务分布
      const taskDistribution = {
        byStatus: {
          todo: tasks.filter(t => t.status === 'TODO').length,
          inProgress: tasks.filter(t => t.status === 'IN_PROGRESS').length,
          done: tasks.filter(t => t.status === 'DONE').length
        },
        byPriority: {
          low: tasks.filter(t => t.priority === 'LOW').length,
          medium: tasks.filter(t => t.priority === 'MEDIUM').length,
          high: tasks.filter(t => t.priority === 'HIGH').length,
          urgent: tasks.filter(t => t.priority === 'URGENT').length
        }
      };

      // 成员工作量
      const memberWorkload = members.map(member => {
        const memberTasks = tasks.filter(task => task.assigneeId === member.id);
        return {
          user: member,
          taskCount: memberTasks.length,
          completedTasks: memberTasks.filter(t => t.status === 'DONE').length,
          pendingTasks: memberTasks.filter(t => t.status !== 'DONE').length
        };
      });

      // 时间统计
      const totalEstimatedHours = tasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
      const totalActualHours = tasks.reduce((sum, task) => sum + (task.actualHours || 0), 0);

      return {
        project,
        taskDistribution,
        memberWorkload,
        timeStats: {
          totalEstimatedHours,
          totalActualHours,
          efficiency: totalEstimatedHours > 0 ? 
            (totalEstimatedHours / totalActualHours * 100).toFixed(2) : 0
        },
        progress: {
          totalTasks: tasks.length,
          completedTasks: tasks.filter(t => t.status === 'DONE').length,
          completionPercentage: tasks.length > 0 ? 
            (tasks.filter(t => t.status === 'DONE').length / tasks.length * 100).toFixed(2) : 0
        }
      };
    }
  }
};
```

## 2. 复杂 Mutation Resolvers

### 批量操作示例

```typescript
const resolvers = {
  Mutation: {
    // 批量更新任务状态
    bulkUpdateTaskStatus: async (parent, { taskIds, status }, context) => {
      if (!context.user) {
        throw new AuthenticationError('需要登录');
      }

      const tasks = taskIds.map(id => db.getTaskById(id)).filter(Boolean);
      
      // 权限检查
      for (const task of tasks) {
        const project = db.getProjectById(task.projectId);
        if (!project || (
          project.ownerId !== context.user.id &&
          !project.memberIds.includes(context.user.id) &&
          task.assigneeId !== context.user.id &&
          context.user.role !== 'ADMIN'
        )) {
          throw new ForbiddenError(`无权修改任务: ${task.title}`);
        }
      }

      // 批量更新
      const updatedTasks = tasks.map(task => {
        const updatedTask = {
          ...task,
          status,
          updatedAt: new Date()
        };
        db.updateTask(task.id, updatedTask);
        return updatedTask;
      });

      // 发布订阅事件
      updatedTasks.forEach(task => {
        pubsub.publish('TASK_UPDATED', { taskUpdated: task });
      });

      return {
        success: true,
        updatedCount: updatedTasks.length,
        tasks: updatedTasks
      };
    },

    // 批量分配任务
    bulkAssignTasks: async (parent, { taskIds, assigneeId }, context) => {
      if (!context.user) {
        throw new AuthenticationError('需要登录');
      }

      const assignee = db.getUserById(assigneeId);
      if (!assignee) {
        throw new UserInputError('指定的用户不存在');
      }

      const tasks = taskIds.map(id => db.getTaskById(id)).filter(Boolean);
      const updatedTasks = [];

      for (const task of tasks) {
        const project = db.getProjectById(task.projectId);
        
        // 权限检查
        if (!project || (
          project.ownerId !== context.user.id &&
          context.user.role !== 'ADMIN' &&
          context.user.role !== 'MANAGER'
        )) {
          continue; // 跳过无权限的任务
        }

        // 检查被分配者是否是项目成员
        if (project.ownerId !== assigneeId && !project.memberIds.includes(assigneeId)) {
          throw new UserInputError(`用户 ${assignee.username} 不是项目成员`);
        }

        const updatedTask = {
          ...task,
          assigneeId,
          updatedAt: new Date()
        };
        
        db.updateTask(task.id, updatedTask);
        updatedTasks.push(updatedTask);

        // 发布订阅事件
        pubsub.publish('TASK_UPDATED', { taskUpdated: updatedTask });
      }

      return {
        success: true,
        assignedCount: updatedTasks.length,
        tasks: updatedTasks
      };
    }
  }
};
```
