import { ApolloClient, InMemoryCache, createHttpLink, split, from } from '@apollo/client/core'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'
import { GraphQLWsLink } from '@apollo/client/link/subscriptions'
import { getMainDefinition } from '@apollo/client/utilities'
import { createClient } from 'graphql-ws'
import { useAuthStore } from '@/stores/auth'

// 网络请求日志链接
const logLink = setContext((operation, { headers }) => {
    console.log(`🚀 GraphQL Request: ${operation.operationName || 'Anonymous'}`)
    console.log('Variables:', operation.variables)
    console.log('Headers:', headers)
    return { headers }
})

// 错误处理链接
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
    if (graphQLErrors) {
        graphQLErrors.forEach(({ message, locations, path }) => {
            console.error(
                `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
        })
    }

    if (networkError) {
        console.error(`[Network error]: ${networkError}`)
        console.error('Network error details:', networkError)
    }

    console.log('Operation:', operation.operationName)
    console.log('Variables:', operation.variables)
})

// HTTP链接
const httpLink = createHttpLink({
    uri: 'http://localhost:4000/graphql',
    credentials: 'include',
    headers: {
        'Content-Type': 'application/json',
    }
})

// WebSocket链接用于订阅
const wsLink = new GraphQLWsLink(
    createClient({
        url: 'ws://localhost:4000/graphql',
        connectionParams: () => {
            const authStore = useAuthStore()
            return {
                authorization: authStore.token ? `Bearer ${authStore.token}` : '',
            }
        },
    })
)

// 认证链接
const authLink = setContext((_, { headers }) => {
    const authStore = useAuthStore()
    return {
        headers: {
            ...headers,
            authorization: authStore.token ? `Bearer ${authStore.token}` : '',
        }
    }
})

// 分割链接：订阅使用WebSocket，其他使用HTTP
const splitLink = split(
    ({ query }) => {
        const definition = getMainDefinition(query)
        return (
            definition.kind === 'OperationDefinition' &&
            definition.operation === 'subscription'
        )
    },
    wsLink,
    from([logLink, errorLink, authLink, httpLink])
)

// 缓存配置
const cache = new InMemoryCache({
    typePolicies: {
        Query: {
            fields: {
                tasks: {
                    merge(existing = { tasks: [], pagination: {} }, incoming) {
                        return incoming
                    }
                },
                projects: {
                    merge(existing = { projects: [], pagination: {} }, incoming) {
                        return incoming
                    }
                },
                users: {
                    merge(existing = { users: [], pagination: {} }, incoming) {
                        return incoming
                    }
                }
            }
        },
        User: {
            fields: {
                tasks: {
                    merge(existing = [], incoming) {
                        return incoming
                    }
                },
                assignedTasks: {
                    merge(existing = [], incoming) {
                        return incoming
                    }
                },
                projects: {
                    merge(existing = [], incoming) {
                        return incoming
                    }
                }
            }
        },
        Project: {
            fields: {
                members: {
                    merge(existing = [], incoming) {
                        return incoming
                    }
                },
                tasks: {
                    merge(existing = [], incoming) {
                        return incoming
                    }
                }
            }
        },
        Task: {
            fields: {
                comments: {
                    merge(existing = [], incoming) {
                        return incoming
                    }
                },
                tags: {
                    merge(existing = [], incoming) {
                        return incoming
                    }
                }
            }
        }
    }
})

// 创建Apollo客户端
export const apolloClient = new ApolloClient({
    link: splitLink,
    cache,
    defaultOptions: {
        watchQuery: {
            errorPolicy: 'all',
            fetchPolicy: 'cache-and-network'
        },
        query: {
            errorPolicy: 'all',
            fetchPolicy: 'cache-first'
        },
        mutate: {
            errorPolicy: 'all'
        }
    },
    connectToDevTools: import.meta.env.DEV
})

// 错误处理
apolloClient.onResetStore(async () => {
    console.log('Apollo store reset')
})

// 导出默认客户端
export default apolloClient
