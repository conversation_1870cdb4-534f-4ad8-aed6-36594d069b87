# Git 故障排查

## 📋 概述

Git 故障排查是开发过程中的重要技能。本文档提供了系统性的故障诊断方法、常见问题的解决方案，以及预防措施。

## 🔍 故障诊断方法论

### 1. 问题分类

**按影响范围分类**:
- **本地问题**: 只影响本地仓库
- **远程问题**: 影响远程仓库同步
- **网络问题**: 连接和传输问题
- **权限问题**: 访问控制相关

**按严重程度分类**:
- **致命错误**: 无法继续工作
- **功能异常**: 部分功能不可用
- **性能问题**: 操作缓慢但可用
- **警告信息**: 不影响功能但需关注

### 2. 诊断流程

**标准诊断步骤**:
```bash
# 1. 检查 Git 状态
git status
git log --oneline -5

# 2. 检查配置
git config --list
git remote -v

# 3. 检查仓库完整性
git fsck --full

# 4. 检查网络连接
ping github.com
git ls-remote origin

# 5. 查看详细错误信息
git --version
git config --get-regexp core
```

## 🚨 常见故障类型

### 1. 仓库损坏

**症状表现**:
```
error: object file .git/objects/xx/xxxxx is empty
fatal: loose object xxxxx (stored in .git/objects/xx/xxxxx) is corrupt
```

**诊断方法**:
```bash
# 检查仓库完整性
git fsck --full --no-dangling

# 查看损坏对象
git fsck --unreachable --no-dangling 2>&1 | grep "missing\|corrupt"

# 检查引用完整性
git for-each-ref --format="%(refname) %(objectname)" | \
while read ref obj; do
    git cat-file -e $obj || echo "Bad ref: $ref -> $obj"
done
```

**修复方法**:
```bash
# 方法1: 从远程恢复
git fetch origin
git reset --hard origin/main

# 方法2: 重建损坏对象
# 如果有备份或其他克隆
cp /path/to/backup/.git/objects/xx/xxxxx .git/objects/xx/

# 方法3: 重新克隆（最后手段）
cd ..
git clone https://github.com/user/repo.git repo-new
cd repo-new
# 复制未提交的工作
```

### 2. 索引问题

**症状表现**:
```
error: bad index file sha1 signature
fatal: index file corrupt
```

**修复方法**:
```bash
# 删除并重建索引
rm .git/index
git reset

# 或者从 HEAD 重建
git read-tree HEAD
git checkout-index -f -a
```

### 3. 引用问题

**症状表现**:
```
error: refs/heads/main does not point to a valid object
fatal: bad object HEAD
```

**诊断和修复**:
```bash
# 检查 HEAD 引用
cat .git/HEAD
cat .git/refs/heads/main

# 修复 HEAD 引用
git symbolic-ref HEAD refs/heads/main

# 修复分支引用
git update-ref refs/heads/main $(git rev-parse origin/main)
```

## 🌐 网络和远程问题

### 1. 连接问题

**常见错误**:
```
fatal: unable to access 'https://github.com/user/repo.git/': 
Could not resolve host: github.com

fatal: The remote end hung up unexpectedly
```

**诊断步骤**:
```bash
# 网络连接测试
ping github.com
nslookup github.com
curl -I https://github.com

# Git 网络配置检查
git config --get-regexp http
git config --get-regexp url

# 代理设置检查
echo $HTTP_PROXY
echo $HTTPS_PROXY
git config --get http.proxy
```

**解决方法**:
```bash
# 设置代理
git config --global http.proxy http://proxy.company.com:8080
git config --global https.proxy https://proxy.company.com:8080

# 取消代理
git config --global --unset http.proxy
git config --global --unset https.proxy

# 增加超时时间
git config --global http.lowSpeedLimit 0
git config --global http.lowSpeedTime 999999

# 使用 SSH 替代 HTTPS
git remote set-<NAME_EMAIL>:user/repo.git
```

### 2. 认证问题

**常见错误**:
```
remote: Invalid username or password
fatal: Authentication failed for 'https://github.com/user/repo.git/'

Permission denied (publickey)
```

**SSH 认证诊断**:
```bash
# 测试 SSH 连接
ssh -T **************

# 检查 SSH 密钥
ls -la ~/.ssh/
ssh-add -l

# 生成新的 SSH 密钥
ssh-keygen -t ed25519 -C "<EMAIL>"
ssh-add ~/.ssh/id_ed25519

# 测试特定密钥
ssh -i ~/.ssh/id_ed25519 -T **************
```

**HTTPS 认证诊断**:
```bash
# 检查凭据存储
git config --get credential.helper

# 清除存储的凭据
git credential-manager-core erase
# 或
git config --global --unset credential.helper

# 使用个人访问令牌
git config --global credential.helper store
# 然后在下次操作时输入用户名和 PAT
```

## 🔧 操作错误恢复

### 1. 误删除恢复

**恢复删除的文件**:
```bash
# 恢复工作区删除的文件
git checkout HEAD -- filename

# 恢复暂存区删除的文件
git reset HEAD filename
git checkout -- filename

# 从历史提交恢复
git show HEAD~1:path/to/file > path/to/file
```

**恢复删除的分支**:
```bash
# 查看引用日志
git reflog

# 恢复分支
git branch recovered-branch HEAD@{n}

# 或者直接检出
git checkout -b recovered-branch HEAD@{n}
```

### 2. 错误提交处理

**修改最后一次提交**:
```bash
# 修改提交信息
git commit --amend -m "正确的提交信息"

# 添加遗漏的文件
git add forgotten-file
git commit --amend --no-edit
```

**撤销提交**:
```bash
# 撤销最后一次提交（保留更改）
git reset --soft HEAD~1

# 撤销最后一次提交（丢弃更改）
git reset --hard HEAD~1

# 创建反向提交
git revert HEAD
```

### 3. 合并冲突处理

**冲突诊断**:
```bash
# 查看冲突状态
git status
git diff

# 查看冲突文件
git diff --name-only --diff-filter=U

# 查看冲突详情
git show :1:filename  # 共同祖先
git show :2:filename  # 当前分支
git show :3:filename  # 合并分支
```

**冲突解决**:
```bash
# 使用合并工具
git mergetool

# 手动解决后标记
git add resolved-file
git commit

# 放弃合并
git merge --abort

# 使用特定版本
git checkout --ours filename    # 使用当前分支版本
git checkout --theirs filename  # 使用合并分支版本
```

## 🛠️ 高级故障排查

### 1. 性能问题诊断

**慢操作诊断**:
```bash
# 启用性能跟踪
export GIT_TRACE=1
export GIT_TRACE_PERFORMANCE=1
export GIT_TRACE_SETUP=1

# 执行慢操作
git status

# 分析输出
unset GIT_TRACE GIT_TRACE_PERFORMANCE GIT_TRACE_SETUP
```

**仓库大小分析**:
```bash
# 查找大对象
git rev-list --objects --all | \
git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
awk '/^blob/ {print substr($0,6)}' | \
sort --numeric-sort --key=2 | \
tail -10

# 分析包文件
git verify-pack -v .git/objects/pack/*.idx | \
sort -k 3 -nr | \
head -10
```

### 2. 数据一致性检查

**完整性验证脚本**:
```bash
#!/bin/bash
# git-health-check.sh

echo "=== Git 仓库健康检查 ==="

# 基础检查
echo "1. 检查仓库完整性..."
if git fsck --full --no-dangling; then
    echo "✅ 仓库完整性正常"
else
    echo "❌ 发现仓库完整性问题"
fi

# 检查引用
echo "2. 检查引用完整性..."
git for-each-ref --format="%(refname) %(objectname)" | \
while read ref obj; do
    if ! git cat-file -e $obj 2>/dev/null; then
        echo "❌ 损坏的引用: $ref -> $obj"
    fi
done

# 检查远程连接
echo "3. 检查远程连接..."
if git ls-remote origin >/dev/null 2>&1; then
    echo "✅ 远程连接正常"
else
    echo "❌ 远程连接失败"
fi

# 检查配置
echo "4. 检查关键配置..."
if git config user.name >/dev/null && git config user.email >/dev/null; then
    echo "✅ 用户配置正常"
else
    echo "⚠️  用户配置缺失"
fi

echo "健康检查完成"
```

## 📊 故障预防

### 1. 监控和告警

**自动化监控脚本**:
```bash
#!/bin/bash
# git-monitor.sh

# 检查仓库大小
REPO_SIZE=$(du -s .git | cut -f1)
if [ $REPO_SIZE -gt 1000000 ]; then  # 1GB
    echo "警告: 仓库大小 ${REPO_SIZE}KB 超过阈值"
fi

# 检查对象数量
OBJECT_COUNT=$(git count-objects | grep 'count' | cut -d' ' -f2)
if [ $OBJECT_COUNT -gt 50000 ]; then
    echo "警告: 对象数量 $OBJECT_COUNT 过多"
fi

# 检查未推送提交
UNPUSHED=$(git log origin/main..HEAD --oneline | wc -l)
if [ $UNPUSHED -gt 10 ]; then
    echo "警告: 有 $UNPUSHED 个未推送的提交"
fi
```

### 2. 备份策略

**自动备份脚本**:
```bash
#!/bin/bash
# git-backup.sh

BACKUP_DIR="/backup/git-repos"
REPO_NAME=$(basename $(git rev-parse --show-toplevel))
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p "$BACKUP_DIR/$REPO_NAME"

# 备份裸仓库
git clone --bare . "$BACKUP_DIR/$REPO_NAME/${REPO_NAME}_${TIMESTAMP}.git"

# 压缩备份
cd "$BACKUP_DIR/$REPO_NAME"
tar -czf "${REPO_NAME}_${TIMESTAMP}.tar.gz" "${REPO_NAME}_${TIMESTAMP}.git"
rm -rf "${REPO_NAME}_${TIMESTAMP}.git"

echo "备份完成: $BACKUP_DIR/$REPO_NAME/${REPO_NAME}_${TIMESTAMP}.tar.gz"
```

### 3. 最佳实践

**预防措施清单**:
```markdown
## Git 故障预防清单

### 日常操作
- [ ] 定期执行 `git gc` 清理
- [ ] 及时推送本地提交
- [ ] 避免强制推送到共享分支
- [ ] 使用有意义的提交信息

### 配置管理
- [ ] 正确配置用户信息
- [ ] 设置合适的 .gitignore
- [ ] 配置适当的钩子脚本
- [ ] 定期更新 Git 版本

### 备份和恢复
- [ ] 定期备份重要仓库
- [ ] 测试备份恢复流程
- [ ] 文档化恢复步骤
- [ ] 监控仓库健康状态

### 团队协作
- [ ] 建立分支保护规则
- [ ] 实施代码审查流程
- [ ] 培训团队成员
- [ ] 制定应急响应计划
```

## 🆘 应急响应

### 1. 紧急恢复流程

**数据丢失应急**:
```bash
# 1. 立即停止所有操作
# 2. 评估损失范围
git reflog --all
git fsck --unreachable

# 3. 尝试从引用日志恢复
git branch recovery-branch HEAD@{n}

# 4. 从远程仓库恢复
git fetch --all
git reset --hard origin/main

# 5. 从备份恢复（如果有）
```

### 2. 团队协调

**事故沟通模板**:
```markdown
## Git 仓库事故报告

### 基本信息
- 发生时间: 2023-12-01 14:30
- 影响仓库: project-name
- 影响范围: 主分支历史丢失
- 发现人员: 张三

### 问题描述
详细描述发生的问题...

### 影响评估
- 数据丢失: 最近 3 次提交
- 影响人员: 5 名开发者
- 业务影响: 延迟 2 小时

### 恢复措施
1. 从备份恢复主分支
2. 通知团队重新同步
3. 验证数据完整性

### 预防措施
1. 加强分支保护
2. 增加自动备份频率
3. 团队培训
```

---

通过系统性的故障排查方法和预防措施，可以最大程度地减少 Git 相关问题的发生，并在问题出现时快速恢复。
