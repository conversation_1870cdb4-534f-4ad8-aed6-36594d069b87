/* Vue.js + RESTful API 集成示例样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px;
    background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
    color: white;
    border-radius: 10px;
}

.section {
    background: white;
    margin: 20px 0;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin: 15px 0;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #42b883;
    box-shadow: 0 0 0 2px rgba(66, 184, 131, 0.2);
}

.btn {
    background: #42b883;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin: 5px;
    transition: background 0.3s;
}

.btn:hover {
    background: #369870;
}

.btn-danger {
    background: #e74c3c;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.user-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #42b883;
    transition: transform 0.3s, box-shadow 0.3s;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 15px;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.user-email {
    color: #666;
    margin-bottom: 10px;
}

.user-meta {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #42b883;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #e74c3c;
    margin: 15px 0;
}

.success {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #28a745;
    margin: 15px 0;
}

.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #42b883;
}

.stat-label {
    color: #666;
    margin-top: 5px;
}

.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-input {
    padding-left: 40px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    gap: 10px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
}

.page-btn:hover {
    background: #42b883;
    color: white;
    border-color: #42b883;
}

.page-btn.active {
    background: #42b883;
    color: white;
    border-color: #42b883;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .users-grid {
        grid-template-columns: 1fr;
    }
    
    .stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 1.5em;
    }
    
    .section {
        padding: 15px;
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
    
    .user-card {
        padding: 15px;
    }
}
