# 🧪 测试指南

欢迎测试 Vue GraphQL 项目管理系统！本文档将帮助您快速上手并体验所有功能。

## 🚀 快速启动

### 1. 启动项目

**Windows 用户:**
```bash
# 双击运行或在命令行执行
start.bat
```

**Linux/macOS 用户:**
```bash
# 添加执行权限并运行
chmod +x start.sh
./start.sh
```

**手动启动:**
```bash
# 安装依赖
npm run install:all

# 启动开发服务器
npm run dev
```

### 2. 等待服务启动

启动完成后，您将看到以下信息：
```
🚀 Server is running!
📊 GraphQL Playground: http://localhost:4000/graphql
🔗 GraphQL Endpoint: http://localhost:4000/graphql
🔌 WebSocket Subscriptions: ws://localhost:4000/graphql
💚 Health Check: http://localhost:4000/health
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端应用** | http://localhost:3000 | 主要的Web应用界面 |
| **GraphQL Playground** | http://localhost:4000/graphql | GraphQL API调试工具 |
| **API信息** | http://localhost:4000/api/info | 服务器信息和功能列表 |
| **健康检查** | http://localhost:4000/health | 服务器状态检查 |
| **统计数据** | http://localhost:4000/api/stats | 数据库统计信息 |

## 👥 演示账户

系统预置了5个不同角色的演示账户，您可以使用任意账户登录体验：

### 管理员账户
- **邮箱**: `<EMAIL>`
- **密码**: `admin123`
- **权限**: 最高权限，可以管理所有用户、项目和任务

### 项目经理账户
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **权限**: 可以创建和管理项目，查看团队成员

### 开发者账户
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **权限**: 可以创建和管理任务，参与项目开发

### 设计师账户
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **权限**: 可以创建和管理设计相关任务

### 测试员账户
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **权限**: 可以创建和管理测试任务，报告问题

## 🎯 功能测试指南

### 1. 登录系统
1. 访问 http://localhost:3000
2. 使用上述任意账户登录
3. 或点击演示账户快速填充登录信息

### 2. 仪表板功能
登录后您将看到：
- **统计卡片**: 显示任务、项目、用户总数
- **任务状态分布**: 可视化任务进度
- **项目状态**: 项目状态统计
- **最近任务**: 最新创建的任务列表
- **最近项目**: 最新创建的项目列表

### 3. 项目管理测试
1. 点击左侧菜单 "项目管理"
2. 查看现有项目列表
3. 点击 "新建项目" 创建项目
4. 点击项目名称查看项目详情
5. 在项目详情页面管理项目成员

### 4. 任务管理测试
1. 点击左侧菜单 "任务管理"
2. 查看任务列表和筛选功能
3. 点击 "新建任务" 创建任务
4. 使用搜索框搜索任务
5. 使用筛选器按状态、优先级筛选
6. 点击任务查看详情和评论

### 5. 用户管理测试（管理员权限）
1. 使用管理员账户登录
2. 点击左侧菜单 "用户管理"
3. 查看用户列表
4. 搜索和筛选用户
5. 编辑用户信息

### 6. 实时功能测试
1. 打开两个浏览器窗口
2. 使用不同账户登录
3. 在一个窗口创建或更新任务
4. 观察另一个窗口的实时更新

## 🔧 GraphQL API 测试

### 访问 GraphQL Playground
1. 打开 http://localhost:4000/graphql
2. 您将看到 GraphQL Playground 界面

### 示例查询

**获取所有用户:**
```graphql
query {
  users {
    users {
      id
      username
      email
      firstName
      lastName
      role
    }
  }
}
```

**获取仪表板统计:**
```graphql
query {
  dashboardStats {
    taskStats {
      total
      todo
      inProgress
      done
    }
    projectStats {
      total
      active
      completed
    }
    userCount
  }
}
```

**登录获取Token:**
```graphql
mutation {
  login(input: {
    email: "<EMAIL>"
    password: "admin123"
  }) {
    token
    user {
      id
      username
      firstName
      lastName
      role
    }
  }
}
```

**创建任务:**
```graphql
mutation {
  createTask(input: {
    title: "测试任务"
    description: "这是一个测试任务"
    projectId: "1"
    priority: MEDIUM
  }) {
    id
    title
    status
    priority
    project {
      name
    }
  }
}
```

### 使用认证Token
1. 先执行登录查询获取token
2. 在Playground底部的"HTTP HEADERS"中添加：
```json
{
  "authorization": "Bearer YOUR_TOKEN_HERE"
}
```

## 📱 移动端测试

1. 在手机浏览器中访问 http://YOUR_IP:3000
2. 测试响应式布局
3. 测试触摸交互
4. 测试移动端菜单

## 🐛 常见问题

### 端口被占用
如果端口3000或4000被占用：
1. 修改 `client/vite.config.ts` 中的端口
2. 修改 `server/src/server.ts` 中的端口
3. 重新启动服务

### 无法连接服务器
1. 检查防火墙设置
2. 确保Node.js版本 >= 16
3. 检查控制台错误信息

### GraphQL查询失败
1. 确保服务器正在运行
2. 检查GraphQL endpoint地址
3. 验证查询语法是否正确

## 📊 性能测试

### 前端性能
1. 打开浏览器开发者工具
2. 切换到Network面板
3. 刷新页面观察加载时间
4. 检查资源大小和加载速度

### API性能
1. 在GraphQL Playground中执行查询
2. 观察响应时间
3. 测试复杂查询的性能
4. 检查网络面板的请求时间

## 🔍 测试重点

### 功能测试
- ✅ 用户登录注册
- ✅ 项目CRUD操作
- ✅ 任务CRUD操作
- ✅ 权限控制
- ✅ 实时更新
- ✅ 搜索筛选
- ✅ 响应式布局

### 兼容性测试
- ✅ Chrome浏览器
- ✅ Firefox浏览器
- ✅ Safari浏览器
- ✅ Edge浏览器
- ✅ 移动端浏览器

### 安全测试
- ✅ 未授权访问拦截
- ✅ 角色权限验证
- ✅ 输入数据验证
- ✅ XSS防护

## 📝 反馈建议

如果您在测试过程中发现问题或有改进建议，请：

1. 记录问题详情（浏览器、操作步骤、错误信息）
2. 截图或录屏问题现象
3. 提供改进建议
4. 联系开发团队

## 🎉 测试完成

恭喜您完成了所有功能测试！这个项目展示了现代Web开发的最佳实践，希望对您的学习和工作有所帮助。

---

**技术支持**: 如有问题请查看项目文档或联系开发团队
**项目地址**: https://github.com/yourusername/vue-graphql-project
