# 编码原则练习题集

## 📝 练习说明

本练习题集旨在帮助您更好地理解和应用编码原则。每个练习都包含：
- 问题描述
- 违反原则的代码示例
- 思考题
- 参考答案

---

## 🎯 OAOO/DRY 原则练习

### 练习 1: 重复的验证逻辑

**问题**: 以下代码违反了哪个原则？如何改进？

```javascript
function validateUserRegistration(userData) {
    if (!userData.email) {
        throw new Error('Email is required');
    }
    if (!userData.email.includes('@')) {
        throw new Error('Invalid email format');
    }
    if (userData.password.length < 8) {
        throw new Error('Password must be at least 8 characters');
    }
    if (!userData.name) {
        throw new Error('Name is required');
    }
}

function validateUserUpdate(userData) {
    if (!userData.email) {
        throw new Error('Email is required');
    }
    if (!userData.email.includes('@')) {
        throw new Error('Invalid email format');
    }
    if (userData.password && userData.password.length < 8) {
        throw new Error('Password must be at least 8 characters');
    }
    if (!userData.name) {
        throw new Error('Name is required');
    }
}

function validateAdminRegistration(userData) {
    if (!userData.email) {
        throw new Error('Email is required');
    }
    if (!userData.email.includes('@')) {
        throw new Error('Invalid email format');
    }
    if (userData.password.length < 12) {
        throw new Error('Admin password must be at least 12 characters');
    }
    if (!userData.name) {
        throw new Error('Name is required');
    }
    if (!userData.department) {
        throw new Error('Department is required for admin');
    }
}
```

**思考题**:
1. 哪些验证逻辑是重复的？
2. 如何抽取公共的验证逻辑？
3. 如何处理不同场景的特殊验证需求？

<details>
<summary>点击查看参考答案</summary>

```javascript
// 抽取基础验证器
class BaseValidator {
    static validateEmail(email) {
        if (!email) {
            throw new Error('Email is required');
        }
        if (!email.includes('@')) {
            throw new Error('Invalid email format');
        }
    }
    
    static validateName(name) {
        if (!name) {
            throw new Error('Name is required');
        }
    }
    
    static validatePassword(password, minLength = 8) {
        if (password && password.length < minLength) {
            throw new Error(`Password must be at least ${minLength} characters`);
        }
    }
}

// 具体验证器
class UserValidator extends BaseValidator {
    static validateRegistration(userData) {
        this.validateEmail(userData.email);
        this.validateName(userData.name);
        this.validatePassword(userData.password, 8);
    }
    
    static validateUpdate(userData) {
        this.validateEmail(userData.email);
        this.validateName(userData.name);
        if (userData.password) {
            this.validatePassword(userData.password, 8);
        }
    }
}

class AdminValidator extends BaseValidator {
    static validateRegistration(userData) {
        this.validateEmail(userData.email);
        this.validateName(userData.name);
        this.validatePassword(userData.password, 12);
        
        if (!userData.department) {
            throw new Error('Department is required for admin');
        }
    }
}
```
</details>

---

## 🎯 KISS 原则练习

### 练习 2: 过度复杂的配置系统

**问题**: 以下代码是否违反了KISS原则？如何简化？

```javascript
class AdvancedConfigurationManager {
    constructor() {
        this.configSources = new Map();
        this.configCache = new WeakMap();
        this.configObservers = new Set();
        this.configMiddleware = [];
        this.configTransformers = new Map();
    }
    
    registerConfigSource(name, source, priority = 0, options = {}) {
        const sourceConfig = {
            source,
            priority,
            options,
            transformer: options.transformer || this.defaultTransformer,
            validator: options.validator || this.defaultValidator,
            cache: options.cache !== false
        };
        
        this.configSources.set(name, sourceConfig);
        this.sortSourcesByPriority();
    }
    
    getConfig(key, context = {}) {
        const cacheKey = this.generateCacheKey(key, context);
        
        if (this.configCache.has(cacheKey)) {
            return this.configCache.get(cacheKey);
        }
        
        let value = this.resolveConfigValue(key, context);
        
        // 应用中间件
        for (const middleware of this.configMiddleware) {
            value = middleware.transform(value, key, context);
        }
        
        // 应用转换器
        if (this.configTransformers.has(key)) {
            value = this.configTransformers.get(key)(value);
        }
        
        this.configCache.set(cacheKey, value);
        this.notifyObservers(key, value);
        
        return value;
    }
    
    // ... 更多复杂方法
}
```

**思考题**:
1. 这个配置管理器是否过度设计了？
2. 对于大多数应用场景，真的需要这么复杂吗？
3. 如何设计一个简单但够用的配置管理器？

<details>
<summary>点击查看参考答案</summary>

```javascript
// 简单的配置管理器
class SimpleConfigManager {
    constructor() {
        this.config = {};
    }
    
    // 从环境变量和配置文件加载
    load(configFile = null) {
        // 加载环境变量
        this.config = { ...process.env };
        
        // 如果有配置文件，加载并覆盖
        if (configFile) {
            try {
                const fileConfig = require(configFile);
                this.config = { ...this.config, ...fileConfig };
            } catch (error) {
                console.warn(`Failed to load config file: ${configFile}`);
            }
        }
    }
    
    get(key, defaultValue = null) {
        return this.config[key] || defaultValue;
    }
    
    set(key, value) {
        this.config[key] = value;
    }
    
    getAll() {
        return { ...this.config };
    }
}

// 使用示例
const config = new SimpleConfigManager();
config.load('./config.json');

const dbHost = config.get('DB_HOST', 'localhost');
const dbPort = config.get('DB_PORT', 3306);
```
</details>

---

## 🎯 SOLID 原则练习

### 练习 3: 单一职责原则 (SRP)

**问题**: 以下类违反了SRP原则，请重构：

```javascript
class OrderProcessor {
    constructor() {
        this.orders = [];
    }
    
    // 订单验证
    validateOrder(order) {
        if (!order.customerId) throw new Error('Customer ID required');
        if (!order.items || order.items.length === 0) throw new Error('Order items required');
        if (order.total <= 0) throw new Error('Order total must be positive');
    }
    
    // 库存检查
    checkInventory(order) {
        for (const item of order.items) {
            const stock = this.getStockLevel(item.productId);
            if (stock < item.quantity) {
                throw new Error(`Insufficient stock for product ${item.productId}`);
            }
        }
    }
    
    // 价格计算
    calculateTotal(order) {
        let total = 0;
        for (const item of order.items) {
            const price = this.getProductPrice(item.productId);
            total += price * item.quantity;
        }
        
        // 应用折扣
        if (order.discountCode) {
            const discount = this.getDiscountAmount(order.discountCode);
            total -= discount;
        }
        
        // 计算税费
        const tax = total * 0.1;
        total += tax;
        
        return total;
    }
    
    // 支付处理
    processPayment(order, paymentInfo) {
        if (paymentInfo.type === 'credit_card') {
            return this.processCreditCardPayment(paymentInfo);
        } else if (paymentInfo.type === 'paypal') {
            return this.processPayPalPayment(paymentInfo);
        }
        throw new Error('Unsupported payment type');
    }
    
    // 发送邮件
    sendConfirmationEmail(order, customer) {
        const emailContent = this.generateEmailContent(order, customer);
        this.sendEmail(customer.email, 'Order Confirmation', emailContent);
    }
    
    // 更新库存
    updateInventory(order) {
        for (const item of order.items) {
            this.decreaseStock(item.productId, item.quantity);
        }
    }
    
    // 主要处理方法
    processOrder(order, customer, paymentInfo) {
        this.validateOrder(order);
        this.checkInventory(order);
        order.total = this.calculateTotal(order);
        
        const paymentResult = this.processPayment(order, paymentInfo);
        if (paymentResult.success) {
            this.updateInventory(order);
            this.orders.push(order);
            this.sendConfirmationEmail(order, customer);
            return { success: true, orderId: order.id };
        }
        
        return { success: false, error: paymentResult.error };
    }
}
```

**思考题**:
1. 这个类承担了哪些不同的职责？
2. 如何将这些职责分离到不同的类中？
3. 分离后的类之间应该如何协作？

<details>
<summary>点击查看参考答案</summary>

```javascript
// 订单验证器
class OrderValidator {
    static validate(order) {
        if (!order.customerId) throw new Error('Customer ID required');
        if (!order.items || order.items.length === 0) throw new Error('Order items required');
        if (order.total <= 0) throw new Error('Order total must be positive');
    }
}

// 库存管理器
class InventoryManager {
    checkAvailability(order) {
        for (const item of order.items) {
            const stock = this.getStockLevel(item.productId);
            if (stock < item.quantity) {
                throw new Error(`Insufficient stock for product ${item.productId}`);
            }
        }
    }
    
    updateStock(order) {
        for (const item of order.items) {
            this.decreaseStock(item.productId, item.quantity);
        }
    }
}

// 价格计算器
class PriceCalculator {
    calculateTotal(order) {
        let total = 0;
        for (const item of order.items) {
            const price = this.getProductPrice(item.productId);
            total += price * item.quantity;
        }
        
        if (order.discountCode) {
            const discount = this.getDiscountAmount(order.discountCode);
            total -= discount;
        }
        
        const tax = total * 0.1;
        total += tax;
        
        return total;
    }
}

// 支付处理器
class PaymentProcessor {
    process(order, paymentInfo) {
        if (paymentInfo.type === 'credit_card') {
            return this.processCreditCardPayment(paymentInfo);
        } else if (paymentInfo.type === 'paypal') {
            return this.processPayPalPayment(paymentInfo);
        }
        throw new Error('Unsupported payment type');
    }
}

// 邮件服务
class EmailService {
    sendOrderConfirmation(order, customer) {
        const emailContent = this.generateEmailContent(order, customer);
        this.sendEmail(customer.email, 'Order Confirmation', emailContent);
    }
}

// 订单仓库
class OrderRepository {
    save(order) {
        // 保存订单到数据库
    }
}

// 订单处理器 - 协调各个组件
class OrderProcessor {
    constructor(validator, inventoryManager, priceCalculator, paymentProcessor, emailService, orderRepository) {
        this.validator = validator;
        this.inventoryManager = inventoryManager;
        this.priceCalculator = priceCalculator;
        this.paymentProcessor = paymentProcessor;
        this.emailService = emailService;
        this.orderRepository = orderRepository;
    }
    
    processOrder(order, customer, paymentInfo) {
        this.validator.validate(order);
        this.inventoryManager.checkAvailability(order);
        order.total = this.priceCalculator.calculateTotal(order);
        
        const paymentResult = this.paymentProcessor.process(order, paymentInfo);
        if (paymentResult.success) {
            this.inventoryManager.updateStock(order);
            this.orderRepository.save(order);
            this.emailService.sendOrderConfirmation(order, customer);
            return { success: true, orderId: order.id };
        }
        
        return { success: false, error: paymentResult.error };
    }
}
```
</details>

### 练习 4: 开闭原则 (OCP)

**问题**: 以下代码在添加新的图形类型时需要修改现有代码，违反了OCP原则。请重构：

```javascript
class AreaCalculator {
    calculateArea(shapes) {
        let totalArea = 0;
        
        for (const shape of shapes) {
            if (shape.type === 'rectangle') {
                totalArea += shape.width * shape.height;
            } else if (shape.type === 'circle') {
                totalArea += Math.PI * shape.radius * shape.radius;
            } else if (shape.type === 'triangle') {
                totalArea += 0.5 * shape.base * shape.height;
            }
            // 每次添加新图形都需要修改这里
        }
        
        return totalArea;
    }
}

// 使用示例
const calculator = new AreaCalculator();
const shapes = [
    { type: 'rectangle', width: 10, height: 5 },
    { type: 'circle', radius: 3 },
    { type: 'triangle', base: 4, height: 6 }
];
```

**思考题**:
1. 为什么这个设计违反了OCP原则？
2. 如何设计才能在添加新图形时不修改现有代码？
3. 使用什么设计模式可以解决这个问题？

<details>
<summary>点击查看参考答案</summary>

```javascript
// 抽象基类
class Shape {
    calculateArea() {
        throw new Error('Must implement calculateArea method');
    }
}

// 具体图形类
class Rectangle extends Shape {
    constructor(width, height) {
        super();
        this.width = width;
        this.height = height;
    }
    
    calculateArea() {
        return this.width * this.height;
    }
}

class Circle extends Shape {
    constructor(radius) {
        super();
        this.radius = radius;
    }
    
    calculateArea() {
        return Math.PI * this.radius * this.radius;
    }
}

class Triangle extends Shape {
    constructor(base, height) {
        super();
        this.base = base;
        this.height = height;
    }
    
    calculateArea() {
        return 0.5 * this.base * this.height;
    }
}

// 新增图形 - 不需要修改现有代码
class Pentagon extends Shape {
    constructor(side, apothem) {
        super();
        this.side = side;
        this.apothem = apothem;
    }
    
    calculateArea() {
        return 2.5 * this.side * this.apothem;
    }
}

// 面积计算器 - 对修改封闭，对扩展开放
class AreaCalculator {
    calculateArea(shapes) {
        return shapes.reduce((total, shape) => {
            return total + shape.calculateArea();
        }, 0);
    }
}

// 使用示例
const calculator = new AreaCalculator();
const shapes = [
    new Rectangle(10, 5),
    new Circle(3),
    new Triangle(4, 6),
    new Pentagon(5, 3.44) // 新增图形，无需修改计算器
];

console.log(calculator.calculateArea(shapes));
```
</details>

---

## 🎯 综合练习

### 练习 5: 电商系统重构

**问题**: 以下是一个简化的电商系统代码，存在多个原则违反问题。请识别问题并重构：

```javascript
class ECommerceSystem {
    constructor() {
        this.products = [];
        this.users = [];
        this.orders = [];
    }
    
    // 用户相关
    registerUser(userData) {
        // 验证用户数据
        if (!userData.email || !userData.email.includes('@')) {
            throw new Error('Invalid email');
        }
        if (!userData.password || userData.password.length < 6) {
            throw new Error('Password too short');
        }
        
        // 检查邮箱是否已存在
        const existingUser = this.users.find(u => u.email === userData.email);
        if (existingUser) {
            throw new Error('Email already exists');
        }
        
        // 创建用户
        const user = {
            id: Date.now(),
            email: userData.email,
            password: userData.password, // 应该加密
            createdAt: new Date()
        };
        
        this.users.push(user);
        
        // 发送欢迎邮件
        console.log(`Sending welcome email to ${user.email}`);
        
        return user;
    }
    
    // 产品相关
    addProduct(productData) {
        // 验证产品数据
        if (!productData.name || !productData.price) {
            throw new Error('Product name and price required');
        }
        
        const product = {
            id: Date.now(),
            name: productData.name,
            price: productData.price,
            stock: productData.stock || 0
        };
        
        this.products.push(product);
        return product;
    }
    
    // 订单相关
    createOrder(userId, items) {
        // 验证用户
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            throw new Error('User not found');
        }
        
        // 验证商品和库存
        let total = 0;
        for (const item of items) {
            const product = this.products.find(p => p.id === item.productId);
            if (!product) {
                throw new Error(`Product ${item.productId} not found`);
            }
            if (product.stock < item.quantity) {
                throw new Error(`Insufficient stock for ${product.name}`);
            }
            total += product.price * item.quantity;
        }
        
        // 创建订单
        const order = {
            id: Date.now(),
            userId: userId,
            items: items,
            total: total,
            status: 'pending',
            createdAt: new Date()
        };
        
        // 更新库存
        for (const item of items) {
            const product = this.products.find(p => p.id === item.productId);
            product.stock -= item.quantity;
        }
        
        this.orders.push(order);
        
        // 发送订单确认邮件
        console.log(`Sending order confirmation to ${user.email}`);
        
        return order;
    }
    
    // 支付处理
    processPayment(orderId, paymentMethod, paymentDetails) {
        const order = this.orders.find(o => o.id === orderId);
        if (!order) {
            throw new Error('Order not found');
        }
        
        // 处理不同支付方式
        if (paymentMethod === 'credit_card') {
            // 信用卡支付逻辑
            console.log('Processing credit card payment');
            if (!paymentDetails.cardNumber || !paymentDetails.cvv) {
                throw new Error('Invalid credit card details');
            }
        } else if (paymentMethod === 'paypal') {
            // PayPal支付逻辑
            console.log('Processing PayPal payment');
            if (!paymentDetails.paypalEmail) {
                throw new Error('PayPal email required');
            }
        } else {
            throw new Error('Unsupported payment method');
        }
        
        // 更新订单状态
        order.status = 'paid';
        order.paidAt = new Date();
        
        return { success: true, transactionId: Date.now() };
    }
}
```

**思考题**:
1. 这个类违反了哪些编码原则？
2. 如何将功能分离到不同的类中？
3. 如何设计才能更容易扩展和测试？

<details>
<summary>点击查看参考答案</summary>

这个练习比较复杂，建议您先自己思考和尝试重构，然后参考以下设计思路：

**违反的原则**:
- SRP: 一个类承担了用户管理、产品管理、订单管理、支付处理等多个职责
- OCP: 添加新的支付方式需要修改现有代码
- DIP: 直接依赖具体实现，难以测试

**重构方向**:
1. 分离不同的业务领域（用户、产品、订单、支付）
2. 使用依赖注入提高可测试性
3. 使用策略模式处理不同的支付方式
4. 抽取验证逻辑到专门的验证器中

由于篇幅限制，完整的重构代码请参考实例代码文档中的相关示例。
</details>

---

## 🎉 练习总结

通过这些练习，您应该能够：

1. **识别代码异味**: 发现违反编码原则的代码模式
2. **应用重构技巧**: 使用适当的重构方法改进代码
3. **平衡不同原则**: 在实际场景中权衡不同原则的应用
4. **设计可扩展系统**: 创建易于维护和扩展的代码架构

**下一步建议**:
- 在实际项目中应用这些原则
- 与团队成员讨论和分享经验
- 持续学习和改进代码设计技能
- 建立代码审查和质量检查流程
