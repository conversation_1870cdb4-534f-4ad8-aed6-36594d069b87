<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ 'sidebar--collapsed': appStore.sidebarCollapsed }">
      <div class="sidebar__header">
        <div class="logo">
          <el-icon><Grid /></el-icon>
          <span v-show="!appStore.sidebarCollapsed" class="logo__text">Vue GraphQL</span>
        </div>
      </div>
      
      <nav class="sidebar__nav">
        <el-menu
          :default-active="$route.path"
          :collapse="appStore.sidebarCollapsed"
          :unique-opened="true"
          router
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-menu-item
              v-if="!route.meta?.roles || authStore.hasRole(route.meta.roles)"
              :index="route.path || ''"
            >
              <el-icon v-if="route.meta?.icon">
                <component :is="route.meta.icon" />
              </el-icon>
              <template #title>{{ route.meta?.title }}</template>
            </el-menu-item>
          </template>
        </el-menu>
      </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="header">
        <div class="header__left">
          <el-button
            type="text"
            @click="appStore.toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon><Expand v-if="appStore.sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>
          
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="breadcrumb in appStore.breadcrumbs"
              :key="breadcrumb.name"
              :to="breadcrumb.path"
            >
              {{ breadcrumb.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header__right">
          <!-- 主题切换 -->
          <el-tooltip content="切换主题">
            <el-button
              type="text"
              @click="appStore.toggleTheme"
              class="theme-toggle"
            >
              <el-icon><Sunny v-if="appStore.theme.isDark" /><Moon v-else /></el-icon>
            </el-button>
          </el-tooltip>

          <!-- 通知 -->
          <el-badge :value="appStore.notificationCount" :hidden="appStore.notificationCount === 0">
            <el-button type="text">
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>

          <!-- 用户菜单 -->
          <el-dropdown trigger="click" @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="authStore.user?.avatar">
                {{ authStore.initials }}
              </el-avatar>
              <span v-show="!appStore.sidebarCollapsed" class="user-name">
                {{ authStore.fullName }}
              </span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { menuRoutes } from '@/router'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'

const authStore = useAuthStore()
const appStore = useAppStore()
const router = useRouter()

// 处理用户菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      // TODO: 实现设置页面
      appStore.notifyInfo('提示', '设置功能正在开发中')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '确认', {
          type: 'warning'
        })
        authStore.logout()
        router.push('/login')
        appStore.notifySuccess('退出成功', '您已成功退出登录')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 250px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  overflow: hidden;

  &--collapsed {
    width: 64px;
  }

  &__header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  &__nav {
    height: calc(100% - 60px);
    overflow-y: auto;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-color-primary);

  &__text {
    transition: opacity 0.3s ease;
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);

  &__left {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  &__right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.sidebar-toggle {
  font-size: 18px;
}

.theme-toggle {
  font-size: 18px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background: var(--el-fill-color-light);
  }
}

.user-name {
  font-size: 14px;
  font-weight: 500;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: var(--el-bg-color-page);
}

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &--collapsed {
      width: 250px;
      transform: translateX(0);
    }
  }

  .main-content {
    width: 100%;
  }

  .header {
    padding: 0 16px;

    &__left {
      gap: 12px;
    }

    &__right {
      gap: 12px;
    }
  }

  .content {
    padding: 16px;
  }

  .user-name {
    display: none;
  }
}
</style>
